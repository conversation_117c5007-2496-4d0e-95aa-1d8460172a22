import './polyfills.server.mjs';
import{a as O,b as Ht,d as tn,e as zt,f as Uc,g as at,h as g}from"./chunk-2FGBTQRU.mjs";var ul=zt((N2,ll)=>{"use strict";var lt=-1,X=1,U=0;function hr(r,t,e,s,n){if(r===t)return r?[[U,r]]:[];if(e!=null){var i=Ig(r,t,e);if(i)return i}var o=bn(r,t),a=r.substring(0,o);r=r.substring(o),t=t.substring(o),o=as(r,t);var l=r.substring(r.length-o);r=r.substring(0,r.length-o),t=t.substring(0,t.length-o);var u=Tg(r,t);return a&&u.unshift([U,a]),l&&u.push([U,l]),yn(u,n),s&&Lg(u),u}function Tg(r,t){var e;if(!r)return[[X,t]];if(!t)return[[lt,r]];var s=r.length>t.length?r:t,n=r.length>t.length?t:r,i=s.indexOf(n);if(i!==-1)return e=[[X,s.substring(0,i)],[U,n],[X,s.substring(i+n.length)]],r.length>t.length&&(e[0][0]=e[2][0]=lt),e;if(n.length===1)return[[lt,r],[X,t]];var o=Sg(r,t);if(o){var a=o[0],l=o[1],u=o[2],c=o[3],f=o[4],h=hr(a,u),p=hr(l,c);return h.concat([[U,f]],p)}return wg(r,t)}function wg(r,t){for(var e=r.length,s=t.length,n=Math.ceil((e+s)/2),i=n,o=2*n,a=new Array(o),l=new Array(o),u=0;u<o;u++)a[u]=-1,l[u]=-1;a[i+1]=0,l[i+1]=0;for(var c=e-s,f=c%2!==0,h=0,p=0,m=0,x=0,A=0;A<n;A++){for(var b=-A+h;b<=A-p;b+=2){var E=i+b,N;b===-A||b!==A&&a[E-1]<a[E+1]?N=a[E+1]:N=a[E-1]+1;for(var T=N-b;N<e&&T<s&&r.charAt(N)===t.charAt(T);)N++,T++;if(a[E]=N,N>e)p+=2;else if(T>s)h+=2;else if(f){var w=i+c-b;if(w>=0&&w<o&&l[w]!==-1){var _=e-l[w];if(N>=_)return Ja(r,t,N,T)}}}for(var J=-A+m;J<=A-x;J+=2){var w=i+J,_;J===-A||J!==A&&l[w-1]<l[w+1]?_=l[w+1]:_=l[w-1]+1;for(var gt=_-J;_<e&&gt<s&&r.charAt(e-_-1)===t.charAt(s-gt-1);)_++,gt++;if(l[w]=_,_>e)x+=2;else if(gt>s)m+=2;else if(!f){var E=i+c-J;if(E>=0&&E<o&&a[E]!==-1){var N=a[E],T=i+N-E;if(_=e-_,N>=_)return Ja(r,t,N,T)}}}}return[[lt,r],[X,t]]}function Ja(r,t,e,s){var n=r.substring(0,e),i=t.substring(0,s),o=r.substring(e),a=t.substring(s),l=hr(n,i),u=hr(o,a);return l.concat(u)}function bn(r,t){if(!r||!t||r.charAt(0)!==t.charAt(0))return 0;for(var e=0,s=Math.min(r.length,t.length),n=s,i=0;e<n;)r.substring(i,n)==t.substring(i,n)?(e=n,i=e):s=n,n=Math.floor((s-e)/2+e);return nl(r.charCodeAt(n-1))&&n--,n}function tl(r,t){var e=r.length,s=t.length;if(e==0||s==0)return 0;e>s?r=r.substring(e-s):e<s&&(t=t.substring(0,e));var n=Math.min(e,s);if(r==t)return n;for(var i=0,o=1;;){var a=r.substring(n-o),l=t.indexOf(a);if(l==-1)return i;o+=l,(l==0||r.substring(n-o)==t.substring(0,o))&&(i=o,o++)}}function as(r,t){if(!r||!t||r.slice(-1)!==t.slice(-1))return 0;for(var e=0,s=Math.min(r.length,t.length),n=s,i=0;e<n;)r.substring(r.length-n,r.length-i)==t.substring(t.length-n,t.length-i)?(e=n,i=e):s=n,n=Math.floor((s-e)/2+e);return il(r.charCodeAt(r.length-n))&&n--,n}function Sg(r,t){var e=r.length>t.length?r:t,s=r.length>t.length?t:r;if(e.length<4||s.length*2<e.length)return null;function n(p,m,x){for(var A=p.substring(x,x+Math.floor(p.length/4)),b=-1,E="",N,T,w,_;(b=m.indexOf(A,b+1))!==-1;){var J=bn(p.substring(x),m.substring(b)),gt=as(p.substring(0,x),m.substring(0,b));E.length<gt+J&&(E=m.substring(b-gt,b)+m.substring(b,b+J),N=p.substring(0,x-gt),T=p.substring(x+J),w=m.substring(0,b-gt),_=m.substring(b+J))}return E.length*2>=p.length?[N,T,w,_,E]:null}var i=n(e,s,Math.ceil(e.length/4)),o=n(e,s,Math.ceil(e.length/2)),a;if(!i&&!o)return null;o?i?a=i[4].length>o[4].length?i:o:a=o:a=i;var l,u,c,f;r.length>t.length?(l=a[0],u=a[1],c=a[2],f=a[3]):(c=a[0],f=a[1],l=a[2],u=a[3]);var h=a[4];return[l,u,c,f,h]}function Lg(r){for(var t=!1,e=[],s=0,n=null,i=0,o=0,a=0,l=0,u=0;i<r.length;)r[i][0]==U?(e[s++]=i,o=l,a=u,l=0,u=0,n=r[i][1]):(r[i][0]==X?l+=r[i][1].length:u+=r[i][1].length,n&&n.length<=Math.max(o,a)&&n.length<=Math.max(l,u)&&(r.splice(e[s-1],0,[lt,n]),r[e[s-1]+1][0]=X,s--,s--,i=s>0?e[s-1]:-1,o=0,a=0,l=0,u=0,n=null,t=!0)),i++;for(t&&yn(r),qg(r),i=1;i<r.length;){if(r[i-1][0]==lt&&r[i][0]==X){var c=r[i-1][1],f=r[i][1],h=tl(c,f),p=tl(f,c);h>=p?(h>=c.length/2||h>=f.length/2)&&(r.splice(i,0,[U,f.substring(0,h)]),r[i-1][1]=c.substring(0,c.length-h),r[i+1][1]=f.substring(h),i++):(p>=c.length/2||p>=f.length/2)&&(r.splice(i,0,[U,c.substring(0,p)]),r[i-1][0]=X,r[i-1][1]=f.substring(0,f.length-p),r[i+1][0]=lt,r[i+1][1]=c.substring(p),i++),i++}i++}}var el=/[^a-zA-Z0-9]/,rl=/\s/,sl=/[\r\n]/,Og=/\n\r?\n$/,Cg=/^\r?\n\r?\n/;function qg(r){function t(p,m){if(!p||!m)return 6;var x=p.charAt(p.length-1),A=m.charAt(0),b=x.match(el),E=A.match(el),N=b&&x.match(rl),T=E&&A.match(rl),w=N&&x.match(sl),_=T&&A.match(sl),J=w&&p.match(Og),gt=_&&m.match(Cg);return J||gt?5:w||_?4:b&&!N&&T?3:N||T?2:b||E?1:0}for(var e=1;e<r.length-1;){if(r[e-1][0]==U&&r[e+1][0]==U){var s=r[e-1][1],n=r[e][1],i=r[e+1][1],o=as(s,n);if(o){var a=n.substring(n.length-o);s=s.substring(0,s.length-o),n=a+n.substring(0,n.length-o),i=a+i}for(var l=s,u=n,c=i,f=t(s,n)+t(n,i);n.charAt(0)===i.charAt(0);){s+=n.charAt(0),n=n.substring(1)+i.charAt(0),i=i.substring(1);var h=t(s,n)+t(n,i);h>=f&&(f=h,l=s,u=n,c=i)}r[e-1][1]!=l&&(l?r[e-1][1]=l:(r.splice(e-1,1),e--),r[e][1]=u,c?r[e+1][1]=c:(r.splice(e+1,1),e--))}e++}}function yn(r,t){r.push([U,""]);for(var e=0,s=0,n=0,i="",o="",a;e<r.length;){if(e<r.length-1&&!r[e][1]){r.splice(e,1);continue}switch(r[e][0]){case X:n++,o+=r[e][1],e++;break;case lt:s++,i+=r[e][1],e++;break;case U:var l=e-n-s-1;if(t){if(l>=0&&al(r[l][1])){var u=r[l][1].slice(-1);if(r[l][1]=r[l][1].slice(0,-1),i=u+i,o=u+o,!r[l][1]){r.splice(l,1),e--;var c=l-1;r[c]&&r[c][0]===X&&(n++,o=r[c][1]+o,c--),r[c]&&r[c][0]===lt&&(s++,i=r[c][1]+i,c--),l=c}}if(ol(r[e][1])){var u=r[e][1].charAt(0);r[e][1]=r[e][1].slice(1),i+=u,o+=u}}if(e<r.length-1&&!r[e][1]){r.splice(e,1);break}if(i.length>0||o.length>0){i.length>0&&o.length>0&&(a=bn(o,i),a!==0&&(l>=0?r[l][1]+=o.substring(0,a):(r.splice(0,0,[U,o.substring(0,a)]),e++),o=o.substring(a),i=i.substring(a)),a=as(o,i),a!==0&&(r[e][1]=o.substring(o.length-a)+r[e][1],o=o.substring(0,o.length-a),i=i.substring(0,i.length-a)));var f=n+s;i.length===0&&o.length===0?(r.splice(e-f,f),e=e-f):i.length===0?(r.splice(e-f,f,[X,o]),e=e-f+1):o.length===0?(r.splice(e-f,f,[lt,i]),e=e-f+1):(r.splice(e-f,f,[lt,i],[X,o]),e=e-f+2)}e!==0&&r[e-1][0]===U?(r[e-1][1]+=r[e][1],r.splice(e,1)):e++,n=0,s=0,i="",o="";break}}r[r.length-1][1]===""&&r.pop();var h=!1;for(e=1;e<r.length-1;)r[e-1][0]===U&&r[e+1][0]===U&&(r[e][1].substring(r[e][1].length-r[e-1][1].length)===r[e-1][1]?(r[e][1]=r[e-1][1]+r[e][1].substring(0,r[e][1].length-r[e-1][1].length),r[e+1][1]=r[e-1][1]+r[e+1][1],r.splice(e-1,1),h=!0):r[e][1].substring(0,r[e+1][1].length)==r[e+1][1]&&(r[e-1][1]+=r[e+1][1],r[e][1]=r[e][1].substring(r[e+1][1].length)+r[e+1][1],r.splice(e+1,1),h=!0)),e++;h&&yn(r,t)}function nl(r){return r>=55296&&r<=56319}function il(r){return r>=56320&&r<=57343}function ol(r){return il(r.charCodeAt(0))}function al(r){return nl(r.charCodeAt(r.length-1))}function _g(r){for(var t=[],e=0;e<r.length;e++)r[e][1].length>0&&t.push(r[e]);return t}function gn(r,t,e,s){return al(r)||ol(s)?null:_g([[U,r],[lt,t],[X,e],[U,s]])}function Ig(r,t,e){var s=typeof e=="number"?{index:e,length:0}:e.oldRange,n=typeof e=="number"?null:e.newRange,i=r.length,o=t.length;if(s.length===0&&(n===null||n.length===0)){var a=s.index,l=r.slice(0,a),u=r.slice(a),c=n?n.index:null;t:{var f=a+o-i;if(c!==null&&c!==f||f<0||f>o)break t;var h=t.slice(0,f),p=t.slice(f);if(p!==u)break t;var m=Math.min(a,f),x=l.slice(0,m),A=h.slice(0,m);if(x!==A)break t;var b=l.slice(m),E=h.slice(m);return gn(x,b,E,u)}t:{if(c!==null&&c!==a)break t;var N=a,h=t.slice(0,N),p=t.slice(N);if(h!==l)break t;var T=Math.min(i-N,o-N),w=u.slice(u.length-T),_=p.slice(p.length-T);if(w!==_)break t;var b=u.slice(0,u.length-T),E=p.slice(0,p.length-T);return gn(l,b,E,w)}}if(s.length>0&&n&&n.length===0)t:{var x=r.slice(0,s.index),w=r.slice(s.index+s.length),m=x.length,T=w.length;if(o<m+T)break t;var A=t.slice(0,m),_=t.slice(o-T);if(x!==A||w!==_)break t;var b=r.slice(m,i-T),E=t.slice(m,o-T);return gn(x,b,E,w)}return null}function ls(r,t,e,s){return hr(r,t,e,s,!0)}ls.INSERT=X;ls.DELETE=lt;ls.EQUAL=U;ll.exports=ls});var kn=zt((dr,$e)=>{"use strict";var kg=200,Al="__lodash_hash_undefined__",El=9007199254740991,Sn="[object Arguments]",Rg="[object Array]",Nl="[object Boolean]",Tl="[object Date]",Bg="[object Error]",Ln="[object Function]",wl="[object GeneratorFunction]",us="[object Map]",Sl="[object Number]",On="[object Object]",cl="[object Promise]",Ll="[object RegExp]",cs="[object Set]",Ol="[object String]",Cl="[object Symbol]",vn="[object WeakMap]",ql="[object ArrayBuffer]",fs="[object DataView]",_l="[object Float32Array]",Il="[object Float64Array]",kl="[object Int8Array]",Rl="[object Int16Array]",Bl="[object Int32Array]",Ml="[object Uint8Array]",Dl="[object Uint8ClampedArray]",jl="[object Uint16Array]",Pl="[object Uint32Array]",Mg=/[\\^$.*+?()[\]{}|]/g,Dg=/\w*$/,jg=/^\[object .+?Constructor\]$/,Pg=/^(?:0|[1-9]\d*)$/,L={};L[Sn]=L[Rg]=L[ql]=L[fs]=L[Nl]=L[Tl]=L[_l]=L[Il]=L[kl]=L[Rl]=L[Bl]=L[us]=L[Sl]=L[On]=L[Ll]=L[cs]=L[Ol]=L[Cl]=L[Ml]=L[Dl]=L[jl]=L[Pl]=!0;L[Bg]=L[Ln]=L[vn]=!1;var Ug=typeof global=="object"&&global&&global.Object===Object&&global,Fg=typeof self=="object"&&self&&self.Object===Object&&self,It=Ug||Fg||Function("return this")(),Ul=typeof dr=="object"&&dr&&!dr.nodeType&&dr,fl=Ul&&typeof $e=="object"&&$e&&!$e.nodeType&&$e,Hg=fl&&fl.exports===Ul;function zg(r,t){return r.set(t[0],t[1]),r}function $g(r,t){return r.add(t),r}function Kg(r,t){for(var e=-1,s=r?r.length:0;++e<s&&t(r[e],e,r)!==!1;);return r}function Gg(r,t){for(var e=-1,s=t.length,n=r.length;++e<s;)r[n+e]=t[e];return r}function Fl(r,t,e,s){var n=-1,i=r?r.length:0;for(s&&i&&(e=r[++n]);++n<i;)e=t(e,r[n],n,r);return e}function Vg(r,t){for(var e=-1,s=Array(r);++e<r;)s[e]=t(e);return s}function Wg(r,t){return r==null?void 0:r[t]}function Hl(r){var t=!1;if(r!=null&&typeof r.toString!="function")try{t=!!(r+"")}catch{}return t}function hl(r){var t=-1,e=Array(r.size);return r.forEach(function(s,n){e[++t]=[n,s]}),e}function Cn(r,t){return function(e){return r(t(e))}}function dl(r){var t=-1,e=Array(r.size);return r.forEach(function(s){e[++t]=s}),e}var Zg=Array.prototype,Xg=Function.prototype,hs=Object.prototype,xn=It["__core-js_shared__"],pl=function(){var r=/[^.]+$/.exec(xn&&xn.keys&&xn.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),zl=Xg.toString,Jt=hs.hasOwnProperty,ds=hs.toString,Yg=RegExp("^"+zl.call(Jt).replace(Mg,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ml=Hg?It.Buffer:void 0,gl=It.Symbol,bl=It.Uint8Array,Qg=Cn(Object.getPrototypeOf,Object),Jg=Object.create,t1=hs.propertyIsEnumerable,e1=Zg.splice,yl=Object.getOwnPropertySymbols,r1=ml?ml.isBuffer:void 0,s1=Cn(Object.keys,Object),An=Ve(It,"DataView"),pr=Ve(It,"Map"),En=Ve(It,"Promise"),Nn=Ve(It,"Set"),Tn=Ve(It,"WeakMap"),mr=Ve(Object,"create"),n1=pe(An),i1=pe(pr),o1=pe(En),a1=pe(Nn),l1=pe(Tn),xl=gl?gl.prototype:void 0,vl=xl?xl.valueOf:void 0;function de(r){var t=-1,e=r?r.length:0;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}function u1(){this.__data__=mr?mr(null):{}}function c1(r){return this.has(r)&&delete this.__data__[r]}function f1(r){var t=this.__data__;if(mr){var e=t[r];return e===Al?void 0:e}return Jt.call(t,r)?t[r]:void 0}function h1(r){var t=this.__data__;return mr?t[r]!==void 0:Jt.call(t,r)}function d1(r,t){var e=this.__data__;return e[r]=mr&&t===void 0?Al:t,this}de.prototype.clear=u1;de.prototype.delete=c1;de.prototype.get=f1;de.prototype.has=h1;de.prototype.set=d1;function kt(r){var t=-1,e=r?r.length:0;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}function p1(){this.__data__=[]}function m1(r){var t=this.__data__,e=ps(t,r);if(e<0)return!1;var s=t.length-1;return e==s?t.pop():e1.call(t,e,1),!0}function g1(r){var t=this.__data__,e=ps(t,r);return e<0?void 0:t[e][1]}function b1(r){return ps(this.__data__,r)>-1}function y1(r,t){var e=this.__data__,s=ps(e,r);return s<0?e.push([r,t]):e[s][1]=t,this}kt.prototype.clear=p1;kt.prototype.delete=m1;kt.prototype.get=g1;kt.prototype.has=b1;kt.prototype.set=y1;function Ke(r){var t=-1,e=r?r.length:0;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}function x1(){this.__data__={hash:new de,map:new(pr||kt),string:new de}}function v1(r){return ms(this,r).delete(r)}function A1(r){return ms(this,r).get(r)}function E1(r){return ms(this,r).has(r)}function N1(r,t){return ms(this,r).set(r,t),this}Ke.prototype.clear=x1;Ke.prototype.delete=v1;Ke.prototype.get=A1;Ke.prototype.has=E1;Ke.prototype.set=N1;function Ge(r){this.__data__=new kt(r)}function T1(){this.__data__=new kt}function w1(r){return this.__data__.delete(r)}function S1(r){return this.__data__.get(r)}function L1(r){return this.__data__.has(r)}function O1(r,t){var e=this.__data__;if(e instanceof kt){var s=e.__data__;if(!pr||s.length<kg-1)return s.push([r,t]),this;e=this.__data__=new Ke(s)}return e.set(r,t),this}Ge.prototype.clear=T1;Ge.prototype.delete=w1;Ge.prototype.get=S1;Ge.prototype.has=L1;Ge.prototype.set=O1;function C1(r,t){var e=_n(r)||J1(r)?Vg(r.length,String):[],s=e.length,n=!!s;for(var i in r)(t||Jt.call(r,i))&&!(n&&(i=="length"||Z1(i,s)))&&e.push(i);return e}function $l(r,t,e){var s=r[t];(!(Jt.call(r,t)&&Wl(s,e))||e===void 0&&!(t in r))&&(r[t]=e)}function ps(r,t){for(var e=r.length;e--;)if(Wl(r[e][0],t))return e;return-1}function q1(r,t){return r&&Kl(t,In(t),r)}function wn(r,t,e,s,n,i,o){var a;if(s&&(a=i?s(r,n,i,o):s(r)),a!==void 0)return a;if(!gs(r))return r;var l=_n(r);if(l){if(a=G1(r),!t)return z1(r,a)}else{var u=he(r),c=u==Ln||u==wl;if(e0(r))return M1(r,t);if(u==On||u==Sn||c&&!i){if(Hl(r))return i?r:{};if(a=V1(c?{}:r),!t)return $1(r,q1(a,r))}else{if(!L[u])return i?r:{};a=W1(r,u,wn,t)}}o||(o=new Ge);var f=o.get(r);if(f)return f;if(o.set(r,a),!l)var h=e?K1(r):In(r);return Kg(h||r,function(p,m){h&&(m=p,p=r[m]),$l(a,m,wn(p,t,e,s,m,r,o))}),a}function _1(r){return gs(r)?Jg(r):{}}function I1(r,t,e){var s=t(r);return _n(r)?s:Gg(s,e(r))}function k1(r){return ds.call(r)}function R1(r){if(!gs(r)||Y1(r))return!1;var t=Xl(r)||Hl(r)?Yg:jg;return t.test(pe(r))}function B1(r){if(!Vl(r))return s1(r);var t=[];for(var e in Object(r))Jt.call(r,e)&&e!="constructor"&&t.push(e);return t}function M1(r,t){if(t)return r.slice();var e=new r.constructor(r.length);return r.copy(e),e}function qn(r){var t=new r.constructor(r.byteLength);return new bl(t).set(new bl(r)),t}function D1(r,t){var e=t?qn(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.byteLength)}function j1(r,t,e){var s=t?e(hl(r),!0):hl(r);return Fl(s,zg,new r.constructor)}function P1(r){var t=new r.constructor(r.source,Dg.exec(r));return t.lastIndex=r.lastIndex,t}function U1(r,t,e){var s=t?e(dl(r),!0):dl(r);return Fl(s,$g,new r.constructor)}function F1(r){return vl?Object(vl.call(r)):{}}function H1(r,t){var e=t?qn(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.length)}function z1(r,t){var e=-1,s=r.length;for(t||(t=Array(s));++e<s;)t[e]=r[e];return t}function Kl(r,t,e,s){e||(e={});for(var n=-1,i=t.length;++n<i;){var o=t[n],a=s?s(e[o],r[o],o,e,r):void 0;$l(e,o,a===void 0?r[o]:a)}return e}function $1(r,t){return Kl(r,Gl(r),t)}function K1(r){return I1(r,In,Gl)}function ms(r,t){var e=r.__data__;return X1(t)?e[typeof t=="string"?"string":"hash"]:e.map}function Ve(r,t){var e=Wg(r,t);return R1(e)?e:void 0}var Gl=yl?Cn(yl,Object):n0,he=k1;(An&&he(new An(new ArrayBuffer(1)))!=fs||pr&&he(new pr)!=us||En&&he(En.resolve())!=cl||Nn&&he(new Nn)!=cs||Tn&&he(new Tn)!=vn)&&(he=function(r){var t=ds.call(r),e=t==On?r.constructor:void 0,s=e?pe(e):void 0;if(s)switch(s){case n1:return fs;case i1:return us;case o1:return cl;case a1:return cs;case l1:return vn}return t});function G1(r){var t=r.length,e=r.constructor(t);return t&&typeof r[0]=="string"&&Jt.call(r,"index")&&(e.index=r.index,e.input=r.input),e}function V1(r){return typeof r.constructor=="function"&&!Vl(r)?_1(Qg(r)):{}}function W1(r,t,e,s){var n=r.constructor;switch(t){case ql:return qn(r);case Nl:case Tl:return new n(+r);case fs:return D1(r,s);case _l:case Il:case kl:case Rl:case Bl:case Ml:case Dl:case jl:case Pl:return H1(r,s);case us:return j1(r,s,e);case Sl:case Ol:return new n(r);case Ll:return P1(r);case cs:return U1(r,s,e);case Cl:return F1(r)}}function Z1(r,t){return t=t==null?El:t,!!t&&(typeof r=="number"||Pg.test(r))&&r>-1&&r%1==0&&r<t}function X1(r){var t=typeof r;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?r!=="__proto__":r===null}function Y1(r){return!!pl&&pl in r}function Vl(r){var t=r&&r.constructor,e=typeof t=="function"&&t.prototype||hs;return r===e}function pe(r){if(r!=null){try{return zl.call(r)}catch{}try{return r+""}catch{}}return""}function Q1(r){return wn(r,!0,!0)}function Wl(r,t){return r===t||r!==r&&t!==t}function J1(r){return t0(r)&&Jt.call(r,"callee")&&(!t1.call(r,"callee")||ds.call(r)==Sn)}var _n=Array.isArray;function Zl(r){return r!=null&&r0(r.length)&&!Xl(r)}function t0(r){return s0(r)&&Zl(r)}var e0=r1||i0;function Xl(r){var t=gs(r)?ds.call(r):"";return t==Ln||t==wl}function r0(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=El}function gs(r){var t=typeof r;return!!r&&(t=="object"||t=="function")}function s0(r){return!!r&&typeof r=="object"}function In(r){return Zl(r)?C1(r):B1(r)}function n0(){return[]}function i0(){return!1}$e.exports=Q1});var Kn=zt((gr,Ze)=>{"use strict";var o0=200,$n="__lodash_hash_undefined__",Ts=1,lu=2,uu=9007199254740991,bs="[object Arguments]",Dn="[object Array]",a0="[object AsyncFunction]",cu="[object Boolean]",fu="[object Date]",hu="[object Error]",du="[object Function]",l0="[object GeneratorFunction]",ys="[object Map]",pu="[object Number]",u0="[object Null]",We="[object Object]",Yl="[object Promise]",c0="[object Proxy]",mu="[object RegExp]",xs="[object Set]",gu="[object String]",f0="[object Symbol]",h0="[object Undefined]",jn="[object WeakMap]",bu="[object ArrayBuffer]",vs="[object DataView]",d0="[object Float32Array]",p0="[object Float64Array]",m0="[object Int8Array]",g0="[object Int16Array]",b0="[object Int32Array]",y0="[object Uint8Array]",x0="[object Uint8ClampedArray]",v0="[object Uint16Array]",A0="[object Uint32Array]",E0=/[\\^$.*+?()[\]{}|]/g,N0=/^\[object .+?Constructor\]$/,T0=/^(?:0|[1-9]\d*)$/,q={};q[d0]=q[p0]=q[m0]=q[g0]=q[b0]=q[y0]=q[x0]=q[v0]=q[A0]=!0;q[bs]=q[Dn]=q[bu]=q[cu]=q[vs]=q[fu]=q[hu]=q[du]=q[ys]=q[pu]=q[We]=q[mu]=q[xs]=q[gu]=q[jn]=!1;var yu=typeof global=="object"&&global&&global.Object===Object&&global,w0=typeof self=="object"&&self&&self.Object===Object&&self,Rt=yu||w0||Function("return this")(),xu=typeof gr=="object"&&gr&&!gr.nodeType&&gr,Ql=xu&&typeof Ze=="object"&&Ze&&!Ze.nodeType&&Ze,vu=Ql&&Ql.exports===xu,Rn=vu&&yu.process,Jl=function(){try{return Rn&&Rn.binding&&Rn.binding("util")}catch{}}(),tu=Jl&&Jl.isTypedArray;function S0(r,t){for(var e=-1,s=r==null?0:r.length,n=0,i=[];++e<s;){var o=r[e];t(o,e,r)&&(i[n++]=o)}return i}function L0(r,t){for(var e=-1,s=t.length,n=r.length;++e<s;)r[n+e]=t[e];return r}function O0(r,t){for(var e=-1,s=r==null?0:r.length;++e<s;)if(t(r[e],e,r))return!0;return!1}function C0(r,t){for(var e=-1,s=Array(r);++e<r;)s[e]=t(e);return s}function q0(r){return function(t){return r(t)}}function _0(r,t){return r.has(t)}function I0(r,t){return r==null?void 0:r[t]}function k0(r){var t=-1,e=Array(r.size);return r.forEach(function(s,n){e[++t]=[n,s]}),e}function R0(r,t){return function(e){return r(t(e))}}function B0(r){var t=-1,e=Array(r.size);return r.forEach(function(s){e[++t]=s}),e}var M0=Array.prototype,D0=Function.prototype,ws=Object.prototype,Bn=Rt["__core-js_shared__"],Au=D0.toString,Tt=ws.hasOwnProperty,eu=function(){var r=/[^.]+$/.exec(Bn&&Bn.keys&&Bn.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),Eu=ws.toString,j0=RegExp("^"+Au.call(Tt).replace(E0,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ru=vu?Rt.Buffer:void 0,As=Rt.Symbol,su=Rt.Uint8Array,Nu=ws.propertyIsEnumerable,P0=M0.splice,me=As?As.toStringTag:void 0,nu=Object.getOwnPropertySymbols,U0=ru?ru.isBuffer:void 0,F0=R0(Object.keys,Object),Pn=Xe(Rt,"DataView"),br=Xe(Rt,"Map"),Un=Xe(Rt,"Promise"),Fn=Xe(Rt,"Set"),Hn=Xe(Rt,"WeakMap"),yr=Xe(Object,"create"),H0=ye(Pn),z0=ye(br),$0=ye(Un),K0=ye(Fn),G0=ye(Hn),iu=As?As.prototype:void 0,Mn=iu?iu.valueOf:void 0;function ge(r){var t=-1,e=r==null?0:r.length;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}function V0(){this.__data__=yr?yr(null):{},this.size=0}function W0(r){var t=this.has(r)&&delete this.__data__[r];return this.size-=t?1:0,t}function Z0(r){var t=this.__data__;if(yr){var e=t[r];return e===$n?void 0:e}return Tt.call(t,r)?t[r]:void 0}function X0(r){var t=this.__data__;return yr?t[r]!==void 0:Tt.call(t,r)}function Y0(r,t){var e=this.__data__;return this.size+=this.has(r)?0:1,e[r]=yr&&t===void 0?$n:t,this}ge.prototype.clear=V0;ge.prototype.delete=W0;ge.prototype.get=Z0;ge.prototype.has=X0;ge.prototype.set=Y0;function Bt(r){var t=-1,e=r==null?0:r.length;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}function Q0(){this.__data__=[],this.size=0}function J0(r){var t=this.__data__,e=Ss(t,r);if(e<0)return!1;var s=t.length-1;return e==s?t.pop():P0.call(t,e,1),--this.size,!0}function tb(r){var t=this.__data__,e=Ss(t,r);return e<0?void 0:t[e][1]}function eb(r){return Ss(this.__data__,r)>-1}function rb(r,t){var e=this.__data__,s=Ss(e,r);return s<0?(++this.size,e.push([r,t])):e[s][1]=t,this}Bt.prototype.clear=Q0;Bt.prototype.delete=J0;Bt.prototype.get=tb;Bt.prototype.has=eb;Bt.prototype.set=rb;function be(r){var t=-1,e=r==null?0:r.length;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}function sb(){this.size=0,this.__data__={hash:new ge,map:new(br||Bt),string:new ge}}function nb(r){var t=Ls(this,r).delete(r);return this.size-=t?1:0,t}function ib(r){return Ls(this,r).get(r)}function ob(r){return Ls(this,r).has(r)}function ab(r,t){var e=Ls(this,r),s=e.size;return e.set(r,t),this.size+=e.size==s?0:1,this}be.prototype.clear=sb;be.prototype.delete=nb;be.prototype.get=ib;be.prototype.has=ob;be.prototype.set=ab;function Es(r){var t=-1,e=r==null?0:r.length;for(this.__data__=new be;++t<e;)this.add(r[t])}function lb(r){return this.__data__.set(r,$n),this}function ub(r){return this.__data__.has(r)}Es.prototype.add=Es.prototype.push=lb;Es.prototype.has=ub;function ee(r){var t=this.__data__=new Bt(r);this.size=t.size}function cb(){this.__data__=new Bt,this.size=0}function fb(r){var t=this.__data__,e=t.delete(r);return this.size=t.size,e}function hb(r){return this.__data__.get(r)}function db(r){return this.__data__.has(r)}function pb(r,t){var e=this.__data__;if(e instanceof Bt){var s=e.__data__;if(!br||s.length<o0-1)return s.push([r,t]),this.size=++e.size,this;e=this.__data__=new be(s)}return e.set(r,t),this.size=e.size,this}ee.prototype.clear=cb;ee.prototype.delete=fb;ee.prototype.get=hb;ee.prototype.has=db;ee.prototype.set=pb;function mb(r,t){var e=Ns(r),s=!e&&qb(r),n=!e&&!s&&zn(r),i=!e&&!s&&!n&&qu(r),o=e||s||n||i,a=o?C0(r.length,String):[],l=a.length;for(var u in r)(t||Tt.call(r,u))&&!(o&&(u=="length"||n&&(u=="offset"||u=="parent")||i&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||wb(u,l)))&&a.push(u);return a}function Ss(r,t){for(var e=r.length;e--;)if(Su(r[e][0],t))return e;return-1}function gb(r,t,e){var s=t(r);return Ns(r)?s:L0(s,e(r))}function vr(r){return r==null?r===void 0?h0:u0:me&&me in Object(r)?Nb(r):Cb(r)}function ou(r){return xr(r)&&vr(r)==bs}function Tu(r,t,e,s,n){return r===t?!0:r==null||t==null||!xr(r)&&!xr(t)?r!==r&&t!==t:bb(r,t,e,s,Tu,n)}function bb(r,t,e,s,n,i){var o=Ns(r),a=Ns(t),l=o?Dn:te(r),u=a?Dn:te(t);l=l==bs?We:l,u=u==bs?We:u;var c=l==We,f=u==We,h=l==u;if(h&&zn(r)){if(!zn(t))return!1;o=!0,c=!1}if(h&&!c)return i||(i=new ee),o||qu(r)?wu(r,t,e,s,n,i):Ab(r,t,l,e,s,n,i);if(!(e&Ts)){var p=c&&Tt.call(r,"__wrapped__"),m=f&&Tt.call(t,"__wrapped__");if(p||m){var x=p?r.value():r,A=m?t.value():t;return i||(i=new ee),n(x,A,e,s,i)}}return h?(i||(i=new ee),Eb(r,t,e,s,n,i)):!1}function yb(r){if(!Cu(r)||Lb(r))return!1;var t=Lu(r)?j0:N0;return t.test(ye(r))}function xb(r){return xr(r)&&Ou(r.length)&&!!q[vr(r)]}function vb(r){if(!Ob(r))return F0(r);var t=[];for(var e in Object(r))Tt.call(r,e)&&e!="constructor"&&t.push(e);return t}function wu(r,t,e,s,n,i){var o=e&Ts,a=r.length,l=t.length;if(a!=l&&!(o&&l>a))return!1;var u=i.get(r);if(u&&i.get(t))return u==t;var c=-1,f=!0,h=e&lu?new Es:void 0;for(i.set(r,t),i.set(t,r);++c<a;){var p=r[c],m=t[c];if(s)var x=o?s(m,p,c,t,r,i):s(p,m,c,r,t,i);if(x!==void 0){if(x)continue;f=!1;break}if(h){if(!O0(t,function(A,b){if(!_0(h,b)&&(p===A||n(p,A,e,s,i)))return h.push(b)})){f=!1;break}}else if(!(p===m||n(p,m,e,s,i))){f=!1;break}}return i.delete(r),i.delete(t),f}function Ab(r,t,e,s,n,i,o){switch(e){case vs:if(r.byteLength!=t.byteLength||r.byteOffset!=t.byteOffset)return!1;r=r.buffer,t=t.buffer;case bu:return!(r.byteLength!=t.byteLength||!i(new su(r),new su(t)));case cu:case fu:case pu:return Su(+r,+t);case hu:return r.name==t.name&&r.message==t.message;case mu:case gu:return r==t+"";case ys:var a=k0;case xs:var l=s&Ts;if(a||(a=B0),r.size!=t.size&&!l)return!1;var u=o.get(r);if(u)return u==t;s|=lu,o.set(r,t);var c=wu(a(r),a(t),s,n,i,o);return o.delete(r),c;case f0:if(Mn)return Mn.call(r)==Mn.call(t)}return!1}function Eb(r,t,e,s,n,i){var o=e&Ts,a=au(r),l=a.length,u=au(t),c=u.length;if(l!=c&&!o)return!1;for(var f=l;f--;){var h=a[f];if(!(o?h in t:Tt.call(t,h)))return!1}var p=i.get(r);if(p&&i.get(t))return p==t;var m=!0;i.set(r,t),i.set(t,r);for(var x=o;++f<l;){h=a[f];var A=r[h],b=t[h];if(s)var E=o?s(b,A,h,t,r,i):s(A,b,h,r,t,i);if(!(E===void 0?A===b||n(A,b,e,s,i):E)){m=!1;break}x||(x=h=="constructor")}if(m&&!x){var N=r.constructor,T=t.constructor;N!=T&&"constructor"in r&&"constructor"in t&&!(typeof N=="function"&&N instanceof N&&typeof T=="function"&&T instanceof T)&&(m=!1)}return i.delete(r),i.delete(t),m}function au(r){return gb(r,kb,Tb)}function Ls(r,t){var e=r.__data__;return Sb(t)?e[typeof t=="string"?"string":"hash"]:e.map}function Xe(r,t){var e=I0(r,t);return yb(e)?e:void 0}function Nb(r){var t=Tt.call(r,me),e=r[me];try{r[me]=void 0;var s=!0}catch{}var n=Eu.call(r);return s&&(t?r[me]=e:delete r[me]),n}var Tb=nu?function(r){return r==null?[]:(r=Object(r),S0(nu(r),function(t){return Nu.call(r,t)}))}:Rb,te=vr;(Pn&&te(new Pn(new ArrayBuffer(1)))!=vs||br&&te(new br)!=ys||Un&&te(Un.resolve())!=Yl||Fn&&te(new Fn)!=xs||Hn&&te(new Hn)!=jn)&&(te=function(r){var t=vr(r),e=t==We?r.constructor:void 0,s=e?ye(e):"";if(s)switch(s){case H0:return vs;case z0:return ys;case $0:return Yl;case K0:return xs;case G0:return jn}return t});function wb(r,t){return t=t==null?uu:t,!!t&&(typeof r=="number"||T0.test(r))&&r>-1&&r%1==0&&r<t}function Sb(r){var t=typeof r;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?r!=="__proto__":r===null}function Lb(r){return!!eu&&eu in r}function Ob(r){var t=r&&r.constructor,e=typeof t=="function"&&t.prototype||ws;return r===e}function Cb(r){return Eu.call(r)}function ye(r){if(r!=null){try{return Au.call(r)}catch{}try{return r+""}catch{}}return""}function Su(r,t){return r===t||r!==r&&t!==t}var qb=ou(function(){return arguments}())?ou:function(r){return xr(r)&&Tt.call(r,"callee")&&!Nu.call(r,"callee")},Ns=Array.isArray;function _b(r){return r!=null&&Ou(r.length)&&!Lu(r)}var zn=U0||Bb;function Ib(r,t){return Tu(r,t)}function Lu(r){if(!Cu(r))return!1;var t=vr(r);return t==du||t==l0||t==a0||t==c0}function Ou(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=uu}function Cu(r){var t=typeof r;return r!=null&&(t=="object"||t=="function")}function xr(r){return r!=null&&typeof r=="object"}var qu=tu?q0(tu):xb;function kb(r){return _b(r)?mb(r):vb(r)}function Rb(){return[]}function Bb(){return!1}Ze.exports=Ib});var _u=zt(Vn=>{"use strict";Object.defineProperty(Vn,"__esModule",{value:!0});var Mb=kn(),Db=Kn(),Gn;(function(r){function t(i={},o={},a=!1){typeof i!="object"&&(i={}),typeof o!="object"&&(o={});let l=Mb(o);a||(l=Object.keys(l).reduce((u,c)=>(l[c]!=null&&(u[c]=l[c]),u),{}));for(let u in i)i[u]!==void 0&&o[u]===void 0&&(l[u]=i[u]);return Object.keys(l).length>0?l:void 0}r.compose=t;function e(i={},o={}){typeof i!="object"&&(i={}),typeof o!="object"&&(o={});let a=Object.keys(i).concat(Object.keys(o)).reduce((l,u)=>(Db(i[u],o[u])||(l[u]=o[u]===void 0?null:o[u]),l),{});return Object.keys(a).length>0?a:void 0}r.diff=e;function s(i={},o={}){i=i||{};let a=Object.keys(o).reduce((l,u)=>(o[u]!==i[u]&&i[u]!==void 0&&(l[u]=o[u]),l),{});return Object.keys(i).reduce((l,u)=>(i[u]!==o[u]&&o[u]===void 0&&(l[u]=null),l),a)}r.invert=s;function n(i,o,a=!1){if(typeof i!="object")return o;if(typeof o!="object")return;if(!a)return o;let l=Object.keys(o).reduce((u,c)=>(i[c]===void 0&&(u[c]=o[c]),u),{});return Object.keys(l).length>0?l:void 0}r.transform=n})(Gn||(Gn={}));Vn.default=Gn});var Xn=zt(Zn=>{"use strict";Object.defineProperty(Zn,"__esModule",{value:!0});var Wn;(function(r){function t(e){return typeof e.delete=="number"?e.delete:typeof e.retain=="number"?e.retain:typeof e.retain=="object"&&e.retain!==null?1:typeof e.insert=="string"?e.insert.length:1}r.length=t})(Wn||(Wn={}));Zn.default=Wn});var ku=zt(Qn=>{"use strict";Object.defineProperty(Qn,"__esModule",{value:!0});var Iu=Xn(),Yn=class{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);let e=this.ops[this.index];if(e){let s=this.offset,n=Iu.default.length(e);if(t>=n-s?(t=n-s,this.index+=1,this.offset=0):this.offset+=t,typeof e.delete=="number")return{delete:t};{let i={};return e.attributes&&(i.attributes=e.attributes),typeof e.retain=="number"?i.retain=t:typeof e.retain=="object"&&e.retain!==null?i.retain=e.retain:typeof e.insert=="string"?i.insert=e.insert.substr(s,t):i.insert=e.insert,i}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?Iu.default.length(this.ops[this.index])-this.offset:1/0}peekType(){let t=this.ops[this.index];return t?typeof t.delete=="number"?"delete":typeof t.retain=="number"||typeof t.retain=="object"&&t.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{let t=this.offset,e=this.index,s=this.next(),n=this.ops.slice(this.index);return this.offset=t,this.index=e,[s].concat(n)}}else return[]}};Qn.default=Yn});var ut=zt((Dt,Cs)=>{"use strict";Object.defineProperty(Dt,"__esModule",{value:!0});Dt.AttributeMap=Dt.OpIterator=Dt.Op=void 0;var Os=ul(),jb=kn(),Jn=Kn(),xe=_u();Dt.AttributeMap=xe.default;var Mt=Xn();Dt.Op=Mt.default;var dt=ku();Dt.OpIterator=dt.default;var Pb="\0",Ru=(r,t)=>{if(typeof r!="object"||r===null)throw new Error(`cannot retain a ${typeof r}`);if(typeof t!="object"||t===null)throw new Error(`cannot retain a ${typeof t}`);let e=Object.keys(r)[0];if(!e||e!==Object.keys(t)[0])throw new Error(`embed types not matched: ${e} != ${Object.keys(t)[0]}`);return[e,r[e],t[e]]},jt=class r{constructor(t){Array.isArray(t)?this.ops=t:t!=null&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){let e=this.handlers[t];if(!e)throw new Error(`no handlers for embed type "${t}"`);return e}insert(t,e){let s={};return typeof t=="string"&&t.length===0?this:(s.insert=t,e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(s.attributes=e),this.push(s))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if(typeof t=="number"&&t<=0)return this;let s={retain:t};return e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(s.attributes=e),this.push(s)}push(t){let e=this.ops.length,s=this.ops[e-1];if(t=jb(t),typeof s=="object"){if(typeof t.delete=="number"&&typeof s.delete=="number")return this.ops[e-1]={delete:s.delete+t.delete},this;if(typeof s.delete=="number"&&t.insert!=null&&(e-=1,s=this.ops[e-1],typeof s!="object"))return this.ops.unshift(t),this;if(Jn(t.attributes,s.attributes)){if(typeof t.insert=="string"&&typeof s.insert=="string")return this.ops[e-1]={insert:s.insert+t.insert},typeof t.attributes=="object"&&(this.ops[e-1].attributes=t.attributes),this;if(typeof t.retain=="number"&&typeof s.retain=="number")return this.ops[e-1]={retain:s.retain+t.retain},typeof t.attributes=="object"&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){let t=this.ops[this.ops.length-1];return t&&typeof t.retain=="number"&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){let e=[],s=[];return this.forEach(n=>{(t(n)?e:s).push(n)}),[e,s]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce((t,e)=>e.insert?t+Mt.default.length(e):e.delete?t-e.delete:t,0)}length(){return this.reduce((t,e)=>t+Mt.default.length(e),0)}slice(t=0,e=1/0){let s=[],n=new dt.default(this.ops),i=0;for(;i<e&&n.hasNext();){let o;i<t?o=n.next(t-i):(o=n.next(e-i),s.push(o)),i+=Mt.default.length(o)}return new r(s)}compose(t){let e=new dt.default(this.ops),s=new dt.default(t.ops),n=[],i=s.peek();if(i!=null&&typeof i.retain=="number"&&i.attributes==null){let a=i.retain;for(;e.peekType()==="insert"&&e.peekLength()<=a;)a-=e.peekLength(),n.push(e.next());i.retain-a>0&&s.next(i.retain-a)}let o=new r(n);for(;e.hasNext()||s.hasNext();)if(s.peekType()==="insert")o.push(s.next());else if(e.peekType()==="delete")o.push(e.next());else{let a=Math.min(e.peekLength(),s.peekLength()),l=e.next(a),u=s.next(a);if(u.retain){let c={};if(typeof l.retain=="number")c.retain=typeof u.retain=="number"?a:u.retain;else if(typeof u.retain=="number")l.retain==null?c.insert=l.insert:c.retain=l.retain;else{let h=l.retain==null?"insert":"retain",[p,m,x]=Ru(l[h],u.retain),A=r.getHandler(p);c[h]={[p]:A.compose(m,x,h==="retain")}}let f=xe.default.compose(l.attributes,u.attributes,typeof l.retain=="number");if(f&&(c.attributes=f),o.push(c),!s.hasNext()&&Jn(o.ops[o.ops.length-1],c)){let h=new r(e.rest());return o.concat(h).chop()}}else typeof u.delete=="number"&&(typeof l.retain=="number"||typeof l.retain=="object"&&l.retain!==null)&&o.push(u)}return o.chop()}concat(t){let e=new r(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new r;let s=[this,t].map(l=>l.map(u=>{if(u.insert!=null)return typeof u.insert=="string"?u.insert:Pb;let c=l===t?"on":"with";throw new Error("diff() called "+c+" non-document")}).join("")),n=new r,i=Os(s[0],s[1],e,!0),o=new dt.default(this.ops),a=new dt.default(t.ops);return i.forEach(l=>{let u=l[1].length;for(;u>0;){let c=0;switch(l[0]){case Os.INSERT:c=Math.min(a.peekLength(),u),n.push(a.next(c));break;case Os.DELETE:c=Math.min(u,o.peekLength()),o.next(c),n.delete(c);break;case Os.EQUAL:c=Math.min(o.peekLength(),a.peekLength(),u);let f=o.next(c),h=a.next(c);Jn(f.insert,h.insert)?n.retain(c,xe.default.diff(f.attributes,h.attributes)):n.push(h).delete(c);break}u-=c}}),n.chop()}eachLine(t,e=`
`){let s=new dt.default(this.ops),n=new r,i=0;for(;s.hasNext();){if(s.peekType()!=="insert")return;let o=s.peek(),a=Mt.default.length(o)-s.peekLength(),l=typeof o.insert=="string"?o.insert.indexOf(e,a)-a:-1;if(l<0)n.push(s.next());else if(l>0)n.push(s.next(l));else{if(t(n,s.next(1).attributes||{},i)===!1)return;i+=1,n=new r}}n.length()>0&&t(n,{},i)}invert(t){let e=new r;return this.reduce((s,n)=>{if(n.insert)e.delete(Mt.default.length(n));else{if(typeof n.retain=="number"&&n.attributes==null)return e.retain(n.retain),s+n.retain;if(n.delete||typeof n.retain=="number"){let i=n.delete||n.retain;return t.slice(s,s+i).forEach(a=>{n.delete?e.push(a):n.retain&&n.attributes&&e.retain(Mt.default.length(a),xe.default.invert(n.attributes,a.attributes))}),s+i}else if(typeof n.retain=="object"&&n.retain!==null){let i=t.slice(s,s+1),o=new dt.default(i.ops).next(),[a,l,u]=Ru(n.retain,o.insert),c=r.getHandler(a);return e.retain({[a]:c.invert(l,u)},xe.default.invert(n.attributes,o.attributes)),s+1}}return s},0),e.chop()}transform(t,e=!1){if(e=!!e,typeof t=="number")return this.transformPosition(t,e);let s=t,n=new dt.default(this.ops),i=new dt.default(s.ops),o=new r;for(;n.hasNext()||i.hasNext();)if(n.peekType()==="insert"&&(e||i.peekType()!=="insert"))o.retain(Mt.default.length(n.next()));else if(i.peekType()==="insert")o.push(i.next());else{let a=Math.min(n.peekLength(),i.peekLength()),l=n.next(a),u=i.next(a);if(l.delete)continue;if(u.delete)o.push(u);else{let c=l.retain,f=u.retain,h=typeof f=="object"&&f!==null?f:a;if(typeof c=="object"&&c!==null&&typeof f=="object"&&f!==null){let p=Object.keys(c)[0];if(p===Object.keys(f)[0]){let m=r.getHandler(p);m&&(h={[p]:m.transform(c[p],f[p],e)})}}o.retain(h,xe.default.transform(l.attributes,u.attributes,e))}}return o.chop()}transformPosition(t,e=!1){e=!!e;let s=new dt.default(this.ops),n=0;for(;s.hasNext()&&n<=t;){let i=s.peekLength(),o=s.peekType();if(s.next(),o==="delete"){t-=Math.min(i,t-n);continue}else o==="insert"&&(n<t||!e)&&(t+=i);n+=i}return t}};jt.Op=Mt.default;jt.OpIterator=dt.default;jt.AttributeMap=xe.default;jt.handlers={};Dt.default=jt;typeof Cs=="object"&&(Cs.exports=jt,Cs.exports.default=jt)});var Du=zt((G2,ri)=>{"use strict";var $b=Object.prototype.hasOwnProperty,Y="~";function Ar(){}Object.create&&(Ar.prototype=Object.create(null),new Ar().__proto__||(Y=!1));function Kb(r,t,e){this.fn=r,this.context=t,this.once=e||!1}function Mu(r,t,e,s,n){if(typeof e!="function")throw new TypeError("The listener must be a function");var i=new Kb(e,s||r,n),o=Y?Y+t:t;return r._events[o]?r._events[o].fn?r._events[o]=[r._events[o],i]:r._events[o].push(i):(r._events[o]=i,r._eventsCount++),r}function qs(r,t){--r._eventsCount===0?r._events=new Ar:delete r._events[t]}function V(){this._events=new Ar,this._eventsCount=0}V.prototype.eventNames=function(){var t=[],e,s;if(this._eventsCount===0)return t;for(s in e=this._events)$b.call(e,s)&&t.push(Y?s.slice(1):s);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(e)):t};V.prototype.listeners=function(t){var e=Y?Y+t:t,s=this._events[e];if(!s)return[];if(s.fn)return[s.fn];for(var n=0,i=s.length,o=new Array(i);n<i;n++)o[n]=s[n].fn;return o};V.prototype.listenerCount=function(t){var e=Y?Y+t:t,s=this._events[e];return s?s.fn?1:s.length:0};V.prototype.emit=function(t,e,s,n,i,o){var a=Y?Y+t:t;if(!this._events[a])return!1;var l=this._events[a],u=arguments.length,c,f;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),u){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,s),!0;case 4:return l.fn.call(l.context,e,s,n),!0;case 5:return l.fn.call(l.context,e,s,n,i),!0;case 6:return l.fn.call(l.context,e,s,n,i,o),!0}for(f=1,c=new Array(u-1);f<u;f++)c[f-1]=arguments[f];l.fn.apply(l.context,c)}else{var h=l.length,p;for(f=0;f<h;f++)switch(l[f].once&&this.removeListener(t,l[f].fn,void 0,!0),u){case 1:l[f].fn.call(l[f].context);break;case 2:l[f].fn.call(l[f].context,e);break;case 3:l[f].fn.call(l[f].context,e,s);break;case 4:l[f].fn.call(l[f].context,e,s,n);break;default:if(!c)for(p=1,c=new Array(u-1);p<u;p++)c[p-1]=arguments[p];l[f].fn.apply(l[f].context,c)}}return!0};V.prototype.on=function(t,e,s){return Mu(this,t,e,s,!1)};V.prototype.once=function(t,e,s){return Mu(this,t,e,s,!0)};V.prototype.removeListener=function(t,e,s,n){var i=Y?Y+t:t;if(!this._events[i])return this;if(!e)return qs(this,i),this;var o=this._events[i];if(o.fn)o.fn===e&&(!n||o.once)&&(!s||o.context===s)&&qs(this,i);else{for(var a=0,l=[],u=o.length;a<u;a++)(o[a].fn!==e||n&&!o[a].once||s&&o[a].context!==s)&&l.push(o[a]);l.length?this._events[i]=l.length===1?l[0]:l:qs(this,i)}return this};V.prototype.removeAllListeners=function(t){var e;return t?(e=Y?Y+t:t,this._events[e]&&qs(this,e)):(this._events=new Ar,this._eventsCount=0),this};V.prototype.off=V.prototype.removeListener;V.prototype.addListener=V.prototype.on;V.prefixed=Y;V.EventEmitter=V;typeof ri<"u"&&(ri.exports=V)});var Fc=typeof global=="object"&&global&&global.Object===Object&&global,Br=Fc;var Hc=typeof self=="object"&&self&&self.Object===Object&&self,zc=Br||Hc||Function("return this")(),D=zc;var $c=D.Symbol,ft=$c;var Fi=Object.prototype,Kc=Fi.hasOwnProperty,Gc=Fi.toString,sr=ft?ft.toStringTag:void 0;function Vc(r){var t=Kc.call(r,sr),e=r[sr];try{r[sr]=void 0;var s=!0}catch{}var n=Gc.call(r);return s&&(t?r[sr]=e:delete r[sr]),n}var Hi=Vc;var Wc=Object.prototype,Zc=Wc.toString;function Xc(r){return Zc.call(r)}var zi=Xc;var Yc="[object Null]",Qc="[object Undefined]",$i=ft?ft.toStringTag:void 0;function Jc(r){return r==null?r===void 0?Qc:Yc:$i&&$i in Object(r)?Hi(r):zi(r)}var bt=Jc;function tf(r){return r!=null&&typeof r=="object"}var z=tf;var ef=Array.isArray,ht=ef;function rf(r){var t=typeof r;return r!=null&&(t=="object"||t=="function")}var $=rf;function sf(r){return r}var Mr=sf;var nf="[object AsyncFunction]",of="[object Function]",af="[object GeneratorFunction]",lf="[object Proxy]";function uf(r){if(!$(r))return!1;var t=bt(r);return t==of||t==af||t==nf||t==lf}var Ne=uf;var cf=D["__core-js_shared__"],Dr=cf;var Ki=function(){var r=/[^.]+$/.exec(Dr&&Dr.keys&&Dr.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}();function ff(r){return!!Ki&&Ki in r}var Gi=ff;var hf=Function.prototype,df=hf.toString;function pf(r){if(r!=null){try{return df.call(r)}catch{}try{return r+""}catch{}}return""}var Lt=pf;var mf=/[\\^$.*+?()[\]{}|]/g,gf=/^\[object .+?Constructor\]$/,bf=Function.prototype,yf=Object.prototype,xf=bf.toString,vf=yf.hasOwnProperty,Af=RegExp("^"+xf.call(vf).replace(mf,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Ef(r){if(!$(r)||Gi(r))return!1;var t=Ne(r)?Af:gf;return t.test(Lt(r))}var Vi=Ef;function Nf(r,t){return r==null?void 0:r[t]}var Wi=Nf;function Tf(r,t){var e=Wi(r,t);return Vi(e)?e:void 0}var tt=Tf;var wf=tt(D,"WeakMap"),jr=wf;var Zi=Object.create,Sf=function(){function r(){}return function(t){if(!$(t))return{};if(Zi)return Zi(t);r.prototype=t;var e=new r;return r.prototype=void 0,e}}(),Xi=Sf;function Lf(r,t,e){switch(e.length){case 0:return r.call(t);case 1:return r.call(t,e[0]);case 2:return r.call(t,e[0],e[1]);case 3:return r.call(t,e[0],e[1],e[2])}return r.apply(t,e)}var Yi=Lf;function Of(r,t){var e=-1,s=r.length;for(t||(t=Array(s));++e<s;)t[e]=r[e];return t}var Pr=Of;var Cf=800,qf=16,_f=Date.now;function If(r){var t=0,e=0;return function(){var s=_f(),n=qf-(s-e);if(e=s,n>0){if(++t>=Cf)return arguments[0]}else t=0;return r.apply(void 0,arguments)}}var Qi=If;function kf(r){return function(){return r}}var Ji=kf;var Rf=function(){try{var r=tt(Object,"defineProperty");return r({},"",{}),r}catch{}}(),Te=Rf;var Bf=Te?function(r,t){return Te(r,"toString",{configurable:!0,enumerable:!1,value:Ji(t),writable:!0})}:Mr,to=Bf;var Mf=Qi(to),eo=Mf;function Df(r,t){for(var e=-1,s=r==null?0:r.length;++e<s&&t(r[e],e,r)!==!1;);return r}var ro=Df;var jf=9007199254740991,Pf=/^(?:0|[1-9]\d*)$/;function Uf(r,t){var e=typeof r;return t=t==null?jf:t,!!t&&(e=="number"||e!="symbol"&&Pf.test(r))&&r>-1&&r%1==0&&r<t}var Ur=Uf;function Ff(r,t,e){t=="__proto__"&&Te?Te(r,t,{configurable:!0,enumerable:!0,value:e,writable:!0}):r[t]=e}var we=Ff;function Hf(r,t){return r===t||r!==r&&t!==t}var vt=Hf;var zf=Object.prototype,$f=zf.hasOwnProperty;function Kf(r,t,e){var s=r[t];(!($f.call(r,t)&&vt(s,e))||e===void 0&&!(t in r))&&we(r,t,e)}var Fr=Kf;function Gf(r,t,e,s){var n=!e;e||(e={});for(var i=-1,o=t.length;++i<o;){var a=t[i],l=s?s(e[a],r[a],a,e,r):void 0;l===void 0&&(l=r[a]),n?we(e,a,l):Fr(e,a,l)}return e}var At=Gf;var so=Math.max;function Vf(r,t,e){return t=so(t===void 0?r.length-1:t,0),function(){for(var s=arguments,n=-1,i=so(s.length-t,0),o=Array(i);++n<i;)o[n]=s[t+n];n=-1;for(var a=Array(t+1);++n<t;)a[n]=s[n];return a[t]=e(o),Yi(r,this,a)}}var no=Vf;function Wf(r,t){return eo(no(r,t,Mr),r+"")}var io=Wf;var Zf=9007199254740991;function Xf(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Zf}var Hr=Xf;function Yf(r){return r!=null&&Hr(r.length)&&!Ne(r)}var $t=Yf;function Qf(r,t,e){if(!$(e))return!1;var s=typeof t;return(s=="number"?$t(e)&&Ur(t,e.length):s=="string"&&t in e)?vt(e[t],r):!1}var oo=Qf;function Jf(r){return io(function(t,e){var s=-1,n=e.length,i=n>1?e[n-1]:void 0,o=n>2?e[2]:void 0;for(i=r.length>3&&typeof i=="function"?(n--,i):void 0,o&&oo(e[0],e[1],o)&&(i=n<3?void 0:i,n=1),t=Object(t);++s<n;){var a=e[s];a&&r(t,a,s,i)}return t})}var ao=Jf;var th=Object.prototype;function eh(r){var t=r&&r.constructor,e=typeof t=="function"&&t.prototype||th;return r===e}var Se=eh;function rh(r,t){for(var e=-1,s=Array(r);++e<r;)s[e]=t(e);return s}var lo=rh;var sh="[object Arguments]";function nh(r){return z(r)&&bt(r)==sh}var en=nh;var uo=Object.prototype,ih=uo.hasOwnProperty,oh=uo.propertyIsEnumerable,ah=en(function(){return arguments}())?en:function(r){return z(r)&&ih.call(r,"callee")&&!oh.call(r,"callee")},nr=ah;function lh(){return!1}var co=lh;var po=typeof exports=="object"&&exports&&!exports.nodeType&&exports,fo=po&&typeof module=="object"&&module&&!module.nodeType&&module,uh=fo&&fo.exports===po,ho=uh?D.Buffer:void 0,ch=ho?ho.isBuffer:void 0,fh=ch||co,Ot=fh;var hh="[object Arguments]",dh="[object Array]",ph="[object Boolean]",mh="[object Date]",gh="[object Error]",bh="[object Function]",yh="[object Map]",xh="[object Number]",vh="[object Object]",Ah="[object RegExp]",Eh="[object Set]",Nh="[object String]",Th="[object WeakMap]",wh="[object ArrayBuffer]",Sh="[object DataView]",Lh="[object Float32Array]",Oh="[object Float64Array]",Ch="[object Int8Array]",qh="[object Int16Array]",_h="[object Int32Array]",Ih="[object Uint8Array]",kh="[object Uint8ClampedArray]",Rh="[object Uint16Array]",Bh="[object Uint32Array]",C={};C[Lh]=C[Oh]=C[Ch]=C[qh]=C[_h]=C[Ih]=C[kh]=C[Rh]=C[Bh]=!0;C[hh]=C[dh]=C[wh]=C[ph]=C[Sh]=C[mh]=C[gh]=C[bh]=C[yh]=C[xh]=C[vh]=C[Ah]=C[Eh]=C[Nh]=C[Th]=!1;function Mh(r){return z(r)&&Hr(r.length)&&!!C[bt(r)]}var mo=Mh;function Dh(r){return function(t){return r(t)}}var Le=Dh;var go=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ir=go&&typeof module=="object"&&module&&!module.nodeType&&module,jh=ir&&ir.exports===go,rn=jh&&Br.process,Ph=function(){try{var r=ir&&ir.require&&ir.require("util").types;return r||rn&&rn.binding&&rn.binding("util")}catch{}}(),Ct=Ph;var bo=Ct&&Ct.isTypedArray,Uh=bo?Le(bo):mo,Oe=Uh;var Fh=Object.prototype,Hh=Fh.hasOwnProperty;function zh(r,t){var e=ht(r),s=!e&&nr(r),n=!e&&!s&&Ot(r),i=!e&&!s&&!n&&Oe(r),o=e||s||n||i,a=o?lo(r.length,String):[],l=a.length;for(var u in r)(t||Hh.call(r,u))&&!(o&&(u=="length"||n&&(u=="offset"||u=="parent")||i&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||Ur(u,l)))&&a.push(u);return a}var zr=zh;function $h(r,t){return function(e){return r(t(e))}}var $r=$h;var Kh=$r(Object.keys,Object),yo=Kh;var Gh=Object.prototype,Vh=Gh.hasOwnProperty;function Wh(r){if(!Se(r))return yo(r);var t=[];for(var e in Object(r))Vh.call(r,e)&&e!="constructor"&&t.push(e);return t}var xo=Wh;function Zh(r){return $t(r)?zr(r):xo(r)}var Ce=Zh;function Xh(r){var t=[];if(r!=null)for(var e in Object(r))t.push(e);return t}var vo=Xh;var Yh=Object.prototype,Qh=Yh.hasOwnProperty;function Jh(r){if(!$(r))return vo(r);var t=Se(r),e=[];for(var s in r)s=="constructor"&&(t||!Qh.call(r,s))||e.push(s);return e}var Ao=Jh;function td(r){return $t(r)?zr(r,!0):Ao(r)}var Et=td;var ed=tt(Object,"create"),qt=ed;function rd(){this.__data__=qt?qt(null):{},this.size=0}var Eo=rd;function sd(r){var t=this.has(r)&&delete this.__data__[r];return this.size-=t?1:0,t}var No=sd;var nd="__lodash_hash_undefined__",id=Object.prototype,od=id.hasOwnProperty;function ad(r){var t=this.__data__;if(qt){var e=t[r];return e===nd?void 0:e}return od.call(t,r)?t[r]:void 0}var To=ad;var ld=Object.prototype,ud=ld.hasOwnProperty;function cd(r){var t=this.__data__;return qt?t[r]!==void 0:ud.call(t,r)}var wo=cd;var fd="__lodash_hash_undefined__";function hd(r,t){var e=this.__data__;return this.size+=this.has(r)?0:1,e[r]=qt&&t===void 0?fd:t,this}var So=hd;function qe(r){var t=-1,e=r==null?0:r.length;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}qe.prototype.clear=Eo;qe.prototype.delete=No;qe.prototype.get=To;qe.prototype.has=wo;qe.prototype.set=So;var sn=qe;function dd(){this.__data__=[],this.size=0}var Lo=dd;function pd(r,t){for(var e=r.length;e--;)if(vt(r[e][0],t))return e;return-1}var Kt=pd;var md=Array.prototype,gd=md.splice;function bd(r){var t=this.__data__,e=Kt(t,r);if(e<0)return!1;var s=t.length-1;return e==s?t.pop():gd.call(t,e,1),--this.size,!0}var Oo=bd;function yd(r){var t=this.__data__,e=Kt(t,r);return e<0?void 0:t[e][1]}var Co=yd;function xd(r){return Kt(this.__data__,r)>-1}var qo=xd;function vd(r,t){var e=this.__data__,s=Kt(e,r);return s<0?(++this.size,e.push([r,t])):e[s][1]=t,this}var _o=vd;function _e(r){var t=-1,e=r==null?0:r.length;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}_e.prototype.clear=Lo;_e.prototype.delete=Oo;_e.prototype.get=Co;_e.prototype.has=qo;_e.prototype.set=_o;var Gt=_e;var Ad=tt(D,"Map"),Vt=Ad;function Ed(){this.size=0,this.__data__={hash:new sn,map:new(Vt||Gt),string:new sn}}var Io=Ed;function Nd(r){var t=typeof r;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?r!=="__proto__":r===null}var ko=Nd;function Td(r,t){var e=r.__data__;return ko(t)?e[typeof t=="string"?"string":"hash"]:e.map}var Wt=Td;function wd(r){var t=Wt(this,r).delete(r);return this.size-=t?1:0,t}var Ro=wd;function Sd(r){return Wt(this,r).get(r)}var Bo=Sd;function Ld(r){return Wt(this,r).has(r)}var Mo=Ld;function Od(r,t){var e=Wt(this,r),s=e.size;return e.set(r,t),this.size+=e.size==s?0:1,this}var Do=Od;function Ie(r){var t=-1,e=r==null?0:r.length;for(this.clear();++t<e;){var s=r[t];this.set(s[0],s[1])}}Ie.prototype.clear=Io;Ie.prototype.delete=Ro;Ie.prototype.get=Bo;Ie.prototype.has=Mo;Ie.prototype.set=Do;var Kr=Ie;function Cd(r,t){for(var e=-1,s=t.length,n=r.length;++e<s;)r[n+e]=t[e];return r}var Gr=Cd;var qd=$r(Object.getPrototypeOf,Object),ke=qd;var _d="[object Object]",Id=Function.prototype,kd=Object.prototype,jo=Id.toString,Rd=kd.hasOwnProperty,Bd=jo.call(Object);function Md(r){if(!z(r)||bt(r)!=_d)return!1;var t=ke(r);if(t===null)return!0;var e=Rd.call(t,"constructor")&&t.constructor;return typeof e=="function"&&e instanceof e&&jo.call(e)==Bd}var Po=Md;function Dd(){this.__data__=new Gt,this.size=0}var Uo=Dd;function jd(r){var t=this.__data__,e=t.delete(r);return this.size=t.size,e}var Fo=jd;function Pd(r){return this.__data__.get(r)}var Ho=Pd;function Ud(r){return this.__data__.has(r)}var zo=Ud;var Fd=200;function Hd(r,t){var e=this.__data__;if(e instanceof Gt){var s=e.__data__;if(!Vt||s.length<Fd-1)return s.push([r,t]),this.size=++e.size,this;e=this.__data__=new Kr(s)}return e.set(r,t),this.size=e.size,this}var $o=Hd;function Re(r){var t=this.__data__=new Gt(r);this.size=t.size}Re.prototype.clear=Uo;Re.prototype.delete=Fo;Re.prototype.get=Ho;Re.prototype.has=zo;Re.prototype.set=$o;var Zt=Re;function zd(r,t){return r&&At(t,Ce(t),r)}var Ko=zd;function $d(r,t){return r&&At(t,Et(t),r)}var Go=$d;var Xo=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Vo=Xo&&typeof module=="object"&&module&&!module.nodeType&&module,Kd=Vo&&Vo.exports===Xo,Wo=Kd?D.Buffer:void 0,Zo=Wo?Wo.allocUnsafe:void 0;function Gd(r,t){if(t)return r.slice();var e=r.length,s=Zo?Zo(e):new r.constructor(e);return r.copy(s),s}var Vr=Gd;function Vd(r,t){for(var e=-1,s=r==null?0:r.length,n=0,i=[];++e<s;){var o=r[e];t(o,e,r)&&(i[n++]=o)}return i}var Yo=Vd;function Wd(){return[]}var Wr=Wd;var Zd=Object.prototype,Xd=Zd.propertyIsEnumerable,Qo=Object.getOwnPropertySymbols,Yd=Qo?function(r){return r==null?[]:(r=Object(r),Yo(Qo(r),function(t){return Xd.call(r,t)}))}:Wr,Be=Yd;function Qd(r,t){return At(r,Be(r),t)}var Jo=Qd;var Jd=Object.getOwnPropertySymbols,tp=Jd?function(r){for(var t=[];r;)Gr(t,Be(r)),r=ke(r);return t}:Wr,Zr=tp;function ep(r,t){return At(r,Zr(r),t)}var ta=ep;function rp(r,t,e){var s=t(r);return ht(r)?s:Gr(s,e(r))}var Xr=rp;function sp(r){return Xr(r,Ce,Be)}var or=sp;function np(r){return Xr(r,Et,Zr)}var ea=np;var ip=tt(D,"DataView"),Yr=ip;var op=tt(D,"Promise"),Qr=op;var ap=tt(D,"Set"),Jr=ap;var ra="[object Map]",lp="[object Object]",sa="[object Promise]",na="[object Set]",ia="[object WeakMap]",oa="[object DataView]",up=Lt(Yr),cp=Lt(Vt),fp=Lt(Qr),hp=Lt(Jr),dp=Lt(jr),ue=bt;(Yr&&ue(new Yr(new ArrayBuffer(1)))!=oa||Vt&&ue(new Vt)!=ra||Qr&&ue(Qr.resolve())!=sa||Jr&&ue(new Jr)!=na||jr&&ue(new jr)!=ia)&&(ue=function(r){var t=bt(r),e=t==lp?r.constructor:void 0,s=e?Lt(e):"";if(s)switch(s){case up:return oa;case cp:return ra;case fp:return sa;case hp:return na;case dp:return ia}return t});var _t=ue;var pp=Object.prototype,mp=pp.hasOwnProperty;function gp(r){var t=r.length,e=new r.constructor(t);return t&&typeof r[0]=="string"&&mp.call(r,"index")&&(e.index=r.index,e.input=r.input),e}var aa=gp;var bp=D.Uint8Array,Me=bp;function yp(r){var t=new r.constructor(r.byteLength);return new Me(t).set(new Me(r)),t}var De=yp;function xp(r,t){var e=t?De(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.byteLength)}var la=xp;var vp=/\w*$/;function Ap(r){var t=new r.constructor(r.source,vp.exec(r));return t.lastIndex=r.lastIndex,t}var ua=Ap;var ca=ft?ft.prototype:void 0,fa=ca?ca.valueOf:void 0;function Ep(r){return fa?Object(fa.call(r)):{}}var ha=Ep;function Np(r,t){var e=t?De(r.buffer):r.buffer;return new r.constructor(e,r.byteOffset,r.length)}var ts=Np;var Tp="[object Boolean]",wp="[object Date]",Sp="[object Map]",Lp="[object Number]",Op="[object RegExp]",Cp="[object Set]",qp="[object String]",_p="[object Symbol]",Ip="[object ArrayBuffer]",kp="[object DataView]",Rp="[object Float32Array]",Bp="[object Float64Array]",Mp="[object Int8Array]",Dp="[object Int16Array]",jp="[object Int32Array]",Pp="[object Uint8Array]",Up="[object Uint8ClampedArray]",Fp="[object Uint16Array]",Hp="[object Uint32Array]";function zp(r,t,e){var s=r.constructor;switch(t){case Ip:return De(r);case Tp:case wp:return new s(+r);case kp:return la(r,e);case Rp:case Bp:case Mp:case Dp:case jp:case Pp:case Up:case Fp:case Hp:return ts(r,e);case Sp:return new s;case Lp:case qp:return new s(r);case Op:return ua(r);case Cp:return new s;case _p:return ha(r)}}var da=zp;function $p(r){return typeof r.constructor=="function"&&!Se(r)?Xi(ke(r)):{}}var es=$p;var Kp="[object Map]";function Gp(r){return z(r)&&_t(r)==Kp}var pa=Gp;var ma=Ct&&Ct.isMap,Vp=ma?Le(ma):pa,ga=Vp;var Wp="[object Set]";function Zp(r){return z(r)&&_t(r)==Wp}var ba=Zp;var ya=Ct&&Ct.isSet,Xp=ya?Le(ya):ba,xa=Xp;var Yp=1,Qp=2,Jp=4,va="[object Arguments]",tm="[object Array]",em="[object Boolean]",rm="[object Date]",sm="[object Error]",Aa="[object Function]",nm="[object GeneratorFunction]",im="[object Map]",om="[object Number]",Ea="[object Object]",am="[object RegExp]",lm="[object Set]",um="[object String]",cm="[object Symbol]",fm="[object WeakMap]",hm="[object ArrayBuffer]",dm="[object DataView]",pm="[object Float32Array]",mm="[object Float64Array]",gm="[object Int8Array]",bm="[object Int16Array]",ym="[object Int32Array]",xm="[object Uint8Array]",vm="[object Uint8ClampedArray]",Am="[object Uint16Array]",Em="[object Uint32Array]",S={};S[va]=S[tm]=S[hm]=S[dm]=S[em]=S[rm]=S[pm]=S[mm]=S[gm]=S[bm]=S[ym]=S[im]=S[om]=S[Ea]=S[am]=S[lm]=S[um]=S[cm]=S[xm]=S[vm]=S[Am]=S[Em]=!0;S[sm]=S[Aa]=S[fm]=!1;function rs(r,t,e,s,n,i){var o,a=t&Yp,l=t&Qp,u=t&Jp;if(e&&(o=n?e(r,s,n,i):e(r)),o!==void 0)return o;if(!$(r))return r;var c=ht(r);if(c){if(o=aa(r),!a)return Pr(r,o)}else{var f=_t(r),h=f==Aa||f==nm;if(Ot(r))return Vr(r,a);if(f==Ea||f==va||h&&!n){if(o=l||h?{}:es(r),!a)return l?ta(r,Go(o,r)):Jo(r,Ko(o,r))}else{if(!S[f])return n?r:{};o=da(r,f,a)}}i||(i=new Zt);var p=i.get(r);if(p)return p;i.set(r,o),xa(r)?r.forEach(function(A){o.add(rs(A,t,e,A,r,i))}):ga(r)&&r.forEach(function(A,b){o.set(b,rs(A,t,e,b,r,i))});var m=u?l?ea:or:l?Et:Ce,x=c?void 0:m(r);return ro(x||r,function(A,b){x&&(b=A,A=r[b]),Fr(o,b,rs(A,t,e,b,r,i))}),o}var Na=rs;var Nm=1,Tm=4;function wm(r){return Na(r,Nm|Tm)}var Nt=wm;var Sm="__lodash_hash_undefined__";function Lm(r){return this.__data__.set(r,Sm),this}var Ta=Lm;function Om(r){return this.__data__.has(r)}var wa=Om;function ss(r){var t=-1,e=r==null?0:r.length;for(this.__data__=new Kr;++t<e;)this.add(r[t])}ss.prototype.add=ss.prototype.push=Ta;ss.prototype.has=wa;var Sa=ss;function Cm(r,t){for(var e=-1,s=r==null?0:r.length;++e<s;)if(t(r[e],e,r))return!0;return!1}var La=Cm;function qm(r,t){return r.has(t)}var Oa=qm;var _m=1,Im=2;function km(r,t,e,s,n,i){var o=e&_m,a=r.length,l=t.length;if(a!=l&&!(o&&l>a))return!1;var u=i.get(r),c=i.get(t);if(u&&c)return u==t&&c==r;var f=-1,h=!0,p=e&Im?new Sa:void 0;for(i.set(r,t),i.set(t,r);++f<a;){var m=r[f],x=t[f];if(s)var A=o?s(x,m,f,t,r,i):s(m,x,f,r,t,i);if(A!==void 0){if(A)continue;h=!1;break}if(p){if(!La(t,function(b,E){if(!Oa(p,E)&&(m===b||n(m,b,e,s,i)))return p.push(E)})){h=!1;break}}else if(!(m===x||n(m,x,e,s,i))){h=!1;break}}return i.delete(r),i.delete(t),h}var ns=km;function Rm(r){var t=-1,e=Array(r.size);return r.forEach(function(s,n){e[++t]=[n,s]}),e}var Ca=Rm;function Bm(r){var t=-1,e=Array(r.size);return r.forEach(function(s){e[++t]=s}),e}var qa=Bm;var Mm=1,Dm=2,jm="[object Boolean]",Pm="[object Date]",Um="[object Error]",Fm="[object Map]",Hm="[object Number]",zm="[object RegExp]",$m="[object Set]",Km="[object String]",Gm="[object Symbol]",Vm="[object ArrayBuffer]",Wm="[object DataView]",_a=ft?ft.prototype:void 0,nn=_a?_a.valueOf:void 0;function Zm(r,t,e,s,n,i,o){switch(e){case Wm:if(r.byteLength!=t.byteLength||r.byteOffset!=t.byteOffset)return!1;r=r.buffer,t=t.buffer;case Vm:return!(r.byteLength!=t.byteLength||!i(new Me(r),new Me(t)));case jm:case Pm:case Hm:return vt(+r,+t);case Um:return r.name==t.name&&r.message==t.message;case zm:case Km:return r==t+"";case Fm:var a=Ca;case $m:var l=s&Mm;if(a||(a=qa),r.size!=t.size&&!l)return!1;var u=o.get(r);if(u)return u==t;s|=Dm,o.set(r,t);var c=ns(a(r),a(t),s,n,i,o);return o.delete(r),c;case Gm:if(nn)return nn.call(r)==nn.call(t)}return!1}var Ia=Zm;var Xm=1,Ym=Object.prototype,Qm=Ym.hasOwnProperty;function Jm(r,t,e,s,n,i){var o=e&Xm,a=or(r),l=a.length,u=or(t),c=u.length;if(l!=c&&!o)return!1;for(var f=l;f--;){var h=a[f];if(!(o?h in t:Qm.call(t,h)))return!1}var p=i.get(r),m=i.get(t);if(p&&m)return p==t&&m==r;var x=!0;i.set(r,t),i.set(t,r);for(var A=o;++f<l;){h=a[f];var b=r[h],E=t[h];if(s)var N=o?s(E,b,h,t,r,i):s(b,E,h,r,t,i);if(!(N===void 0?b===E||n(b,E,e,s,i):N)){x=!1;break}A||(A=h=="constructor")}if(x&&!A){var T=r.constructor,w=t.constructor;T!=w&&"constructor"in r&&"constructor"in t&&!(typeof T=="function"&&T instanceof T&&typeof w=="function"&&w instanceof w)&&(x=!1)}return i.delete(r),i.delete(t),x}var ka=Jm;var tg=1,Ra="[object Arguments]",Ba="[object Array]",is="[object Object]",eg=Object.prototype,Ma=eg.hasOwnProperty;function rg(r,t,e,s,n,i){var o=ht(r),a=ht(t),l=o?Ba:_t(r),u=a?Ba:_t(t);l=l==Ra?is:l,u=u==Ra?is:u;var c=l==is,f=u==is,h=l==u;if(h&&Ot(r)){if(!Ot(t))return!1;o=!0,c=!1}if(h&&!c)return i||(i=new Zt),o||Oe(r)?ns(r,t,e,s,n,i):Ia(r,t,l,e,s,n,i);if(!(e&tg)){var p=c&&Ma.call(r,"__wrapped__"),m=f&&Ma.call(t,"__wrapped__");if(p||m){var x=p?r.value():r,A=m?t.value():t;return i||(i=new Zt),n(x,A,e,s,i)}}return h?(i||(i=new Zt),ka(r,t,e,s,n,i)):!1}var Da=rg;function ja(r,t,e,s,n){return r===t?!0:r==null||t==null||!z(r)&&!z(t)?r!==r&&t!==t:Da(r,t,e,s,ja,n)}var Pa=ja;function sg(r){return function(t,e,s){for(var n=-1,i=Object(t),o=s(t),a=o.length;a--;){var l=o[r?a:++n];if(e(i[l],l,i)===!1)break}return t}}var Ua=sg;var ng=Ua(),Fa=ng;function ig(r,t,e){(e!==void 0&&!vt(r[t],e)||e===void 0&&!(t in r))&&we(r,t,e)}var ar=ig;function og(r){return z(r)&&$t(r)}var Ha=og;function ag(r,t){if(!(t==="constructor"&&typeof r[t]=="function")&&t!="__proto__")return r[t]}var lr=ag;function lg(r){return At(r,Et(r))}var za=lg;function ug(r,t,e,s,n,i,o){var a=lr(r,e),l=lr(t,e),u=o.get(l);if(u){ar(r,e,u);return}var c=i?i(a,l,e+"",r,t,o):void 0,f=c===void 0;if(f){var h=ht(l),p=!h&&Ot(l),m=!h&&!p&&Oe(l);c=l,h||p||m?ht(a)?c=a:Ha(a)?c=Pr(a):p?(f=!1,c=Vr(l,!0)):m?(f=!1,c=ts(l,!0)):c=[]:Po(l)||nr(l)?(c=a,nr(a)?c=za(a):(!$(a)||Ne(a))&&(c=es(l))):f=!1}f&&(o.set(l,c),n(c,l,s,i,o),o.delete(l)),ar(r,e,c)}var $a=ug;function Ka(r,t,e,s,n){r!==t&&Fa(t,function(i,o){if(n||(n=new Zt),$(i))$a(r,t,o,e,Ka,s,n);else{var a=s?s(lr(r,o),i,o+"",r,t,n):void 0;a===void 0&&(a=i),ar(r,o,a)}},Et)}var Ga=Ka;function cg(r,t){return Pa(r,t)}var ce=cg;var fg=ao(function(r,t,e){Ga(r,t,e)}),et=fg;var fr={};Uc(fr,{Attributor:()=>Z,AttributorStore:()=>ur,BlockBlot:()=>fe,ClassAttributor:()=>j,ContainerBlot:()=>He,EmbedBlot:()=>I,InlineBlot:()=>os,LeafBlot:()=>P,ParentBlot:()=>rt,Registry:()=>Qt,Scope:()=>y,ScrollBlot:()=>cr,StyleAttributor:()=>st,TextBlot:()=>ze});var y=(r=>(r[r.TYPE=3]="TYPE",r[r.LEVEL=12]="LEVEL",r[r.ATTRIBUTE=13]="ATTRIBUTE",r[r.BLOT=14]="BLOT",r[r.INLINE=7]="INLINE",r[r.BLOCK=11]="BLOCK",r[r.BLOCK_BLOT=10]="BLOCK_BLOT",r[r.INLINE_BLOT=6]="INLINE_BLOT",r[r.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",r[r.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",r[r.ANY=15]="ANY",r))(y||{}),Z=class{constructor(t,e,s={}){this.attrName=t,this.keyName=e;let n=y.TYPE&y.ATTRIBUTE;this.scope=s.scope!=null?s.scope&y.LEVEL|n:y.ATTRIBUTE,s.whitelist!=null&&(this.whitelist=s.whitelist)}static keys(t){return Array.from(t.attributes).map(e=>e.name)}add(t,e){return this.canAdd(t,e)?(t.setAttribute(this.keyName,e),!0):!1}canAdd(t,e){return this.whitelist==null?!0:typeof e=="string"?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1}remove(t){t.removeAttribute(this.keyName)}value(t){let e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}},Yt=class extends Error{constructor(t){t="[Parchment] "+t,super(t),this.message=t,this.name=this.constructor.name}},hg=(()=>{let r=class an{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(e,s=!1){if(e==null)return null;if(this.blots.has(e))return this.blots.get(e)||null;if(s){let n=null;try{n=e.parentNode}catch{return null}return this.find(n,s)}return null}create(e,s,n){let i=this.query(s);if(i==null)throw new Yt(`Unable to create ${s} blot`);let o=i,a=s instanceof Node||s.nodeType===Node.TEXT_NODE?s:o.create(n),l=new o(e,a,n);return an.blots.set(l.domNode,l),l}find(e,s=!1){return an.find(e,s)}query(e,s=y.ANY){let n;return typeof e=="string"?n=this.types[e]||this.attributes[e]:e instanceof Text||e.nodeType===Node.TEXT_NODE?n=this.types.text:typeof e=="number"?e&y.LEVEL&y.BLOCK?n=this.types.block:e&y.LEVEL&y.INLINE&&(n=this.types.inline):e instanceof Element&&((e.getAttribute("class")||"").split(/\s+/).some(i=>(n=this.classes[i],!!n)),n=n||this.tags[e.tagName]),n==null?null:"scope"in n&&s&y.LEVEL&n.scope&&s&y.TYPE&n.scope?n:null}register(...e){return e.map(s=>{let n="blotName"in s,i="attrName"in s;if(!n&&!i)throw new Yt("Invalid definition");if(n&&s.blotName==="abstract")throw new Yt("Cannot register abstract class");let o=n?s.blotName:i?s.attrName:void 0;return this.types[o]=s,i?typeof s.keyName=="string"&&(this.attributes[s.keyName]=s):n&&(s.className&&(this.classes[s.className]=s),s.tagName&&(Array.isArray(s.tagName)?s.tagName=s.tagName.map(a=>a.toUpperCase()):s.tagName=s.tagName.toUpperCase(),(Array.isArray(s.tagName)?s.tagName:[s.tagName]).forEach(a=>{(this.tags[a]==null||s.className==null)&&(this.tags[a]=s)}))),s})}};return r.blots=new WeakMap,r})(),Qt=hg;function Va(r,t){return(r.getAttribute("class")||"").split(/\s+/).filter(e=>e.indexOf(`${t}-`)===0)}var ln=class extends Z{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(e=>e.split("-").slice(0,-1).join("-"))}add(t,e){return this.canAdd(t,e)?(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0):!1}remove(t){Va(t,this.keyName).forEach(e=>{t.classList.remove(e)}),t.classList.length===0&&t.removeAttribute("class")}value(t){let e=(Va(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}},j=ln;function on(r){let t=r.split("-"),e=t.slice(1).map(s=>s[0].toUpperCase()+s.slice(1)).join("");return t[0]+e}var un=class extends Z{static keys(t){return(t.getAttribute("style")||"").split(";").map(e=>e.split(":")[0].trim())}add(t,e){return this.canAdd(t,e)?(t.style[on(this.keyName)]=e,!0):!1}remove(t){t.style[on(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){let e=t.style[on(this.keyName)];return this.canAdd(t,e)?e:""}},st=un,cn=class{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(t.value(this.domNode)!=null?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};let t=Qt.find(this.domNode);if(t==null)return;let e=Z.keys(this.domNode),s=j.keys(this.domNode),n=st.keys(this.domNode);e.concat(s).concat(n).forEach(i=>{let o=t.scroll.query(i,y.ATTRIBUTE);o instanceof Z&&(this.attributes[o.attrName]=o)})}copy(t){Object.keys(this.attributes).forEach(e=>{let s=this.attributes[e].value(this.domNode);t.format(e,s)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(e=>{this.attributes[e].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}},ur=cn,Za=class{constructor(t,e){this.scroll=t,this.domNode=e,Qt.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(this.tagName==null)throw new Yt("Blot definition missing tagName");let e,s;return Array.isArray(this.tagName)?(typeof t=="string"?(s=t.toUpperCase(),parseInt(s,10).toString()===s&&(s=parseInt(s,10))):typeof t=="number"&&(s=t),typeof s=="number"?e=document.createElement(this.tagName[s-1]):s&&this.tagName.indexOf(s)>-1?e=document.createElement(s):e=document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){let t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){this.parent!=null&&this.parent.removeChild(this),Qt.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,s,n){let i=this.isolate(t,e);if(this.scroll.query(s,y.BLOT)!=null&&n)i.wrap(s,n);else if(this.scroll.query(s,y.ATTRIBUTE)!=null){let o=this.scroll.create(this.statics.scope);i.wrap(o),o.format(s,n)}}insertAt(t,e,s){let n=s==null?this.scroll.create("text",e):this.scroll.create(e,s),i=this.split(t);this.parent.insertBefore(n,i||void 0)}isolate(t,e){let s=this.split(t);if(s==null)throw new Error("Attempt to isolate at end");return s.split(e),s}length(){return 1}offset(t=this.parent){return this.parent==null||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){let s=typeof t=="string"?this.scroll.create(t,e):t;return this.parent!=null&&(this.parent.insertBefore(s,this.next||void 0),this.remove()),s}split(t,e){return t===0?this:this.next}update(t,e){}wrap(t,e){let s=typeof t=="string"?this.scroll.create(t,e):t;if(this.parent!=null&&this.parent.insertBefore(s,this.next||void 0),typeof s.appendChild!="function")throw new Yt(`Cannot wrap ${t}`);return s.appendChild(this),s}};Za.blotName="abstract";var Xa=Za,Ya=class extends Xa{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let s=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(s+=1),[this.parent.domNode,s]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};Ya.scope=y.INLINE_BLOT;var dg=Ya,P=dg,fn=class{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){let e=t.slice(1);this.append(...e)}}at(t){let e=this.iterator(),s=e();for(;s&&t>0;)t-=1,s=e();return s}contains(t){let e=this.iterator(),s=e();for(;s;){if(s===t)return!0;s=e()}return!1}indexOf(t){let e=this.iterator(),s=e(),n=0;for(;s;){if(s===t)return n;n+=1,s=e()}return-1}insertBefore(t,e){t!=null&&(this.remove(t),t.next=e,e!=null?(t.prev=e.prev,e.prev!=null&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):this.tail!=null?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,s=this.head;for(;s!=null;){if(s===t)return e;e+=s.length(),s=s.next}return-1}remove(t){this.contains(t)&&(t.prev!=null&&(t.prev.next=t.next),t.next!=null&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{let e=t;return t!=null&&(t=t.next),e}}find(t,e=!1){let s=this.iterator(),n=s();for(;n;){let i=n.length();if(t<i||e&&t===i&&(n.next==null||n.next.length()!==0))return[n,t];t-=i,n=s()}return[null,0]}forEach(t){let e=this.iterator(),s=e();for(;s;)t(s),s=e()}forEachAt(t,e,s){if(e<=0)return;let[n,i]=this.find(t),o=t-i,a=this.iterator(n),l=a();for(;l&&o<t+e;){let u=l.length();t>o?s(l,t-o,Math.min(e,o+u-t)):s(l,0,Math.min(u,t+e-o)),o+=u,l=a()}}map(t){return this.reduce((e,s)=>(e.push(t(s)),e),[])}reduce(t,e){let s=this.iterator(),n=s();for(;n;)e=t(e,n),n=s();return e}};function Wa(r,t){let e=t.find(r);if(e)return e;try{return t.create(r)}catch{let s=t.create(y.INLINE);return Array.from(r.childNodes).forEach(n=>{s.domNode.appendChild(n)}),r.parentNode&&r.parentNode.replaceChild(s.domNode,r),s.attach(),s}}var pg=(()=>{let r=class Xt extends Xa{constructor(e,s){super(e,s),this.uiNode=null,this.build()}appendChild(e){this.insertBefore(e)}attach(){super.attach(),this.children.forEach(e=>{e.attach()})}attachUI(e){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=e,Xt.uiClass&&this.uiNode.classList.add(Xt.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new fn,Array.from(this.domNode.childNodes).filter(e=>e!==this.uiNode).reverse().forEach(e=>{try{let s=Wa(e,this.scroll);this.insertBefore(s,this.children.head||void 0)}catch(s){if(s instanceof Yt)return;throw s}})}deleteAt(e,s){if(e===0&&s===this.length())return this.remove();this.children.forEachAt(e,s,(n,i,o)=>{n.deleteAt(i,o)})}descendant(e,s=0){let[n,i]=this.children.find(s);return e.blotName==null&&e(n)||e.blotName!=null&&n instanceof e?[n,i]:n instanceof Xt?n.descendant(e,i):[null,-1]}descendants(e,s=0,n=Number.MAX_VALUE){let i=[],o=n;return this.children.forEachAt(s,n,(a,l,u)=>{(e.blotName==null&&e(a)||e.blotName!=null&&a instanceof e)&&i.push(a),a instanceof Xt&&(i=i.concat(a.descendants(e,l,o))),o-=u}),i}detach(){this.children.forEach(e=>{e.detach()}),super.detach()}enforceAllowedChildren(){let e=!1;this.children.forEach(s=>{e||this.statics.allowedChildren.some(n=>s instanceof n)||(s.statics.scope===y.BLOCK_BLOT?(s.next!=null&&this.splitAfter(s),s.prev!=null&&this.splitAfter(s.prev),s.parent.unwrap(),e=!0):s instanceof Xt?s.unwrap():s.remove())})}formatAt(e,s,n,i){this.children.forEachAt(e,s,(o,a,l)=>{o.formatAt(a,l,n,i)})}insertAt(e,s,n){let[i,o]=this.children.find(e);if(i)i.insertAt(o,s,n);else{let a=n==null?this.scroll.create("text",s):this.scroll.create(s,n);this.appendChild(a)}}insertBefore(e,s){e.parent!=null&&e.parent.children.remove(e);let n=null;this.children.insertBefore(e,s||null),e.parent=this,s!=null&&(n=s.domNode),(this.domNode.parentNode!==e.domNode||this.domNode.nextSibling!==n)&&this.domNode.insertBefore(e.domNode,n),e.attach()}length(){return this.children.reduce((e,s)=>e+s.length(),0)}moveChildren(e,s){this.children.forEach(n=>{e.insertBefore(n,s)})}optimize(e){if(super.optimize(e),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){let s=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(s)}else this.remove()}path(e,s=!1){let[n,i]=this.children.find(e,s),o=[[this,e]];return n instanceof Xt?o.concat(n.path(i,s)):(n!=null&&o.push([n,i]),o)}removeChild(e){this.children.remove(e)}replaceWith(e,s){let n=typeof e=="string"?this.scroll.create(e,s):e;return n instanceof Xt&&this.moveChildren(n),super.replaceWith(n)}split(e,s=!1){if(!s){if(e===0)return this;if(e===this.length())return this.next}let n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(e,this.length(),(i,o,a)=>{let l=i.split(o,s);l!=null&&n.appendChild(l)}),n}splitAfter(e){let s=this.clone();for(;e.next!=null;)s.appendChild(e.next);return this.parent&&this.parent.insertBefore(s,this.next||void 0),s}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(e,s){let n=[],i=[];e.forEach(o=>{o.target===this.domNode&&o.type==="childList"&&(n.push(...o.addedNodes),i.push(...o.removedNodes))}),i.forEach(o=>{if(o.parentNode!=null&&o.tagName!=="IFRAME"&&document.body.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;let a=this.scroll.find(o);a!=null&&(a.domNode.parentNode==null||a.domNode.parentNode===this.domNode)&&a.detach()}),n.filter(o=>o.parentNode===this.domNode&&o!==this.uiNode).sort((o,a)=>o===a?0:o.compareDocumentPosition(a)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(o=>{let a=null;o.nextSibling!=null&&(a=this.scroll.find(o.nextSibling));let l=Wa(o,this.scroll);(l.next!==a||l.next==null)&&(l.parent!=null&&l.parent.removeChild(this),this.insertBefore(l,a||void 0))}),this.enforceAllowedChildren()}};return r.uiClass="",r})(),mg=pg,rt=mg;function gg(r,t){if(Object.keys(r).length!==Object.keys(t).length)return!1;for(let e in r)if(r[e]!==t[e])return!1;return!0}var je=class Pe extends rt{static create(t){return super.create(t)}static formats(t,e){let s=e.query(Pe.blotName);if(!(s!=null&&t.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new ur(this.domNode)}format(t,e){if(t===this.statics.blotName&&!e)this.children.forEach(s=>{s instanceof Pe||(s=s.wrap(Pe.blotName,!0)),this.attributes.copy(s)}),this.unwrap();else{let s=this.scroll.query(t,y.INLINE);if(s==null)return;s instanceof Z?this.attributes.attribute(s,e):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)}}formats(){let t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,s,n){this.formats()[s]!=null||this.scroll.query(s,y.ATTRIBUTE)?this.isolate(t,e).format(s,n):super.formatAt(t,e,s,n)}optimize(t){super.optimize(t);let e=this.formats();if(Object.keys(e).length===0)return this.unwrap();let s=this.next;s instanceof Pe&&s.prev===this&&gg(e,s.formats())&&(s.moveChildren(this),s.remove())}replaceWith(t,e){let s=super.replaceWith(t,e);return this.attributes.copy(s),s}update(t,e){super.update(t,e),t.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}wrap(t,e){let s=super.wrap(t,e);return s instanceof Pe&&this.attributes.move(s),s}};je.allowedChildren=[je,P],je.blotName="inline",je.scope=y.INLINE_BLOT,je.tagName="SPAN";var bg=je,os=bg,Ue=class hn extends rt{static create(t){return super.create(t)}static formats(t,e){let s=e.query(hn.blotName);if(!(s!=null&&t.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new ur(this.domNode)}format(t,e){let s=this.scroll.query(t,y.BLOCK);s!=null&&(s instanceof Z?this.attributes.attribute(s,e):t===this.statics.blotName&&!e?this.replaceWith(hn.blotName):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e))}formats(){let t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,s,n){this.scroll.query(s,y.BLOCK)!=null?this.format(s,n):super.formatAt(t,e,s,n)}insertAt(t,e,s){if(s==null||this.scroll.query(e,y.INLINE)!=null)super.insertAt(t,e,s);else{let n=this.split(t);if(n!=null){let i=this.scroll.create(e,s);n.parent.insertBefore(i,n)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){let s=super.replaceWith(t,e);return this.attributes.copy(s),s}update(t,e){super.update(t,e),t.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}};Ue.blotName="block",Ue.scope=y.BLOCK_BLOT,Ue.tagName="P",Ue.allowedChildren=[os,Ue,P];var yg=Ue,fe=yg,dn=class extends rt{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,s,n){super.formatAt(t,e,s,n),this.enforceAllowedChildren()}insertAt(t,e,s){super.insertAt(t,e,s),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};dn.blotName="container",dn.scope=y.BLOCK_BLOT;var xg=dn,He=xg,pn=class extends P{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,s,n){t===0&&e===this.length()?this.format(s,n):super.formatAt(t,e,s,n)}formats(){return this.statics.formats(this.domNode,this.scroll)}},I=pn,vg={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Ag=100,Fe=class extends rt{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(s=>{this.update(s)}),this.observer.observe(this.domNode,vg),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){let s=this.registry.find(t,e);return s?s.scroll===this?s:e?this.find(s.scroll.domNode.parentNode,!0):null:null}query(t,e=y.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),t===0&&e===this.length()?this.children.forEach(s=>{s.remove()}):super.deleteAt(t,e)}formatAt(t,e,s,n){this.update(),super.formatAt(t,e,s,n)}insertAt(t,e,s){this.update(),super.insertAt(t,e,s)}optimize(t=[],e={}){super.optimize(e);let s=e.mutationsMap||new WeakMap,n=Array.from(this.observer.takeRecords());for(;n.length>0;)t.push(n.pop());let i=(l,u=!0)=>{l==null||l===this||l.domNode.parentNode!=null&&(s.has(l.domNode)||s.set(l.domNode,[]),u&&i(l.parent))},o=l=>{s.has(l.domNode)&&(l instanceof rt&&l.children.forEach(o),s.delete(l.domNode),l.optimize(e))},a=t;for(let l=0;a.length>0;l+=1){if(l>=Ag)throw new Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(u=>{let c=this.find(u.target,!0);c!=null&&(c.domNode===u.target&&(u.type==="childList"?(i(this.find(u.previousSibling,!1)),Array.from(u.addedNodes).forEach(f=>{let h=this.find(f,!1);i(h,!1),h instanceof rt&&h.children.forEach(p=>{i(p,!1)})})):u.type==="attributes"&&i(c.prev)),i(c))}),this.children.forEach(o),a=Array.from(this.observer.takeRecords()),n=a.slice();n.length>0;)t.push(n.pop())}}update(t,e={}){t=t||this.observer.takeRecords();let s=new WeakMap;t.map(n=>{let i=this.find(n.target,!0);return i==null?null:s.has(i.domNode)?(s.get(i.domNode).push(n),null):(s.set(i.domNode,[n]),i)}).forEach(n=>{n!=null&&n!==this&&s.has(n.domNode)&&n.update(s.get(n.domNode)||[],e)}),e.mutationsMap=s,s.has(this.domNode)&&super.update(s.get(this.domNode),e),this.optimize(t,e)}};Fe.blotName="scroll",Fe.defaultChild=fe,Fe.allowedChildren=[fe,He],Fe.scope=y.BLOCK_BLOT,Fe.tagName="DIV";var Eg=Fe,cr=Eg,mn=class Qa extends P{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,s){s==null?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,s)}length(){return this.text.length}optimize(t){super.optimize(t),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof Qa&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}let s=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(s,this.next||void 0),this.text=this.statics.value(this.domNode),s}update(t,e){t.some(s=>s.type==="characterData"&&s.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};mn.blotName="text",mn.scope=y.INLINE_BLOT;var Ng=mn,ze=Ng;var se=at(ut(),1);var k=at(ut(),1);var ti=at(ut(),1);var Ub=(()=>{class r extends I{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}return r.blotName="break",r.tagName="BR",r})(),K=Ub;var B=class extends ze{},Fb={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function ve(r){return r.replace(/[&<>"']/g,t=>Fb[t])}var Hb=(()=>{let t=class t extends os{static compare(s,n){let i=t.order.indexOf(s),o=t.order.indexOf(n);return i>=0||o>=0?i-o:s===n?0:s<n?-1:1}formatAt(s,n,i,o){if(t.compare(this.statics.blotName,i)<0&&this.scroll.query(i,y.BLOT)){let a=this.isolate(s,n);o&&a.wrap(i,o)}else super.formatAt(s,n,i,o)}optimize(s){if(super.optimize(s),this.parent instanceof t&&t.compare(this.statics.blotName,this.parent.statics.blotName)>0){let n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}};g(t,"allowedChildren",[t,K,I,B]),g(t,"order",["cursor","inline","link","underline","strike","italic","bold","script","code"]);let r=t;return r})(),G=Hb;var Bu=1,M=(()=>{class r extends fe{constructor(){super(...arguments);g(this,"cache",{})}delta(){return this.cache.delta==null&&(this.cache.delta=ei(this)),this.cache.delta}deleteAt(s,n){super.deleteAt(s,n),this.cache={}}formatAt(s,n,i,o){n<=0||(this.scroll.query(i,y.BLOCK)?s+n===this.length()&&this.format(i,o):super.formatAt(s,Math.min(n,this.length()-s-1),i,o),this.cache={})}insertAt(s,n,i){if(i!=null){super.insertAt(s,n,i),this.cache={};return}if(n.length===0)return;let o=n.split(`
`),a=o.shift();a.length>0&&(s<this.length()-1||this.children.tail==null?super.insertAt(Math.min(s,this.length()-1),a):this.children.tail.insertAt(this.children.tail.length(),a),this.cache={});let l=this;o.reduce((u,c)=>(l=l.split(u,!0),l.insertAt(0,c),c.length),s+a.length)}insertBefore(s,n){let{head:i}=this.children;super.insertBefore(s,n),i instanceof K&&i.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+Bu),this.cache.length}moveChildren(s,n){super.moveChildren(s,n),this.cache={}}optimize(s){super.optimize(s),this.cache={}}path(s){return super.path(s,!0)}removeChild(s){super.removeChild(s),this.cache={}}split(s){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(n&&(s===0||s>=this.length()-Bu)){let o=this.clone();return s===0?(this.parent.insertBefore(o,this),this):(this.parent.insertBefore(o,this.next),o)}let i=super.split(s,n);return this.cache={},i}}return r.blotName="block",r.tagName="P",r.defaultChild=K,r.allowedChildren=[K,G,I,B],r})(),F=class extends I{attach(){super.attach(),this.attributes=new ur(this.domNode)}delta(){return new ti.default().insert(this.value(),O(O({},this.formats()),this.attributes.values()))}format(t,e){let s=this.scroll.query(t,y.BLOCK_ATTRIBUTE);s!=null&&this.attributes.attribute(s,e)}formatAt(t,e,s,n){this.format(s,n)}insertAt(t,e,s){if(s!=null){super.insertAt(t,e,s);return}let n=e.split(`
`),i=n.pop(),o=n.map(l=>{let u=this.scroll.create(M.blotName);return u.insertAt(0,l),u}),a=this.split(t);o.forEach(l=>{this.parent.insertBefore(l,a)}),i&&this.parent.insertBefore(this.scroll.create("text",i),a)}};F.scope=y.BLOCK_BLOT;function ei(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return r.descendants(P).reduce((e,s)=>s.length()===0?e:e.insert(s.value(),nt(s,{},t)),new ti.default).insert(`
`,nt(r))}function nt(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return r==null||("formats"in r&&typeof r.formats=="function"&&(t=O(O({},t),r.formats()),e&&delete t["code-token"]),r.parent==null||r.parent.statics.blotName==="scroll"||r.parent.statics.scope!==r.statics.scope)?t:nt(r.parent,t,e)}var zb=(()=>{let t=class t extends I{static value(){}constructor(s,n,i){super(s,n),this.selection=i,this.textNode=document.createTextNode(t.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(s,n){if(this.savedLength!==0){super.format(s,n);return}let i=this,o=0;for(;i!=null&&i.statics.scope!==y.BLOCK_BLOT;)o+=i.offset(i.parent),i=i.parent;i!=null&&(this.savedLength=t.CONTENTS.length,i.optimize(),i.formatAt(o,t.CONTENTS.length,s,n),this.savedLength=0)}index(s,n){return s===this.textNode?0:super.index(s,n)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;let s=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);let n=this.prev instanceof B?this.prev:null,i=n?n.length():0,o=this.next instanceof B?this.next:null,a=o?o.text:"",{textNode:l}=this,u=l.data.split(t.CONTENTS).join("");l.data=t.CONTENTS;let c;if(n)c=n,(u||o)&&(n.insertAt(n.length(),u+a),o&&o.remove());else if(o)c=o,o.insertAt(0,u);else{let f=document.createTextNode(u);c=this.scroll.create(f),this.parent.insertBefore(c,this)}if(this.remove(),s){let f=(m,x)=>n&&m===n.domNode?x:m===l?i+x-1:o&&m===o.domNode?i+u.length+x:null,h=f(s.start.node,s.start.offset),p=f(s.end.node,s.end.offset);if(h!==null&&p!==null)return{startNode:c.domNode,startOffset:h,endNode:c.domNode,endOffset:p}}return null}update(s,n){if(s.some(i=>i.type==="characterData"&&i.target===this.textNode)){let i=this.restore();i&&(n.range=i)}}optimize(s){super.optimize(s);let{parent:n}=this;for(;n;){if(n.domNode.tagName==="A"){this.savedLength=t.CONTENTS.length,n.isolate(this.offset(n),this.length()).unwrap(),this.savedLength=0;break}n=n.parent}}value(){return""}};g(t,"blotName","cursor"),g(t,"className","ql-cursor"),g(t,"tagName","span"),g(t,"CONTENTS","\uFEFF");let r=t;return r})(),re=zb;var si=at(Du(),1);var Er=new WeakMap;var ni=["error","warn","log","info"],ii="warn";function ju(r){if(ii&&ni.indexOf(r)<=ni.indexOf(ii)){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];console[r](...e)}}function oi(r){return ni.reduce((t,e)=>(t[e]=ju.bind(console,e,r),t),{})}oi.level=r=>{ii=r};ju.level=oi.level;var ct=oi;var ai=ct("quill:events"),Gb=["selectionchange","mousedown","mouseup","click"];Gb.forEach(r=>{document.addEventListener(r,function(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];Array.from(document.querySelectorAll(".ql-container")).forEach(n=>{let i=Er.get(n);i&&i.emitter&&i.emitter.handleDOM(...e)})})});var Vb=(()=>{class r extends si.default{constructor(){super(),this.domListeners={},this.on("error",ai.error)}emit(){for(var e=arguments.length,s=new Array(e),n=0;n<e;n++)s[n]=arguments[n];return ai.log.call(ai,...s),super.emit(...s)}handleDOM(e){for(var s=arguments.length,n=new Array(s>1?s-1:0),i=1;i<s;i++)n[i-1]=arguments[i];(this.domListeners[e.type]||[]).forEach(o=>{let{node:a,handler:l}=o;(e.target===a||a.contains(e.target))&&l(e,...n)})}listenDOM(e,s,n){this.domListeners[e]||(this.domListeners[e]=[]),this.domListeners[e].push({node:s,handler:n})}}return g(r,"events",{EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"}),g(r,"sources",{API:"api",SILENT:"silent",USER:"user"}),r})(),v=Vb;var li=ct("quill:selection"),Q=class{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}},ci=class{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new Q(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,v.sources.USER),1)}),this.emitter.on(v.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;let s=this.getNativeRange();s!=null&&s.start.node!==this.cursor.textNode&&this.emitter.once(v.events.SCROLL_UPDATE,(n,i)=>{try{this.root.contains(s.start.node)&&this.root.contains(s.end.node)&&this.setNativeRange(s.start.node,s.start.offset,s.end.node,s.end.offset);let o=i.some(a=>a.type==="characterData"||a.type==="childList"||a.type==="attributes"&&a.target===this.root);this.update(o?v.sources.SILENT:n)}catch{}})}),this.emitter.on(v.events.SCROLL_OPTIMIZE,(s,n)=>{if(n.range){let{startNode:i,startOffset:o,endNode:a,endOffset:l}=n.range;this.setNativeRange(i,o,a,l),this.update(v.sources.SILENT)}}),this.update(v.sources.SILENT)}handleComposition(){this.emitter.on(v.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(v.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){let t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(v.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();let s=this.getNativeRange();if(!(s==null||!s.native.collapsed||this.scroll.query(t,y.BLOCK))){if(s.start.node!==this.cursor.textNode){let n=this.scroll.find(s.start.node,!1);if(n==null)return;if(n instanceof P){let i=n.split(s.start.offset);n.parent.insertBefore(this.cursor,i)}else n.insertBefore(this.cursor,s.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=this.scroll.length();t=Math.min(t,s-1),e=Math.min(t+e,s-1)-t;let n,[i,o]=this.scroll.leaf(t);if(i==null)return null;if(e>0&&o===i.length()){let[c]=this.scroll.leaf(t+1);if(c){let[f]=this.scroll.line(t),[h]=this.scroll.line(t+1);f===h&&(i=c,o=0)}}[n,o]=i.position(o,!0);let a=document.createRange();if(e>0)return a.setStart(n,o),[i,o]=this.scroll.leaf(t+e),i==null?null:([n,o]=i.position(o,!0),a.setEnd(n,o),a.getBoundingClientRect());let l="left",u;if(n instanceof Text){if(!n.data.length)return null;o<n.data.length?(a.setStart(n,o),a.setEnd(n,o+1)):(a.setStart(n,o-1),a.setEnd(n,o),l="right"),u=a.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;u=i.domNode.getBoundingClientRect(),o>0&&(l="right")}return{bottom:u.top+u.height,height:u.height,left:u[l],right:u[l],top:u.top,width:0}}getNativeRange(){let t=document.getSelection();if(t==null||t.rangeCount<=0)return null;let e=t.getRangeAt(0);if(e==null)return null;let s=this.normalizeNative(e);return li.info("getNativeRange",s),s}getRange(){let t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];let e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&ui(this.root,document.activeElement)}normalizedToRange(t){let e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);let s=e.map(o=>{let[a,l]=o,u=this.scroll.find(a,!0),c=u.offset(this.scroll);return l===0?c:u instanceof P?c+u.index(a,l):c+u.length()}),n=Math.min(Math.max(...s),this.scroll.length()-1),i=Math.min(n,...s);return new Q(i,n-i)}normalizeNative(t){if(!ui(this.root,t.startContainer)||!t.collapsed&&!ui(this.root,t.endContainer))return null;let e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(s=>{let{node:n,offset:i}=s;for(;!(n instanceof Text)&&n.childNodes.length>0;)if(n.childNodes.length>i)n=n.childNodes[i],i=0;else if(n.childNodes.length===i)n=n.lastChild,n instanceof Text?i=n.data.length:n.childNodes.length>0?i=n.childNodes.length:i=n.childNodes.length+1;else break;s.node=n,s.offset=i}),e}rangeToNative(t){let e=this.scroll.length(),s=(n,i)=>{n=Math.min(e-1,n);let[o,a]=this.scroll.leaf(n);return o?o.position(a,i):[null,-1]};return[...s(t.index,!1),...s(t.index+t.length,!0)]}setNativeRange(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(li.info("setNativeRange",t,e,s,n),t!=null&&(this.root.parentNode==null||t.parentNode==null||s.parentNode==null))return;let o=document.getSelection();if(o!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});let{native:a}=this.getNativeRange()||{};if(a==null||i||t!==a.startContainer||e!==a.startOffset||s!==a.endContainer||n!==a.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),s instanceof Element&&s.tagName==="BR"&&(n=Array.from(s.parentNode.childNodes).indexOf(s),s=s.parentNode);let l=document.createRange();l.setStart(t,e),l.setEnd(s,n),o.removeAllRanges(),o.addRange(l)}}else o.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:v.sources.API;if(typeof e=="string"&&(s=e,e=!1),li.info("setRange",t),t!=null){let n=this.rangeToNative(t);this.setNativeRange(...n,e)}else this.setNativeRange(null);this.update(s)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:v.sources.USER,e=this.lastRange,[s,n]=this.getRange();if(this.lastRange=s,this.lastNative=n,this.lastRange!=null&&(this.savedRange=this.lastRange),!ce(e,this.lastRange)){if(!this.composing&&n!=null&&n.native.collapsed&&n.start.node!==this.cursor.textNode){let o=this.cursor.restore();o&&this.setNativeRange(o.startNode,o.startOffset,o.endNode,o.endOffset)}let i=[v.events.SELECTION_CHANGE,Nt(this.lastRange),Nt(e),t];this.emitter.emit(v.events.EDITOR_CHANGE,...i),t!==v.sources.SILENT&&this.emitter.emit(...i)}}};function ui(r,t){try{t.parentNode}catch{return!1}return r.contains(t)}var Pu=ci;var Wb=/^[ -~]*$/,hi=class{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();let s=Uu(t),n=new k.default;return Xb(s.ops.slice()).reduce((o,a)=>{let l=k.Op.length(a),u=a.attributes||{},c=!1,f=!1;if(a.insert!=null){if(n.retain(l),typeof a.insert=="string"){let m=a.insert;f=!m.endsWith(`
`)&&(e<=o||!!this.scroll.descendant(F,o)[0]),this.scroll.insertAt(o,m);let[x,A]=this.scroll.line(o),b=et({},nt(x));if(x instanceof M){let[E]=x.descendant(P,A);E&&(b=et(b,nt(E)))}u=k.AttributeMap.diff(b,u)||{}}else if(typeof a.insert=="object"){let m=Object.keys(a.insert)[0];if(m==null)return o;let x=this.scroll.query(m,y.INLINE)!=null;if(x)(e<=o||this.scroll.descendant(F,o)[0])&&(f=!0);else if(o>0){let[A,b]=this.scroll.descendant(P,o-1);A instanceof B?A.value()[b]!==`
`&&(c=!0):A instanceof I&&A.statics.scope===y.INLINE_BLOT&&(c=!0)}if(this.scroll.insertAt(o,m,a.insert[m]),x){let[A]=this.scroll.descendant(P,o);if(A){let b=et({},nt(A));u=k.AttributeMap.diff(b,u)||{}}}}e+=l}else if(n.push(a),a.retain!==null&&typeof a.retain=="object"){let m=Object.keys(a.retain)[0];if(m==null)return o;this.scroll.updateEmbedAt(o,m,a.retain[m])}Object.keys(u).forEach(m=>{this.scroll.formatAt(o,l,m,u[m])});let h=c?1:0,p=f?1:0;return e+=h+p,n.retain(h),n.delete(p),o+l+h+p},0),n.reduce((o,a)=>typeof a.delete=="number"?(this.scroll.deleteAt(o,a.delete),o):o+k.Op.length(a),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(s)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new k.default().retain(t).delete(e))}formatLine(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(s).forEach(i=>{this.scroll.lines(t,Math.max(e,1)).forEach(o=>{o.format(i,s[i])})}),this.scroll.optimize();let n=new k.default().retain(t).retain(e,Nt(s));return this.update(n)}formatText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(s).forEach(i=>{this.scroll.formatAt(t,e,i,s[i])});let n=new k.default().retain(t).retain(e,Nt(s));return this.update(n)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new k.default)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=[],n=[];e===0?this.scroll.path(t).forEach(a=>{let[l]=a;l instanceof M?s.push(l):l instanceof P&&n.push(l)}):(s=this.scroll.lines(t,e),n=this.scroll.descendants(P,t,e));let[i,o]=[s,n].map(a=>{let l=a.shift();if(l==null)return{};let u=nt(l);for(;Object.keys(u).length>0;){let c=a.shift();if(c==null)return u;u=Zb(nt(c),u)}return u});return O(O({},i),o)}getHTML(t,e){let[s,n]=this.scroll.line(t);if(s){let i=s.length();return s.length()>=n+e&&!(n===0&&e===i)?Nr(s,n,e,!0):Nr(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(s=>typeof s.insert=="string").map(s=>s.insert).join("")}insertContents(t,e){let s=Uu(e),n=new k.default().retain(t).concat(s);return this.scroll.insertContents(t,s),this.update(n)}insertEmbed(t,e,s){return this.scroll.insertAt(t,e,s),this.update(new k.default().retain(t).insert({[e]:s}))}insertText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(s).forEach(n=>{this.scroll.formatAt(t,e.length,n,s[n])}),this.update(new k.default().retain(t).insert(e,Nt(s)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;let t=this.scroll.children.head;if((t==null?void 0:t.statics.blotName)!==M.blotName)return!1;let e=t;return e.children.length>1?!1:e.children.head instanceof K}removeFormat(t,e){let s=this.getText(t,e),[n,i]=this.scroll.line(t+e),o=0,a=new k.default;n!=null&&(o=n.length()-i,a=n.delta().slice(i,i+o-1).insert(`
`));let u=this.getContents(t,e+o).diff(new k.default().insert(s).concat(a)),c=new k.default().retain(t).concat(u);return this.applyDelta(c)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,n=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match(Wb)&&this.scroll.find(e[0].target)){let i=this.scroll.find(e[0].target),o=nt(i),a=i.offset(this.scroll),l=e[0].oldValue.replace(re.CONTENTS,""),u=new k.default().insert(l),c=new k.default().insert(i.value()),f=s&&{oldRange:Fu(s.oldRange,-a),newRange:Fu(s.newRange,-a)};t=new k.default().retain(a).concat(u.diff(c,f)).reduce((p,m)=>m.insert?p.insert(m.insert,o):p.push(m),new k.default),this.delta=n.compose(t)}else this.delta=this.getDelta(),(!t||!ce(n.compose(t),this.delta))&&(t=n.diff(this.delta,s));return t}};function Ye(r,t,e){if(r.length===0){let[p]=fi(e.pop());return t<=0?`</li></${p}>`:`</li></${p}>${Ye([],t-1,e)}`}let[{child:s,offset:n,length:i,indent:o,type:a},...l]=r,[u,c]=fi(a);if(o>t)return e.push(a),o===t+1?`<${u}><li${c}>${Nr(s,n,i)}${Ye(l,o,e)}`:`<${u}><li>${Ye(r,t+1,e)}`;let f=e[e.length-1];if(o===t&&a===f)return`</li><li${c}>${Nr(s,n,i)}${Ye(l,o,e)}`;let[h]=fi(e.pop());return`</li></${h}>${Ye(r,t-1,e)}`}function Nr(r,t,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in r&&typeof r.html=="function")return r.html(t,e);if(r instanceof B)return ve(r.value().slice(t,t+e)).replaceAll(" ","&nbsp;");if(r instanceof rt){if(r.statics.blotName==="list-container"){let u=[];return r.children.forEachAt(t,e,(c,f,h)=>{let p="formats"in c&&typeof c.formats=="function"?c.formats():{};u.push({child:c,offset:f,length:h,indent:p.indent||0,type:p.list})}),Ye(u,-1,[])}let n=[];if(r.children.forEachAt(t,e,(u,c,f)=>{n.push(Nr(u,c,f))}),s||r.statics.blotName==="list")return n.join("");let{outerHTML:i,innerHTML:o}=r.domNode,[a,l]=i.split(`>${o}<`);return a==="<table"?`<table style="border: 1px solid #000;">${n.join("")}<${l}`:`${a}>${n.join("")}<${l}`}return r.domNode instanceof Element?r.domNode.outerHTML:""}function Zb(r,t){return Object.keys(t).reduce((e,s)=>{if(r[s]==null)return e;let n=t[s];return n===r[s]?e[s]=n:Array.isArray(n)?n.indexOf(r[s])<0?e[s]=n.concat([r[s]]):e[s]=n:e[s]=[n,r[s]],e},{})}function fi(r){let t=r==="ordered"?"ol":"ul";switch(r){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function Uu(r){return r.reduce((t,e)=>{if(typeof e.insert=="string"){let s=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(s,e.attributes)}return t.push(e)},new k.default)}function Fu(r,t){let{index:e,length:s}=r;return new Q(e+t,s)}function Xb(r){let t=[];return r.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((n,i)=>{i&&t.push({insert:`
`,attributes:e.attributes}),n&&t.push({insert:n,attributes:e.attributes})}):t.push(e)}),t}var Hu=hi;var Yb=(()=>{class r{constructor(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=e,this.options=s}}return g(r,"DEFAULTS",{}),r})(),R=Yb;var _s="\uFEFF",di=class extends I{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(s=>{this.contentNode.appendChild(s)}),this.leftGuard=document.createTextNode(_s),this.rightGuard=document.createTextNode(_s),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,s,n=t.data.split(_s).join("");if(t===this.leftGuard)if(this.prev instanceof B){let i=this.prev.length();this.prev.insertAt(i,n),e={startNode:this.prev.domNode,startOffset:i+n.length}}else s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this),e={startNode:s,startOffset:n.length};else t===this.rightGuard&&(this.next instanceof B?(this.next.insertAt(0,n),e={startNode:this.next.domNode,startOffset:n.length}):(s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this.next),e={startNode:s,startOffset:n.length}));return t.data=_s,e}update(t,e){t.forEach(s=>{if(s.type==="characterData"&&(s.target===this.leftGuard||s.target===this.rightGuard)){let n=this.restore(s.target);n&&(e.range=n)}})}},Qe=di;var pi=class{constructor(t,e){g(this,"isComposing",!1);this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){let e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof Qe)&&(this.emitter.emit(v.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(v.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(v.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(v.events.COMPOSITION_END,t),this.isComposing=!1}},zu=pi;var Qb=(()=>{let t=class t{constructor(s,n){g(this,"modules",{});this.quill=s,this.options=n}init(){Object.keys(this.options.modules).forEach(s=>{this.modules[s]==null&&this.addModule(s)})}addModule(s){let n=this.quill.constructor.import(`modules/${s}`);return this.modules[s]=new n(this.quill,this.options.modules[s]||{}),this.modules[s]}};g(t,"DEFAULTS",{modules:{}}),g(t,"themes",{default:t});let r=t;return r})(),Je=Qb;var Jb=r=>r.parentElement||r.getRootNode().host||null,ty=r=>{let t=r.getBoundingClientRect(),e="offsetWidth"in r&&Math.abs(t.width)/r.offsetWidth||1,s="offsetHeight"in r&&Math.abs(t.height)/r.offsetHeight||1;return{top:t.top,right:t.left+r.clientWidth*e,bottom:t.top+r.clientHeight*s,left:t.left}},Is=r=>{let t=parseInt(r,10);return Number.isNaN(t)?0:t},$u=(r,t,e,s,n,i)=>r<e&&t>s?0:r<e?-(e-r+n):t>s?t-r>s-e?r+n-e:t-s+i:0,ey=(r,t)=>{var i,o,a,l,u;let e=r.ownerDocument,s=t,n=r;for(;n;){let c=n===e.body,f=c?{top:0,right:(o=(i=window.visualViewport)==null?void 0:i.width)!=null?o:e.documentElement.clientWidth,bottom:(l=(a=window.visualViewport)==null?void 0:a.height)!=null?l:e.documentElement.clientHeight,left:0}:ty(n),h=getComputedStyle(n),p=$u(s.left,s.right,f.left,f.right,Is(h.scrollPaddingLeft),Is(h.scrollPaddingRight)),m=$u(s.top,s.bottom,f.top,f.bottom,Is(h.scrollPaddingTop),Is(h.scrollPaddingBottom));if(p||m)if(c)(u=e.defaultView)==null||u.scrollBy(p,m);else{let{scrollLeft:x,scrollTop:A}=n;m&&(n.scrollTop+=m),p&&(n.scrollLeft+=p);let b=n.scrollLeft-x,E=n.scrollTop-A;s={left:s.left-b,top:s.top-E,right:s.right-b,bottom:s.bottom-E}}n=c||h.position==="fixed"?null:Jb(n)}},Ku=ey;var ry=100,sy=["block","break","cursor","inline","scroll","text"],ny=(r,t,e)=>{let s=new Qt;return sy.forEach(n=>{let i=t.query(n);i&&s.register(i)}),r.forEach(n=>{var a;let i=t.query(n);i||e.error(`Cannot register "${n}" specified in "formats" config. Are you sure it was registered?`);let o=0;for(;i;)if(s.register(i),i="blotName"in i&&(a=i.requiredContainer)!=null?a:null,o+=1,o>ry){e.error(`Cycle detected in registering blot requiredContainer: "${n}"`);break}}),s},Gu=ny;var tr=ct("quill"),ks=new Qt;rt.uiClass="ql-ui";var xt=class xt{static debug(t){t===!0&&(t="log"),ct.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return Er.get(t)||ks.find(t,e)}static import(t){return this.imports[t]==null&&tr.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){let t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),s="attrName"in t?t.attrName:t.blotName;typeof s=="string"?this.register(`formats/${s}`,t,e):Object.keys(t).forEach(n=>{this.register(n,t[n],e)})}else{let t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],s=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!s&&tr.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&ks.register(e),typeof e.register=="function"&&e.register(ks)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=iy(t,e),this.container=this.options.container,this.container==null){tr.error("Invalid Quill container",t);return}this.options.debug&&xt.debug(this.options.debug);let s=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",Er.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new v;let n=cr.blotName,i=this.options.registry.query(n);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${n}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new Hu(this.scroll),this.selection=new Pu(this.scroll,this.emitter),this.composition=new zu(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(v.events.EDITOR_CHANGE,o=>{o===v.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(v.events.SCROLL_UPDATE,(o,a)=>{let l=this.selection.lastRange,[u]=this.selection.getRange(),c=l&&u?{oldRange:l,newRange:u}:void 0;yt.call(this,()=>this.editor.update(null,a,c),o)}),this.emitter.on(v.events.SCROLL_EMBED_UPDATE,(o,a)=>{let l=this.selection.lastRange,[u]=this.selection.getRange(),c=l&&u?{oldRange:l,newRange:u}:void 0;yt.call(this,()=>{let f=new se.default().retain(o.offset(this)).retain({[o.statics.blotName]:a});return this.editor.update(f,[],c)},xt.sources.USER)}),s){let o=this.clipboard.convert({html:`${s}<p><br></p>`,text:`
`});this.setContents(o)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){let s=t;t=document.createElement("div"),t.classList.add(s)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,s){return[t,e,,s]=Pt(t,e,s),yt.call(this,()=>this.editor.deleteText(t,e),s,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;let e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:v.sources.API;return yt.call(this,()=>{let n=this.getSelection(!0),i=new se.default;if(n==null)return i;if(this.scroll.query(t,y.BLOCK))i=this.editor.formatLine(n.index,n.length,{[t]:e});else{if(n.length===0)return this.selection.format(t,e),i;i=this.editor.formatText(n.index,n.length,{[t]:e})}return this.setSelection(n,v.sources.SILENT),i},s)}formatLine(t,e,s,n,i){let o;return[t,e,o,i]=Pt(t,e,s,n,i),yt.call(this,()=>this.editor.formatLine(t,e,o),i,t,0)}formatText(t,e,s,n,i){let o;return[t,e,o,i]=Pt(t,e,s,n,i),yt.call(this,()=>this.editor.formatText(t,e,o),i,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=null;if(typeof t=="number"?s=this.selection.getBounds(t,e):s=this.selection.getBounds(t.index,t.length),!s)return null;let n=this.container.getBoundingClientRect();return{bottom:s.bottom-n.top,height:s.height,left:s.left-n.left,right:s.right-n.left,top:s.top-n.top,width:s.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=Pt(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e!=null?e:this.getLength()-t),[t,e]=Pt(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e!=null?e:this.getLength()-t),[t,e]=Pt(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:xt.sources.API;return yt.call(this,()=>this.editor.insertEmbed(t,e,s),n,t)}insertText(t,e,s,n,i){let o;return[t,,o,i]=Pt(t,0,s,n,i),yt.call(this,()=>this.editor.insertText(t,e,o),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,s){return[t,e,,s]=Pt(t,e,s),yt.call(this,()=>this.editor.removeFormat(t,e),s,t)}scrollRectIntoView(t){Ku(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){let t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:v.sources.API;return yt.call(this,()=>{t=new se.default(t);let s=this.getLength(),n=this.editor.deleteText(0,s),i=this.editor.insertContents(0,t),o=this.editor.deleteText(this.getLength()-1,1);return n.compose(i).compose(o)},e)}setSelection(t,e,s){t==null?this.selection.setRange(null,e||xt.sources.API):([t,e,,s]=Pt(t,e,s),this.selection.setRange(new Q(Math.max(0,t),e),s),s!==v.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:v.sources.API,s=new se.default().insert(t);return this.setContents(s,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:v.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:v.sources.API;return yt.call(this,()=>(t=new se.default(t),this.editor.applyDelta(t)),e,!0)}};g(xt,"DEFAULTS",{bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:ks,theme:"default"}),g(xt,"events",v.events),g(xt,"sources",v.sources),g(xt,"version","2.0.3"),g(xt,"imports",{delta:se.default,parchment:fr,"core/module":R,"core/theme":Je});var d=xt;function Vu(r){return typeof r=="string"?document.querySelector(r):r}function mi(r){return Object.entries(r!=null?r:{}).reduce((t,e)=>{let[s,n]=e;return Ht(O({},t),{[s]:n===!0?{}:n})},{})}function Wu(r){return Object.fromEntries(Object.entries(r).filter(t=>t[1]!==void 0))}function iy(r,t){let e=Vu(r);if(!e)throw new Error("Invalid Quill container");let n=!t.theme||t.theme===d.DEFAULTS.theme?Je:d.import(`themes/${t.theme}`);if(!n)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);let p=d.DEFAULTS,{modules:i}=p,o=tn(p,["modules"]),m=n.DEFAULTS,{modules:a}=m,l=tn(m,["modules"]),u=mi(t.modules);u!=null&&u.toolbar&&u.toolbar.constructor!==Object&&(u=Ht(O({},u),{toolbar:{container:u.toolbar}}));let c=et({},mi(i),mi(a),u),f=O(O(O({},o),Wu(l)),Wu(t)),h=t.registry;return h?t.formats&&tr.warn('Ignoring "formats" option because "registry" is specified'):h=t.formats?Gu(t.formats,f.registry,tr):f.registry,Ht(O({},f),{registry:h,container:e,theme:n,modules:Object.entries(c).reduce((x,A)=>{let[b,E]=A;if(!E)return x;let N=d.import(`modules/${b}`);return N==null?(tr.error(`Cannot load ${b} module. Are you sure you registered it?`),x):Ht(O({},x),{[b]:et({},N.DEFAULTS||{},E)})},{}),bounds:Vu(f.bounds)})}function yt(r,t,e,s){if(!this.isEnabled()&&t===v.sources.USER&&!this.allowReadOnlyEdits)return new se.default;let n=e==null?null:this.getSelection(),i=this.editor.delta,o=r();if(n!=null&&(e===!0&&(e=n.index),s==null?n=Zu(n,o,t):s!==0&&(n=Zu(n,e,s,t)),this.setSelection(n,v.sources.SILENT)),o.length()>0){let a=[v.events.TEXT_CHANGE,o,i,t];this.emitter.emit(v.events.EDITOR_CHANGE,...a),t!==v.sources.SILENT&&this.emitter.emit(...a)}return o}function Pt(r,t,e,s,n){let i={};return typeof r.index=="number"&&typeof r.length=="number"?typeof t!="number"?(n=s,s=e,e=t,t=r.length,r=r.index):(t=r.length,r=r.index):typeof t!="number"&&(n=s,s=e,e=t,t=0),typeof e=="object"?(i=e,n=s):typeof e=="string"&&(s!=null?i[e]=s:n=e),n=n||v.sources.API,[r,t,i,n]}function Zu(r,t,e,s){let n=typeof e=="number"?e:0;if(r==null)return null;let i,o;return t&&typeof t.transformPosition=="function"?[i,o]=[r.index,r.index+r.length].map(a=>t.transformPosition(a,s!==v.sources.USER)):[i,o]=[r.index,r.index+r.length].map(a=>a<t||a===t&&s===v.sources.USER?a:n>=0?a+n:Math.max(t,a+n)),new Q(i,o-i)}var gi=class extends He{},pt=gi;var mt=at(ut(),1);function Xu(r){return r instanceof M||r instanceof F}function Yu(r){return typeof r.updateContent=="function"}var oy=(()=>{class r extends cr{constructor(e,s,n){let{emitter:i}=n;super(e,s),this.emitter=i,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",o=>this.handleDragStart(o))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;let e=this.batch;this.batch=!1,this.update(e)}emitMount(e){this.emitter.emit(v.events.SCROLL_BLOT_MOUNT,e)}emitUnmount(e){this.emitter.emit(v.events.SCROLL_BLOT_UNMOUNT,e)}emitEmbedUpdate(e,s){this.emitter.emit(v.events.SCROLL_EMBED_UPDATE,e,s)}deleteAt(e,s){let[n,i]=this.line(e),[o]=this.line(e+s);if(super.deleteAt(e,s),o!=null&&n!==o&&i>0){if(n instanceof F||o instanceof F){this.optimize();return}let a=o.children.head instanceof K?null:o.children.head;n.moveChildren(o,a),n.remove()}this.optimize()}enable(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",e?"true":"false")}formatAt(e,s,n,i){super.formatAt(e,s,n,i),this.optimize()}insertAt(e,s,n){if(e>=this.length())if(n==null||this.scroll.query(s,y.BLOCK)==null){let i=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(i),n==null&&s.endsWith(`
`)?i.insertAt(0,s.slice(0,-1),n):i.insertAt(0,s,n)}else{let i=this.scroll.create(s,n);this.appendChild(i)}else super.insertAt(e,s,n);this.optimize()}insertBefore(e,s){if(e.statics.scope===y.INLINE_BLOT){let n=this.scroll.create(this.statics.defaultChild.blotName);n.appendChild(e),super.insertBefore(n,s)}else super.insertBefore(e,s)}insertContents(e,s){let n=this.deltaToRenderBlocks(s.concat(new mt.default().insert(`
`))),i=n.pop();if(i==null)return;this.batchStart();let o=n.shift();if(o){let u=o.type==="block"&&(o.delta.length()===0||!this.descendant(F,e)[0]&&e<this.length()),c=o.type==="block"?o.delta:new mt.default().insert({[o.key]:o.value});bi(this,e,c);let f=o.type==="block"?1:0,h=e+c.length()+f;u&&this.insertAt(h-1,`
`);let p=nt(this.line(e)[0]),m=mt.AttributeMap.diff(p,o.attributes)||{};Object.keys(m).forEach(x=>{this.formatAt(h-1,1,x,m[x])}),e=h}let[a,l]=this.children.find(e);if(n.length&&(a&&(a=a.split(l),l=0),n.forEach(u=>{if(u.type==="block"){let c=this.createBlock(u.attributes,a||void 0);bi(c,0,u.delta)}else{let c=this.create(u.key,u.value);this.insertBefore(c,a||void 0),Object.keys(u.attributes).forEach(f=>{c.format(f,u.attributes[f])})}})),i.type==="block"&&i.delta.length()){let u=a?a.offset(a.scroll)+l:this.length();bi(this,u,i.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(e){let s=this.path(e).pop();if(!s)return[null,-1];let[n,i]=s;return n instanceof P?[n,i]:[null,-1]}line(e){return e===this.length()?this.line(e-1):this.descendant(Xu,e)}lines(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,n=(i,o,a)=>{let l=[],u=a;return i.children.forEachAt(o,a,(c,f,h)=>{Xu(c)?l.push(c):c instanceof He&&(l=l.concat(n(c,f,u))),u-=h}),l};return n(this,e,s)}optimize(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(e,s),e.length>0&&this.emitter.emit(v.events.SCROLL_OPTIMIZE,e,s))}path(e){return super.path(e).slice(1)}remove(){}update(e){if(this.batch){Array.isArray(e)&&(this.batch=this.batch.concat(e));return}let s=v.sources.USER;typeof e=="string"&&(s=e),Array.isArray(e)||(e=this.observer.takeRecords()),e=e.filter(n=>{let{target:i}=n,o=this.find(i,!0);return o&&!Yu(o)}),e.length>0&&this.emitter.emit(v.events.SCROLL_BEFORE_UPDATE,s,e),super.update(e.concat([])),e.length>0&&this.emitter.emit(v.events.SCROLL_UPDATE,s,e)}updateEmbedAt(e,s,n){let[i]=this.descendant(o=>o instanceof F,e);i&&i.statics.blotName===s&&Yu(i)&&i.updateContent(n)}handleDragStart(e){e.preventDefault()}deltaToRenderBlocks(e){let s=[],n=new mt.default;return e.forEach(i=>{var a;let o=i==null?void 0:i.insert;if(o)if(typeof o=="string"){let l=o.split(`
`);l.slice(0,-1).forEach(c=>{var f;n.insert(c,i.attributes),s.push({type:"block",delta:n,attributes:(f=i.attributes)!=null?f:{}}),n=new mt.default});let u=l[l.length-1];u&&n.insert(u,i.attributes)}else{let l=Object.keys(o)[0];if(!l)return;this.query(l,y.INLINE)?n.push(i):(n.length()&&s.push({type:"block",delta:n,attributes:{}}),n=new mt.default,s.push({type:"blockEmbed",key:l,value:o[l],attributes:(a=i.attributes)!=null?a:{}}))}}),n.length()&&s.push({type:"block",delta:n,attributes:{}}),s}createBlock(e,s){let n,i={};Object.entries(e).forEach(l=>{let[u,c]=l;this.query(u,y.BLOCK&y.BLOT)!=null?n=u:i[u]=c});let o=this.create(n||this.statics.defaultChild.blotName,n?e[n]:void 0);this.insertBefore(o,s||void 0);let a=o.length();return Object.entries(i).forEach(l=>{let[u,c]=l;o.formatAt(0,a,u,c)}),o}}return g(r,"blotName","scroll"),g(r,"className","ql-editor"),g(r,"tagName","DIV"),g(r,"defaultChild",M),g(r,"allowedChildren",[M,F,pt]),r})();function bi(r,t,e){e.reduce((s,n)=>{let i=mt.Op.length(n),o=n.attributes||{};if(n.insert!=null){if(typeof n.insert=="string"){let a=n.insert;r.insertAt(s,a);let[l]=r.descendant(P,s),u=nt(l);o=mt.AttributeMap.diff(u,o)||{}}else if(typeof n.insert=="object"){let a=Object.keys(n.insert)[0];if(a==null)return s;if(r.insertAt(s,a,n.insert[a]),r.scroll.query(a,y.INLINE)!=null){let[u]=r.descendant(P,s),c=nt(u);o=mt.AttributeMap.diff(c,o)||{}}}}return Object.keys(o).forEach(a=>{r.formatAt(s,i,a,o[a])}),s+i},t)}var Qu=oy;var it=at(ut(),1);var yi={scope:y.BLOCK,whitelist:["right","center","justify"]},Ju=new Z("align","align",yi),xi=new j("align","ql-align",yi),Rs=new st("align","text-align",yi);var Tr=class extends st{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(n=>`00${parseInt(n,10).toString(16)}`.slice(-2)).join("")}`):e}},tc=new j("color","ql-color",{scope:y.INLINE}),wr=new Tr("color","color",{scope:y.INLINE});var ec=new j("background","ql-bg",{scope:y.INLINE}),Sr=new Tr("background","background-color",{scope:y.INLINE});var wt=class extends pt{static create(t){let e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(s=>s.length()<=1?"":s.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${ve(this.code(t,e))}
</pre>`}},H=(()=>{class r extends M{static register(){d.register(wt)}}return g(r,"TAB","  "),r})(),rc=(()=>{class r extends G{}return r.blotName="code",r.tagName="CODE",r})();H.blotName="code-block";H.className="ql-code-block";H.tagName="DIV";wt.blotName="code-block-container";wt.className="ql-code-block-container";wt.tagName="DIV";wt.allowedChildren=[H];H.allowedChildren=[B,K,re];H.requiredContainer=wt;var vi={scope:y.BLOCK,whitelist:["rtl"]},Bs=new Z("direction","dir",vi),Ai=new j("direction","ql-direction",vi),Ms=new st("direction","direction",vi);var sc={scope:y.INLINE,whitelist:["serif","monospace"]},Ni=new j("font","ql-font",sc),Ei=class extends st{value(t){return super.value(t).replace(/["']/g,"")}},Ds=new Ei("font","font-family",sc);var Ti=new j("size","ql-size",{scope:y.INLINE,whitelist:["small","large","huge"]}),js=new st("size","font-size",{scope:y.INLINE,whitelist:["10px","18px","32px"]});var W=at(ut(),1);var ay=ct("quill:keyboard"),ly=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",Lr=class r extends R{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(s=>!!e[s]!==t[s]&&e[s]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(s=>{this.options.bindings[s]&&this.addBinding(this.options.bindings[s])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=cy(t);if(n==null){ay.warn("Attempted to add invalid keyboard binding",n);return}typeof e=="function"&&(e={handler:e}),typeof s=="function"&&(s={handler:s}),(Array.isArray(n.key)?n.key:[n.key]).forEach(o=>{let a=O(O(Ht(O({},n),{key:o}),e),s);this.bindings[a.key]=this.bindings[a.key]||[],this.bindings[a.key].push(a)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;let n=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(b=>r.match(t,b));if(n.length===0)return;let i=d.find(t.target,!0);if(i&&i.scroll!==this.quill.scroll)return;let o=this.quill.getSelection();if(o==null||!this.quill.hasFocus())return;let[a,l]=this.quill.getLine(o.index),[u,c]=this.quill.getLeaf(o.index),[f,h]=o.length===0?[u,c]:this.quill.getLeaf(o.index+o.length),p=u instanceof ze?u.value().slice(0,c):"",m=f instanceof ze?f.value().slice(h):"",x={collapsed:o.length===0,empty:o.length===0&&a.length()<=1,format:this.quill.getFormat(o),line:a,offset:l,prefix:p,suffix:m,event:t};n.some(b=>{if(b.collapsed!=null&&b.collapsed!==x.collapsed||b.empty!=null&&b.empty!==x.empty||b.offset!=null&&b.offset!==x.offset)return!1;if(Array.isArray(b.format)){if(b.format.every(E=>x.format[E]==null))return!1}else if(typeof b.format=="object"&&!Object.keys(b.format).every(E=>b.format[E]===!0?x.format[E]!=null:b.format[E]===!1?x.format[E]==null:ce(b.format[E],x.format[E])))return!1;return b.prefix!=null&&!b.prefix.test(x.prefix)||b.suffix!=null&&!b.suffix.test(x.suffix)?!1:b.handler.call(this,o,x,b)!==!0})&&t.preventDefault()})}handleBackspace(t,e){let s=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let n={},[i]=this.quill.getLine(t.index),o=new W.default().retain(t.index-s).delete(s);if(e.offset===0){let[a]=this.quill.getLine(t.index-1);if(a&&!(a.statics.blotName==="block"&&a.length()<=1)){let u=i.formats(),c=this.quill.getFormat(t.index-1,1);if(n=W.AttributeMap.diff(u,c)||{},Object.keys(n).length>0){let f=new W.default().retain(t.index+i.length()-2).retain(1,n);o=o.compose(f)}}}this.quill.updateContents(o,d.sources.USER),this.quill.focus()}handleDelete(t,e){let s=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-s)return;let n={},[i]=this.quill.getLine(t.index),o=new W.default().retain(t.index).delete(s);if(e.offset>=i.length()-1){let[a]=this.quill.getLine(t.index+1);if(a){let l=i.formats(),u=this.quill.getFormat(t.index,1);n=W.AttributeMap.diff(l,u)||{},Object.keys(n).length>0&&(o=o.retain(a.length()-1).retain(1,n))}}this.quill.updateContents(o,d.sources.USER),this.quill.focus()}handleDeleteRange(t){Or({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){let s=Object.keys(e.format).reduce((i,o)=>(this.quill.scroll.query(o,y.BLOCK)&&!Array.isArray(e.format[o])&&(i[o]=e.format[o]),i),{}),n=new W.default().retain(t.index).delete(t.length).insert(`
`,s);this.quill.updateContents(n,d.sources.USER),this.quill.setSelection(t.index+1,d.sources.SILENT),this.quill.focus()}},uy={bindings:{bold:wi("bold"),italic:wi("italic"),underline:wi("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(r,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",d.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(r,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",d.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(r,t){t.format.indent!=null?this.quill.format("indent","-1",d.sources.USER):t.format.list!=null&&this.quill.format("list",!1,d.sources.USER)}},"indent code-block":nc(!0),"outdent code-block":nc(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(r){this.quill.deleteText(r.index-1,1,d.sources.USER)}},tab:{key:"Tab",handler(r,t){if(t.format.table)return!0;this.quill.history.cutoff();let e=new W.default().retain(r.index).delete(r.length).insert("	");return this.quill.updateContents(e,d.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(r.index+1,d.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,d.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(r,t){let e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(r.index,r.length,e,d.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(r){let[t,e]=this.quill.getLine(r.index),s=Ht(O({},t.formats()),{list:"checked"}),n=new W.default().retain(r.index).insert(`
`,s).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(n,d.sources.USER),this.quill.setSelection(r.index+1,d.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(r,t){let[e,s]=this.quill.getLine(r.index),n=new W.default().retain(r.index).insert(`
`,t.format).retain(e.length()-s-1).retain(1,{header:null});this.quill.updateContents(n,d.sources.USER),this.quill.setSelection(r.index+1,d.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(r){let t=this.quill.getModule("table");if(t){let[e,s,n,i]=t.getTable(r),o=fy(e,s,n,i);if(o==null)return;let a=e.offset();if(o<0){let l=new W.default().retain(a).insert(`
`);this.quill.updateContents(l,d.sources.USER),this.quill.setSelection(r.index+1,r.length,d.sources.SILENT)}else if(o>0){a+=e.length();let l=new W.default().retain(a).insert(`
`);this.quill.updateContents(l,d.sources.USER),this.quill.setSelection(a,d.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(r,t){let{event:e,line:s}=t,n=s.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(n-1,d.sources.USER):this.quill.setSelection(n+s.length(),d.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(r,t){if(this.quill.scroll.query("list")==null)return!0;let{length:e}=t.prefix,[s,n]=this.quill.getLine(r.index);if(n>e)return!0;let i;switch(t.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(r.index," ",d.sources.USER),this.quill.history.cutoff();let o=new W.default().retain(r.index-n).delete(e+1).retain(s.length()-2-n).retain(1,{list:i});return this.quill.updateContents(o,d.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(r.index-e,d.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(r){let[t,e]=this.quill.getLine(r.index),s=2,n=t;for(;n!=null&&n.length()<=1&&n.formats()["code-block"];)if(n=n.prev,s-=1,s<=0){let i=new W.default().retain(r.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,d.sources.USER),this.quill.setSelection(r.index-1,d.sources.SILENT),!1}return!0}},"embed left":Ps("ArrowLeft",!1),"embed left shift":Ps("ArrowLeft",!0),"embed right":Ps("ArrowRight",!1),"embed right shift":Ps("ArrowRight",!0),"table down":ic(!1),"table up":ic(!0)}};Lr.DEFAULTS=uy;function nc(r){return{key:"Tab",shiftKey:!r,format:{"code-block":!0},handler(t,e){let{event:s}=e,n=this.quill.scroll.query("code-block"),{TAB:i}=n;if(t.length===0&&!s.shiftKey){this.quill.insertText(t.index,i,d.sources.USER),this.quill.setSelection(t.index+i.length,d.sources.SILENT);return}let o=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t),{index:a,length:l}=t;o.forEach((u,c)=>{r?(u.insertAt(0,i),c===0?a+=i.length:l+=i.length):u.domNode.textContent.startsWith(i)&&(u.deleteAt(0,i.length),c===0?a-=i.length:l-=i.length)}),this.quill.update(d.sources.USER),this.quill.setSelection(a,l,d.sources.SILENT)}}}function Ps(r,t){return{key:r,shiftKey:t,altKey:null,[r==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(s){let{index:n}=s;r==="ArrowRight"&&(n+=s.length+1);let[i]=this.quill.getLeaf(n);return i instanceof I?(r==="ArrowLeft"?t?this.quill.setSelection(s.index-1,s.length+1,d.sources.USER):this.quill.setSelection(s.index-1,d.sources.USER):t?this.quill.setSelection(s.index,s.length+1,d.sources.USER):this.quill.setSelection(s.index+s.length+1,d.sources.USER),!1):!0}}}function wi(r){return{key:r[0],shortKey:!0,handler(t,e){this.quill.format(r,!e.format[r],d.sources.USER)}}}function ic(r){return{key:r?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){let s=r?"prev":"next",n=e.line,i=n.parent[s];if(i!=null){if(i.statics.blotName==="table-row"){let o=i.children.head,a=n;for(;a.prev!=null;)a=a.prev,o=o.next;let l=o.offset(this.quill.scroll)+Math.min(e.offset,o.length()-1);this.quill.setSelection(l,0,d.sources.USER)}}else{let o=n.table()[s];o!=null&&(r?this.quill.setSelection(o.offset(this.quill.scroll)+o.length()-1,0,d.sources.USER):this.quill.setSelection(o.offset(this.quill.scroll),0,d.sources.USER))}return!1}}}function cy(r){if(typeof r=="string"||typeof r=="number")r={key:r};else if(typeof r=="object")r=Nt(r);else return null;return r.shortKey&&(r[ly]=r.shortKey,delete r.shortKey),r}function Or(r){let{quill:t,range:e}=r,s=t.getLines(e),n={};if(s.length>1){let i=s[0].formats(),o=s[s.length-1].formats();n=W.AttributeMap.diff(o,i)||{}}t.deleteText(e,d.sources.USER),Object.keys(n).length>0&&t.formatLine(e.index,1,n,d.sources.USER),t.setSelection(e.index,d.sources.SILENT)}function fy(r,t,e,s){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?s===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}var hy=/font-weight:\s*normal/,dy=["P","OL","UL"],oc=r=>r&&dy.includes(r.tagName),py=r=>{Array.from(r.querySelectorAll("br")).filter(t=>oc(t.previousElementSibling)&&oc(t.nextElementSibling)).forEach(t=>{var e;(e=t.parentNode)==null||e.removeChild(t)})},my=r=>{Array.from(r.querySelectorAll('b[style*="font-weight"]')).filter(t=>{var e;return(e=t.getAttribute("style"))==null?void 0:e.match(hy)}).forEach(t=>{var s;let e=r.createDocumentFragment();e.append(...t.childNodes),(s=t.parentNode)==null||s.replaceChild(e,t)})};function Si(r){r.querySelector('[id^="docs-internal-guid-"]')&&(my(r),py(r))}var gy=/\bmso-list:[^;]*ignore/i,by=/\bmso-list:[^;]*\bl(\d+)/i,yy=/\bmso-list:[^;]*\blevel(\d+)/i,xy=(r,t)=>{let e=r.getAttribute("style"),s=e==null?void 0:e.match(by);if(!s)return null;let n=Number(s[1]),i=e==null?void 0:e.match(yy),o=i?Number(i[1]):1,a=new RegExp(`@list l${n}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),l=t.match(a),u=l&&l[1]==="bullet"?"bullet":"ordered";return{id:n,indent:o,type:u,element:r}},vy=r=>{var o,a;let t=Array.from(r.querySelectorAll("[style*=mso-list]")),e=[],s=[];t.forEach(l=>{(l.getAttribute("style")||"").match(gy)?e.push(l):s.push(l)}),e.forEach(l=>{var u;return(u=l.parentNode)==null?void 0:u.removeChild(l)});let n=r.documentElement.innerHTML,i=s.map(l=>xy(l,n)).filter(l=>l);for(;i.length;){let l=[],u=i.shift();for(;u;)l.push(u),u=i.length&&((o=i[0])==null?void 0:o.element)===u.element.nextElementSibling&&i[0].id===u.id?i.shift():null;let c=document.createElement("ul");l.forEach(p=>{let m=document.createElement("li");m.setAttribute("data-list",p.type),p.indent>1&&m.setAttribute("class",`ql-indent-${p.indent-1}`),m.innerHTML=p.element.innerHTML,c.appendChild(m)});let f=(a=l[0])==null?void 0:a.element,{parentNode:h}=f!=null?f:{};f&&(h==null||h.replaceChild(c,f)),l.slice(1).forEach(p=>{let{element:m}=p;h==null||h.removeChild(m)})}};function Li(r){r.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&vy(r)}var Ay=[Li,Si],Ey=r=>{r.documentElement&&Ay.forEach(t=>{t(r)})},ac=Ey;var Ny=ct("quill:clipboard"),Ty=[[Node.TEXT_NODE,My],[Node.TEXT_NODE,uc],["br",Cy],[Node.ELEMENT_NODE,uc],[Node.ELEMENT_NODE,Oy],[Node.ELEMENT_NODE,Ly],[Node.ELEMENT_NODE,Ry],["li",Iy],["ol, ul",ky],["pre",qy],["tr",By],["b",Oi("bold")],["i",Oi("italic")],["strike",Oi("strike")],["style",_y]],wy=[Ju,Bs].reduce((r,t)=>(r[t.keyName]=t,r),{}),lc=[Rs,Sr,wr,Ms,Ds,js].reduce((r,t)=>(r[t.keyName]=t,r),{}),cc=(()=>{class r extends R{constructor(e,s){var n;super(e,s),this.quill.root.addEventListener("copy",i=>this.onCaptureCopy(i,!1)),this.quill.root.addEventListener("cut",i=>this.onCaptureCopy(i,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Ty.concat((n=this.options.matchers)!=null?n:[]).forEach(i=>{let[o,a]=i;this.addMatcher(o,a)})}addMatcher(e,s){this.matchers.push([e,s])}convert(e){let{html:s,text:n}=e,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(i[H.blotName])return new it.default().insert(n||"",{[H.blotName]:i[H.blotName]});if(!s)return new it.default().insert(n||"",i);let o=this.convertHTML(s);return Cr(o,`
`)&&(o.ops[o.ops.length-1].attributes==null||i.table)?o.compose(new it.default().retain(o.length()-1).delete(1)):o}normalizeHTML(e){ac(e)}convertHTML(e){let s=new DOMParser().parseFromString(e,"text/html");this.normalizeHTML(s);let n=s.body,i=new WeakMap,[o,a]=this.prepareMatching(n,i);return Fs(this.quill.scroll,n,o,a,i)}dangerouslyPasteHTML(e,s){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:d.sources.API;if(typeof e=="string"){let i=this.convert({html:e,text:""});this.quill.setContents(i,s),this.quill.setSelection(0,d.sources.SILENT)}else{let i=this.convert({html:s,text:""});this.quill.updateContents(new it.default().retain(e).concat(i),n),this.quill.setSelection(e+i.length(),d.sources.SILENT)}}onCaptureCopy(e){var a,l;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(e.defaultPrevented)return;e.preventDefault();let[n]=this.quill.selection.getRange();if(n==null)return;let{html:i,text:o}=this.onCopy(n,s);(a=e.clipboardData)==null||a.setData("text/plain",o),(l=e.clipboardData)==null||l.setData("text/html",i),s&&Or({range:n,quill:this.quill})}normalizeURIList(e){return e.split(/\r?\n/).filter(s=>s[0]!=="#").join(`
`)}onCapturePaste(e){var a,l,u,c,f;if(e.defaultPrevented||!this.quill.isEnabled())return;e.preventDefault();let s=this.quill.getSelection(!0);if(s==null)return;let n=(a=e.clipboardData)==null?void 0:a.getData("text/html"),i=(l=e.clipboardData)==null?void 0:l.getData("text/plain");if(!n&&!i){let h=(u=e.clipboardData)==null?void 0:u.getData("text/uri-list");h&&(i=this.normalizeURIList(h))}let o=Array.from(((c=e.clipboardData)==null?void 0:c.files)||[]);if(!n&&o.length>0){this.quill.uploader.upload(s,o);return}if(n&&o.length>0){let h=new DOMParser().parseFromString(n,"text/html");if(h.body.childElementCount===1&&((f=h.body.firstElementChild)==null?void 0:f.tagName)==="IMG"){this.quill.uploader.upload(s,o);return}}this.onPaste(s,{html:n,text:i})}onCopy(e){let s=this.quill.getText(e);return{html:this.quill.getSemanticHTML(e),text:s}}onPaste(e,s){let{text:n,html:i}=s,o=this.quill.getFormat(e.index),a=this.convert({text:n,html:i},o);Ny.log("onPaste",a,{text:n,html:i});let l=new it.default().retain(e.index).delete(e.length).concat(a);this.quill.updateContents(l,d.sources.USER),this.quill.setSelection(l.length()-e.length,d.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(e,s){let n=[],i=[];return this.matchers.forEach(o=>{let[a,l]=o;switch(a){case Node.TEXT_NODE:i.push(l);break;case Node.ELEMENT_NODE:n.push(l);break;default:Array.from(e.querySelectorAll(a)).forEach(u=>{if(s.has(u)){let c=s.get(u);c==null||c.push(l)}else s.set(u,[l])});break}}),[n,i]}}return g(r,"DEFAULTS",{matchers:[]}),r})();function Ae(r,t,e,s){return s.query(t)?r.reduce((n,i)=>{if(!i.insert)return n;if(i.attributes&&i.attributes[t])return n.push(i);let o=e?{[t]:e}:{};return n.insert(i.insert,O(O({},o),i.attributes))},new it.default):r}function Cr(r,t){let e="";for(let s=r.ops.length-1;s>=0&&e.length<t.length;--s){let n=r.ops[s];if(typeof n.insert!="string")break;e=n.insert+e}return e.slice(-1*t.length)===t}function ne(r,t){if(!(r instanceof Element))return!1;let e=t.query(r);return e&&e.prototype instanceof I?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(r.tagName.toLowerCase())}function Sy(r,t){return r.previousElementSibling&&r.nextElementSibling&&!ne(r.previousElementSibling,t)&&!ne(r.nextElementSibling,t)}var Us=new WeakMap;function fc(r){return r==null?!1:(Us.has(r)||(r.tagName==="PRE"?Us.set(r,!0):Us.set(r,fc(r.parentNode))),Us.get(r))}function Fs(r,t,e,s,n){return t.nodeType===t.TEXT_NODE?s.reduce((i,o)=>o(t,i,r),new it.default):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((i,o)=>{let a=Fs(r,o,e,s,n);return o.nodeType===t.ELEMENT_NODE&&(a=e.reduce((l,u)=>u(o,l,r),a),a=(n.get(o)||[]).reduce((l,u)=>u(o,l,r),a)),i.concat(a)},new it.default):new it.default}function Oi(r){return(t,e,s)=>Ae(e,r,!0,s)}function Ly(r,t,e){let s=Z.keys(r),n=j.keys(r),i=st.keys(r),o={};return s.concat(n).concat(i).forEach(a=>{let l=e.query(a,y.ATTRIBUTE);l!=null&&(o[l.attrName]=l.value(r),o[l.attrName])||(l=wy[a],l!=null&&(l.attrName===a||l.keyName===a)&&(o[l.attrName]=l.value(r)||void 0),l=lc[a],l!=null&&(l.attrName===a||l.keyName===a)&&(l=lc[a],o[l.attrName]=l.value(r)||void 0))}),Object.entries(o).reduce((a,l)=>{let[u,c]=l;return Ae(a,u,c,e)},t)}function Oy(r,t,e){let s=e.query(r);if(s==null)return t;if(s.prototype instanceof I){let n={},i=s.value(r);if(i!=null)return n[s.blotName]=i,new it.default().insert(n,s.formats(r,e))}else if(s.prototype instanceof fe&&!Cr(t,`
`)&&t.insert(`
`),"blotName"in s&&"formats"in s&&typeof s.formats=="function")return Ae(t,s.blotName,s.formats(r,e),e);return t}function Cy(r,t){return Cr(t,`
`)||t.insert(`
`),t}function qy(r,t,e){let s=e.query("code-block"),n=s&&"formats"in s&&typeof s.formats=="function"?s.formats(r,e):!0;return Ae(t,"code-block",n,e)}function _y(){return new it.default}function Iy(r,t,e){let s=e.query(r);if(s==null||s.blotName!=="list"||!Cr(t,`
`))return t;let n=-1,i=r.parentNode;for(;i!=null;)["OL","UL"].includes(i.tagName)&&(n+=1),i=i.parentNode;return n<=0?t:t.reduce((o,a)=>a.insert?a.attributes&&typeof a.attributes.indent=="number"?o.push(a):o.insert(a.insert,O({indent:n},a.attributes||{})):o,new it.default)}function ky(r,t,e){let s=r,n=s.tagName==="OL"?"ordered":"bullet",i=s.getAttribute("data-checked");return i&&(n=i==="true"?"checked":"unchecked"),Ae(t,"list",n,e)}function uc(r,t,e){if(!Cr(t,`
`)){if(ne(r,e)&&(r.childNodes.length>0||r instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&r.nextSibling){let s=r.nextSibling;for(;s!=null;){if(ne(s,e))return t.insert(`
`);let n=e.query(s);if(n&&n.prototype instanceof F)return t.insert(`
`);s=s.firstChild}}}return t}function Ry(r,t,e){var i;let s={},n=r.style||{};return n.fontStyle==="italic"&&(s.italic=!0),n.textDecoration==="underline"&&(s.underline=!0),n.textDecoration==="line-through"&&(s.strike=!0),((i=n.fontWeight)!=null&&i.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(s.bold=!0),t=Object.entries(s).reduce((o,a)=>{let[l,u]=a;return Ae(o,l,u,e)},t),parseFloat(n.textIndent||0)>0?new it.default().insert("	").concat(t):t}function By(r,t,e){var n,i;let s=((n=r.parentElement)==null?void 0:n.tagName)==="TABLE"?r.parentElement:(i=r.parentElement)==null?void 0:i.parentElement;if(s!=null){let a=Array.from(s.querySelectorAll("tr")).indexOf(r)+1;return Ae(t,"table",a,e)}return t}function My(r,t,e){var n;let s=r.data;if(((n=r.parentElement)==null?void 0:n.tagName)==="O:P")return t.insert(s.trim());if(!fc(r)){if(s.trim().length===0&&s.includes(`
`)&&!Sy(r,e))return t;s=s.replace(/[^\S\u00a0]/g," "),s=s.replace(/ {2,}/g," "),(r.previousSibling==null&&r.parentElement!=null&&ne(r.parentElement,e)||r.previousSibling instanceof Element&&ne(r.previousSibling,e))&&(s=s.replace(/^ /,"")),(r.nextSibling==null&&r.parentElement!=null&&ne(r.parentElement,e)||r.nextSibling instanceof Element&&ne(r.nextSibling,e))&&(s=s.replace(/ $/,"")),s=s.replaceAll("\xA0"," ")}return t.insert(s)}var dc=(()=>{class r extends R{constructor(s,n){super(s,n);g(this,"lastRecorded",0);g(this,"ignoreChange",!1);g(this,"stack",{undo:[],redo:[]});g(this,"currentRange",null);this.quill.on(d.events.EDITOR_CHANGE,(i,o,a,l)=>{i===d.events.SELECTION_CHANGE?o&&l!==d.sources.SILENT&&(this.currentRange=o):i===d.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||l===d.sources.USER?this.record(o,a):this.transform(o)),this.currentRange=Ci(this.currentRange,o))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",i=>{i.inputType==="historyUndo"?(this.undo(),i.preventDefault()):i.inputType==="historyRedo"&&(this.redo(),i.preventDefault())})}change(s,n){if(this.stack[s].length===0)return;let i=this.stack[s].pop();if(!i)return;let o=this.quill.getContents(),a=i.delta.invert(o);this.stack[n].push({delta:a,range:Ci(i.range,a)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(i.delta,d.sources.USER),this.ignoreChange=!1,this.restoreSelection(i)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(s,n){if(s.ops.length===0)return;this.stack.redo=[];let i=s.invert(n),o=this.currentRange,a=Date.now();if(this.lastRecorded+this.options.delay>a&&this.stack.undo.length>0){let l=this.stack.undo.pop();l&&(i=i.compose(l.delta),o=l.range)}else this.lastRecorded=a;i.length()!==0&&(this.stack.undo.push({delta:i,range:o}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(s){hc(this.stack.undo,s),hc(this.stack.redo,s)}undo(){this.change("undo","redo")}restoreSelection(s){if(s.range)this.quill.setSelection(s.range,d.sources.USER);else{let n=jy(this.quill.scroll,s.delta);this.quill.setSelection(n,d.sources.USER)}}}return g(r,"DEFAULTS",{delay:1e3,maxStack:100,userOnly:!1}),r})();function hc(r,t){let e=t;for(let s=r.length-1;s>=0;s-=1){let n=r[s];r[s]={delta:e.transform(n.delta,!0),range:n.range&&Ci(n.range,e)},e=n.delta.transform(e),r[s].delta.length()===0&&r.splice(s,1)}}function Dy(r,t){let e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(s=>r.query(s,y.BLOCK)!=null):!1}function jy(r,t){let e=t.reduce((n,i)=>n+(i.delete||0),0),s=t.length()-e;return Dy(r,t)&&(s-=1),s}function Ci(r,t){if(!r)return r;let e=t.transformPosition(r.index),s=t.transformPosition(r.index+r.length);return{index:e,length:s-e}}var pc=at(ut(),1);var Py=(()=>{class r extends R{constructor(e,s){super(e,s),e.root.addEventListener("drop",n=>{var a;n.preventDefault();let i=null;if(document.caretRangeFromPoint)i=document.caretRangeFromPoint(n.clientX,n.clientY);else if(document.caretPositionFromPoint){let l=document.caretPositionFromPoint(n.clientX,n.clientY);i=document.createRange(),i.setStart(l.offsetNode,l.offset),i.setEnd(l.offsetNode,l.offset)}let o=i&&e.selection.normalizeNative(i);if(o){let l=e.selection.normalizedToRange(o);(a=n.dataTransfer)!=null&&a.files&&this.upload(l,n.dataTransfer.files)}})}upload(e,s){let n=[];Array.from(s).forEach(i=>{var o;i&&((o=this.options.mimetypes)!=null&&o.includes(i.type))&&n.push(i)}),n.length>0&&this.options.handler.call(this,e,n)}}return r.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(t,e){if(!this.quill.scroll.query("image"))return;let s=e.map(n=>new Promise(i=>{let o=new FileReader;o.onload=()=>{i(o.result)},o.readAsDataURL(n)}));Promise.all(s).then(n=>{let i=n.reduce((o,a)=>o.insert({image:a}),new pc.default().retain(t.index).delete(t.length));this.quill.updateContents(i,v.sources.USER),this.quill.setSelection(t.index+n.length,v.sources.SILENT)})}},r})(),mc=Py;var ie=at(ut(),1);var gc=at(ut(),1);var Uy=["insertText","insertReplacementText"],qi=class extends R{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",s=>{this.handleBeforeInput(s)}),/Android/i.test(navigator.userAgent)||t.on(d.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){Or({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){let s=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new gc.default().retain(t.index).insert(e,s),d.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,d.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!Uy.includes(t.inputType))return;let e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;let s=Fy(t);if(s==null)return;let n=this.quill.selection.normalizeNative(e),i=n?this.quill.selection.normalizedToRange(n):null;i&&this.replaceText(i,s)&&t.preventDefault()}handleCompositionStart(){let t=this.quill.getSelection();t&&this.replaceText(t)}};function Fy(r){var t;return typeof r.data=="string"?r.data:(t=r.dataTransfer)!=null&&t.types.includes("text/plain")?r.dataTransfer.getData("text/plain"):null}var bc=qi;var Hy=/Mac/i.test(navigator.platform),zy=100,$y=r=>!!(r.key==="ArrowLeft"||r.key==="ArrowRight"||r.key==="ArrowUp"||r.key==="ArrowDown"||r.key==="Home"||Hy&&r.key==="a"&&r.ctrlKey===!0),_i=class extends R{constructor(e,s){super(e,s);g(this,"isListening",!1);g(this,"selectionChangeDeadline",0);this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,s){let{line:n,event:i}=s;if(!(n instanceof rt)||!n.uiNode)return!0;let o=getComputedStyle(n.domNode).direction==="rtl";return o&&i.key!=="ArrowRight"||!o&&i.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(i.shiftKey?1:0),d.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&$y(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+zy,this.isListening)return;this.isListening=!0;let e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){let e=document.getSelection();if(!e)return;let s=e.getRangeAt(0);if(s.collapsed!==!0||s.startOffset!==0)return;let n=this.quill.scroll.find(s.startContainer);if(!(n instanceof rt)||!n.uiNode)return;let i=document.createRange();i.setStartAfter(n.uiNode),i.setEndAfter(n.uiNode),e.removeAllRanges(),e.addRange(i)}},yc=_i;d.register({"blots/block":M,"blots/block/embed":F,"blots/break":K,"blots/container":pt,"blots/cursor":re,"blots/embed":Qe,"blots/inline":G,"blots/scroll":Qu,"blots/text":B,"modules/clipboard":cc,"modules/history":dc,"modules/keyboard":Lr,"modules/uploader":mc,"modules/input":bc,"modules/uiNode":yc});var Hs=d;var Ii=class extends j{add(t,e){let s=0;if(e==="+1"||e==="-1"){let n=this.value(t)||0;s=e==="+1"?n+1:n-1}else typeof e=="number"&&(s=e);return s===0?(this.remove(t),!0):super.add(t,s.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}},Ky=new Ii("indent","ql-indent",{scope:y.BLOCK,whitelist:[1,2,3,4,5,6,7,8]}),xc=Ky;var Gy=(()=>{class r extends M{}return g(r,"blotName","blockquote"),g(r,"tagName","blockquote"),r})(),vc=Gy;var Vy=(()=>{class r extends M{static formats(e){return this.tagName.indexOf(e.tagName)+1}}return g(r,"blotName","header"),g(r,"tagName",["H1","H2","H3","H4","H5","H6"]),r})(),Ac=Vy;var ki=(()=>{class r extends pt{}return r.blotName="list-container",r.tagName="OL",r})(),zs=(()=>{class r extends M{static create(e){let s=super.create();return s.setAttribute("data-list",e),s}static formats(e){return e.getAttribute("data-list")||void 0}static register(){d.register(ki)}constructor(e,s){super(e,s);let n=s.ownerDocument.createElement("span"),i=o=>{if(!e.isEnabled())return;let a=this.statics.formats(s,e);a==="checked"?(this.format("list","unchecked"),o.preventDefault()):a==="unchecked"&&(this.format("list","checked"),o.preventDefault())};n.addEventListener("mousedown",i),n.addEventListener("touchstart",i),this.attachUI(n)}format(e,s){e===this.statics.blotName&&s?this.domNode.setAttribute("data-list",s):super.format(e,s)}}return r.blotName="list",r.tagName="LI",r})();ki.allowedChildren=[zs];zs.requiredContainer=ki;var Wy=(()=>{class r extends G{static create(){return super.create()}static formats(){return!0}optimize(e){super.optimize(e),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}return g(r,"blotName","bold"),g(r,"tagName",["STRONG","B"]),r})(),er=Wy;var Zy=(()=>{class r extends er{}return g(r,"blotName","italic"),g(r,"tagName",["EM","I"]),r})(),Ec=Zy;var Ee=(()=>{class r extends G{static create(e){let s=super.create(e);return s.setAttribute("href",this.sanitize(e)),s.setAttribute("rel","noopener noreferrer"),s.setAttribute("target","_blank"),s}static formats(e){return e.getAttribute("href")}static sanitize(e){return Ri(e,this.PROTOCOL_WHITELIST)?e:this.SANITIZED_URL}format(e,s){e!==this.statics.blotName||!s?super.format(e,s):this.domNode.setAttribute("href",this.constructor.sanitize(s))}}return g(r,"blotName","link"),g(r,"tagName","A"),g(r,"SANITIZED_URL","about:blank"),g(r,"PROTOCOL_WHITELIST",["http","https","mailto","tel","sms"]),r})();function Ri(r,t){let e=document.createElement("a");e.href=r;let s=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(s)>-1}var Xy=(()=>{class r extends G{static create(e){return e==="super"?document.createElement("sup"):e==="sub"?document.createElement("sub"):super.create(e)}static formats(e){if(e.tagName==="SUB")return"sub";if(e.tagName==="SUP")return"super"}}return g(r,"blotName","script"),g(r,"tagName",["SUB","SUP"]),r})(),Nc=Xy;var Yy=(()=>{class r extends er{}return g(r,"blotName","strike"),g(r,"tagName",["S","STRIKE"]),r})(),Tc=Yy;var Qy=(()=>{class r extends G{}return g(r,"blotName","underline"),g(r,"tagName","U"),r})(),wc=Qy;var Jy=(()=>{class r extends Qe{static create(e){if(window.katex==null)throw new Error("Formula module requires KaTeX.");let s=super.create(e);return typeof e=="string"&&(window.katex.render(e,s,{throwOnError:!1,errorColor:"#f00"}),s.setAttribute("data-value",e)),s}static value(e){return e.getAttribute("data-value")}html(){let{formula:e}=this.value();return`<span>${e}</span>`}}return g(r,"blotName","formula"),g(r,"className","ql-formula"),g(r,"tagName","SPAN"),r})(),Sc=Jy;var Lc=["alt","height","width"],tx=(()=>{class r extends I{static create(e){let s=super.create(e);return typeof e=="string"&&s.setAttribute("src",this.sanitize(e)),s}static formats(e){return Lc.reduce((s,n)=>(e.hasAttribute(n)&&(s[n]=e.getAttribute(n)),s),{})}static match(e){return/\.(jpe?g|gif|png)$/.test(e)||/^data:image\/.+;base64/.test(e)}static sanitize(e){return Ri(e,["http","https","data"])?e:"//:0"}static value(e){return e.getAttribute("src")}format(e,s){Lc.indexOf(e)>-1?s?this.domNode.setAttribute(e,s):this.domNode.removeAttribute(e):super.format(e,s)}}return g(r,"blotName","image"),g(r,"tagName","IMG"),r})(),Oc=tx;var Cc=["height","width"],ex=(()=>{class r extends F{static create(e){let s=super.create(e);return s.setAttribute("frameborder","0"),s.setAttribute("allowfullscreen","true"),s.setAttribute("src",this.sanitize(e)),s}static formats(e){return Cc.reduce((s,n)=>(e.hasAttribute(n)&&(s[n]=e.getAttribute(n)),s),{})}static sanitize(e){return Ee.sanitize(e)}static value(e){return e.getAttribute("src")}format(e,s){Cc.indexOf(e)>-1?s?this.domNode.setAttribute(e,s):this.domNode.removeAttribute(e):super.format(e,s)}html(){let{video:e}=this.value();return`<a href="${e}">${e}</a>`}}return g(r,"blotName","video"),g(r,"className","ql-video"),g(r,"tagName","IFRAME"),r})(),qc=ex;var Ks=at(ut(),1);var qr=new j("code-token","hljs",{scope:y.INLINE}),_r=(()=>{class r extends G{static formats(e,s){for(;e!=null&&e!==s.domNode;){if(e.classList&&e.classList.contains(H.className))return super.formats(e,s);e=e.parentNode}}constructor(e,s,n){super(e,s,n),qr.add(this.domNode,n)}format(e,s){e!==r.blotName?super.format(e,s):s?qr.add(this.domNode,s):(qr.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),qr.value(this.domNode)||this.unwrap()}}return r.blotName="code-token",r.className="ql-token",r})(),ot=class extends H{static create(t){let e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),_r.blotName,!1),super.replaceWith(t,e)}},$s=(()=>{class r extends wt{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(e,s){e===ot.blotName&&(this.forceNext=!0,this.children.forEach(n=>{n.format(e,s)}))}formatAt(e,s,n,i){n===ot.blotName&&(this.forceNext=!0),super.formatAt(e,s,n,i)}highlight(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;let i=`${Array.from(this.domNode.childNodes).filter(a=>a!==this.uiNode).map(a=>a.textContent).join(`
`)}
`,o=ot.formats(this.children.head.domNode);if(s||this.forceNext||this.cachedText!==i){if(i.trim().length>0||this.cachedText==null){let a=this.children.reduce((u,c)=>u.concat(ei(c,!1)),new Ks.default),l=e(i,o);a.diff(l).reduce((u,c)=>{let{retain:f,attributes:h}=c;return f?(h&&Object.keys(h).forEach(p=>{[ot.blotName,_r.blotName].includes(p)&&this.formatAt(u,f,p,h[p])}),u+f):u},0)}this.cachedText=i,this.forceNext=!1}}html(e,s){let[n]=this.children.find(e);return`<pre data-language="${n?ot.formats(n.domNode):"plain"}">
${ve(this.code(e,s))}
</pre>`}optimize(e){if(super.optimize(e),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){let s=ot.formats(this.children.head.domNode);s!==this.uiNode.value&&(this.uiNode.value=s)}}}return r.allowedChildren=[ot],r})();ot.requiredContainer=$s;ot.allowedChildren=[_r,re,B,K];var rx=(r,t,e)=>{if(typeof r.versionString=="string"){let s=r.versionString.split(".")[0];if(parseInt(s,10)>=11)return r.highlight(e,{language:t}).value}return r.highlight(t,e).value},Ir=class extends R{static register(){d.register(_r,!0),d.register(ot,!0),d.register($s,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((s,n)=>{let{key:i}=n;return s[i]=!0,s},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(d.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof $s))return;let e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(s=>{let{key:n,label:i}=s,o=e.ownerDocument.createElement("option");o.textContent=i,o.setAttribute("value",n),e.appendChild(o)}),e.addEventListener("change",()=>{t.format(ot.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=ot.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(d.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(d.sources.USER);let s=this.quill.getSelection();(t==null?this.quill.scroll.descendants($s):[t]).forEach(i=>{i.highlight(this.highlightBlot,e)}),this.quill.update(d.sources.SILENT),s!=null&&this.quill.setSelection(s,d.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return ve(t).split(`
`).reduce((n,i,o)=>(o!==0&&n.insert(`
`,{[H.blotName]:e}),n.insert(i)),new Ks.default);let s=this.quill.root.ownerDocument.createElement("div");return s.classList.add(H.className),s.innerHTML=rx(this.options.hljs,e,t),Fs(this.quill.scroll,s,[(n,i)=>{let o=qr.value(n);return o?i.compose(new Ks.default().retain(i.length(),{[_r.blotName]:o})):i}],[(n,i)=>n.data.split(`
`).reduce((o,a,l)=>(l!==0&&o.insert(`
`,{[H.blotName]:e}),o.insert(a)),i)],new WeakMap)}};Ir.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};var _c=at(ut(),1);var St=(()=>{let t=class t extends M{static create(s){let n=super.create();return s?n.setAttribute("data-row",s):n.setAttribute("data-row",Vs()),n}static formats(s){if(s.hasAttribute("data-row"))return s.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(s,n){s===t.blotName&&n?this.domNode.setAttribute("data-row",n):super.format(s,n)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}};g(t,"blotName","table"),g(t,"tagName","TD");let r=t;return r})(),oe=(()=>{class r extends pt{checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){let e=this.children.head.formats(),s=this.children.tail.formats(),n=this.next.children.head.formats(),i=this.next.children.tail.formats();return e.table===s.table&&e.table===n.table&&e.table===i.table}return!1}optimize(e){super.optimize(e),this.children.forEach(s=>{if(s.next==null)return;let n=s.formats(),i=s.next.formats();if(n.table!==i.table){let o=this.splitAfter(s);o&&o.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}return g(r,"blotName","table-row"),g(r,"tagName","TR"),r})(),Ut=(()=>{class r extends pt{}return g(r,"blotName","table-body"),g(r,"tagName","TBODY"),r})(),Gs=(()=>{class r extends pt{balanceCells(){let e=this.descendants(oe),s=e.reduce((n,i)=>Math.max(i.children.length,n),0);e.forEach(n=>{new Array(s-n.children.length).fill(0).forEach(()=>{let i;n.children.head!=null&&(i=St.formats(n.children.head.domNode));let o=this.scroll.create(St.blotName,i);n.appendChild(o),o.optimize()})})}cells(e){return this.rows().map(s=>s.children.at(e))}deleteColumn(e){let[s]=this.descendant(Ut);s==null||s.children.head==null||s.children.forEach(n=>{let i=n.children.at(e);i!=null&&i.remove()})}insertColumn(e){let[s]=this.descendant(Ut);s==null||s.children.head==null||s.children.forEach(n=>{let i=n.children.at(e),o=St.formats(n.children.head.domNode),a=this.scroll.create(St.blotName,o);n.insertBefore(a,i)})}insertRow(e){let[s]=this.descendant(Ut);if(s==null||s.children.head==null)return;let n=Vs(),i=this.scroll.create(oe.blotName);s.children.head.children.forEach(()=>{let a=this.scroll.create(St.blotName,n);i.appendChild(a)});let o=s.children.at(e);s.insertBefore(i,o)}rows(){let e=this.children.head;return e==null?[]:e.children.map(s=>s)}}return g(r,"blotName","table-container"),g(r,"tagName","TABLE"),r.allowedChildren=[Ut],r})();Ut.requiredContainer=Gs;Ut.allowedChildren=[oe];oe.requiredContainer=Ut;oe.allowedChildren=[St];St.requiredContainer=oe;function Vs(){return`row-${Math.random().toString(36).slice(2,6)}`}var Bi=class extends R{static register(){d.register(St),d.register(oe),d.register(Ut),d.register(Gs)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(Gs).forEach(t=>{t.balanceCells()})}deleteColumn(){let[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(d.sources.USER))}deleteRow(){let[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(d.sources.USER))}deleteTable(){let[t]=this.getTable();if(t==null)return;let e=t.offset();t.remove(),this.quill.update(d.sources.USER),this.quill.setSelection(e,d.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];let[e,s]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==St.blotName)return[null,null,null,-1];let n=e.parent;return[n.parent.parent,n,e,s]}insertColumn(t){let e=this.quill.getSelection();if(!e)return;let[s,n,i]=this.getTable(e);if(i==null)return;let o=i.cellOffset();s.insertColumn(o+t),this.quill.update(d.sources.USER);let a=n.rowOffset();t===0&&(a+=1),this.quill.setSelection(e.index+a,e.length,d.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){let e=this.quill.getSelection();if(!e)return;let[s,n,i]=this.getTable(e);if(i==null)return;let o=n.rowOffset();s.insertRow(o+t),this.quill.update(d.sources.USER),t>0?this.quill.setSelection(e,d.sources.SILENT):this.quill.setSelection(e.index+n.children.length,e.length,d.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){let s=this.quill.getSelection();if(s==null)return;let n=new Array(t).fill(0).reduce(i=>{let o=new Array(e).fill(`
`).join("");return i.insert(o,{table:Vs()})},new _c.default().retain(s.index));this.quill.updateContents(n,d.sources.USER),this.quill.setSelection(s.index,d.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(d.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(d.events.TEXT_CHANGE,(s,n,i)=>{i===d.sources.USER&&this.balanceTables()}),!0):!1)})}},Ic=Bi;var Bc=at(ut(),1);var kc=ct("quill:toolbar"),Mi=(()=>{class r extends R{constructor(e,s){var n,i;if(super(e,s),Array.isArray(this.options.container)){let o=document.createElement("div");o.setAttribute("role","toolbar"),sx(o,this.options.container),(i=(n=e.container)==null?void 0:n.parentNode)==null||i.insertBefore(o,e.container),this.container=o}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){kc.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(o=>{var l;let a=(l=this.options.handlers)==null?void 0:l[o];a&&this.addHandler(o,a)}),Array.from(this.container.querySelectorAll("button, select")).forEach(o=>{this.attach(o)}),this.quill.on(d.events.EDITOR_CHANGE,()=>{let[o]=this.quill.selection.getRange();this.update(o)})}addHandler(e,s){this.handlers[e]=s}attach(e){let s=Array.from(e.classList).find(i=>i.indexOf("ql-")===0);if(!s)return;if(s=s.slice(3),e.tagName==="BUTTON"&&e.setAttribute("type","button"),this.handlers[s]==null&&this.quill.scroll.query(s)==null){kc.warn("ignoring attaching to nonexistent format",s,e);return}let n=e.tagName==="SELECT"?"change":"click";e.addEventListener(n,i=>{let o;if(e.tagName==="SELECT"){if(e.selectedIndex<0)return;let l=e.options[e.selectedIndex];l.hasAttribute("selected")?o=!1:o=l.value||!1}else e.classList.contains("ql-active")?o=!1:o=e.value||!e.hasAttribute("value"),i.preventDefault();this.quill.focus();let[a]=this.quill.selection.getRange();if(this.handlers[s]!=null)this.handlers[s].call(this,o);else if(this.quill.scroll.query(s).prototype instanceof I){if(o=prompt(`Enter ${s}`),!o)return;this.quill.updateContents(new Bc.default().retain(a.index).delete(a.length).insert({[s]:o}),d.sources.USER)}else this.quill.format(s,o,d.sources.USER);this.update(a)}),this.controls.push([s,e])}update(e){let s=e==null?{}:this.quill.getFormat(e);this.controls.forEach(n=>{let[i,o]=n;if(o.tagName==="SELECT"){let a=null;if(e==null)a=null;else if(s[i]==null)a=o.querySelector("option[selected]");else if(!Array.isArray(s[i])){let l=s[i];typeof l=="string"&&(l=l.replace(/"/g,'\\"')),a=o.querySelector(`option[value="${l}"]`)}a==null?(o.value="",o.selectedIndex=-1):a.selected=!0}else if(e==null)o.classList.remove("ql-active"),o.setAttribute("aria-pressed","false");else if(o.hasAttribute("value")){let a=s[i],l=a===o.getAttribute("value")||a!=null&&a.toString()===o.getAttribute("value")||a==null&&!o.getAttribute("value");o.classList.toggle("ql-active",l),o.setAttribute("aria-pressed",l.toString())}else{let a=s[i]!=null;o.classList.toggle("ql-active",a),o.setAttribute("aria-pressed",a.toString())}})}}return r.DEFAULTS={},r})();function Rc(r,t,e){let s=document.createElement("button");s.setAttribute("type","button"),s.classList.add(`ql-${t}`),s.setAttribute("aria-pressed","false"),e!=null?(s.value=e,s.setAttribute("aria-label",`${t}: ${e}`)):s.setAttribute("aria-label",t),r.appendChild(s)}function sx(r,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{let s=document.createElement("span");s.classList.add("ql-formats"),e.forEach(n=>{if(typeof n=="string")Rc(s,n);else{let i=Object.keys(n)[0],o=n[i];Array.isArray(o)?nx(s,i,o):Rc(s,i,o)}}),r.appendChild(s)})}function nx(r,t,e){let s=document.createElement("select");s.classList.add(`ql-${t}`),e.forEach(n=>{let i=document.createElement("option");n!==!1?i.setAttribute("value",String(n)):i.setAttribute("selected","selected"),s.appendChild(i)}),r.appendChild(s)}Mi.DEFAULTS={container:null,handlers:{clean(){let r=this.quill.getSelection();if(r!=null)if(r.length===0){let t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,y.INLINE)!=null&&this.quill.format(e,!1,d.sources.USER)})}else this.quill.removeFormat(r.index,r.length,d.sources.USER)},direction(r){let{align:t}=this.quill.getFormat();r==="rtl"&&t==null?this.quill.format("align","right",d.sources.USER):!r&&t==="right"&&this.quill.format("align",!1,d.sources.USER),this.quill.format("direction",r,d.sources.USER)},indent(r){let t=this.quill.getSelection(),e=this.quill.getFormat(t),s=parseInt(e.indent||0,10);if(r==="+1"||r==="-1"){let n=r==="+1"?1:-1;e.direction==="rtl"&&(n*=-1),this.quill.format("indent",s+n,d.sources.USER)}},link(r){r===!0&&(r=prompt("Enter link URL:")),this.quill.format("link",r,d.sources.USER)},list(r){let t=this.quill.getSelection(),e=this.quill.getFormat(t);r==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,d.sources.USER):this.quill.format("list","unchecked",d.sources.USER):this.quill.format("list",r,d.sources.USER)}}};var ix='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',ox='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',ax='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',lx='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',ux='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',cx='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',fx='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',hx='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',Mc='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',dx='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',px='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',mx='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',gx='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',bx='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',yx='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',xx='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',vx='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',Ax='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Ex='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',Nx='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',Tx='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',wx='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',Sx='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',Lx='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',Ox='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',Cx='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',qx='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',_x='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',Ix='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',kx='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',Rx='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',Bx='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',Mx='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',ae={align:{"":ix,center:ox,right:ax,justify:lx},background:ux,blockquote:cx,bold:fx,clean:hx,code:Mc,"code-block":Mc,color:dx,direction:{"":px,rtl:mx},formula:gx,header:{1:bx,2:yx,3:xx,4:vx,5:Ax,6:Ex},italic:Nx,image:Tx,indent:{"+1":wx,"-1":Sx},link:Lx,list:{bullet:Ox,check:Cx,ordered:qx},script:{sub:_x,super:Ix},strike:kx,table:Rx,underline:Bx,video:Mx};var Dx='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>',Dc=0;function jc(r,t){r.setAttribute(t,`${r.getAttribute(t)!=="true"}`)}var Di=class{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break;default:}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),jc(this.label,"aria-expanded"),jc(this.options,"aria-hidden")}buildItem(t){let e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");let s=t.getAttribute("value");return s&&e.setAttribute("data-value",s),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",n=>{switch(n.key){case"Enter":this.selectItem(e,!0),n.preventDefault();break;case"Escape":this.escape(),n.preventDefault();break;default:}}),e}buildLabel(){let t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=Dx,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){let t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${Dc}`,Dc+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{let s=this.buildItem(e);t.appendChild(s),e.selected===!0&&this.selectItem(s)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=this.container.querySelector(".ql-selected");t!==s&&(s!=null&&s.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){let s=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(s)}else this.selectItem(null);let e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}},le=Di;var ji=class extends le{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(s=>{s.classList.add("ql-primary")})}buildItem(t){let e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);let s=this.label.querySelector(".ql-color-label"),n=t&&t.getAttribute("data-value")||"";s&&(s.tagName==="line"?s.style.stroke=n:s.style.fill=n)}},Ws=ji;var Pi=class extends le{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(s=>{s.innerHTML=e[s.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);let s=t||this.defaultItem;if(s!=null){if(this.label.innerHTML===s.innerHTML)return;this.label.innerHTML=s.innerHTML}}},Zs=Pi;var jx=r=>{let{overflowY:t}=getComputedStyle(r,null);return t!=="visible"&&t!=="clip"},Ui=class{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,jx(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){let e=t.left+t.width/2-this.root.offsetWidth/2,s=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${s}px`,this.root.classList.remove("ql-flip");let n=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect(),o=0;if(i.right>n.right&&(o=n.right-i.right,this.root.style.left=`${e+o}px`),i.left<n.left&&(o=n.left-i.left,this.root.style.left=`${e+o}px`),i.bottom>n.bottom){let a=i.bottom-i.top,l=t.bottom-t.top+a;this.root.style.top=`${s-l}px`,this.root.classList.add("ql-flip")}return o}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}},Xs=Ui;var Px=[!1,"center","right","justify"],Ux=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],Fx=[!1,"serif","monospace"],Hx=["1","2","3",!1],zx=["small",!1,"large","huge"],Ft=class extends Je{constructor(t,e){super(t,e);let s=n=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",s);return}this.tooltip!=null&&!this.tooltip.root.contains(n.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(i=>{i.container.contains(n.target)||i.close()})};t.emitter.listenDOM("click",document.body,s)}addModule(t){let e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(s=>{(s.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),e[i]!=null))if(i==="direction")s.innerHTML=e[i][""]+e[i].rtl;else if(typeof e[i]=="string")s.innerHTML=e[i];else{let o=s.value||"";o!=null&&e[i][o]&&(s.innerHTML=e[i][o])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(n=>{if(n.classList.contains("ql-align")&&(n.querySelector("option")==null&&kr(n,Px),typeof e.align=="object"))return new Zs(n,e.align);if(n.classList.contains("ql-background")||n.classList.contains("ql-color")){let i=n.classList.contains("ql-background")?"background":"color";return n.querySelector("option")==null&&kr(n,Ux,i==="background"?"#ffffff":"#000000"),new Ws(n,e[i])}return n.querySelector("option")==null&&(n.classList.contains("ql-font")?kr(n,Fx):n.classList.contains("ql-header")?kr(n,Hx):n.classList.contains("ql-size")&&kr(n,zx)),new le(n)});let s=()=>{this.pickers.forEach(n=>{n.update()})};this.quill.on(v.events.EDITOR_CHANGE,s)}};Ft.DEFAULTS=et({},Je.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let r=this.container.querySelector("input.ql-image[type=file]");r==null&&(r=document.createElement("input"),r.setAttribute("type","file"),r.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),r.classList.add("ql-image"),r.addEventListener("change",()=>{let t=this.quill.getSelection(!0);this.quill.uploader.upload(t,r.files),r.value=""}),this.container.appendChild(r)),r.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});var rr=class extends Xs{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");let s=this.quill.getBounds(this.quill.selection.savedRange);s!=null&&this.position(s),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{let{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,v.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,v.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=$x(t);case"formula":{if(!t)break;let e=this.quill.getSelection(!0);if(e!=null){let s=e.index+e.length;this.quill.insertEmbed(s,this.root.getAttribute("data-mode"),t,v.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(s+1," ",v.sources.USER),this.quill.setSelection(s+2,v.sources.USER)}break}default:}this.textbox.value="",this.hide()}};function $x(r){let t=r.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||r.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=r.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:r}function kr(r,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(s=>{let n=document.createElement("option");s===e?n.setAttribute("selected","selected"):n.setAttribute("value",String(s)),r.appendChild(n)})}var Kx=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],Ys=class extends rr{constructor(t,e){super(t,e),this.quill.on(v.events.EDITOR_CHANGE,(s,n,i,o)=>{if(s===v.events.SELECTION_CHANGE)if(n!=null&&n.length>0&&o===v.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;let a=this.quill.getLines(n.index,n.length);if(a.length===1){let l=this.quill.getBounds(n);l!=null&&this.position(l)}else{let l=a[a.length-1],u=this.quill.getIndex(l),c=Math.min(l.length()-1,n.index+n.length-u),f=this.quill.getBounds(new Q(u,c));f!=null&&this.position(f)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(v.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;let t=this.quill.getSelection();if(t!=null){let e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){let e=super.position(t),s=this.root.querySelector(".ql-tooltip-arrow");return s.style.marginLeft="",e!==0&&(s.style.marginLeft=`${-1*e-s.offsetWidth/2}px`),e}};g(Ys,"TEMPLATE",['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""));var Rr=class extends Ft{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Kx),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new Ys(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),ae),this.buildPickers(t.container.querySelectorAll("select"),ae))}};Rr.DEFAULTS=et({},Ft.DEFAULTS,{modules:{toolbar:{handlers:{link(r){r?this.quill.theme.tooltip.edit():this.quill.format("link",!1,d.sources.USER)}}}}});var Gx=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],Qs=class extends rr{constructor(){super(...arguments);g(this,"preview",this.root.querySelector("a.ql-preview"))}listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){let s=this.linkRange;this.restoreFocus(),this.quill.formatText(s,"link",!1,v.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(v.events.SELECTION_CHANGE,(e,s,n)=>{if(e!=null){if(e.length===0&&n===v.sources.USER){let[i,o]=this.quill.scroll.descendant(Ee,e.index);if(i!=null){this.linkRange=new Q(e.index-o,i.length());let a=Ee.formats(i.domNode);this.preview.textContent=a,this.preview.setAttribute("href",a),this.show();let l=this.quill.getBounds(this.linkRange);l!=null&&this.position(l);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}};g(Qs,"TEMPLATE",['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""));var Js=class extends Ft{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Gx),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),ae),this.buildPickers(t.container.querySelectorAll("select"),ae),this.tooltip=new Qs(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,s)=>{t.handlers.link.call(t,!s.format.link)}))}};Js.DEFAULTS=et({},Ft.DEFAULTS,{modules:{toolbar:{handlers:{link(r){if(r){let t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);let{tooltip:s}=this.quill.theme;s.edit("link",e)}else this.quill.format("link",!1,d.sources.USER)}}}}});var Pc=Js;Hs.register({"attributors/attribute/direction":Bs,"attributors/class/align":xi,"attributors/class/background":ec,"attributors/class/color":tc,"attributors/class/direction":Ai,"attributors/class/font":Ni,"attributors/class/size":Ti,"attributors/style/align":Rs,"attributors/style/background":Sr,"attributors/style/color":wr,"attributors/style/direction":Ms,"attributors/style/font":Ds,"attributors/style/size":js},!0);Hs.register({"formats/align":xi,"formats/direction":Ai,"formats/indent":xc,"formats/background":Sr,"formats/color":wr,"formats/font":Ni,"formats/size":Ti,"formats/blockquote":vc,"formats/code-block":H,"formats/header":Ac,"formats/list":zs,"formats/bold":er,"formats/code":rc,"formats/italic":Ec,"formats/link":Ee,"formats/script":Nc,"formats/strike":Tc,"formats/underline":wc,"formats/formula":Sc,"formats/image":Oc,"formats/video":qc,"modules/syntax":Ir,"modules/table":Ic,"modules/toolbar":Mi,"themes/bubble":Rr,"themes/snow":Pc,"ui/icons":ae,"ui/picker":le,"ui/icon-picker":Zs,"ui/color-picker":Ws,"ui/tooltip":Xs},!0);var VC=Hs;var export_AttributeMap=ie.AttributeMap;var export_Delta=ie.default;var export_Op=ie.Op;var export_OpIterator=ie.OpIterator;export{export_AttributeMap as AttributeMap,export_Delta as Delta,R as Module,export_Op as Op,export_OpIterator as OpIterator,fr as Parchment,Q as Range,VC as default};
