package tndung.vnfb.smm.repository.nontenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.ChatRoom;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChatRoomRepository extends JpaRepository<ChatRoom, Long> {

    @Query("SELECT DISTINCT cr FROM ChatRoom cr " +
           "JOIN cr.participants cp " +
           "WHERE cp.userId = :userId AND cp.isActive = true " +
           "ORDER BY cr.updatedAt DESC")
    Page<ChatRoom> findChatRoomsByUserId(@Param("userId") Long userId, Pageable pageable);

    @Query("SELECT cr FROM ChatRoom cr " +
           "JOIN cr.participants cp1 " +
           "JOIN cr.participants cp2 " +
           "WHERE cp1.userId = :userId1 AND cp2.userId = :userId2 " +
           "AND cr.type = 'DIRECT' " +
           "AND cp1.isActive = true AND cp2.isActive = true")
    Optional<ChatRoom> findDirectChatRoom(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    @Query("SELECT cr FROM ChatRoom cr " +
           "JOIN cr.participants cp " +
           "WHERE cp.userId = :userId AND cp.isActive = true " +
           "AND cr.isActive = true")
    List<ChatRoom> findActiveChatRoomsByUserId(@Param("userId") Long userId);
}
