package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.dto.request.OrderReq;
import tndung.vnfb.smm.dto.response.OrderRes;
import tndung.vnfb.smm.dto.response.SpecialPriceLiteRes;
import tndung.vnfb.smm.dto.response.SuperOrderRes;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderStatusRes;
import tndung.vnfb.smm.dto.response.smm.SMMOrderStatusRes.SMMOrderStatusResBuilder;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GOrder.GOrderBuilder;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.SpecialPrice;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class OrderMapperImpl implements OrderMapper {

    @Autowired
    private ApiProviderMapper apiProviderMapper;
    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public GOrder toEntity(OrderReq req) {
        if ( req == null ) {
            return null;
        }

        GOrderBuilder gOrder = GOrder.builder();

        gOrder.link( req.getLink() );
        gOrder.quantity( req.getQuantity() );

        return gOrder.build();
    }

    @Override
    public OrderRes toRes(GOrder entity) {
        if ( entity == null ) {
            return null;
        }

        OrderRes orderRes = new OrderRes();

        if ( entity.getId() != null ) {
            orderRes.setId( entity.getId().intValue() );
        }
        orderRes.setService( gServiceToServiceRes( entity.getService() ) );
        orderRes.setType( entity.getType() );
        orderRes.setLink( entity.getLink() );
        orderRes.setQuantity( entity.getQuantity() );
        orderRes.setComments( entity.getComments() );
        orderRes.setCharge( entity.getCharge() );
        orderRes.setActualCharge( entity.getActualCharge() );
        orderRes.setStartCount( entity.getStartCount() );
        orderRes.setRemains( entity.getRemains() );
        orderRes.setBalance( entity.getBalance() );
        orderRes.setNote( entity.getNote() );
        orderRes.setStatus( entity.getStatus() );
        orderRes.setTag( entity.getTag() );
        orderRes.setUpdatedAt( entity.getUpdatedAt() );
        orderRes.setCreatedAt( entity.getCreatedAt() );

        return orderRes;
    }

    @Override
    public List<OrderRes> toRes(List<GOrder> entity) {
        if ( entity == null ) {
            return null;
        }

        List<OrderRes> list = new ArrayList<OrderRes>( entity.size() );
        for ( GOrder gOrder : entity ) {
            list.add( toRes( gOrder ) );
        }

        return list;
    }

    @Override
    public SuperOrderRes toSuperRes(GOrder entity) {
        if ( entity == null ) {
            return null;
        }

        SuperOrderRes superOrderRes = new SuperOrderRes();

        if ( entity.getId() != null ) {
            superOrderRes.setId( entity.getId().intValue() );
        }
        superOrderRes.setService( gServiceToServiceRes( entity.getService() ) );
        superOrderRes.setType( entity.getType() );
        superOrderRes.setLink( entity.getLink() );
        superOrderRes.setQuantity( entity.getQuantity() );
        superOrderRes.setComments( entity.getComments() );
        superOrderRes.setCharge( entity.getCharge() );
        superOrderRes.setActualCharge( entity.getActualCharge() );
        superOrderRes.setStartCount( entity.getStartCount() );
        superOrderRes.setRemains( entity.getRemains() );
        superOrderRes.setBalance( entity.getBalance() );
        superOrderRes.setNote( entity.getNote() );
        superOrderRes.setStatus( entity.getStatus() );
        superOrderRes.setTag( entity.getTag() );
        superOrderRes.setUpdatedAt( entity.getUpdatedAt() );
        superOrderRes.setCreatedAt( entity.getCreatedAt() );
        superOrderRes.setApiProvider( apiProviderMapper.toRes( entity.getApiProvider() ) );
        superOrderRes.setApiOrderId( entity.getApiOrderId() );
        superOrderRes.setApiServiceId( entity.getApiServiceId() );
        superOrderRes.setUser( gUserMapper.toApiKey( entity.getUser() ) );

        return superOrderRes;
    }

    @Override
    public List<SuperOrderRes> toSuperRes(List<GOrder> entity) {
        if ( entity == null ) {
            return null;
        }

        List<SuperOrderRes> list = new ArrayList<SuperOrderRes>( entity.size() );
        for ( GOrder gOrder : entity ) {
            list.add( toSuperRes( gOrder ) );
        }

        return list;
    }

    @Override
    public SMMOrderStatusRes toSMM(GOrder entity) {
        if ( entity == null ) {
            return null;
        }

        SMMOrderStatusResBuilder sMMOrderStatusRes = SMMOrderStatusRes.builder();

        sMMOrderStatusRes.status( entityStatusValue( entity ) );
        if ( entity.getCharge() != null ) {
            sMMOrderStatusRes.charge( entity.getCharge().doubleValue() );
        }
        sMMOrderStatusRes.startCount( entity.getStartCount() );
        sMMOrderStatusRes.remains( entity.getRemains() );
        sMMOrderStatusRes.note( entity.getNote() );

        return sMMOrderStatusRes.build();
    }

    protected SpecialPriceLiteRes specialPriceToSpecialPriceLiteRes(SpecialPrice specialPrice) {
        if ( specialPrice == null ) {
            return null;
        }

        SpecialPriceLiteRes specialPriceLiteRes = new SpecialPriceLiteRes();

        specialPriceLiteRes.setId( specialPrice.getId() );
        if ( specialPrice.getDiscountType() != null ) {
            specialPriceLiteRes.setDiscountType( specialPrice.getDiscountType().name() );
        }
        specialPriceLiteRes.setDiscountValue( specialPrice.getDiscountValue() );

        return specialPriceLiteRes;
    }

    protected List<SpecialPriceLiteRes> specialPriceListToSpecialPriceLiteResList(List<SpecialPrice> list) {
        if ( list == null ) {
            return null;
        }

        List<SpecialPriceLiteRes> list1 = new ArrayList<SpecialPriceLiteRes>( list.size() );
        for ( SpecialPrice specialPrice : list ) {
            list1.add( specialPriceToSpecialPriceLiteRes( specialPrice ) );
        }

        return list1;
    }

    protected ServiceRes gServiceToServiceRes(GService gService) {
        if ( gService == null ) {
            return null;
        }

        ServiceRes serviceRes = new ServiceRes();

        if ( gService.getId() != null ) {
            serviceRes.setId( gService.getId().intValue() );
        }
        serviceRes.setName( gService.getName() );
        serviceRes.setDescription( gService.getDescription() );
        serviceRes.setPrice( gService.getPrice() );
        serviceRes.setAddType( gService.getAddType() );
        List<ServiceLabel> list = gService.getLabels();
        if ( list != null ) {
            serviceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        serviceRes.setType( gService.getType() );
        serviceRes.setMin( gService.getMin() );
        serviceRes.setMax( gService.getMax() );
        serviceRes.setAverageTime( gService.getAverageTime() );
        serviceRes.setRefill( gService.getRefill() );
        serviceRes.setRefillDays( gService.getRefillDays() );
        serviceRes.setSort( gService.getSort() );
        serviceRes.setSampleLink( gService.getSampleLink() );
        serviceRes.setIsOverflow( gService.getIsOverflow() );
        serviceRes.setOverflow( gService.getOverflow() );
        serviceRes.setSpeedPerDay( gService.getSpeedPerDay() );
        serviceRes.setIsFixedPrice( gService.getIsFixedPrice() );
        serviceRes.setCancelButton( gService.getCancelButton() );
        serviceRes.setSpecialPrices( specialPriceListToSpecialPriceLiteResList( gService.getSpecialPrices() ) );

        return serviceRes;
    }

    private String entityStatusValue(GOrder gOrder) {
        if ( gOrder == null ) {
            return null;
        }
        OrderStatus status = gOrder.getStatus();
        if ( status == null ) {
            return null;
        }
        String value = status.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }
}
