package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.NotificationReq;
import tndung.vnfb.smm.dto.response.NotificationRes;
import tndung.vnfb.smm.entity.Notification;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class NotificationMapperImpl implements NotificationMapper {

    @Override
    public Notification toEntity(NotificationReq req) {
        if ( req == null ) {
            return null;
        }

        Notification notification = new Notification();

        notification.setOfferEndDate( NotificationMapper.localToOffset( req.getOfferEndDate() ) );
        notification.setContent( req.getContent() );
        notification.setTitle( req.getTitle() );
        notification.setOfferEndingType( req.getOfferEndingType() );
        notification.setType( req.getType() );
        notification.setShow( req.getShow() );
        notification.setAutoDismiss( req.getAutoDismiss() );
        notification.setDismissHours( req.getDismissHours() );

        return notification;
    }

    @Override
    public NotificationRes toDTO(Notification notification) {
        if ( notification == null ) {
            return null;
        }

        NotificationRes notificationRes = new NotificationRes();

        notificationRes.setId( notification.getId() );
        notificationRes.setContent( notification.getContent() );
        notificationRes.setType( notification.getType() );
        notificationRes.setOfferEndingType( notification.getOfferEndingType() );
        notificationRes.setOfferEndDate( notification.getOfferEndDate() );
        notificationRes.setTitle( notification.getTitle() );
        notificationRes.setShow( notification.getShow() );
        notificationRes.setAutoDismiss( notification.getAutoDismiss() );
        notificationRes.setDismissHours( notification.getDismissHours() );

        return notificationRes;
    }

    @Override
    public List<NotificationRes> toDTO(List<Notification> notifications) {
        if ( notifications == null ) {
            return null;
        }

        List<NotificationRes> list = new ArrayList<NotificationRes>( notifications.size() );
        for ( Notification notification : notifications ) {
            list.add( toDTO( notification ) );
        }

        return list;
    }

    @Override
    public void updateEntityFromDTO(NotificationReq req, Notification notification) {
        if ( req == null ) {
            return;
        }

        notification.setOfferEndDate( NotificationMapper.localToOffset( req.getOfferEndDate() ) );
        notification.setContent( req.getContent() );
        notification.setTitle( req.getTitle() );
        notification.setOfferEndingType( req.getOfferEndingType() );
        notification.setType( req.getType() );
        notification.setShow( req.getShow() );
        notification.setAutoDismiss( req.getAutoDismiss() );
        notification.setDismissHours( req.getDismissHours() );
    }
}
