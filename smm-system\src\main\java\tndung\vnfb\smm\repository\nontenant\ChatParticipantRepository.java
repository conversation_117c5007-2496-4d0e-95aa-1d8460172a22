package tndung.vnfb.smm.repository.nontenant;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tndung.vnfb.smm.entity.ChatParticipant;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ChatParticipantRepository extends JpaRepository<ChatParticipant, Long> {

    List<ChatParticipant> findByChatRoomIdAndIsActiveTrue(Long chatRoomId);

    Optional<ChatParticipant> findByChatRoomIdAndUserIdAndIsActiveTrue(Long chatRoomId, Long userId);

    @Modifying
    @Query("UPDATE ChatParticipant cp SET cp.lastReadAt = :readAt " +
           "WHERE cp.chatRoomId = :chatRoomId AND cp.userId = :userId")
    void updateLastReadAt(@Param("chatRoomId") Long chatRoomId, 
                         @Param("userId") Long userId, 
                         @Param("readAt") OffsetDateTime readAt);

    @Query("SELECT cp.userId FROM ChatParticipant cp " +
           "WHERE cp.chatRoomId = :chatRoomId AND cp.isActive = true")
    List<Long> findUserIdsByChatRoomId(@Param("chatRoomId") Long chatRoomId);
}
