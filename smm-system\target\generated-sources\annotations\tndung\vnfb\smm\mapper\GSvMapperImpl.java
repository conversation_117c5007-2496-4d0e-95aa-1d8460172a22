package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.dto.request.ServiceReq;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.service.SuperServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes.SMMServiceResBuilder;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.Platform;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class GSvMapperImpl implements GSvMapper {

    @Autowired
    private SpecialPriceMapper specialPriceMapper;

    @Override
    public void update(ServiceReq serviceReq, GService generalService) {
        if ( serviceReq == null ) {
            return;
        }

        generalService.setName( serviceReq.getName() );
        generalService.setDescription( serviceReq.getDescription() );
        generalService.setPrice( serviceReq.getPrice() );
        generalService.setPrice1( serviceReq.getPrice1() );
        generalService.setPrice2( serviceReq.getPrice2() );
        generalService.setPercent( serviceReq.getPercent() );
        generalService.setPercent1( serviceReq.getPercent1() );
        generalService.setPercent2( serviceReq.getPercent2() );
        generalService.setIsFixedPrice( serviceReq.getIsFixedPrice() );
        generalService.setRefill( serviceReq.getRefill() );
        generalService.setRefillDays( serviceReq.getRefillDays() );
        generalService.setOriginalPrice( serviceReq.getOriginalPrice() );
        generalService.setMin( serviceReq.getMin() );
        generalService.setMax( serviceReq.getMax() );
        generalService.setApiServiceId( serviceReq.getApiServiceId() );
        generalService.setAddType( serviceReq.getAddType() );
        if ( generalService.getLabels() != null ) {
            List<ServiceLabel> list = serviceReq.getLabels();
            if ( list != null ) {
                generalService.getLabels().clear();
                generalService.getLabels().addAll( list );
            }
            else {
                generalService.setLabels( null );
            }
        }
        else {
            List<ServiceLabel> list = serviceReq.getLabels();
            if ( list != null ) {
                generalService.setLabels( new ArrayList<ServiceLabel>( list ) );
            }
        }
        generalService.setType( serviceReq.getType() );
        generalService.setAutoSync( serviceReq.getAutoSync() );
        generalService.setSyncMinMax( serviceReq.getSyncMinMax() );
        generalService.setSyncRefill( serviceReq.getSyncRefill() );
        generalService.setSyncCancel( serviceReq.getSyncCancel() );
        generalService.setSyncStatus( serviceReq.getSyncStatus() );
        generalService.setLimitFrom( serviceReq.getLimitFrom() );
        generalService.setLimitTo( serviceReq.getLimitTo() );
        generalService.setSampleLink( serviceReq.getSampleLink() );
        generalService.setIsOverflow( serviceReq.getIsOverflow() );
        generalService.setOverflow( serviceReq.getOverflow() );
        generalService.setSpeedPerDay( serviceReq.getSpeedPerDay() );
        generalService.setCancelButton( serviceReq.getCancelButton() );
    }

    @Override
    public GService toEntity(ServiceReq req) {
        if ( req == null ) {
            return null;
        }

        GService gService = new GService();

        gService.setName( req.getName() );
        gService.setDescription( req.getDescription() );
        gService.setPrice( req.getPrice() );
        gService.setPrice1( req.getPrice1() );
        gService.setPrice2( req.getPrice2() );
        gService.setPercent( req.getPercent() );
        gService.setPercent1( req.getPercent1() );
        gService.setPercent2( req.getPercent2() );
        gService.setIsFixedPrice( req.getIsFixedPrice() );
        gService.setRefill( req.getRefill() );
        gService.setRefillDays( req.getRefillDays() );
        gService.setOriginalPrice( req.getOriginalPrice() );
        gService.setMin( req.getMin() );
        gService.setMax( req.getMax() );
        gService.setApiServiceId( req.getApiServiceId() );
        gService.setAddType( req.getAddType() );
        List<ServiceLabel> list = req.getLabels();
        if ( list != null ) {
            gService.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        gService.setType( req.getType() );
        gService.setAutoSync( req.getAutoSync() );
        gService.setSyncMinMax( req.getSyncMinMax() );
        gService.setSyncRefill( req.getSyncRefill() );
        gService.setSyncCancel( req.getSyncCancel() );
        gService.setSyncStatus( req.getSyncStatus() );
        gService.setLimitFrom( req.getLimitFrom() );
        gService.setLimitTo( req.getLimitTo() );
        gService.setSampleLink( req.getSampleLink() );
        gService.setIsOverflow( req.getIsOverflow() );
        gService.setOverflow( req.getOverflow() );
        gService.setSpeedPerDay( req.getSpeedPerDay() );
        gService.setCancelButton( req.getCancelButton() );

        return gService;
    }

    @Override
    public SMMServiceRes toSMM(GService entity) {
        if ( entity == null ) {
            return null;
        }

        SMMServiceResBuilder sMMServiceRes = SMMServiceRes.builder();

        sMMServiceRes.category( entityCategoryName( entity ) );
        if ( entity.getPrice() != null ) {
            sMMServiceRes.rate( entity.getPrice().doubleValue() );
        }
        sMMServiceRes.refill( entity.getRefill() );
        if ( entity.getId() != null ) {
            sMMServiceRes.service( entity.getId().intValue() );
        }
        sMMServiceRes.cancel( entity.getCancelButton() );
        sMMServiceRes.name( entity.getName() );
        if ( entity.getType() != null ) {
            sMMServiceRes.type( entity.getType().name() );
        }
        sMMServiceRes.min( entity.getMin() );
        sMMServiceRes.max( entity.getMax() );
        sMMServiceRes.description( entity.getDescription() );

        return sMMServiceRes.build();
    }

    @Override
    public List<SMMServiceRes> toSMM(List<GService> entity) {
        if ( entity == null ) {
            return null;
        }

        List<SMMServiceRes> list = new ArrayList<SMMServiceRes>( entity.size() );
        for ( GService gService : entity ) {
            list.add( toSMM( gService ) );
        }

        return list;
    }

    @Override
    public ServiceRes toRes(GService entity) {
        if ( entity == null ) {
            return null;
        }

        ServiceRes serviceRes = new ServiceRes();

        serviceRes.setIcon( entityCategoryPlatformIcon( entity ) );
        if ( entity.getId() != null ) {
            serviceRes.setId( entity.getId().intValue() );
        }
        serviceRes.setName( entity.getName() );
        serviceRes.setDescription( entity.getDescription() );
        serviceRes.setPrice( entity.getPrice() );
        serviceRes.setAddType( entity.getAddType() );
        List<ServiceLabel> list = entity.getLabels();
        if ( list != null ) {
            serviceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        serviceRes.setType( entity.getType() );
        serviceRes.setMin( entity.getMin() );
        serviceRes.setMax( entity.getMax() );
        serviceRes.setAverageTime( entity.getAverageTime() );
        serviceRes.setRefill( entity.getRefill() );
        serviceRes.setRefillDays( entity.getRefillDays() );
        serviceRes.setSort( entity.getSort() );
        serviceRes.setSampleLink( entity.getSampleLink() );
        serviceRes.setIsOverflow( entity.getIsOverflow() );
        serviceRes.setOverflow( entity.getOverflow() );
        serviceRes.setSpeedPerDay( entity.getSpeedPerDay() );
        serviceRes.setIsFixedPrice( entity.getIsFixedPrice() );
        serviceRes.setCancelButton( entity.getCancelButton() );
        serviceRes.setSpecialPrices( specialPriceMapper.toLiteRes( entity.getSpecialPrices() ) );

        return serviceRes;
    }

    @Override
    public List<ServiceRes> toRes(List<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ServiceRes> list = new ArrayList<ServiceRes>( entities.size() );
        for ( GService gService : entities ) {
            list.add( toRes( gService ) );
        }

        return list;
    }

    @Override
    public Set<ServiceRes> toRes(Set<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        Set<ServiceRes> set = new HashSet<ServiceRes>( Math.max( (int) ( entities.size() / .75f ) + 1, 16 ) );
        for ( GService gService : entities ) {
            set.add( toRes( gService ) );
        }

        return set;
    }

    @Override
    public SuperServiceRes toSuperRes(GService entity) {
        if ( entity == null ) {
            return null;
        }

        SuperServiceRes superServiceRes = new SuperServiceRes();

        Long id = entityCategoryId( entity );
        if ( id != null ) {
            superServiceRes.setCategoryId( id.intValue() );
        }
        if ( entity.getId() != null ) {
            superServiceRes.setId( entity.getId().intValue() );
        }
        superServiceRes.setName( entity.getName() );
        superServiceRes.setDescription( entity.getDescription() );
        superServiceRes.setPrice( entity.getPrice() );
        List<ServiceLabel> list = entity.getLabels();
        if ( list != null ) {
            superServiceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        superServiceRes.setAverageTime( entity.getAverageTime() );
        superServiceRes.setRefill( entity.getRefill() );
        superServiceRes.setRefillDays( entity.getRefillDays() );
        superServiceRes.setSort( entity.getSort() );
        superServiceRes.setSampleLink( entity.getSampleLink() );
        superServiceRes.setIsOverflow( entity.getIsOverflow() );
        superServiceRes.setOverflow( entity.getOverflow() );
        superServiceRes.setSpeedPerDay( entity.getSpeedPerDay() );
        superServiceRes.setIsFixedPrice( entity.getIsFixedPrice() );
        superServiceRes.setCancelButton( entity.getCancelButton() );
        superServiceRes.setSpecialPrices( specialPriceMapper.toLiteRes( entity.getSpecialPrices() ) );
        superServiceRes.setPrice1( entity.getPrice1() );
        superServiceRes.setPrice2( entity.getPrice2() );
        superServiceRes.setOriginalPrice( entity.getOriginalPrice() );
        superServiceRes.setMin( entity.getMin() );
        superServiceRes.setMax( entity.getMax() );
        superServiceRes.setApiServiceId( entity.getApiServiceId() );
        superServiceRes.setApiProvider( apiProviderToApiProviderRes( entity.getApiProvider() ) );
        superServiceRes.setStatus( entity.getStatus() );
        superServiceRes.setAutoSync( entity.getAutoSync() );
        superServiceRes.setSyncMinMax( entity.getSyncMinMax() );
        superServiceRes.setSyncRefill( entity.getSyncRefill() );
        superServiceRes.setSyncCancel( entity.getSyncCancel() );
        superServiceRes.setSyncStatus( entity.getSyncStatus() );
        superServiceRes.setLimitFrom( entity.getLimitFrom() );
        superServiceRes.setLimitTo( entity.getLimitTo() );
        superServiceRes.setType( entity.getType() );
        if ( entity.getPercent() != null ) {
            superServiceRes.setPercent( entity.getPercent().doubleValue() );
        }
        if ( entity.getPercent1() != null ) {
            superServiceRes.setPercent1( entity.getPercent1().doubleValue() );
        }
        if ( entity.getPercent2() != null ) {
            superServiceRes.setPercent2( entity.getPercent2().doubleValue() );
        }
        superServiceRes.setAddType( entity.getAddType() );

        return superServiceRes;
    }

    @Override
    public List<SuperServiceRes> toSuperRes(List<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SuperServiceRes> list = new ArrayList<SuperServiceRes>( entities.size() );
        for ( GService gService : entities ) {
            list.add( toSuperRes( gService ) );
        }

        return list;
    }

    @Override
    public Set<SuperServiceRes> toSuperRes(Set<GService> entities) {
        if ( entities == null ) {
            return null;
        }

        Set<SuperServiceRes> set = new HashSet<SuperServiceRes>( Math.max( (int) ( entities.size() / .75f ) + 1, 16 ) );
        for ( GService gService : entities ) {
            set.add( toSuperRes( gService ) );
        }

        return set;
    }

    private String entityCategoryName(GService gService) {
        if ( gService == null ) {
            return null;
        }
        Category category = gService.getCategory();
        if ( category == null ) {
            return null;
        }
        String name = category.getName();
        if ( name == null ) {
            return null;
        }
        return name;
    }

    private String entityCategoryPlatformIcon(GService gService) {
        if ( gService == null ) {
            return null;
        }
        Category category = gService.getCategory();
        if ( category == null ) {
            return null;
        }
        Platform platform = category.getPlatform();
        if ( platform == null ) {
            return null;
        }
        String icon = platform.getIcon();
        if ( icon == null ) {
            return null;
        }
        return icon;
    }

    private Long entityCategoryId(GService gService) {
        if ( gService == null ) {
            return null;
        }
        Category category = gService.getCategory();
        if ( category == null ) {
            return null;
        }
        Long id = category.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }

    protected ApiProviderRes apiProviderToApiProviderRes(ApiProvider apiProvider) {
        if ( apiProvider == null ) {
            return null;
        }

        ApiProviderRes apiProviderRes = new ApiProviderRes();

        apiProviderRes.setId( apiProvider.getId() );
        apiProviderRes.setName( apiProvider.getName() );
        apiProviderRes.setUrl( apiProvider.getUrl() );
        apiProviderRes.setBalanceAlert( apiProvider.getBalanceAlert() );
        apiProviderRes.setBalance( apiProvider.getBalance() );
        if ( apiProvider.getCurrency() != null ) {
            apiProviderRes.setCurrency( Enum.valueOf( Currency.class, apiProvider.getCurrency() ) );
        }
        apiProviderRes.setStatus( apiProvider.getStatus() );

        return apiProviderRes;
    }
}
