package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.UserNotificationReq;
import tndung.vnfb.smm.dto.UserNotificationRes;
import tndung.vnfb.smm.entity.UserNotification;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class UserNotificationMapperImpl implements UserNotificationMapper {

    @Override
    public UserNotification toEntity(UserNotificationReq req) {
        if ( req == null ) {
            return null;
        }

        UserNotification userNotification = new UserNotification();

        userNotification.setUserId( req.getUserId() );
        userNotification.setTitle( req.getTitle() );
        userNotification.setContent( req.getContent() );
        userNotification.setType( req.getType() );
        userNotification.setCategory( req.getCategory() );

        return userNotification;
    }

    @Override
    public UserNotificationRes toDto(UserNotification entity) {
        if ( entity == null ) {
            return null;
        }

        UserNotificationRes userNotificationRes = new UserNotificationRes();

        userNotificationRes.setId( entity.getId() );
        userNotificationRes.setTitle( entity.getTitle() );
        userNotificationRes.setContent( entity.getContent() );
        userNotificationRes.setType( entity.getType() );
        userNotificationRes.setCategory( entity.getCategory() );
        userNotificationRes.setIsRead( entity.getIsRead() );
        userNotificationRes.setCreatedAt( entity.getCreatedAt() );
        userNotificationRes.setUpdatedAt( entity.getUpdatedAt() );

        return userNotificationRes;
    }

    @Override
    public List<UserNotificationRes> toDto(List<UserNotification> entities) {
        if ( entities == null ) {
            return null;
        }

        List<UserNotificationRes> list = new ArrayList<UserNotificationRes>( entities.size() );
        for ( UserNotification userNotification : entities ) {
            list.add( toDto( userNotification ) );
        }

        return list;
    }
}
