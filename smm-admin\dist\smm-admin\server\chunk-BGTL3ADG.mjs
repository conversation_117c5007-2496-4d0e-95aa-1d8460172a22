import './polyfills.server.mjs';
import{M as r}from"./chunk-CQYMAYZS.mjs";import{Rc as s,T as g,Y as a}from"./chunk-3IIE333G.mjs";var l=(()=>{let e=class e{constructor(t,i){this.http=t,this.configService=i}getLanguageSettings(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/language`)}updateLanguageSettings(t){return this.http.put(`${this.configService.apiUrl}/tenant-settings/language`,t)}getTenantDefaultLanguage(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/language/default`)}getTenantAvailableLanguages(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/language/available`)}getPredefinedLanguages(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/languages/predefined`)}getCustomLanguages(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/languages/custom`)}createCustomLanguage(t){return this.http.post(`${this.configService.apiUrl}/tenant-settings/languages/custom`,t)}updateCustomLanguage(t,i){return this.http.put(`${this.configService.apiUrl}/tenant-settings/languages/custom/${t}`,i)}deleteCustomLanguage(t){return this.http.delete(`${this.configService.apiUrl}/tenant-settings/languages/custom/${t}`)}getAllAvailableLanguages(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/languages/all`)}};e.\u0275fac=function(i){return new(i||e)(a(s),a(r))},e.\u0275prov=g({token:e,factory:e.\u0275fac,providedIn:"root"});let n=e;return n})();export{l as a};
