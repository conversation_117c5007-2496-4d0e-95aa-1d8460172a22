package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.response.LoginHistoryRes;
import tndung.vnfb.smm.entity.LoginHistory;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class LoginHistoryMapperImpl implements LoginHistoryMapper {

    @Override
    public LoginHistoryRes toRes(LoginHistory entity) {
        if ( entity == null ) {
            return null;
        }

        LoginHistoryRes loginHistoryRes = new LoginHistoryRes();

        if ( entity.getId() != null ) {
            loginHistoryRes.setId( entity.getId().intValue() );
        }
        loginHistoryRes.setIp( entity.getIp() );
        loginHistoryRes.setUserAgent( entity.getUserAgent() );
        loginHistoryRes.setCreatedAt( entity.getCreatedAt() );

        return loginHistoryRes;
    }

    @Override
    public List<LoginHistoryRes> toRes(List<LoginHistory> entities) {
        if ( entities == null ) {
            return null;
        }

        List<LoginHistoryRes> list = new ArrayList<LoginHistoryRes>( entities.size() );
        for ( LoginHistory loginHistory : entities ) {
            list.add( toRes( loginHistory ) );
        }

        return list;
    }
}
