import{a as L,b as z,c as G,d as q,e as J,f as Q}from"./chunk-VCNU6NX7.js";import{e as H,f as K}from"./chunk-C5WQGZWY.js";import"./chunk-HJYZAHTZ.js";import{$a as u,Ab as T,Bb as f,Bc as $,Cc as j,Da as t,Ea as w,Eb as M,Fb as F,Hb as p,Hc as A,Ib as m,Ic as D,Jb as P,Jc as R,Sa as v,Ua as o,Za as a,_a as r,ac as O,ba as I,bc as k,cb as C,cc as B,db as b,eb as x,hc as N,kc as V,la as y,ma as S,md as U,na as E,ob as d,pb as _,qb as g}from"./chunk-MCI2ITGN.js";import"./chunk-WVXK5ZBG.js";var Z=()=>["fas","lightbulb"],ee=()=>["fas","info-circle"],te=(n,c,i)=>({"text-yellow-600 font-medium":n,"text-green-600 font-medium":c,"text-red-500":i}),W=()=>["fas","undo"],X=()=>["fas","server"],ie=(n,c,i,e)=>({"text-red-600 font-medium":n,"text-yellow-600 font-medium":c,"text-green-600":i,"text-gray-500":e}),ne=()=>["fas","sync-alt"],ae=()=>["fas","ellipsis-h"];function re(n,c){n&1&&(a(0,"span"),d(1),p(2,"translate"),r()),n&2&&(t(),_(m(2,1,"panels.new_panel")))}function oe(n,c){n&1&&(a(0,"span",4),E(),a(1,"svg",18),u(2,"circle",19)(3,"path",20),r(),d(4),p(5,"translate"),r()),n&2&&(t(4),g(" ",m(5,1,"panels.creating")," "))}function se(n,c){n&1&&u(0,"app-loading")}function le(n,c){if(n&1){let i=C();a(0,"button",37),b("click",function(){y(i);let s=x().$implicit,l=x(2);return S(l.onRestore(s))}),u(1,"fa-icon",38),d(2),p(3,"translate"),r()}n&2&&(t(),o("icon",f(4,W)),t(),g(" ",m(3,2,"panels.restore")," "))}function de(n,c){if(n&1){let i=C();a(0,"tr",27)(1,"td",28),d(2),r(),a(3,"td",29),d(4),p(5,"date"),r(),a(6,"td",29)(7,"span",30),d(8),r()(),a(9,"td",31)(10,"app-toggle-switch",32),b("toggled",function(s){let l=y(i).$implicit,h=x(2);return S(h.onToggleChange(s,l))}),r()(),a(11,"td",31)(12,"span",33),d(13),r()(),a(14,"td",34)(15,"div",35),v(16,le,4,5,"button",36),r()()()}if(n&2){let i=c.$implicit,e=x(2);t(2),g(" ",i.domain," "),t(2),g(" ",P(5,8,i.subscription_end_date,"short")," "),t(3),o("ngClass",M(11,te,i.days_until_expiration!==void 0&&i.days_until_expiration<=7,i.days_until_expiration!==void 0&&i.days_until_expiration>7&&i.days_until_expiration<=30,i.days_until_expiration===void 0||i.status==="Expired")),t(),g(" ",e.getDaysLeftText(i)," "),t(2),o("isChecked",i.auto_renewal),t(2),o("ngClass",e.getStatusClass(i.status)),t(),g(" ",e.getStatusLabel(i.status)," "),t(3),o("ngIf",i.status==="Expired")}}function ce(n,c){n&1&&(a(0,"tr")(1,"td",39)(2,"div",40),u(3,"fa-icon",41),a(4,"p",42),d(5),p(6,"translate"),r()()()()),n&2&&(t(3),o("icon",f(4,X)),t(2),_(m(6,2,"panels.no_panels")))}function pe(n,c){if(n&1&&(a(0,"table",21)(1,"thead",22)(2,"tr")(3,"th",23),d(4),p(5,"translate"),r(),a(6,"th",23),d(7),p(8,"translate"),r(),a(9,"th",23),d(10),p(11,"translate"),r(),a(12,"th",23),d(13),p(14,"translate"),u(15,"fa-icon",24),r(),a(16,"th",23),d(17),p(18,"translate"),r(),a(19,"th",23),d(20),p(21,"translate"),r()()(),a(22,"tbody",25),v(23,de,17,15,"tr",26)(24,ce,7,5,"tr",10),r()()),n&2){let i=x();t(4),g(" ",m(5,9,"panels.domain")," "),t(3),g(" ",m(8,11,"panels.expiry")," "),t(3),g(" ",m(11,13,"panels.days_left")," "),t(3),g(" ",m(14,15,"panels.renew")," "),t(2),o("icon",f(21,ee)),t(2),g(" ",m(18,17,"panels.status")," "),t(3),g(" ",m(21,19,"panels.actions")," "),t(3),o("ngForOf",i.panels),t(),o("ngIf",i.panels.length===0)}}function me(n,c){n&1&&u(0,"app-loading")}function ge(n,c){if(n&1){let i=C();a(0,"button",37),b("click",function(){y(i);let s=x().$implicit,l=x(2);return S(l.onRestore(s))}),u(1,"fa-icon",38),d(2),p(3,"translate"),r()}n&2&&(t(),o("icon",f(4,W)),t(),g(" ",m(3,2,"panels.restore")," "))}function ue(n,c){if(n&1){let i=C();a(0,"div",46)(1,"div",47)(2,"div")(3,"div",48),d(4),r(),a(5,"div",49),d(6),p(7,"date"),r(),a(8,"div",50),d(9),r()(),a(10,"span",33),d(11),r()(),a(12,"div",51)(13,"div",52)(14,"div",4),u(15,"fa-icon",53),a(16,"span",54),d(17),p(18,"translate"),r()(),a(19,"app-toggle-switch",32),b("toggled",function(s){let l=y(i).$implicit,h=x(2);return S(h.onToggleChange(s,l))}),r()()(),a(20,"div",55),v(21,ge,4,5,"button",36),a(22,"button",37),b("click",function(){let s=y(i).$implicit,l=x(2);return S(l.onActions(s))}),u(23,"fa-icon",38),d(24),p(25,"translate"),r()()()}if(n&2){let i=c.$implicit,e=x(2);t(4),_(i.domain),t(2),_(P(7,12,i.subscription_end_date,"short")),t(2),o("ngClass",F(19,ie,i.days_until_expiration!==void 0&&i.days_until_expiration<=7,i.days_until_expiration!==void 0&&i.days_until_expiration>7&&i.days_until_expiration<=30,i.days_until_expiration!==void 0&&i.days_until_expiration>30,i.days_until_expiration===void 0||i.status==="Expired")),t(),g(" ",e.getDaysLeftText(i)," "),t(),o("ngClass",e.getStatusClass(i.status)),t(),g(" ",e.getStatusLabel(i.status)," "),t(4),o("icon",f(24,ne)),t(2),_(m(18,15,"panels.auto_renewal")),t(2),o("isChecked",i.auto_renewal),t(2),o("ngIf",i.status==="Expired"),t(2),o("icon",f(25,ae)),t(),g(" ",m(25,17,"panels.actions")," ")}}function xe(n,c){n&1&&(a(0,"div",56)(1,"div",40),u(2,"fa-icon",41),a(3,"p",42),d(4),p(5,"translate"),r()()()),n&2&&(t(2),o("icon",f(4,X)),t(2),_(m(5,2,"panels.no_panels")))}function _e(n,c){if(n&1&&(a(0,"div",43),v(1,ue,26,26,"div",44)(2,xe,6,5,"div",45),r()),n&2){let i=x();t(),o("ngForOf",i.panels),t(),o("ngIf",i.panels.length===0)}}var Be=(()=>{let c=class c{constructor(e,s,l,h,Y){this.tenantService=e,this.toastService=s,this.modalService=l,this.panelService=h,this.translateService=Y,this.panels=[],this.loading=!1,this.currentStep=0,this.domainName="",this.isCreatingPanel=!1}ngOnInit(){this.loadPanels()}loadPanels(){this.loading=!0,this.tenantService.getAccessibleTenants().subscribe({next:e=>{this.panels=e,this.loading=!1},error:e=>{console.error("Error loading panels:",e),this.toastService.showError((e==null?void 0:e.message)||this.translateService.instant("panels.failed_to_load")),this.loading=!1}})}onToggleChange(e,s){console.log("Toggle changed for panel:",s,"new value:",e),this.tenantService.toggleAutoRenewal(s.id,e).subscribe({next:l=>{console.log("Auto-renewal toggle success:",l),s.auto_renewal=e,this.toastService.showSuccess(e?this.translateService.instant("panels.auto_renewal_enabled"):this.translateService.instant("panels.auto_renewal_disabled"))},error:l=>{console.error("Error toggling auto-renewal:",l),s.auto_renewal=!e,this.toastService.showError((l==null?void 0:l.message)||this.translateService.instant("panels.failed_to_update_auto_renewal"))}})}onRestore(e){console.log("Restore panel:",e),this.toastService.showSuccess(this.translateService.instant("panels.restore_not_implemented"))}onActions(e){console.log("Actions for panel:",e),this.toastService.showSuccess(this.translateService.instant("panels.actions_not_implemented"))}getDaysLeftText(e){return e.status==="Expired"?this.translateService.instant("panels.expired"):e.days_until_expiration===void 0||e.days_until_expiration===null?"N/A":e.days_until_expiration<0?this.translateService.instant("panels.expired"):e.days_until_expiration===0?this.translateService.instant("panels.expires_today"):e.days_until_expiration===1?`1 ${this.translateService.instant("panels.day_left")}`:`${e.days_until_expiration} ${this.translateService.instant("panels.days_left_text")}`}getStatusClass(e){if(!e)return"bg-gray-100 text-gray-800";switch(e){case"Active":return"bg-green-100 text-green-800 border-green-200";case"Failed":return"bg-red-100 text-red-800 border-red-200";case"Expired":return"bg-red-100 text-red-800 border-red-200";case"New":return"bg-blue-100 text-blue-800 border-blue-200";case"Nginx80":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"Configured":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}}getStatusLabel(e){if(!e)return this.translateService.instant("panels.unknown");switch(e){case"Ready":return this.translateService.instant("panels.active");case"Failed":return this.translateService.instant("panels.error");case"Expired":return this.translateService.instant("panels.expired");case"New":return this.translateService.instant("panels.new");case"Nginx80":case"Configured":return this.translateService.instant("panels.configuring");case"Pending":return this.translateService.instant("panels.pending");default:return e}}formatDate(e){if(!e)return"N/A";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return this.translateService.instant("panels.invalid_date")}}createPanel(){this.domainName="";let e=this.modalService.open(L,{domain:this.domainName});e.instance.close.subscribe(()=>{this.modalService.close()}),e.instance.next.subscribe(s=>{this.domainName=s,this.openStep2Modal()})}openStep2Modal(){this.modalService.close();let e=this.modalService.open(z,{domain:this.domainName});e.instance.close.subscribe(()=>{this.modalService.close()}),e.instance.back.subscribe(()=>{this.modalService.close(),this.createPanel()}),e.instance.next.subscribe(()=>{this.openStep3Modal()})}openStep3Modal(){this.modalService.close();let e=this.modalService.open(G,{domain:this.domainName});e.instance.close.subscribe(()=>{this.modalService.close()}),e.instance.back.subscribe(()=>{this.modalService.close(),this.openStep2Modal()}),e.instance.createPanel.subscribe(s=>{this.createPanelSubmit()})}createPanelSubmit(){this.isCreatingPanel=!0,this.panelService.createPanel(this.domainName).subscribe({next:e=>{this.toastService.showSuccess(`${this.translateService.instant("panels.panel_created_success")} ${this.domainName}`),this.modalService.close(),this.isCreatingPanel=!1,this.loadPanels()},error:e=>{console.error("Error creating panel:",e);let s=(e==null?void 0:e.message)||this.translateService.instant("panels.failed_to_create");this.toastService.showError(s),this.isCreatingPanel=!1}})}};c.\u0275fac=function(s){return new(s||c)(w(H),w(U),w(J),w(q),w(A))},c.\u0275cmp=I({type:c,selectors:[["app-panels-setting"]],standalone:!0,features:[T],decls:28,vars:18,consts:[[1,"panel-layout-container","md:p-6","p-2"],[1,"mb-4"],[1,"text-xl","font-bold"],[1,"mb-6","p-4","bg-blue-50","border","border-blue-200","rounded-lg"],[1,"flex","items-center"],[1,"text-blue-600","mr-3",3,"icon"],[1,"text-blue-800","font-medium"],[1,"text-blue-600","text-sm"],[1,"mb-6"],[1,"px-4","py-2","bg-[#3b82f6]","text-white","rounded-lg","hover:bg-[#2563eb]","transition-colors","duration-200",3,"click","disabled"],[4,"ngIf"],["class","flex items-center",4,"ngIf"],[1,"hidden","md:block","overflow-x-auto"],[1,"inline-block","min-w-full","align-middle"],[1,"overflow-hidden","rounded-lg","shadow-sm"],["class","min-w-full divide-y divide-gray-200",4,"ngIf"],[1,"md:hidden"],["class","space-y-4",4,"ngIf"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24",1,"animate-spin","-ml-1","mr-2","h-4","w-4","text-white"],["cx","12","cy","12","r","10","stroke","currentColor","stroke-width","4",1,"opacity-25"],["fill","currentColor","d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",1,"opacity-75"],[1,"min-w-full","divide-y","divide-gray-200"],[1,"bg-gray-50"],["scope","col",1,"px-6","py-3","text-left","text-xs","font-medium","text-gray-500","uppercase","tracking-wider"],[1,"ml-1","text-gray-400",3,"icon"],[1,"bg-white","divide-y","divide-gray-200"],["class","hover:bg-[#f0f7ff] transition-colors duration-150",4,"ngFor","ngForOf"],[1,"hover:bg-[#f0f7ff]","transition-colors","duration-150"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium","text-gray-900"],[1,"px-6","py-4","whitespace-nowrap","text-sm","text-gray-700"],[3,"ngClass"],[1,"px-6","py-4","whitespace-nowrap"],["circleColor","#FFF","toggledBgColor","#3B82F6",3,"toggled","isChecked"],[1,"px-2","py-1","text-xs","font-medium","rounded-full",3,"ngClass"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium"],[1,"flex","space-x-2"],["class","inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200",3,"click",4,"ngIf"],[1,"inline-flex","items-center","px-3","py-1","border","border-gray-300","rounded-md","text-xs","text-gray-700","bg-white","hover:bg-gray-50","transition-colors","duration-200",3,"click"],[1,"mr-1",3,"icon"],["colspan","6",1,"px-6","py-12","text-center"],[1,"flex","flex-col","items-center","justify-center"],[1,"text-gray-300","text-4xl","mb-3",3,"icon"],[1,"text-gray-500"],[1,"space-y-4"],["class","bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:border-[#3b82f6] transition-colors duration-150",4,"ngFor","ngForOf"],["class","text-center py-12",4,"ngIf"],[1,"bg-white","rounded-lg","shadow-sm","border","border-gray-200","overflow-hidden","hover:border-[#3b82f6]","transition-colors","duration-150"],[1,"flex","justify-between","items-center","p-4","border-b","border-gray-100"],[1,"font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],[1,"text-xs","mt-1",3,"ngClass"],[1,"p-4","space-y-3"],[1,"flex","items-center","justify-between"],[1,"text-gray-400","w-5",3,"icon"],[1,"ml-2","text-sm","text-gray-700"],[1,"px-4","py-3","bg-gray-50","flex","justify-end","space-x-2"],[1,"text-center","py-12"]],template:function(s,l){s&1&&(a(0,"div",0)(1,"div",1)(2,"h2",2),d(3),p(4,"translate"),r()(),a(5,"div",3)(6,"div",4),u(7,"fa-icon",5),a(8,"div")(9,"div",6),d(10),p(11,"translate"),r(),a(12,"div",7),d(13),p(14,"translate"),r()()()(),a(15,"div",8)(16,"button",9),b("click",function(){return l.createPanel()}),v(17,re,3,3,"span",10)(18,oe,6,3,"span",11),r()(),a(19,"div")(20,"div",12)(21,"div",13)(22,"div",14),v(23,se,1,0,"app-loading",10)(24,pe,25,22,"table",15),r()()(),a(25,"div",16),v(26,me,1,0,"app-loading",10)(27,_e,3,2,"div",17),r()()()),s&2&&(t(3),_(m(4,11,"panels.title")),t(4),o("icon",f(17,Z)),t(3),_(m(11,13,"panels.start_business")),t(3),_(m(14,15,"panels.price_per_month")),t(3),o("disabled",l.isCreatingPanel),t(),o("ngIf",!l.isCreatingPanel),t(),o("ngIf",l.isCreatingPanel),t(5),o("ngIf",l.loading),t(),o("ngIf",!l.loading),t(2),o("ngIf",l.loading),t(),o("ngIf",!l.loading))},dependencies:[V,O,k,B,N,j,$,R,D,Q,K],styles:[".panel-layout-container[_ngcontent-%COMP%]{margin-left:auto;margin-right:auto;max-width:100%}table[_ngcontent-%COMP%]{width:100%;text-align:left;font-size:.875rem;--tw-text-opacity: 1;color:rgb(33 33 33 / var(--tw-text-opacity, 1))}thead[_ngcontent-%COMP%]{font-size:.75rem;--tw-text-opacity: 1;color:rgb(158 158 158 / var(--tw-text-opacity, 1))}th[_ngcontent-%COMP%], td[_ngcontent-%COMP%]{padding:.75rem 1.5rem}tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1))}.status-badge[_ngcontent-%COMP%]{border-radius:9999px;padding:.25rem .5rem;font-size:.75rem;font-weight:500}.mobile-card[_ngcontent-%COMP%]{overflow:hidden;border-radius:.5rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.mobile-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(245 245 245 / var(--tw-border-opacity, 1));padding:1rem}.mobile-card-body[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.75rem * var(--tw-space-y-reverse))}.mobile-card-body[_ngcontent-%COMP%]{padding:1rem}.mobile-card-footer[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1));padding:.75rem 1rem}.mobile-card-item[_ngcontent-%COMP%]{display:flex;align-items:center}.mobile-card-icon[_ngcontent-%COMP%]{width:1.25rem;--tw-text-opacity: 1;color:rgb(189 189 189 / var(--tw-text-opacity, 1))}.mobile-card-text[_ngcontent-%COMP%]{margin-left:.5rem;font-size:.875rem;--tw-text-opacity: 1;color:rgb(97 97 97 / var(--tw-text-opacity, 1))}@media (max-width: 768px){.layout-container[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}}"]});let n=c;return n})();export{Be as PanelsSettingComponent};
