package tndung.vnfb.smm.mapper;

import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.chat.ChatParticipantRes;
import tndung.vnfb.smm.entity.ChatParticipant;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ChatParticipantMapperImpl implements ChatParticipantMapper {

    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public ChatParticipantRes toDTO(ChatParticipant entity) {
        if ( entity == null ) {
            return null;
        }

        ChatParticipantRes chatParticipantRes = new ChatParticipantRes();

        chatParticipantRes.setId( entity.getId() );
        chatParticipantRes.setChatRoomId( entity.getChatRoomId() );
        chatParticipantRes.setUserId( entity.getUserId() );
        chatParticipantRes.setJoinedAt( entity.getJoinedAt() );
        chatParticipantRes.setLastReadAt( entity.getLastReadAt() );
        chatParticipantRes.setIsActive( entity.getIsActive() );
        chatParticipantRes.setUser( gUserMapper.toApiKey( entity.getUser() ) );

        return chatParticipantRes;
    }
}
