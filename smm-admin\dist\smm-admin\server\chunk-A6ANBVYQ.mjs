import './polyfills.server.mjs';
import{e as J}from"./chunk-YCBU6FSH.mjs";import{I as L,M as ee,N as te,b as K,c as O,d as F,e as B,i as V,l as Q,o as X,t as Z}from"./chunk-CQYMAYZS.mjs";import{Ab as q,Fb as $,Fc as P,Gb as z,Hb as N,Jb as T,Ka as i,Kb as x,La as I,Qb as p,Rb as u,Rc as G,T as W,Y as j,_a as v,ab as l,ba as M,cb as A,fb as a,gb as o,hb as _,kb as w,la as C,ma as S,mb as b,nb as g,qb as E,wc as H,xb as d,xc as Y,yb as y,zb as h}from"./chunk-3IIE333G.mjs";import{a as D}from"./chunk-2FGBTQRU.mjs";var ie=(()=>{let s=class s{constructor(t,e){this.http=t,this.configService=e}getTenantCurrencies(){return this.http.get(`${this.configService.apiUrl}/tenant-currencies`)}updateTenantCurrencies(t){return this.http.put(`${this.configService.apiUrl}/tenant-currencies`,t)}getAvailableCurrencies(){return this.http.get(`${this.configService.apiUrl}/tenant-currencies/available`)}triggerCurrencySync(){return this.http.post(`${this.configService.apiUrl}/tenant-currencies/sync`,{})}};s.\u0275fac=function(e){return new(e||s)(j(G),j(ee))},s.\u0275prov=W({token:s,factory:s.\u0275fac,providedIn:"root"});let r=s;return r})();var re=()=>["fas","plus"],de=()=>["fas","sync-alt"],pe=()=>["fas","info-circle"],U=()=>["fas","search"],ue=()=>["fas","clock"],me=()=>["fas","lock"],ge=()=>["fas","edit"],ae=()=>["fas","check-circle"],ye=()=>["fas","coins"],fe=()=>["fas","times"];function _e(r,s){if(r&1&&(a(0,"div",4),_(1,"fa-icon",31),d(2),p(3,"translate"),o()),r&2){let c=g();i(),l("icon",x(5,ue)),i(),q(" ",u(3,3,"Last synced"),": ",c.formatLastSync(c.tenantCurrencies==null?null:c.tenantCurrencies.last_currency_sync)," ")}}function xe(r,s){if(r&1&&(a(0,"span",50),d(1),o()),r&2){let c=g().$implicit;i(),y(c.symbol)}}function be(r,s){r&1&&(a(0,"span",51),d(1),p(2,"translate"),o()),r&2&&(i(),h(" ",u(2,1,"Base")," "))}function ve(r,s){if(r&1&&(_(0,"fa-icon",52),p(1,"translate"),p(2,"translate")),r&2){let c=g().$implicit;E("title",c.base_currency?u(1,2,"Base currency rate is system managed"):u(2,4,"Rate is automatically synced")),l("icon",x(6,me))}}function he(r,s){r&1&&(_(0,"fa-icon",53),p(1,"translate")),r&2&&(E("title",u(1,2,"Custom rate set")),l("icon",x(4,ge)))}function Ce(r,s){if(r&1){let c=w();a(0,"div")(1,"label",54)(2,"input",55),b("change",function(){C(c);let e=g().$implicit,n=g();return S(n.toggleCurrencySync(e))}),o(),_(3,"span",56),o()()}if(r&2){let c,t=g().$implicit,e=g();i(2),l("checked",(c=(c=e.getCurrencySettings(t.code))==null?null:c.sync_enabled)!==null&&c!==void 0?c:!0)}}function Se(r,s){r&1&&(a(0,"div",57),_(1,"fa-icon",58),d(2),p(3,"translate"),o()),r&2&&(i(),l("icon",x(4,ae)),i(),h(" ",u(3,2,"System managed")," "))}function we(r,s){if(r&1){let c=w();a(0,"div")(1,"label",54)(2,"input",55),b("change",function(){C(c);let e=g().$implicit,n=g();return S(n.togglePaymentSync(e))}),o(),_(3,"span",56),o()()}if(r&2){let c,t=g().$implicit,e=g();i(2),l("checked",(c=(c=e.getCurrencySettings(t.code))==null?null:c.payment_sync_enabled)!==null&&c!==void 0?c:!1)}}function Ie(r,s){r&1&&(a(0,"div",57),_(1,"fa-icon",58),d(2),p(3,"translate"),o()),r&2&&(i(),l("icon",x(4,ae)),i(),h(" ",u(3,2,"System managed")," "))}function Ee(r,s){if(r&1){let c=w();a(0,"button",59),b("click",function(){C(c);let e=g().$implicit,n=g();return S(n.removeCurrency(e))}),d(1),p(2,"translate"),o()}r&2&&(i(),h(" ",u(2,1,"Remove")," "))}function ke(r,s){if(r&1){let c=w();a(0,"tr",32)(1,"td",33)(2,"div",34)(3,"div",35)(4,"span",36),d(5),o()(),a(6,"div",37)(7,"div",38),d(8),o(),a(9,"div",39),d(10),o()(),v(11,xe,2,1,"span",40)(12,be,3,3,"span",41),o()(),a(13,"td",33)(14,"div",34)(15,"input",42),b("input",function(e){let n=C(c).$implicit,m=g();return S(m.editingRates[n.code]=+e.target.value)})("blur",function(){let e=C(c).$implicit,n=g();return S(n.updateExchangeRate(e))}),o(),v(16,ve,3,7,"fa-icon",43)(17,he,2,5,"fa-icon",44),o()(),a(18,"td",33),v(19,Ce,4,1,"div",45)(20,Se,4,5,"div",46),o(),a(21,"td",33),v(22,we,4,1,"div",45)(23,Ie,4,5,"div",46),o(),a(24,"td",47)(25,"div",48),v(26,Ee,3,3,"button",49),o()()()}if(r&2){let c,t,e=s.$implicit,n=g();i(5),y(e.code.substring(0,2)),i(3),y(e.code),i(2),y(e.name),i(),l("ngIf",e.symbol),i(),l("ngIf",e.base_currency),i(3),A("bg-gray-100",!n.canEditRate(e)||e.base_currency)("border-orange-300",((c=n.getCurrencySettings(e.code))==null?null:c.custom_rate)&&!((c=n.getCurrencySettings(e.code))!=null&&c.sync_enabled)),l("value",n.editingRates[e.code])("disabled",!n.canEditRate(e)||e.base_currency),i(),l("ngIf",!n.canEditRate(e)||e.base_currency),i(),l("ngIf",((t=n.getCurrencySettings(e.code))==null?null:t.custom_rate)&&!((t=n.getCurrencySettings(e.code))!=null&&t.sync_enabled)),i(2),l("ngIf",!e.base_currency),i(),l("ngIf",e.base_currency),i(2),l("ngIf",!e.base_currency),i(),l("ngIf",e.base_currency),i(3),l("ngIf",!e.base_currency)}}function Me(r,s){r&1&&(a(0,"div",60),_(1,"div",61),a(2,"span",62),d(3),p(4,"translate"),o()()),r&2&&(i(3),y(u(4,1,"Loading currencies...")))}function Te(r,s){r&1&&(a(0,"div",63),_(1,"fa-icon",64),a(2,"p",65),d(3),p(4,"translate"),o()()),r&2&&(i(),l("icon",x(4,ye)),i(2),y(u(4,2,"No currencies found")))}function Pe(r,s){if(r&1){let c=w();a(0,"div",73),b("click",function(){let e=C(c).$implicit,n=g(2);return S(n.addCurrency(e))}),a(1,"div",34)(2,"div",35)(3,"span",36),d(4),o()(),a(5,"div",37)(6,"div",38),d(7),o(),a(8,"div",74),d(9),o()()(),_(10,"fa-icon",75),o()}if(r&2){let c=s.$implicit;i(4),y(c.code.substring(0,2)),i(3),y(c.code),i(2),y(c.name),i(),l("icon",x(4,re))}}function Oe(r,s){r&1&&(a(0,"div",63),_(1,"fa-icon",76),a(2,"p",65),d(3),p(4,"translate"),o(),a(5,"p",39),d(6),p(7,"translate"),o()()),r&2&&(i(),l("icon",x(7,U)),i(2),y(u(4,3,"No currencies available to add")),i(3),y(u(7,5,"All available currencies have been added to your tenant")))}function Fe(r,s){if(r&1){let c=w();a(0,"div",66),b("click",function(){C(c);let e=g();return S(e.toggleCurrencySelector())}),a(1,"div",67),b("click",function(e){return C(c),S(e.stopPropagation())}),a(2,"div",20)(3,"div",68)(4,"div")(5,"h3",21),d(6),p(7,"translate"),o(),a(8,"p",4),d(9),p(10,"translate"),o()(),a(11,"button",69),b("click",function(){C(c);let e=g();return S(e.toggleCurrencySelector())}),_(12,"fa-icon",7),o()()(),a(13,"div",20)(14,"div",16),_(15,"fa-icon",17),a(16,"input",18),p(17,"translate"),N("ngModelChange",function(e){C(c);let n=g();return z(n.searchText,e)||(n.searchText=e),S(e)}),o()()(),a(18,"div",70)(19,"div",71),v(20,Pe,11,5,"div",72),o(),v(21,Oe,8,8,"div",29),o()()()}if(r&2){let c=g();i(6),y(u(7,8,"Add Currency")),i(3),y(u(10,10,"Select currencies to add to your tenant")),i(3),l("icon",x(14,fe)),i(3),l("icon",x(15,U)),i(),E("placeholder",u(17,12,"Search currencies...")),$("ngModel",c.searchText),i(4),l("ngForOf",c.filteredCurrenciesForSelection),i(),l("ngIf",c.filteredCurrenciesForSelection.length===0)}}var oe=(()=>{let s=class s{constructor(t,e,n){this.tenantCurrencyService=t,this.toastService=e,this.translateService=n,this.isLoading=!1,this.isSyncing=!1,this.searchText="",this.tenantCurrencies=null,this.selectedCurrencies=new Set,this.editingRates={},this.showCurrencySelector=!1,this.availableCurrenciesForSelection=[]}ngOnInit(){this.loadTenantCurrencies()}loadTenantCurrencies(){this.isLoading=!0,this.tenantCurrencyService.getTenantCurrencies().subscribe({next:t=>{this.tenantCurrencies=t,this.selectedCurrencies=new Set(t.available_currencies),t.all_currencies.forEach(e=>{let n=this.getCurrencySettings(e.code),m;n&&!n.sync_enabled&&n.custom_rate?m=n.custom_rate:m=e.exchange_rate,this.editingRates[e.code]=m}),this.availableCurrenciesForSelection=t.all_currencies.filter(e=>!this.selectedCurrencies.has(e.code)&&!e.base_currency),this.isLoading=!1},error:t=>{this.toastService.showError((t==null?void 0:t.message)||"Failed to load currencies"),this.isLoading=!1}})}get availableCurrencies(){var t;return(t=this.tenantCurrencies)!=null&&t.all_currencies?this.tenantCurrencies.all_currencies.filter(e=>this.selectedCurrencies.has(e.code)):[]}get filteredAvailableCurrencies(){return this.availableCurrencies.filter(t=>t.name.toLowerCase().includes(this.searchText.toLowerCase())||t.code.toLowerCase().includes(this.searchText.toLowerCase()))}get filteredCurrenciesForSelection(){return this.availableCurrenciesForSelection.filter(t=>t.name.toLowerCase().includes(this.searchText.toLowerCase())||t.code.toLowerCase().includes(this.searchText.toLowerCase()))}getCurrencySettings(t){var e,n;return((n=(e=this.tenantCurrencies)==null?void 0:e.currency_settings)==null?void 0:n.find(m=>m.currency_code===t))||null}toggleCurrencySync(t){var f;if(t.base_currency){this.toastService.showError("Base currency sync settings cannot be modified");return}let e=this.getCurrencySettings(t.code),n=!((f=e==null?void 0:e.sync_enabled)==null||f),m={[t.code]:n};this.updateCurrencySettings({currency_sync_settings:m})}togglePaymentSync(t){var f;if(t.base_currency){this.toastService.showError("Base currency payment sync settings cannot be modified");return}let e=this.getCurrencySettings(t.code),n=!((f=e==null?void 0:e.payment_sync_enabled)!=null&&f),m={[t.code]:n};this.updateCurrencySettings({payment_sync_settings:m})}updateCurrencySettings(t){this.isLoading=!0;let e=Array.from(this.selectedCurrencies),n=D({available_currencies:e},t);this.tenantCurrencyService.updateTenantCurrencies(n).subscribe({next:m=>{this.tenantCurrencies=m,m.all_currencies.forEach(f=>{let k=this.getCurrencySettings(f.code),R;k&&!k.sync_enabled&&k.custom_rate?R=k.custom_rate:R=f.exchange_rate,this.editingRates[f.code]=R}),this.toastService.showSuccess("Currency settings updated successfully"),this.isLoading=!1},error:m=>{this.toastService.showError((m==null?void 0:m.message)||"Failed to update currency settings"),this.isLoading=!1}})}updateExchangeRate(t){let e=this.editingRates[t.code],n=this.getCurrencySettings(t.code),m=(n==null?void 0:n.custom_rate)||t.exchange_rate;if(!e||e<=0){this.toastService.showError(this.translateService.instant("currency_settings.please_enter_valid_rate"));return}if(Math.abs(e-m)<1e-4){this.toastService.showSuccess(this.translateService.instant("currency_settings.rate_already_updated"));return}if(n!=null&&n.sync_enabled){this.toastService.showError(this.translateService.instant("currency_settings.cannot_set_custom_rate_sync_enabled"));return}let f={[t.code]:e};this.updateCurrencySettings({custom_rates:f})}triggerSync(){var e,n,m;if(!((m=(n=(e=this.tenantCurrencies)==null?void 0:e.currency_settings)==null?void 0:n.some(f=>f.sync_enabled))!=null?m:!1)){this.toastService.showError("No currencies have sync enabled");return}this.isSyncing=!0,this.tenantCurrencyService.triggerCurrencySync().subscribe({next:()=>{this.toastService.showSuccess("Currency rates updated from latest sync data"),this.loadTenantCurrencies(),this.isSyncing=!1},error:f=>{this.toastService.showError((f==null?void 0:f.message)||"Failed to update currency rates"),this.isSyncing=!1}})}addCurrency(t){this.selectedCurrencies.add(t.code),this.availableCurrenciesForSelection=this.availableCurrenciesForSelection.filter(e=>e.code!==t.code),this.updateCurrencySettings({})}removeCurrency(t){if(t.base_currency){this.toastService.showError("Base currency cannot be removed");return}this.selectedCurrencies.delete(t.code),this.availableCurrenciesForSelection.push(t),this.updateCurrencySettings({})}formatLastSync(t){return t?new Date(t).toLocaleString():"Never"}canEditRate(t){var n;let e=this.getCurrencySettings(t.code);return!((n=e==null?void 0:e.sync_enabled)==null||n)}toggleCurrencySelector(){this.showCurrencySelector=!this.showCurrencySelector,this.showCurrencySelector&&(this.searchText="")}};s.\u0275fac=function(e){return new(e||s)(I(ie),I(te),I(K))},s.\u0275cmp=M({type:s,selectors:[["app-currency-settings"]],standalone:!0,features:[T],decls:68,vars:61,consts:[[1,"currency-settings-container"],[1,"header-section","bg-white","rounded-lg","shadow-sm","border","border-gray-200","p-6","mb-6"],[1,"flex","items-center","justify-between","mb-4"],[1,"text-2xl","font-bold","text-gray-900"],[1,"text-sm","text-gray-600"],[1,"flex","gap-3"],[1,"btn-secondary","flex","items-center","gap-2",3,"click"],[3,"icon"],[1,"btn-primary","flex","items-center","gap-2",3,"click","disabled"],["class","text-sm text-gray-600",4,"ngIf"],[1,"mt-4","p-4","bg-blue-50","border","border-blue-200","rounded-lg"],[1,"flex","items-start"],[1,"text-blue-600","mt-0.5","mr-2",3,"icon"],[1,"text-sm","text-blue-800"],[1,"font-medium"],[1,"search-section","mb-4"],[1,"relative"],[1,"absolute","left-3","top-1/2","transform","-translate-y-1/2","text-gray-400",3,"icon"],["type","text",1,"w-full","pl-10","pr-4","py-2","border","border-gray-300","rounded-lg","focus:ring-2","focus:ring-blue-500","focus:border-transparent",3,"ngModelChange","ngModel","placeholder"],[1,"currency-table-section","bg-white","rounded-lg","shadow-sm","border","border-gray-200","overflow-hidden"],[1,"px-6","py-4","border-b","border-gray-200"],[1,"text-lg","font-semibold","text-gray-900"],[1,"overflow-x-auto"],[1,"w-full"],[1,"bg-gray-50"],[1,"px-6","py-3","text-left","text-xs","font-medium","text-gray-500","uppercase","tracking-wider"],[1,"bg-white","divide-y","divide-gray-200"],["class","hover:bg-gray-50",4,"ngFor","ngForOf"],["class","flex items-center justify-center py-8",4,"ngIf"],["class","text-center py-8",4,"ngIf"],["class","fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",3,"click",4,"ngIf"],[1,"mr-1",3,"icon"],[1,"hover:bg-gray-50"],[1,"px-6","py-4","whitespace-nowrap"],[1,"flex","items-center"],[1,"flex-shrink-0","w-8","h-8","bg-blue-100","rounded-full","flex","items-center","justify-center"],[1,"text-xs","font-bold","text-blue-600"],[1,"ml-3"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],["class","ml-2 text-sm text-gray-400",4,"ngIf"],["class","ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full",4,"ngIf"],["type","number","step","0.0001",1,"w-24","px-2","py-1","text-sm","border","border-gray-300","rounded","focus:ring-2","focus:ring-blue-500","focus:border-transparent",3,"input","blur","value","disabled"],["class","ml-2 text-gray-400 text-xs",3,"icon","title",4,"ngIf"],["class","ml-2 text-orange-500 text-xs",3,"icon","title",4,"ngIf"],[4,"ngIf"],["class","flex items-center text-sm text-gray-500",4,"ngIf"],[1,"px-6","py-4","whitespace-nowrap","text-sm","font-medium"],[1,"flex","items-center","gap-2"],["class","text-red-600 hover:text-red-900",3,"click",4,"ngIf"],[1,"ml-2","text-sm","text-gray-400"],[1,"ml-2","px-2","py-1","text-xs","bg-yellow-100","text-yellow-800","rounded-full"],[1,"ml-2","text-gray-400","text-xs",3,"icon","title"],[1,"ml-2","text-orange-500","text-xs",3,"icon","title"],[1,"toggle-switch"],["type","checkbox",3,"change","checked"],[1,"toggle-slider"],[1,"flex","items-center","text-sm","text-gray-500"],[1,"text-green-500","mr-1",3,"icon"],[1,"text-red-600","hover:text-red-900",3,"click"],[1,"flex","items-center","justify-center","py-8"],[1,"animate-spin","rounded-full","h-8","w-8","border-b-2","border-blue-600"],[1,"ml-3","text-gray-600"],[1,"text-center","py-8"],[1,"text-gray-400","text-4xl","mb-4",3,"icon"],[1,"text-gray-600"],[1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50",3,"click"],[1,"bg-white","rounded-lg","shadow-xl","max-w-2xl","w-full","mx-4","max-h-[80vh]","overflow-hidden",3,"click"],[1,"flex","items-center","justify-between"],[1,"text-gray-400","hover:text-gray-600",3,"click"],[1,"px-6","py-4","max-h-96","overflow-y-auto"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-3"],["class","flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer",3,"click",4,"ngFor","ngForOf"],[1,"flex","items-center","justify-between","p-3","border","border-gray-200","rounded-lg","hover:bg-gray-50","cursor-pointer",3,"click"],[1,"text-xs","text-gray-500"],[1,"text-blue-600",3,"icon"],[1,"text-gray-400","text-3xl","mb-3",3,"icon"]],template:function(e,n){e&1&&(a(0,"div",0)(1,"div",1)(2,"div",2)(3,"div")(4,"h2",3),d(5),p(6,"translate"),o(),a(7,"p",4),d(8),p(9,"translate"),o()(),a(10,"div",5)(11,"button",6),b("click",function(){return n.toggleCurrencySelector()}),_(12,"fa-icon",7),d(13),p(14,"translate"),o(),a(15,"button",8),b("click",function(){return n.triggerSync()}),_(16,"fa-icon",7),d(17),p(18,"translate"),p(19,"translate"),o()()(),v(20,_e,4,6,"div",9),a(21,"div",10)(22,"div",11),_(23,"fa-icon",12),a(24,"div",13)(25,"p",14),d(26),p(27,"translate"),o(),a(28,"p"),d(29),p(30,"translate"),o()()()()(),a(31,"div",15)(32,"div",16),_(33,"fa-icon",17),a(34,"input",18),p(35,"translate"),N("ngModelChange",function(f){return z(n.searchText,f)||(n.searchText=f),f}),o()()(),a(36,"div",19)(37,"div",20)(38,"h3",21),d(39),p(40,"translate"),o(),a(41,"p",4),d(42),p(43,"translate"),o()(),a(44,"div",22)(45,"table",23)(46,"thead",24)(47,"tr")(48,"th",25),d(49),p(50,"translate"),o(),a(51,"th",25),d(52),p(53,"translate"),o(),a(54,"th",25),d(55),p(56,"translate"),o(),a(57,"th",25),d(58),p(59,"translate"),o(),a(60,"th",25),d(61),p(62,"translate"),o()()(),a(63,"tbody",26),v(64,ke,27,18,"tr",27),o()()(),v(65,Me,5,3,"div",28)(66,Te,5,5,"div",29),o(),v(67,Fe,22,16,"div",30),o()),e&2&&(i(5),y(u(6,27,"Currency Management")),i(3),y(u(9,29,"Manage currencies for your tenant and configure sync settings")),i(4),l("icon",x(57,re)),i(),h(" ",u(14,31,"Add Currency")," "),i(2),l("disabled",n.isSyncing),i(),A("animate-spin",n.isSyncing),l("icon",x(58,de)),i(),h(" ",n.isSyncing?u(18,33,"Updating..."):u(19,35,"Update Rates")," "),i(3),l("ngIf",n.tenantCurrencies==null?null:n.tenantCurrencies.last_currency_sync),i(3),l("icon",x(59,pe)),i(3),y(u(27,37,"Currency Sync Information")),i(3),y(u(30,39,'Base currency (USD) cannot be modified or removed. When sync is disabled, you can set custom rates. "Update Rates" copies latest rates from background sync job to avoid API rate limits.')),i(4),l("icon",x(60,U)),i(),E("placeholder",u(35,41,"Search currencies...")),$("ngModel",n.searchText),i(5),y(u(40,43,"Tenant Currencies")),i(3),y(u(43,45,"Manage currency rates and sync settings for your tenant")),i(7),h(" ",u(50,47,"Currency")," "),i(3),h(" ",u(53,49,"Exchange Rate")," "),i(3),h(" ",u(56,51,"Currency Sync")," "),i(3),h(" ",u(59,53,"Payment Sync")," "),i(3),h(" ",u(62,55,"Actions")," "),i(3),l("ngForOf",n.filteredAvailableCurrencies),i(),l("ngIf",n.isLoading),i(),l("ngIf",!n.isLoading&&n.filteredAvailableCurrencies.length===0),i(),l("ngIf",n.showCurrencySelector))},dependencies:[P,H,Y,L,Q,X,Z,F,O,V,B],styles:['.currency-settings-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:24px}.btn-primary[_ngcontent-%COMP%]{border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1));padding:.5rem 1rem;font-weight:500;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.btn-primary[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.btn-primary[_ngcontent-%COMP%]:disabled{cursor:not-allowed;opacity:.5}.btn-secondary[_ngcontent-%COMP%]{border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(245 245 245 / var(--tw-bg-opacity, 1));padding:.5rem 1rem;font-weight:500;--tw-text-opacity: 1;color:rgb(97 97 97 / var(--tw-text-opacity, 1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.btn-secondary[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(238 238 238 / var(--tw-bg-opacity, 1))}.btn-secondary[_ngcontent-%COMP%]:disabled{cursor:not-allowed;opacity:.5}.toggle-switch[_ngcontent-%COMP%]{position:relative;display:inline-block;width:44px;height:24px}.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{opacity:0;width:0;height:0}.toggle-slider[_ngcontent-%COMP%]{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#cbd5e1;transition:.3s;border-radius:24px}.toggle-slider[_ngcontent-%COMP%]:before{position:absolute;content:"";height:18px;width:18px;left:3px;bottom:3px;background-color:#fff;transition:.3s;border-radius:50%}input[_ngcontent-%COMP%]:checked + .toggle-slider[_ngcontent-%COMP%]{background-color:#3b82f6}input[_ngcontent-%COMP%]:checked + .toggle-slider[_ngcontent-%COMP%]:before{transform:translate(20px)}input[_ngcontent-%COMP%]:disabled + .toggle-slider[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.animate-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.currency-settings-container[_ngcontent-%COMP%]{padding:16px}.overflow-x-auto[_ngcontent-%COMP%]{-webkit-overflow-scrolling:touch}table[_ngcontent-%COMP%]{min-width:600px}}input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button, input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}input[type=number][_ngcontent-%COMP%]{-moz-appearance:textfield}.loading-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;bottom:0;background:#fffc;display:flex;align-items:center;justify-content:center;z-index:10}.hover-effect[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.hover-effect[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}']});let r=s;return r})();var Be=()=>["fas","arrow-left"],Qe=(()=>{let s=class s{constructor(t){this.router=t}ngOnInit(){}goBack(){this.router.navigate(["/panel/settings/general"])}};s.\u0275fac=function(e){return new(e||s)(I(J))},s.\u0275cmp=M({type:s,selectors:[["app-currency-settings-page"]],standalone:!0,features:[T],decls:10,vars:5,consts:[[1,"page-container"],[1,"content-container"],[1,"page-header"],[1,"flex","items-center","gap-3"],[1,"back-button",3,"click"],[3,"icon"],[1,"page-title"]],template:function(e,n){e&1&&(a(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"button",4),b("click",function(){return n.goBack()}),_(5,"fa-icon",5),o(),a(6,"h1",6),d(7),p(8,"translate"),o()()(),_(9,"app-currency-settings"),o()()),e&2&&(i(5),l("icon",x(4,Be)),i(2),y(u(8,2,"general_settings.currency")))},dependencies:[P,L,F,O,V,B,oe],styles:[".page-container[_ngcontent-%COMP%]{margin-left:auto;margin-right:auto;width:100%;max-width:80rem;padding-top:.5rem;padding-bottom:.5rem}.content-container[_ngcontent-%COMP%]{border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:0!important;--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.page-header[_ngcontent-%COMP%]{margin-bottom:1rem;display:flex;align-items:center;justify-content:space-between;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1));padding-bottom:.75rem}.page-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:var(--gray-800)}.back-button[_ngcontent-%COMP%]{border-radius:9999px;padding:.5rem;--tw-text-opacity: 1;color:rgb(158 158 158 / var(--tw-text-opacity, 1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.back-button[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(245 245 245 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(97 97 97 / var(--tw-text-opacity, 1))}app-currency-settings[_ngcontent-%COMP%]{display:block;width:100%}"]});let r=s;return r})();export{Qe as CurrencySettingsPageComponent};
