package tndung.vnfb.smm.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.response.TenantInfoDto;
import tndung.vnfb.smm.dto.response.TenantInfoDto.TenantInfoDtoBuilder;
import tndung.vnfb.smm.entity.Tenant;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class TenantMapperImpl implements TenantMapper {

    @Override
    public TenantInfoDto toRes(Tenant entity) {
        if ( entity == null ) {
            return null;
        }

        TenantInfoDtoBuilder tenantInfoDto = TenantInfoDto.builder();

        tenantInfoDto.id( entity.getId() );
        tenantInfoDto.domain( entity.getDomain() );
        if ( entity.getStatus() != null ) {
            tenantInfoDto.status( entity.getStatus().name() );
        }
        tenantInfoDto.subscriptionStartDate( entity.getSubscriptionStartDate() );
        tenantInfoDto.subscriptionEndDate( entity.getSubscriptionEndDate() );
        tenantInfoDto.daysUntilExpiration( entity.getDaysUntilExpiration() );
        tenantInfoDto.autoRenewal( entity.getAutoRenewal() );
        tenantInfoDto.contactEmail( entity.getContactEmail() );

        return tenantInfoDto.build();
    }
}
