package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.dto.request.ApiProviderReq;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.entity.ApiProvider;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ApiProviderMapperImpl implements ApiProviderMapper {

    @Override
    public ApiProvider toEntity(ApiProviderReq req) {
        if ( req == null ) {
            return null;
        }

        ApiProvider apiProvider = new ApiProvider();

        apiProvider.setUrl( req.getUrl() );
        apiProvider.setSecretKey( req.getSecretKey() );

        return apiProvider;
    }

    @Override
    public ApiProviderRes toRes(ApiProvider entity) {
        if ( entity == null ) {
            return null;
        }

        ApiProviderRes apiProviderRes = new ApiProviderRes();

        apiProviderRes.setId( entity.getId() );
        apiProviderRes.setName( entity.getName() );
        apiProviderRes.setUrl( entity.getUrl() );
        apiProviderRes.setBalanceAlert( entity.getBalanceAlert() );
        apiProviderRes.setBalance( entity.getBalance() );
        if ( entity.getCurrency() != null ) {
            apiProviderRes.setCurrency( Enum.valueOf( Currency.class, entity.getCurrency() ) );
        }
        apiProviderRes.setStatus( entity.getStatus() );

        return apiProviderRes;
    }

    @Override
    public List<ApiProviderRes> toRes(List<ApiProvider> entity) {
        if ( entity == null ) {
            return null;
        }

        List<ApiProviderRes> list = new ArrayList<ApiProviderRes>( entity.size() );
        for ( ApiProvider apiProvider : entity ) {
            list.add( toRes( apiProvider ) );
        }

        return list;
    }
}
