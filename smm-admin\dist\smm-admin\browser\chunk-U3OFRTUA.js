import{e as ne,f as ie}from"./chunk-C5WQGZWY.js";import"./chunk-HJYZAHTZ.js";import{$a as g,Ab as k,Bb as b,Bc as T,Cb as _,Da as a,Db as Y,Ea as x,Eb as Q,Fc as Z,H as A,Jc as R,Mc as z,Pc as F,Qc as K,Sa as p,Sc as $,T as J,Ua as c,Uc as H,Vc as q,Y as U,Za as i,_a as r,ac as E,ba as O,bc as N,cb as w,cc as D,db as v,dd as ee,eb as m,hd as G,jd as L,kc as j,kd as te,la as h,ma as y,md as W,ob as l,pb as I,qb as f,tc as X,va as B,wb as C,xb as P,yb as S}from"./chunk-MCI2ITGN.js";import"./chunk-WVXK5ZBG.js";var V=(()=>{let d=class d{constructor(n){this.http=n,this.API_URL=`${te.apiUrl}/admin/managers`}getManagers(){return this.http.get(this.API_URL)}addManager(n){return this.http.post(this.API_URL,n)}updateManager(n,s){return this.http.put(`${this.API_URL}/${n}`,s)}deleteManager(n){return this.http.delete(`${this.API_URL}/${n}`)}toggleManagerStatus(n){return this.http.patch(`${this.API_URL}/${n}/toggle-status`,{})}};d.\u0275fac=function(s){return new(s||d)(U(X))},d.\u0275prov=J({token:d,factory:d.\u0275fac,providedIn:"root"});let e=d;return e})();var me=()=>["fas","times"],ue=(e,d,t)=>({"border-[var(--primary)] bg-[var(--primary-light)]":e,"border-gray-200 bg-white hover:border-gray-300":d,"border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed":t}),fe=(e,d)=>({"border-[var(--primary)] bg-[var(--primary)]":e,"border-gray-300":d}),be=e=>({"text-gray-500":e}),ve=e=>({"text-gray-400":e}),he=()=>["fas","check-circle"],ye=()=>["fas","spinner"];function xe(e,d){e&1&&g(0,"div",33)}function we(e,d){e&1&&g(0,"fa-icon",34),e&2&&c("icon",b(1,he))}function _e(e,d){e&1&&(i(0,"div",35),g(1,"img",36),r())}function Me(e,d){e&1&&(i(0,"div",35),g(1,"img",37),r())}function Ie(e,d){e&1&&(i(0,"div",35),g(1,"img",38),r())}function Ce(e,d){if(e&1){let t=w();i(0,"div",23),v("click",function(){let s=h(t).$implicit,o=m();return y(o.selectRole(s.id))}),i(1,"div",24)(2,"div",25),p(3,xe,1,0,"div",26),r(),i(4,"div",27)(5,"div",24)(6,"span",28),l(7),r(),p(8,we,1,2,"fa-icon",29),r(),i(9,"p",30),l(10),r()()(),i(11,"div",31),p(12,_e,2,0,"div",32)(13,Me,2,0,"div",32)(14,Ie,2,0,"div",32),r()()}if(e&2){let t=d.$implicit,n=m();c("ngClass",Q(11,ue,n.selectedRole===t.id&&t.enabled,n.selectedRole!==t.id&&t.enabled,!t.enabled)),a(2),c("ngClass",Y(15,fe,n.selectedRole===t.id&&t.enabled,n.selectedRole!==t.id||!t.enabled)),a(),c("ngIf",n.selectedRole===t.id&&t.enabled),a(3),c("ngClass",_(18,be,!t.enabled)),a(),f(" ",t.name," "),a(),c("ngIf",t.id==="Admin"),a(),c("ngClass",_(20,ve,!t.enabled)),a(),f(" ",t.description," "),a(2),c("ngIf",t.id==="Admin"),a(),c("ngIf",t.id==="Moderator"),a(),c("ngIf",t.id==="Agent")}}function Pe(e,d){if(e&1){let t=w();i(0,"div",24)(1,"input",39),v("change",function(){let s=h(t).$implicit,o=m();return y(o.togglePanel(s.id))}),r(),i(2,"label",40),l(3),r()()}if(e&2){let t=d.$implicit;a(),c("id",t.id)("checked",t.checked),a(),c("for",t.id),a(),f(" ",t.name," ")}}function Se(e,d){if(e&1&&(i(0,"div",41)(1,"p",42),l(2),r()()),e&2){let t=m();a(2),I(t.errorMessage)}}function Ae(e,d){e&1&&(i(0,"span"),l(1,"Add"),r())}function Oe(e,d){e&1&&(i(0,"span",43),g(1,"fa-icon",44),l(2," Adding... "),r()),e&2&&(a(),c("icon",b(2,ye))("spin",!0))}var oe=(()=>{let d=class d{constructor(n,s,o){this.managerService=n,this.toastService=s,this.tenantService=o,this.close=new B,this.managerAdded=new B,this.login="",this.password="",this.selectedRole="Admin",this.roles=[{id:"Admin",name:"Admin",description:"Same rights as of main account",enabled:!0},{id:"Moderator",name:"Moderator",description:"Cant see balance, providers and panel settings. Can work with orders, edit services and answer in support.",enabled:!1},{id:"Agent",name:"Agent",description:"Can only answer in support, see orders and services but cant edit. Doesn't see providers and panel settings.",enabled:!1}],this.panels=[],this.loading=!1,this.errorMessage=""}ngOnInit(){document.body.style.overflow="hidden",this.loadAccessibleTenants()}loadAccessibleTenants(){this.tenantService.getAccessibleTenants().subscribe({next:n=>{this.panels=n.map(s=>({id:s.id,name:s.domain,checked:!1}))},error:n=>{console.error("Error loading accessible tenants:",n),this.toastService.showError("Failed to load accessible panels")}})}selectRole(n){let s=this.roles.find(o=>o.id===n);s&&s.enabled&&(this.selectedRole=n)}togglePanel(n){let s=this.panels.find(o=>o.id===n);s&&(s.checked=!s.checked)}addManager(){if(!this.validateForm())return;this.loading=!0,this.errorMessage="";let n=this.panels.filter(o=>o.checked).map(o=>o.id),s={login:this.login,password:this.password,role:this.selectedRole,accessiblePanels:n};this.managerService.addManager(s).pipe(A(()=>this.loading=!1)).subscribe({next:o=>{o.success&&o.data?(this.toastService.showSuccess("Manager added successfully"),this.managerAdded.emit(o.data),this.closeModal()):this.errorMessage=o.message||"Failed to add manager"},error:o=>{console.error("Error adding manager:",o),this.errorMessage=(o==null?void 0:o.message)||"Failed to add manager. Please try again.",this.toastService.showError(this.errorMessage)}})}validateForm(){return this.login.trim()?this.password.trim()?this.password.length<6?(this.errorMessage="Password must be at least 6 characters",!1):this.panels.filter(s=>s.checked).length===0?(this.errorMessage="Please select at least one accessible panel",!1):!0:(this.errorMessage="Password is required",!1):(this.errorMessage="Login is required",!1)}closeModal(){document.body.style.overflow="auto",this.close.emit()}};d.\u0275fac=function(s){return new(s||d)(x(V),x(W),x(ne))},d.\u0275cmp=O({type:d,selectors:[["app-add-manager"]],outputs:{close:"close",managerAdded:"managerAdded"},standalone:!0,features:[k],decls:31,vars:10,consts:[[1,"overlay-black"],[1,"modal-container","bg-white","rounded-2xl","shadow-lg","w-[500px]","max-w-full"],[1,"flex","justify-between","items-center","p-6","border-b","border-gray-200"],[1,"text-xl","font-semibold","text-gray-800"],[1,"text-gray-500","hover:text-gray-700",3,"click"],[1,"text-lg",3,"icon"],[1,"p-6"],[3,"ngSubmit"],[1,"mb-4"],["for","login",1,"block","text-sm","font-medium","text-gray-700","mb-1"],["type","text","id","login","name","login","placeholder","agent9165","required","",1,"w-full","px-4","py-3","bg-[var(--background)]","border","border-gray-200","rounded-lg","text-gray-800","focus:outline-none","focus:ring-2","focus:ring-[var(--primary)]","focus:border-[var(--primary)]",3,"ngModelChange","ngModel"],[1,"mb-6"],["for","password",1,"block","text-sm","font-medium","text-gray-700","mb-1"],["type","password","id","password","name","password","placeholder","\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022","required","",1,"w-full","px-4","py-3","bg-[var(--background)]","border","border-gray-200","rounded-lg","text-gray-800","focus:outline-none","focus:ring-2","focus:ring-[var(--primary)]","focus:border-[var(--primary)]",3,"ngModelChange","ngModel"],[1,"block","text-sm","font-medium","text-gray-700","mb-3"],[1,"space-y-3"],["class","flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"space-y-2"],["class","flex items-center",4,"ngFor","ngForOf"],["class","mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",4,"ngIf"],["type","submit",1,"w-full","bg-[var(--primary)]","text-white","py-3","px-4","rounded-lg","font-medium","hover:bg-[var(--primary-hover)]","disabled:opacity-50","disabled:cursor-not-allowed","transition-colors",3,"disabled"],[4,"ngIf"],["class","flex items-center justify-center",4,"ngIf"],[1,"flex","items-center","justify-between","p-4","border","rounded-lg","cursor-pointer","transition-colors",3,"click","ngClass"],[1,"flex","items-center"],[1,"w-6","h-6","rounded-full","border-2","flex","items-center","justify-center","mr-3",3,"ngClass"],["class","w-2 h-2 bg-white rounded-full",4,"ngIf"],[1,"flex-1"],[1,"font-medium","text-gray-900",3,"ngClass"],["class","ml-2 text-green-500",3,"icon",4,"ngIf"],[1,"text-sm","text-gray-600","mt-1",3,"ngClass"],[1,"ml-4"],["class","w-12 h-12 rounded-full flex items-center justify-center",4,"ngIf"],[1,"w-2","h-2","bg-white","rounded-full"],[1,"ml-2","text-green-500",3,"icon"],[1,"w-12","h-12","rounded-full","flex","items-center","justify-center"],["src","data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSIxMiIgeT0iMTIiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMTIgMUwyMSA1VjExQzIxIDE2LjU1IDE3LjE2IDIxLjc0IDEyIDIzQzYuODQgMjEuNzQgMyAxNi41NSAzIDExVjVMMTIgMVoiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik05IDEyTDExIDE0TDE1IDEwIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+","alt","Admin",1,"w-12","h-12"],["src","data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNGRjk1MDAiLz4KPHN2ZyB4PSIxMiIgeT0iMTIiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMjAgNkw5IDEyTDQgOSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNiIgY3k9IjMiIHI9IjIiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS41Ii8+CjxwYXRoIGQ9Ik0yIDEwVjlBMyAzIDAgMCAxIDUgNkgxMUEzIDMgMCAwIDEgMTQgOVYxMCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+Cjwvc3ZnPgo=","alt","Moderator",1,"w-12","h-12"],["src","data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMxNkE2NEYiLz4KPHN2ZyB4PSIxMiIgeT0iMTIiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjMiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNiAyMVY5QTMgMyAwIDAgMSA5IDZIMTVBMyAzIDAgMCAxIDE4IDlWMjEiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik04IDE0SDE2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8cGF0aCBkPSJNOCAxN0gxNiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+Cjwvc3ZnPgo=","alt","Agent",1,"w-12","h-12"],["type","checkbox",1,"w-4","h-4","text-[var(--primary)]","bg-white","border-gray-300","rounded","focus:ring-[var(--primary)]",3,"change","id","checked"],[1,"ml-2","text-sm","text-gray-700","cursor-pointer",3,"for"],[1,"mb-4","p-3","bg-red-50","border","border-red-200","rounded-lg"],[1,"text-sm","text-red-600"],[1,"flex","items-center","justify-center"],[1,"mr-2",3,"icon","spin"]],template:function(s,o){s&1&&(i(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2",3),l(4,"Add manager"),r(),i(5,"button",4),v("click",function(){return o.closeModal()}),g(6,"fa-icon",5),r()(),i(7,"div",6)(8,"form",7),v("ngSubmit",function(){return o.addManager()}),i(9,"div",8)(10,"label",9),l(11,"Login"),r(),i(12,"input",10),S("ngModelChange",function(u){return P(o.login,u)||(o.login=u),u}),r()(),i(13,"div",11)(14,"label",12),l(15,"Password"),r(),i(16,"input",13),S("ngModelChange",function(u){return P(o.password,u)||(o.password=u),u}),r()(),i(17,"div",11)(18,"label",14),l(19,"Role"),r(),i(20,"div",15),p(21,Ce,15,22,"div",16),r()(),i(22,"div",11)(23,"label",14),l(24,"Select accessible panels"),r(),i(25,"div",17),p(26,Pe,4,4,"div",18),r()(),p(27,Se,3,1,"div",19),i(28,"button",20),p(29,Ae,2,0,"span",21)(30,Oe,3,3,"span",22),r()()()()()),s&2&&(a(6),c("icon",b(9,me)),a(6),C("ngModel",o.login),a(4),C("ngModel",o.password),a(5),c("ngForOf",o.roles),a(5),c("ngForOf",o.panels),a(),c("ngIf",o.errorMessage),a(),c("disabled",o.loading),a(),c("ngIf",!o.loading),a(),c("ngIf",o.loading))},dependencies:[j,E,N,D,G,q,z,F,K,ee,H,$,Z,T,R],styles:['.overlay-black[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#000000b3;z-index:1000;padding:20px;overflow-y:auto;display:flex;align-items:flex-start;justify-content:center}.modal-container[_ngcontent-%COMP%]{position:relative;animation:_ngcontent-%COMP%_modalSlideIn .3s ease-out;margin:20px 0;box-shadow:0 4px 6px #0000001a}@keyframes _ngcontent-%COMP%_modalSlideIn{0%{opacity:0;transform:translateY(-20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}.role-option[_ngcontent-%COMP%]{transition:all .2s ease}.role-option[_ngcontent-%COMP%]:hover:not(.disabled){transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.role-radio[_ngcontent-%COMP%]{-webkit-appearance:none;appearance:none;width:20px;height:20px;border:2px solid #d1d5db;border-radius:50%;position:relative;cursor:pointer}.role-radio[_ngcontent-%COMP%]:checked{border-color:var(--primary);background-color:var(--primary)}.role-radio[_ngcontent-%COMP%]:checked:after{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:8px;height:8px;background-color:#fff;border-radius:50%}input[type=checkbox][_ngcontent-%COMP%]{accent-color:var(--primary)}input[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #3b82f61a}button[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 12px #3b82f633}.disabled[_ngcontent-%COMP%]{pointer-events:none;opacity:.6}@media (max-width: 640px){.modal-container[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem;width:100%}}']});let e=d;return e})();var de=()=>["fas","plus"],ke=()=>["fas","search"],se=e=>["fas",e],Ee=()=>["fas","user-cog"],Ne=()=>["fas","users-cog"];function De(e,d){e&1&&g(0,"app-loading")}function je(e,d){if(e&1&&(i(0,"span",33),l(1),r()),e&2){let t=d.$implicit;a(),f(" ",t," ")}}function Te(e,d){if(e&1&&(i(0,"tr",22)(1,"td",23)(2,"div",24)(3,"div",25),g(4,"fa-icon",4),r(),i(5,"div",26)(6,"div",27),l(7),r(),i(8,"div",28),l(9),r()()()(),i(10,"td",23)(11,"span",29),l(12),r()(),i(13,"td",30)(14,"div",31),p(15,je,2,1,"span",32),r()()()),e&2){let t=d.$implicit,n=m(2);a(3),c("ngClass",n.getRoleColor(t.role)),a(),c("icon",_(7,se,n.getRoleIcon(t.role))),a(3),I(t.login),a(2),f("ID: ",t.id,""),a(2),c("ngClass",n.getRoleColor(t.role)),a(),f(" ",t.role," "),a(3),c("ngForOf",t.accessiblePanels)}}function Ze(e,d){e&1&&(i(0,"div",34),g(1,"fa-icon",35),i(2,"h3",36),l(3,"No managers found"),r(),i(4,"p",37),l(5,"Get started by adding your first manager."),r()()),e&2&&(a(),c("icon",b(1,Ee)))}function Re(e,d){if(e&1&&(i(0,"div",15)(1,"table",16)(2,"thead",17)(3,"tr")(4,"th",18),l(5,"Manager"),r(),i(6,"th",18),l(7,"Role"),r(),i(8,"th",18),l(9,"Accessible Panels"),r()()(),i(10,"tbody",19),p(11,Te,16,9,"tr",20),r()(),p(12,Ze,6,2,"div",21),r()),e&2){let t=m();a(11),c("ngForOf",t.filteredManagers),a(),c("ngIf",t.filteredManagers.length===0)}}function ze(e,d){if(e&1&&(i(0,"span",33),l(1),r()),e&2){let t=d.$implicit;a(),f(" ",t," ")}}function Fe(e,d){if(e&1&&(i(0,"div",40)(1,"div",41)(2,"div",24)(3,"div",25),g(4,"fa-icon",4),r(),i(5,"div",42)(6,"div",43),l(7),r(),i(8,"div",28),l(9),r()()()(),i(10,"div",44)(11,"div",45)(12,"span",28),l(13,"Role:"),r(),i(14,"span",29),l(15),r()(),i(16,"div")(17,"span",46),l(18,"Accessible Panels:"),r(),i(19,"div",31),p(20,ze,2,1,"span",32),r()()()()),e&2){let t=d.$implicit,n=m(2);a(3),c("ngClass",n.getRoleColor(t.role)),a(),c("icon",_(7,se,n.getRoleIcon(t.role))),a(3),I(t.login),a(2),f("ID: ",t.id,""),a(5),c("ngClass",n.getRoleColor(t.role)),a(),f(" ",t.role," "),a(5),c("ngForOf",t.accessiblePanels)}}function He(e,d){if(e&1){let t=w();i(0,"div",34),g(1,"fa-icon",35),i(2,"h3",36),l(3,"No managers found"),r(),i(4,"p",37),l(5,"Get started by adding your first manager."),r(),i(6,"button",3),v("click",function(){h(t);let s=m(2);return y(s.openAddManagerPopup())}),g(7,"fa-icon",4),l(8," Add Manager "),r()()}e&2&&(a(),c("icon",b(2,Ne)),a(6),c("icon",b(3,de)))}function Ge(e,d){if(e&1&&(i(0,"div",38),p(1,Fe,21,9,"div",39)(2,He,9,4,"div",21),r()),e&2){let t=m();a(),c("ngForOf",t.filteredManagers),a(),c("ngIf",t.filteredManagers.length===0)}}function Le(e,d){if(e&1){let t=w();i(0,"app-add-manager",47),v("close",function(){h(t);let s=m();return y(s.closeAddManagerPopup())})("managerAdded",function(s){h(t);let o=m();return y(o.onManagerAdded(s))}),r()}}var ct=(()=>{let d=class d{constructor(n,s){this.managerService=n,this.toastService=s,this.managers=[],this.loading=!1,this.showAddManagerPopup=!1,this.searchTerm="",this.subscriptions=[]}ngOnInit(){this.loadManagers()}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}loadManagers(){this.loading=!0;let n=this.managerService.getManagers().pipe(A(()=>this.loading=!1)).subscribe({next:s=>{s.success&&s.data?this.managers=s.data:this.toastService.showToast(s.message||"Failed to load managers",L.ERROR)},error:s=>{var o;console.error("Error loading managers:",s),this.toastService.showToast(((o=s.error)==null?void 0:o.message)||"Failed to load managers",L.ERROR)}});this.subscriptions.push(n)}get filteredManagers(){return this.managers.filter(n=>!this.searchTerm||n.login.toLowerCase().includes(this.searchTerm.toLowerCase())||n.role.toLowerCase().includes(this.searchTerm.toLowerCase()))}openAddManagerPopup(){this.showAddManagerPopup=!0}closeAddManagerPopup(){this.showAddManagerPopup=!1}onManagerAdded(n){this.managers.unshift(n),this.toastService.showToast("Manager added successfully",L.SUCCESS)}getRoleIcon(n){switch(n.toLowerCase()){case"admin":return"shield";case"moderator":return"user-pen";case"agent":return"headset";default:return"user"}}getRoleColor(n){switch(n.toLowerCase()){case"admin":return"text-blue-600 bg-blue-100";case"moderator":return"text-orange-600 bg-orange-100";case"agent":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}}};d.\u0275fac=function(s){return new(s||d)(x(V),x(W))},d.\u0275cmp=O({type:d,selectors:[["app-managers"]],standalone:!0,features:[k],decls:18,vars:9,consts:[[1,"admin-container"],[1,"admin-header"],[1,"admin-title"],[1,"btn","btn-primary",3,"click"],[3,"icon"],[1,"search-filter-container"],[1,"flex","flex-col","md:flex-row","gap-4","mb-6"],[1,"flex-1"],[1,"search-input-wrapper"],["type","text","placeholder","Search managers...",1,"search-input",3,"ngModelChange","ngModel"],[1,"search-button"],[4,"ngIf"],["class","hidden md:block bg-white rounded-lg shadow-sm overflow-hidden",4,"ngIf"],["class","md:hidden space-y-4",4,"ngIf"],[3,"close","managerAdded",4,"ngIf"],[1,"hidden","md:block","bg-white","rounded-lg","shadow-sm","overflow-hidden"],[1,"w-full"],[1,"bg-gray-50"],[1,"px-6","py-3","text-left","text-xs","font-medium","text-gray-500","uppercase","tracking-wider"],[1,"bg-white","divide-y","divide-gray-200"],["class","hover:bg-gray-50",4,"ngFor","ngForOf"],["class","text-center py-12",4,"ngIf"],[1,"hover:bg-gray-50"],[1,"px-6","py-4","whitespace-nowrap"],[1,"flex","items-center"],[1,"w-10","h-10","rounded-full","flex","items-center","justify-center",3,"ngClass"],[1,"ml-4"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],[1,"inline-flex","items-center","px-2.5","py-0.5","rounded-full","text-xs","font-medium",3,"ngClass"],[1,"px-6","py-4"],[1,"flex","flex-wrap","gap-1"],["class","inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700",4,"ngFor","ngForOf"],[1,"inline-flex","items-center","px-2","py-1","rounded","text-xs","bg-gray-100","text-gray-700"],[1,"text-center","py-12"],[1,"text-gray-400","text-4xl","mb-4",3,"icon"],[1,"text-lg","font-medium","text-gray-900","mb-2"],[1,"text-gray-500","mb-4"],[1,"md:hidden","space-y-4"],["class","bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",4,"ngFor","ngForOf"],[1,"bg-white","rounded-lg","shadow-sm","border","border-gray-200","overflow-hidden"],[1,"flex","justify-between","items-center","p-4","border-b","border-gray-100"],[1,"ml-3"],[1,"font-medium","text-gray-900"],[1,"p-4","space-y-3"],[1,"flex","justify-between","items-center"],[1,"text-sm","text-gray-500","block","mb-2"],[3,"close","managerAdded"]],template:function(s,o){s&1&&(i(0,"div",0)(1,"div",1)(2,"h1",2),l(3,"Manager Management"),r(),i(4,"button",3),v("click",function(){return o.openAddManagerPopup()}),g(5,"fa-icon",4),l(6," Add Manager "),r()(),i(7,"div",5)(8,"div",6)(9,"div",7)(10,"div",8)(11,"input",9),S("ngModelChange",function(u){return P(o.searchTerm,u)||(o.searchTerm=u),u}),r(),i(12,"button",10),g(13,"fa-icon",4),r()()()()(),p(14,De,1,0,"app-loading",11)(15,Re,13,2,"div",12)(16,Ge,3,2,"div",13),r(),p(17,Le,1,0,"app-add-manager",14)),s&2&&(a(5),c("icon",b(7,de)),a(6),C("ngModel",o.searchTerm),a(2),c("icon",b(8,ke)),a(),c("ngIf",o.loading),a(),c("ngIf",!o.loading),a(),c("ngIf",!o.loading),a(),c("ngIf",o.showAddManagerPopup))},dependencies:[j,E,N,D,G,z,F,H,Z,T,R,oe,ie],styles:[".admin-container[_ngcontent-%COMP%]{width:100%;border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:.5rem}@media (min-width: 768px){.admin-container[_ngcontent-%COMP%]{padding:1.5rem}}.admin-header[_ngcontent-%COMP%]{margin-bottom:1.5rem;display:flex;align-items:center;justify-content:space-between}.admin-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700;text-transform:uppercase;--tw-text-opacity: 1;color:rgb(66 66 66 / var(--tw-text-opacity, 1))}.search-filter-container[_ngcontent-%COMP%]{margin-bottom:1.5rem}.search-input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;width:100%;align-items:center}.search-input[_ngcontent-%COMP%]{height:52px;width:100%;border-radius:.5rem;border-style:none;--tw-bg-opacity: 1;background-color:rgb(245 247 252 / var(--tw-bg-opacity, 1));padding:.5rem 1rem}.search-input[_ngcontent-%COMP%]:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-color: var(--primary)}.search-button[_ngcontent-%COMP%]{position:absolute;right:.5rem;border-radius:.375rem;background-color:var(--primary);padding:.5rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.search-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-hover)}table[_ngcontent-%COMP%]{width:100%;text-align:left;font-size:.875rem}thead[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1))}th[_ngcontent-%COMP%]{padding:.75rem 1.5rem;text-align:left;font-size:.75rem;font-weight:500;text-transform:uppercase;letter-spacing:.05em;--tw-text-opacity: 1;color:rgb(158 158 158 / var(--tw-text-opacity, 1))}tbody[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-divide-y-reverse: 0;border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(1px * var(--tw-divide-y-reverse));--tw-divide-opacity: 1;border-color:rgb(238 238 238 / var(--tw-divide-opacity, 1))}tbody[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}tr[_ngcontent-%COMP%]{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}tr[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1))}td[_ngcontent-%COMP%]{white-space:nowrap;padding:1rem 1.5rem;font-size:.875rem}.status-badge[_ngcontent-%COMP%]{border-radius:9999px;padding:.25rem .5rem;font-size:.75rem;font-weight:500}.role-badge[_ngcontent-%COMP%]{border-radius:9999px;padding:.125rem .625rem;font-size:.75rem;font-weight:500}.panel-tag[_ngcontent-%COMP%]{display:inline-flex;align-items:center;border-radius:.25rem;--tw-bg-opacity: 1;background-color:rgb(245 245 245 / var(--tw-bg-opacity, 1));padding:.25rem .5rem;font-size:.75rem;--tw-text-opacity: 1;color:rgb(97 97 97 / var(--tw-text-opacity, 1))}.manager-card[_ngcontent-%COMP%]{overflow:hidden;border-radius:.5rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow);transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.manager-card[_ngcontent-%COMP%]:hover{border-color:var(--primary)}.card-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(245 245 245 / var(--tw-border-opacity, 1));padding:1rem}.card-content[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.75rem * var(--tw-space-y-reverse))}.card-content[_ngcontent-%COMP%]{padding:1rem}.btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;border-radius:.5rem;padding:.5rem 1rem;font-weight:500;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.btn-primary[_ngcontent-%COMP%]{background-color:var(--primary);--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.btn-primary[_ngcontent-%COMP%]:hover{--tw-translate-y: -.125rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));background-color:var(--primary-hover);--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -2px rgba(0, 0, 0, .05);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.btn-secondary[_ngcontent-%COMP%]{border-width:1px;border-color:var(--primary);--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));color:var(--primary)}.btn-secondary[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(250 250 250 / var(--tw-bg-opacity, 1))}.empty-state[_ngcontent-%COMP%]{padding-top:3rem;padding-bottom:3rem;text-align:center}.empty-state-icon[_ngcontent-%COMP%]{margin-bottom:1rem;font-size:2.25rem;--tw-text-opacity: 1;color:rgb(189 189 189 / var(--tw-text-opacity, 1))}.empty-state-title[_ngcontent-%COMP%]{margin-bottom:.5rem;font-size:1.125rem;font-weight:500;--tw-text-opacity: 1;color:rgb(33 33 33 / var(--tw-text-opacity, 1))}.empty-state-description[_ngcontent-%COMP%]{margin-bottom:1rem;--tw-text-opacity: 1;color:rgb(158 158 158 / var(--tw-text-opacity, 1))}.loading-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding-top:3rem;padding-bottom:3rem}@media (max-width: 768px){.admin-container[_ngcontent-%COMP%]{padding:1rem}.admin-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:1rem}.search-filter-container[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.search-filter-container[_ngcontent-%COMP%]{max-height:calc(100vh - 200px);overflow-y:auto}.search-filter-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.search-filter-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:2px}.search-filter-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--primary);border-radius:2px}}.manager-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .3s ease-out}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.manager-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000001a}.search-input[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #3b82f61a}.form-select[_ngcontent-%COMP%]:focus{border-color:var(--primary);outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-color: var(--primary)}@media (max-width: 768px){.space-y-4[_ngcontent-%COMP%]{max-height:calc(100vh - 200px);overflow-y:auto}.space-y-4[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.space-y-4[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:2px}.space-y-4[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--primary);border-radius:2px}}"]});let e=d;return e})();export{ct as ManagersComponent};
