import{a as Z,b as m1,c as fn,g as I,h as K0}from"./chunk-WVXK5ZBG.js";function N(t){return typeof t=="function"}function _8(t){return N(t==null?void 0:t.lift)}function F(t){return e=>{if(_8(e))return e.lift(function(n){try{return t(n,this)}catch(c){this.error(c)}});throw new TypeError("Unable to lift unknown Observable type")}}function X0(t){return F((e,n)=>{try{e.subscribe(n)}finally{n.add(t)}})}function dn(t,e,n,c){function s(o){return o instanceof n?o:new n(function(i){i(o)})}return new(n||(n=Promise))(function(o,i){function r(f){try{l(c.next(f))}catch(u){i(u)}}function a(f){try{l(c.throw(f))}catch(u){i(u)}}function l(f){f.done?o(f.value):s(f.value).then(r,a)}l((c=c.apply(t,e||[])).next())})}function un(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],c=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&c>=t.length&&(t=void 0),{value:t&&t[c++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function m3(t){return this instanceof m3?(this.v=t,this):new m3(t)}function hn(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var c=n.apply(t,e||[]),s,o=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",i),s[Symbol.asyncIterator]=function(){return this},s;function i(h){return function(m){return Promise.resolve(m).then(h,u)}}function r(h,m){c[h]&&(s[h]=function(C){return new Promise(function(M,g){o.push([h,C,M,g])>1||a(h,C)})},m&&(s[h]=m(s[h])))}function a(h,m){try{l(c[h](m))}catch(C){d(o[0][3],C)}}function l(h){h.value instanceof m3?Promise.resolve(h.value.v).then(f,u):d(o[0][2],h)}function f(h){a("next",h)}function u(h){a("throw",h)}function d(h,m){h(m),o.shift(),o.length&&a(o[0][0],o[0][1])}}function mn(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof un=="function"?un(t):t[Symbol.iterator](),n={},c("next"),c("throw"),c("return"),n[Symbol.asyncIterator]=function(){return this},n);function c(o){n[o]=t[o]&&function(i){return new Promise(function(r,a){i=t[o](i),s(r,a,i.done,i.value)})}}function s(o,i,r,a){Promise.resolve(a).then(function(l){o({value:l,done:r})},i)}}var q3=t=>t&&typeof t.length=="number"&&typeof t!="function";function J0(t){return N(t==null?void 0:t.then)}function G3(t){let n=t(c=>{Error.call(c),c.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var e6=G3(t=>function(n){t(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((c,s)=>`${s+1}) ${c.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function p3(t,e){if(t){let n=t.indexOf(e);0<=n&&t.splice(n,1)}}var t1=class t{constructor(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let e;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let o of n)o.remove(this);else n.remove(this);let{initialTeardown:c}=this;if(N(c))try{c()}catch(o){e=o instanceof e6?o.errors:[o]}let{_finalizers:s}=this;if(s){this._finalizers=null;for(let o of s)try{pn(o)}catch(i){e=e!=null?e:[],i instanceof e6?e=[...e,...i.errors]:e.push(i)}}if(e)throw new e6(e)}}add(e){var n;if(e&&e!==this)if(this.closed)pn(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(e)}}_hasParent(e){let{_parentage:n}=this;return n===e||Array.isArray(n)&&n.includes(e)}_addParent(e){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(e),n):n?[n,e]:e}_removeParent(e){let{_parentage:n}=this;n===e?this._parentage=null:Array.isArray(n)&&p3(n,e)}remove(e){let{_finalizers:n}=this;n&&p3(n,e),e instanceof t&&e._removeParent(this)}};t1.EMPTY=(()=>{let t=new t1;return t.closed=!0,t})();var k8=t1.EMPTY;function t6(t){return t instanceof t1||t&&"closed"in t&&N(t.remove)&&N(t.add)&&N(t.unsubscribe)}function pn(t){N(t)?t():t.unsubscribe()}var K1={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var W3={setTimeout(t,e,...n){let{delegate:c}=W3;return c!=null&&c.setTimeout?c.setTimeout(t,e,...n):setTimeout(t,e,...n)},clearTimeout(t){let{delegate:e}=W3;return((e==null?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function c6(t){W3.setTimeout(()=>{let{onUnhandledError:e}=K1;if(e)e(t);else throw t})}function q4(){}var gn=F8("C",void 0,void 0);function Mn(t){return F8("E",void 0,t)}function Cn(t){return F8("N",t,void 0)}function F8(t,e,n){return{kind:t,value:e,error:n}}var g3=null;function Z3(t){if(K1.useDeprecatedSynchronousErrorHandling){let e=!g3;if(e&&(g3={errorThrown:!1,error:null}),t(),e){let{errorThrown:n,error:c}=g3;if(g3=null,n)throw c}}else t()}function zn(t){K1.useDeprecatedSynchronousErrorHandling&&g3&&(g3.errorThrown=!0,g3.error=t)}var M3=class extends t1{constructor(e){super(),this.isStopped=!1,e?(this.destination=e,t6(e)&&e.add(this)):this.destination=Jd}static create(e,n,c){return new X1(e,n,c)}next(e){this.isStopped?V8(Cn(e),this):this._next(e)}error(e){this.isStopped?V8(Mn(e),this):(this.isStopped=!0,this._error(e))}complete(){this.isStopped?V8(gn,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(e){this.destination.next(e)}_error(e){try{this.destination.error(e)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Kd=Function.prototype.bind;function P8(t,e){return Kd.call(t,e)}var O8=class{constructor(e){this.partialObserver=e}next(e){let{partialObserver:n}=this;if(n.next)try{n.next(e)}catch(c){n6(c)}}error(e){let{partialObserver:n}=this;if(n.error)try{n.error(e)}catch(c){n6(c)}else n6(e)}complete(){let{partialObserver:e}=this;if(e.complete)try{e.complete()}catch(n){n6(n)}}},X1=class extends M3{constructor(e,n,c){super();let s;if(N(e)||!e)s={next:e!=null?e:void 0,error:n!=null?n:void 0,complete:c!=null?c:void 0};else{let o;this&&K1.useDeprecatedNextContext?(o=Object.create(e),o.unsubscribe=()=>this.unsubscribe(),s={next:e.next&&P8(e.next,o),error:e.error&&P8(e.error,o),complete:e.complete&&P8(e.complete,o)}):s=e}this.destination=new O8(s)}};function n6(t){K1.useDeprecatedSynchronousErrorHandling?zn(t):c6(t)}function Xd(t){throw t}function V8(t,e){let{onStoppedNotification:n}=K1;n&&W3.setTimeout(()=>n(t,e))}var Jd={closed:!0,next:q4,error:Xd,complete:q4};var Y3=typeof Symbol=="function"&&Symbol.observable||"@@observable";function k1(t){return t}function eh(...t){return R8(t)}function R8(t){return t.length===0?k1:t.length===1?t[0]:function(n){return t.reduce((c,s)=>s(c),n)}}var P=(()=>{class t{constructor(n){n&&(this._subscribe=n)}lift(n){let c=new t;return c.source=this,c.operator=n,c}subscribe(n,c,s){let o=ch(n)?n:new X1(n,c,s);return Z3(()=>{let{operator:i,source:r}=this;o.add(i?i.call(o,r):r?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(n){try{return this._subscribe(n)}catch(c){n.error(c)}}forEach(n,c){return c=Ln(c),new c((s,o)=>{let i=new X1({next:r=>{try{n(r)}catch(a){o(a),i.unsubscribe()}},error:o,complete:s});this.subscribe(i)})}_subscribe(n){var c;return(c=this.source)===null||c===void 0?void 0:c.subscribe(n)}[Y3](){return this}pipe(...n){return R8(n)(this)}toPromise(n){return n=Ln(n),new n((c,s)=>{let o;this.subscribe(i=>o=i,i=>s(i),()=>c(o))})}}return t.create=e=>new t(e),t})();function Ln(t){var e;return(e=t!=null?t:K1.Promise)!==null&&e!==void 0?e:Promise}function th(t){return t&&N(t.next)&&N(t.error)&&N(t.complete)}function ch(t){return t&&t instanceof M3||th(t)&&t6(t)}function s6(t){return N(t[Y3])}function o6(t){return Symbol.asyncIterator&&N(t==null?void 0:t[Symbol.asyncIterator])}function i6(t){return new TypeError(`You provided ${t!==null&&typeof t=="object"?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function nh(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var r6=nh();function a6(t){return N(t==null?void 0:t[r6])}function l6(t){return hn(this,arguments,function*(){let n=t.getReader();try{for(;;){let{value:c,done:s}=yield m3(n.read());if(s)return yield m3(void 0);yield yield m3(c)}}finally{n.releaseLock()}})}function f6(t){return N(t==null?void 0:t.getReader)}function U(t){if(t instanceof P)return t;if(t!=null){if(s6(t))return sh(t);if(q3(t))return oh(t);if(J0(t))return ih(t);if(o6(t))return vn(t);if(a6(t))return rh(t);if(f6(t))return ah(t)}throw i6(t)}function sh(t){return new P(e=>{let n=t[Y3]();if(N(n.subscribe))return n.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function oh(t){return new P(e=>{for(let n=0;n<t.length&&!e.closed;n++)e.next(t[n]);e.complete()})}function ih(t){return new P(e=>{t.then(n=>{e.closed||(e.next(n),e.complete())},n=>e.error(n)).then(null,c6)})}function rh(t){return new P(e=>{for(let n of t)if(e.next(n),e.closed)return;e.complete()})}function vn(t){return new P(e=>{lh(t,e).catch(n=>e.error(n))})}function ah(t){return vn(l6(t))}function lh(t,e){var n,c,s,o;return dn(this,void 0,void 0,function*(){try{for(n=mn(t);c=yield n.next(),!c.done;){let i=c.value;if(e.next(i),e.closed)return}}catch(i){s={error:i}}finally{try{c&&!c.done&&(o=n.return)&&(yield o.call(n))}finally{if(s)throw s.error}}e.complete()})}function k(t,e,n,c,s){return new H8(t,e,n,c,s)}var H8=class extends M3{constructor(e,n,c,s,o,i){super(e),this.onFinalize=o,this.shouldUnsubscribe=i,this._next=n?function(r){try{n(r)}catch(a){e.error(a)}}:super._next,this._error=s?function(r){try{s(r)}catch(a){e.error(a)}finally{this.unsubscribe()}}:super._error,this._complete=c?function(){try{c()}catch(r){e.error(r)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((e=this.onFinalize)===null||e===void 0||e.call(this))}}};function yn(t){return F((e,n)=>{let c=!1,s=null,o=null,i=!1,r=()=>{if(o==null||o.unsubscribe(),o=null,c){c=!1;let l=s;s=null,n.next(l)}i&&n.complete()},a=()=>{o=null,i&&n.complete()};e.subscribe(k(n,l=>{c=!0,s=l,o||U(t(l)).subscribe(o=k(n,r,a))},()=>{i=!0,(!c||!o||o.closed)&&n.complete()}))})}var u6=class extends t1{constructor(e,n){super()}schedule(e,n=0){return this}};var G4={setInterval(t,e,...n){let{delegate:c}=G4;return c!=null&&c.setInterval?c.setInterval(t,e,...n):setInterval(t,e,...n)},clearInterval(t){let{delegate:e}=G4;return((e==null?void 0:e.clearInterval)||clearInterval)(t)},delegate:void 0};var Q3=class extends u6{constructor(e,n){super(e,n),this.scheduler=e,this.work=n,this.pending=!1}schedule(e,n=0){var c;if(this.closed)return this;this.state=e;let s=this.id,o=this.scheduler;return s!=null&&(this.id=this.recycleAsyncId(o,s,n)),this.pending=!0,this.delay=n,this.id=(c=this.id)!==null&&c!==void 0?c:this.requestAsyncId(o,this.id,n),this}requestAsyncId(e,n,c=0){return G4.setInterval(e.flush.bind(e,this),c)}recycleAsyncId(e,n,c=0){if(c!=null&&this.delay===c&&this.pending===!1)return n;n!=null&&G4.clearInterval(n)}execute(e,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let c=this._execute(e,n);if(c)return c;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,n){let c=!1,s;try{this.work(e)}catch(o){c=!0,s=o||new Error("Scheduled action threw falsy error")}if(c)return this.unsubscribe(),s}unsubscribe(){if(!this.closed){let{id:e,scheduler:n}=this,{actions:c}=n;this.work=this.state=this.scheduler=null,this.pending=!1,p3(c,this),e!=null&&(this.id=this.recycleAsyncId(n,e,null)),this.delay=null,super.unsubscribe()}}};var W4={now(){return(W4.delegate||Date).now()},delegate:void 0};var K3=class t{constructor(e,n=t.now){this.schedulerActionCtor=e,this.now=n}schedule(e,n=0,c){return new this.schedulerActionCtor(this,e).schedule(c,n)}};K3.now=W4.now;var X3=class extends K3{constructor(e,n=K3.now){super(e,n),this.actions=[],this._active=!1}flush(e){let{actions:n}=this;if(this._active){n.push(e);return}let c;this._active=!0;do if(c=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,c){for(;e=n.shift();)e.unsubscribe();throw c}}};var C3=new X3(Q3),bn=C3;function d6(t){return t&&N(t.schedule)}function xn(t){return t instanceof Date&&!isNaN(t)}function h6(t=0,e,n=bn){let c=-1;return e!=null&&(d6(e)?n=e:c=e),new P(s=>{let o=xn(t)?+t-n.now():t;o<0&&(o=0);let i=0;return n.schedule(function(){s.closed||(s.next(i++),0<=c?this.schedule(void 0,c):s.complete())},o)})}function fh(t,e=C3){return yn(()=>h6(t,e))}function j8(t){return t[t.length-1]}function m6(t){return N(j8(t))?t.pop():void 0}function a2(t){return d6(j8(t))?t.pop():void 0}function Nn(t,e){return typeof j8(t)=="number"?t.pop():e}function w1(t,e,n,c=0,s=!1){let o=e.schedule(function(){n(),s?t.add(this.schedule(null,c)):this.unsubscribe()},c);if(t.add(o),!s)return o}function B8(t){return F((e,n)=>{let c=null,s=!1,o;c=e.subscribe(k(n,void 0,void 0,i=>{o=U(t(i,B8(t)(e))),c?(c.unsubscribe(),c=null,o.subscribe(n)):s=!0})),s&&(c.unsubscribe(),c=null,o.subscribe(n))})}var{isArray:uh}=Array,{getPrototypeOf:dh,prototype:hh,keys:mh}=Object;function p6(t){if(t.length===1){let e=t[0];if(uh(e))return{args:e,keys:null};if(ph(e)){let n=mh(e);return{args:n.map(c=>e[c]),keys:n}}}return{args:t,keys:null}}function ph(t){return t&&typeof t=="object"&&dh(t)===hh}function g6(t,e=0){return F((n,c)=>{n.subscribe(k(c,s=>w1(c,t,()=>c.next(s),e),()=>w1(c,t,()=>c.complete(),e),s=>w1(c,t,()=>c.error(s),e)))})}function M6(t,e=0){return F((n,c)=>{c.add(t.schedule(()=>n.subscribe(c),e))})}function wn(t,e){return U(t).pipe(M6(e),g6(e))}function Dn(t,e){return U(t).pipe(M6(e),g6(e))}function Sn(t,e){return new P(n=>{let c=0;return e.schedule(function(){c===t.length?n.complete():(n.next(t[c++]),n.closed||this.schedule())})})}function En(t,e){return new P(n=>{let c;return w1(n,e,()=>{c=t[r6](),w1(n,e,()=>{let s,o;try{({value:s,done:o}=c.next())}catch(i){n.error(i);return}o?n.complete():n.next(s)},0,!0)}),()=>N(c==null?void 0:c.return)&&c.return()})}function C6(t,e){if(!t)throw new Error("Iterable cannot be null");return new P(n=>{w1(n,e,()=>{let c=t[Symbol.asyncIterator]();w1(n,e,()=>{c.next().then(s=>{s.done?n.complete():n.next(s.value)})},0,!0)})})}function An(t,e){return C6(l6(t),e)}function In(t,e){if(t!=null){if(s6(t))return wn(t,e);if(q3(t))return Sn(t,e);if(J0(t))return Dn(t,e);if(o6(t))return C6(t,e);if(a6(t))return En(t,e);if(f6(t))return An(t,e)}throw i6(t)}function D1(t,e){return e?In(t,e):U(t)}function o1(t,e){return F((n,c)=>{let s=0;n.subscribe(k(c,o=>{c.next(t.call(e,o,s++))}))})}var{isArray:gh}=Array;function Mh(t,e){return gh(e)?t(...e):t(e)}function J3(t){return o1(e=>Mh(t,e))}function z6(t,e){return t.reduce((n,c,s)=>(n[c]=e[s],n),{})}function Ch(...t){let e=a2(t),n=m6(t),{args:c,keys:s}=p6(t);if(c.length===0)return D1([],e);let o=new P(zh(c,e,s?i=>z6(s,i):k1));return n?o.pipe(J3(n)):o}function zh(t,e,n=k1){return c=>{Tn(e,()=>{let{length:s}=t,o=new Array(s),i=s,r=s;for(let a=0;a<s;a++)Tn(e,()=>{let l=D1(t[a],e),f=!1;l.subscribe(k(c,u=>{o[a]=u,f||(f=!0,r--),r||c.next(n(o.slice()))},()=>{--i||c.complete()}))},c)},c)}}function Tn(t,e,n){t?w1(n,t,e):e()}function _n(t,e,n,c,s,o,i,r){let a=[],l=0,f=0,u=!1,d=()=>{u&&!a.length&&!l&&e.complete()},h=C=>l<c?m(C):a.push(C),m=C=>{o&&e.next(C),l++;let M=!1;U(n(C,f++)).subscribe(k(e,g=>{s==null||s(g),o?h(g):e.next(g)},()=>{M=!0},void 0,()=>{if(M)try{for(l--;a.length&&l<c;){let g=a.shift();i?w1(e,i,()=>m(g)):m(g)}d()}catch(g){e.error(g)}}))};return t.subscribe(k(e,h,()=>{u=!0,d()})),()=>{r==null||r()}}function y2(t,e,n=1/0){return N(e)?y2((c,s)=>o1((o,i)=>e(c,o,s,i))(U(t(c,s))),n):(typeof e=="number"&&(n=e),F((c,s)=>_n(c,s,t,n)))}function kn(t,e,n,c,s){return(o,i)=>{let r=n,a=e,l=0;o.subscribe(k(i,f=>{let u=l++;a=r?t(a,f,u):(r=!0,f),c&&i.next(a)},s&&(()=>{r&&i.next(a),i.complete()})))}}function Z4(t=1/0){return y2(k1,t)}function Fn(){return Z4(1)}function Y4(t,e){return N(e)?y2(t,e,1):y2(t,1)}var Pn=G3(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var U1=(()=>{class t extends P{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let c=new L6(this,this);return c.operator=n,c}_throwIfClosed(){if(this.closed)throw new Pn}next(n){Z3(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let c of this.currentObservers)c.next(n)}})}error(n){Z3(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:c}=this;for(;c.length;)c.shift().error(n)}})}complete(){Z3(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:c,isStopped:s,observers:o}=this;return c||s?k8:(this.currentObservers=null,o.push(n),new t1(()=>{this.currentObservers=null,p3(o,n)}))}_checkFinalizedStatuses(n){let{hasError:c,thrownError:s,isStopped:o}=this;c?n.error(s):o&&n.complete()}asObservable(){let n=new P;return n.source=this,n}}return t.create=(e,n)=>new L6(e,n),t})(),L6=class extends U1{constructor(e,n){super(),this.destination=e,this.source=n}next(e){var n,c;(c=(n=this.destination)===null||n===void 0?void 0:n.next)===null||c===void 0||c.call(n,e)}error(e){var n,c;(c=(n=this.destination)===null||n===void 0?void 0:n.error)===null||c===void 0||c.call(n,e)}complete(){var e,n;(n=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||n===void 0||n.call(e)}_subscribe(e){var n,c;return(c=(n=this.source)===null||n===void 0?void 0:n.subscribe(e))!==null&&c!==void 0?c:k8}};function Lh(t,e=C3){return F((n,c)=>{let s=null,o=null,i=null,r=()=>{if(s){s.unsubscribe(),s=null;let l=o;o=null,c.next(l)}};function a(){let l=i+t,f=e.now();if(f<l){s=this.schedule(void 0,l-f),c.add(s);return}r()}n.subscribe(k(c,l=>{o=l,i=e.now(),s||(s=e.schedule(a,t),c.add(s))},()=>{r(),c.complete()},void 0,()=>{o=s=null}))})}function Q4(t){return F((e,n)=>{let c=!1;e.subscribe(k(n,s=>{c=!0,n.next(s)},()=>{c||n.next(t),n.complete()}))})}function z3(...t){return Fn()(D1(t,a2(t)))}var L3=new P(t=>t.complete());function $2(t){return t<=0?()=>L3:F((e,n)=>{let c=0;e.subscribe(k(n,s=>{++c<=t&&(n.next(s),t<=c&&n.complete())}))})}function vh(t){return o1(()=>t)}function l2(...t){let e=a2(t);return D1(t,e)}function yh(t,e){let n=N(t)?t:()=>t,c=s=>s.error(n());return new P(e?s=>e.schedule(c,0,s):c)}function v3(t,e){return F((n,c)=>{let s=0;n.subscribe(k(c,o=>t.call(e,o,s++)&&c.next(o)))})}var b2=G3(t=>function(){t(this),this.name="EmptyError",this.message="no elements in sequence"});function v6(t=bh){return F((e,n)=>{let c=!1;e.subscribe(k(n,s=>{c=!0,n.next(s)},()=>c?n.complete():n.error(t())))})}function bh(){return new b2}function U8(t,e){let n=arguments.length>=2;return c=>c.pipe(t?v3((s,o)=>t(s,o,c)):k1,$2(1),n?Q4(e):v6(()=>new b2))}function $8(t){return t<=0?()=>L3:F((e,n)=>{let c=[];e.subscribe(k(n,s=>{c.push(s),t<c.length&&c.shift()},()=>{for(let s of c)n.next(s);n.complete()},void 0,()=>{c=null}))})}function xh(t,e){let n=arguments.length>=2;return c=>c.pipe(t?v3((s,o)=>t(s,o,c)):k1,$8(1),n?Q4(e):v6(()=>new b2))}function q8(){return F((t,e)=>{let n=null;t._refCount++;let c=k(e,void 0,void 0,void 0,()=>{if(!t||t._refCount<=0||0<--t._refCount){n=null;return}let s=t._connection,o=n;n=null,s&&(!o||s===o)&&s.unsubscribe(),e.unsubscribe()});t.subscribe(c),c.closed||(n=t.connect())})}var G8=class extends P{constructor(e,n){super(),this.source=e,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,_8(e)&&(this.lift=e.lift)}_subscribe(e){return this.getSubject().subscribe(e)}getSubject(){let e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:e}=this;this._subject=this._connection=null,e==null||e.unsubscribe()}connect(){let e=this._connection;if(!e){e=this._connection=new t1;let n=this.getSubject();e.add(this.source.subscribe(k(n,void 0,()=>{this._teardown(),n.complete()},c=>{this._teardown(),n.error(c)},()=>this._teardown()))),e.closed&&(this._connection=null,e=t1.EMPTY)}return e}refCount(){return q8()(this)}};var K4=class extends U1{constructor(e){super(),this._value=e}get value(){return this.getValue()}_subscribe(e){let n=super._subscribe(e);return!n.closed&&e.next(this._value),n}getValue(){let{hasError:e,thrownError:n,_value:c}=this;if(e)throw n;return this._throwIfClosed(),c}next(e){super.next(this._value=e)}};var y6=class extends U1{constructor(e=1/0,n=1/0,c=W4){super(),this._bufferSize=e,this._windowTime=n,this._timestampProvider=c,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,e),this._windowTime=Math.max(1,n)}next(e){let{isStopped:n,_buffer:c,_infiniteTimeWindow:s,_timestampProvider:o,_windowTime:i}=this;n||(c.push(e),!s&&c.push(o.now()+i)),this._trimBuffer(),super.next(e)}_subscribe(e){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(e),{_infiniteTimeWindow:c,_buffer:s}=this,o=s.slice();for(let i=0;i<o.length&&!e.closed;i+=c?1:2)e.next(o[i]);return this._checkFinalizedStatuses(e),n}_trimBuffer(){let{_bufferSize:e,_timestampProvider:n,_buffer:c,_infiniteTimeWindow:s}=this,o=(s?1:2)*e;if(e<1/0&&o<c.length&&c.splice(0,c.length-o),!s){let i=n.now(),r=0;for(let a=1;a<c.length&&c[a]<=i;a+=2)r=a;r&&c.splice(0,r+1)}}};function Nh(t=0,e=C3){return t<0&&(t=0),h6(t,t,e)}function wh(t,e){return F(kn(t,e,arguments.length>=2,!0))}function Vn(t={}){let{connector:e=()=>new U1,resetOnError:n=!0,resetOnComplete:c=!0,resetOnRefCountZero:s=!0}=t;return o=>{let i,r,a,l=0,f=!1,u=!1,d=()=>{r==null||r.unsubscribe(),r=void 0},h=()=>{d(),i=a=void 0,f=u=!1},m=()=>{let C=i;h(),C==null||C.unsubscribe()};return F((C,M)=>{l++,!u&&!f&&d();let g=a=a!=null?a:e();M.add(()=>{l--,l===0&&!u&&!f&&(r=W8(m,s))}),g.subscribe(M),!i&&l>0&&(i=new X1({next:b=>g.next(b),error:b=>{u=!0,d(),r=W8(h,n,b),g.error(b)},complete:()=>{f=!0,d(),r=W8(h,c),g.complete()}}),U(C).subscribe(i))})(o)}}function W8(t,e,...n){if(e===!0){t();return}if(e===!1)return;let c=new X1({next:()=>{c.unsubscribe(),t()}});return U(e(...n)).subscribe(c)}function X4(t,e,n){let c,s=!1;return t&&typeof t=="object"?{bufferSize:c=1/0,windowTime:e=1/0,refCount:s=!1,scheduler:n}=t:c=t!=null?t:1/0,Vn({connector:()=>new y6(c,e,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:s})}function Dh(...t){let e=a2(t);return F((n,c)=>{(e?z3(t,n,e):z3(t,n)).subscribe(c)})}function y3(t,e){return F((n,c)=>{let s=null,o=0,i=!1,r=()=>i&&!s&&c.complete();n.subscribe(k(c,a=>{s==null||s.unsubscribe();let l=0,f=o++;U(t(a,f)).subscribe(s=k(c,u=>c.next(e?e(a,u,f,l++):u),()=>{s=null,r()}))},()=>{i=!0,r()}))})}function Sh(t){return F((e,n)=>{U(t).subscribe(k(n,()=>n.complete(),q4)),!n.closed&&e.subscribe(n)})}function b6(t,e,n){let c=N(t)||e||n?{next:t,error:e,complete:n}:t;return c?F((s,o)=>{var i;(i=c.subscribe)===null||i===void 0||i.call(c);let r=!0;s.subscribe(k(o,a=>{var l;(l=c.next)===null||l===void 0||l.call(c,a),o.next(a)},()=>{var a;r=!1,(a=c.complete)===null||a===void 0||a.call(c),o.complete()},a=>{var l;r=!1,(l=c.error)===null||l===void 0||l.call(c,a),o.error(a)},()=>{var a,l;r&&((a=c.unsubscribe)===null||a===void 0||a.call(c)),(l=c.finalize)===null||l===void 0||l.call(c)}))}):k1}function Eh(t,e){return Object.is(t,e)}var i1=null,x6=!1,N6=1,q2=Symbol("SIGNAL");function O(t){let e=i1;return i1=t,e}var Y8={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Q8(t){if(x6)throw new Error("");if(i1===null)return;i1.consumerOnSignalRead(t);let e=i1.nextProducerIndex++;if(e4(i1),e<i1.producerNode.length&&i1.producerNode[e]!==t&&J4(i1)){let n=i1.producerNode[e];w6(n,i1.producerIndexOfThis[e])}i1.producerNode[e]!==t&&(i1.producerNode[e]=t,i1.producerIndexOfThis[e]=J4(i1)?Un(t,i1,e):0),i1.producerLastReadVersion[e]=t.version}function Ah(){N6++}function Ih(t){if(!(J4(t)&&!t.dirty)&&!(!t.dirty&&t.lastCleanEpoch===N6)){if(!t.producerMustRecompute(t)&&!K8(t)){t.dirty=!1,t.lastCleanEpoch=N6;return}t.producerRecomputeValue(t),t.dirty=!1,t.lastCleanEpoch=N6}}function On(t){if(t.liveConsumerNode===void 0)return;let e=x6;x6=!0;try{for(let n of t.liveConsumerNode)n.dirty||Th(n)}finally{x6=e}}function Rn(){return(i1==null?void 0:i1.consumerAllowSignalWrites)!==!1}function Th(t){var e;t.dirty=!0,On(t),(e=t.consumerMarkedDirty)==null||e.call(t,t)}function Hn(t){return t&&(t.nextProducerIndex=0),O(t)}function jn(t,e){if(O(e),!(!t||t.producerNode===void 0||t.producerIndexOfThis===void 0||t.producerLastReadVersion===void 0)){if(J4(t))for(let n=t.nextProducerIndex;n<t.producerNode.length;n++)w6(t.producerNode[n],t.producerIndexOfThis[n]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function K8(t){e4(t);for(let e=0;e<t.producerNode.length;e++){let n=t.producerNode[e],c=t.producerLastReadVersion[e];if(c!==n.version||(Ih(n),c!==n.version))return!0}return!1}function Bn(t){if(e4(t),J4(t))for(let e=0;e<t.producerNode.length;e++)w6(t.producerNode[e],t.producerIndexOfThis[e]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function Un(t,e,n){if($n(t),e4(t),t.liveConsumerNode.length===0)for(let c=0;c<t.producerNode.length;c++)t.producerIndexOfThis[c]=Un(t.producerNode[c],t,c);return t.liveConsumerIndexOfThis.push(n),t.liveConsumerNode.push(e)-1}function w6(t,e){if($n(t),e4(t),t.liveConsumerNode.length===1)for(let c=0;c<t.producerNode.length;c++)w6(t.producerNode[c],t.producerIndexOfThis[c]);let n=t.liveConsumerNode.length-1;if(t.liveConsumerNode[e]=t.liveConsumerNode[n],t.liveConsumerIndexOfThis[e]=t.liveConsumerIndexOfThis[n],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,e<t.liveConsumerNode.length){let c=t.liveConsumerIndexOfThis[e],s=t.liveConsumerNode[e];e4(s),s.producerIndexOfThis[c]=e}}function J4(t){var e,n;return t.consumerIsAlwaysLive||((n=(e=t==null?void 0:t.liveConsumerNode)==null?void 0:e.length)!=null?n:0)>0}function e4(t){var e,n,c;(e=t.producerNode)!=null||(t.producerNode=[]),(n=t.producerIndexOfThis)!=null||(t.producerIndexOfThis=[]),(c=t.producerLastReadVersion)!=null||(t.producerLastReadVersion=[])}function $n(t){var e,n;(e=t.liveConsumerNode)!=null||(t.liveConsumerNode=[]),(n=t.liveConsumerIndexOfThis)!=null||(t.liveConsumerIndexOfThis=[])}function _h(){throw new Error}var qn=_h;function Gn(){qn()}function Wn(t){qn=t}var Z8=null;function Zn(t){let e=Object.create(X8);e.value=t;let n=()=>(Q8(e),e.value);return n[q2]=e,n}function D6(t,e){Rn()||Gn(),t.equal(t.value,e)||(t.value=e,kh(t))}function Yn(t,e){Rn()||Gn(),D6(t,e(t.value))}var X8=m1(Z({},Y8),{equal:Eh,value:void 0});function kh(t){t.version++,Ah(),On(t),Z8==null||Z8()}var t4={schedule(t){let e=requestAnimationFrame,n=cancelAnimationFrame,{delegate:c}=t4;c&&(e=c.requestAnimationFrame,n=c.cancelAnimationFrame);let s=e(o=>{n=void 0,t(o)});return new t1(()=>n==null?void 0:n(s))},requestAnimationFrame(...t){let{delegate:e}=t4;return((e==null?void 0:e.requestAnimationFrame)||requestAnimationFrame)(...t)},cancelAnimationFrame(...t){let{delegate:e}=t4;return((e==null?void 0:e.cancelAnimationFrame)||cancelAnimationFrame)(...t)},delegate:void 0};var S6=class extends Q3{constructor(e,n){super(e,n),this.scheduler=e,this.work=n}requestAsyncId(e,n,c=0){return c!==null&&c>0?super.requestAsyncId(e,n,c):(e.actions.push(this),e._scheduled||(e._scheduled=t4.requestAnimationFrame(()=>e.flush(void 0))))}recycleAsyncId(e,n,c=0){var s;if(c!=null?c>0:this.delay>0)return super.recycleAsyncId(e,n,c);let{actions:o}=e;n!=null&&n===e._scheduled&&((s=o[o.length-1])===null||s===void 0?void 0:s.id)!==n&&(t4.cancelAnimationFrame(n),e._scheduled=void 0)}};var E6=class extends X3{flush(e){this._active=!0;let n;e?n=e.id:(n=this._scheduled,this._scheduled=void 0);let{actions:c}=this,s;e=e||c.shift();do if(s=e.execute(e.state,e.delay))break;while((e=c[0])&&e.id===n&&c.shift());if(this._active=!1,s){for(;(e=c[0])&&e.id===n&&c.shift();)e.unsubscribe();throw s}}};var Fh=new E6(S6);function b3(t){return!!t&&(t instanceof P||N(t.lift)&&N(t.subscribe))}function Ph(t,e){let n=typeof e=="object";return new Promise((c,s)=>{let o=new X1({next:i=>{c(i),o.unsubscribe()},error:s,complete:()=>{n?c(e.defaultValue):s(new b2)}});t.subscribe(o)})}function A6(t){return new P(e=>{U(t()).subscribe(e)})}function e0(...t){let e=m6(t),{args:n,keys:c}=p6(t),s=new P(o=>{let{length:i}=n;if(!i){o.complete();return}let r=new Array(i),a=i,l=i;for(let f=0;f<i;f++){let u=!1;U(n[f]).subscribe(k(o,d=>{u||(u=!0,l--),r[f]=d},()=>a--,void 0,()=>{(!a||!u)&&(l||o.next(c?z6(c,r):r),o.complete())}))}});return e?s.pipe(J3(e)):s}var Vh=["addListener","removeListener"],Oh=["addEventListener","removeEventListener"],Rh=["on","off"];function J8(t,e,n,c){if(N(n)&&(c=n,n=void 0),c)return J8(t,e,n).pipe(J3(c));let[s,o]=Bh(t)?Oh.map(i=>r=>t[i](e,r,n)):Hh(t)?Vh.map(Qn(t,e)):jh(t)?Rh.map(Qn(t,e)):[];if(!s&&q3(t))return y2(i=>J8(i,e,n))(U(t));if(!s)throw new TypeError("Invalid event target");return new P(i=>{let r=(...a)=>i.next(1<a.length?a:a[0]);return s(r),()=>o(r)})}function Qn(t,e){return n=>c=>t[n](e,c)}function Hh(t){return N(t.addListener)&&N(t.removeListener)}function jh(t){return N(t.on)&&N(t.off)}function Bh(t){return N(t.addEventListener)&&N(t.removeEventListener)}function Uh(...t){let e=a2(t),n=Nn(t,1/0),c=t;return c.length?c.length===1?U(c[0]):Z4(n)(D1(c,e)):L3}var R9="https://g.co/ng/security#xss",S=class extends Error{constructor(e,n){super(me(e,n)),this.code=e}};function me(t,e){return`${`NG0${Math.abs(t)}`}${e?": "+e:""}`}var H9=Symbol("InputSignalNode#UNSET"),$h=m1(Z({},X8),{transformFn:void 0,applyValueToInputSignal(t,e){D6(t,e)}});function j9(t,e){let n=Object.create($h);n.value=t,n.transformFn=e==null?void 0:e.transform;function c(){if(Q8(n),n.value===H9)throw new S(-950,!1);return n.value}return c[q2]=n,c}function h0(t){return{toString:t}.toString()}var I6="__parameters__";function qh(t){return function(...n){if(t){let c=t(...n);for(let s in c)this[s]=c[s]}}}function B9(t,e,n){return h0(()=>{let c=qh(e);function s(...o){if(this instanceof s)return c.apply(this,o),this;let i=new s(...o);return r.annotation=i,r;function r(a,l,f){let u=a.hasOwnProperty(I6)?a[I6]:Object.defineProperty(a,I6,{value:[]})[I6];for(;u.length<=f;)u.push(null);return(u[f]=u[f]||[]).push(i),a}}return n&&(s.prototype=Object.create(n.prototype)),s.prototype.ngMetadataName=t,s.annotationCls=s,s})}var F1=globalThis;function q(t){for(let e in t)if(t[e]===q)return e;throw Error("Could not find renamed property on target object.")}function Gh(t,e){for(let n in e)e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n])}function y1(t){if(typeof t=="string")return t;if(Array.isArray(t))return"["+t.map(y1).join(", ")+"]";if(t==null)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;let e=t.toString();if(e==null)return""+e;let n=e.indexOf(`
`);return n===-1?e:e.substring(0,n)}function C5(t,e){return t==null||t===""?e===null?"":e:e==null||e===""?t:t+" "+e}var Wh=q({__forward_ref__:q});function u1(t){return t.__forward_ref__=u1,t.toString=function(){return y1(this())},t}function L1(t){return U9(t)?t():t}function U9(t){return typeof t=="function"&&t.hasOwnProperty(Wh)&&t.__forward_ref__===u1}function y(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function A1(t){return{providers:t.providers||[],imports:t.imports||[]}}function pe(t){return Kn(t,$9)||Kn(t,q9)}function q_(t){return pe(t)!==null}function Kn(t,e){return t.hasOwnProperty(e)?t[e]:null}function Zh(t){let e=t&&(t[$9]||t[q9]);return e||null}function Xn(t){return t&&(t.hasOwnProperty(Jn)||t.hasOwnProperty(Yh))?t[Jn]:null}var $9=q({\u0275prov:q}),Jn=q({\u0275inj:q}),q9=q({ngInjectableDef:q}),Yh=q({ngInjectorDef:q}),w=class{constructor(e,n){this._desc=e,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=y({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function G9(t){return t&&!!t.\u0275providers}var Qh=q({\u0275cmp:q}),Kh=q({\u0275dir:q}),Xh=q({\u0275pipe:q}),Jh=q({\u0275mod:q}),$6=q({\u0275fac:q}),c0=q({__NG_ELEMENT_ID__:q}),e9=q({__NG_ENV_ID__:q});function Y(t){return typeof t=="string"?t:t==null?"":String(t)}function em(t){return typeof t=="function"?t.name||t.toString():typeof t=="object"&&t!=null&&typeof t.type=="function"?t.type.name||t.type.toString():Y(t)}function tm(t,e){let n=e?`. Dependency path: ${e.join(" > ")} > ${t}`:"";throw new S(-200,t)}function D7(t,e){throw new S(-201,!1)}var R=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}(R||{}),z5;function W9(){return z5}function S1(t){let e=z5;return z5=t,e}function Z9(t,e,n){let c=pe(t);if(c&&c.providedIn=="root")return c.value===void 0?c.value=c.factory():c.value;if(n&R.Optional)return null;if(e!==void 0)return e;D7(t,"Injector")}var cm={},n0=cm,L5="__NG_DI_FLAG__",q6="ngTempTokenPath",nm="ngTokenPath",sm=/\n/gm,om="\u0275",t9="__source",i4;function im(){return i4}function W2(t){let e=i4;return i4=t,e}function rm(t,e=R.Default){if(i4===void 0)throw new S(-203,!1);return i4===null?Z9(t,void 0,e):i4.get(t,e&R.Optional?null:void 0,e)}function L(t,e=R.Default){return(W9()||rm)(L1(t),e)}function x(t,e=R.Default){return L(t,ge(e))}function ge(t){return typeof t>"u"||typeof t=="number"?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function v5(t){let e=[];for(let n=0;n<t.length;n++){let c=L1(t[n]);if(Array.isArray(c)){if(c.length===0)throw new S(900,!1);let s,o=R.Default;for(let i=0;i<c.length;i++){let r=c[i],a=am(r);typeof a=="number"?a===-1?s=r.token:o|=a:s=r}e.push(L(s,o))}else e.push(L(c))}return e}function Y9(t,e){return t[L5]=e,t.prototype[L5]=e,t}function am(t){return t[L5]}function lm(t,e,n,c){let s=t[q6];throw e[t9]&&s.unshift(e[t9]),t.message=fm(`
`+t.message,s,n,c),t[nm]=s,t[q6]=null,t}function fm(t,e,n,c=null){t=t&&t.charAt(0)===`
`&&t.charAt(1)==om?t.slice(2):t;let s=y1(e);if(Array.isArray(e))s=e.map(y1).join(" -> ");else if(typeof e=="object"){let o=[];for(let i in e)if(e.hasOwnProperty(i)){let r=e[i];o.push(i+":"+(typeof r=="string"?JSON.stringify(r):y1(r)))}s=`{${o.join(", ")}}`}return`${n}${c?"("+c+")":""}[${s}]: ${t.replace(sm,`
  `)}`}var S7=Y9(B9("Optional"),8);var Q9=Y9(B9("SkipSelf"),4);function w3(t,e){let n=t.hasOwnProperty($6);return n?t[$6]:null}function um(t,e,n){if(t.length!==e.length)return!1;for(let c=0;c<t.length;c++){let s=t[c],o=e[c];if(n&&(s=n(s),o=n(o)),o!==s)return!1}return!0}function dm(t){return t.flat(Number.POSITIVE_INFINITY)}function E7(t,e){t.forEach(n=>Array.isArray(n)?E7(n,e):e(n))}function K9(t,e,n){e>=t.length?t.push(n):t.splice(e,0,n)}function G6(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function hm(t,e){let n=[];for(let c=0;c<t;c++)n.push(e);return n}function mm(t,e,n,c){let s=t.length;if(s==e)t.push(n,c);else if(s===1)t.push(c,t[0]),t[0]=n;else{for(s--,t.push(t[s-1],t[s]);s>e;){let o=s-2;t[s]=t[o],s--}t[e]=n,t[e+1]=c}}function A7(t,e,n){let c=m0(t,e);return c>=0?t[c|1]=n:(c=~c,mm(t,c,e,n)),c}function e5(t,e){let n=m0(t,e);if(n>=0)return t[n|1]}function m0(t,e){return pm(t,e,1)}function pm(t,e,n){let c=0,s=t.length>>n;for(;s!==c;){let o=c+(s-c>>1),i=t[o<<n];if(e===i)return o<<n;i>e?s=o:c=o+1}return~(s<<n)}var a4={},v1=[],l4=new w(""),X9=new w("",-1),J9=new w(""),W6=class{get(e,n=n0){if(n===n0){let c=new Error(`NullInjectorError: No provider for ${y1(e)}!`);throw c.name="NullInjectorError",c}return n}},es=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(es||{}),d2=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(d2||{}),d1=function(t){return t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",t}(d1||{});function gm(t,e,n){let c=t.length;for(;;){let s=t.indexOf(e,n);if(s===-1)return s;if(s===0||t.charCodeAt(s-1)<=32){let o=e.length;if(s+o===c||t.charCodeAt(s+o)<=32)return s}n=s+1}}function y5(t,e,n){let c=0;for(;c<n.length;){let s=n[c];if(typeof s=="number"){if(s!==0)break;c++;let o=n[c++],i=n[c++],r=n[c++];t.setAttribute(e,i,r,o)}else{let o=s,i=n[++c];Mm(o)?t.setProperty(e,o,i):t.setAttribute(e,o,i),c++}}return c}function ts(t){return t===3||t===4||t===6}function Mm(t){return t.charCodeAt(0)===64}function s0(t,e){if(!(e===null||e.length===0))if(t===null||t.length===0)t=e.slice();else{let n=-1;for(let c=0;c<e.length;c++){let s=e[c];typeof s=="number"?n=s:n===0||(n===-1||n===2?c9(t,n,s,null,e[++c]):c9(t,n,s,null,null))}}return t}function c9(t,e,n,c,s){let o=0,i=t.length;if(e===-1)i=-1;else for(;o<t.length;){let r=t[o++];if(typeof r=="number"){if(r===e){i=-1;break}else if(r>e){i=o-1;break}}}for(;o<t.length;){let r=t[o];if(typeof r=="number")break;if(r===n){if(c===null){s!==null&&(t[o+1]=s);return}else if(c===t[o+1]){t[o+2]=s;return}}o++,c!==null&&o++,s!==null&&o++}i!==-1&&(t.splice(i,0,e),o=i+1),t.splice(o++,0,n),c!==null&&t.splice(o++,0,c),s!==null&&t.splice(o++,0,s)}var cs="ng-template";function Cm(t,e,n,c){let s=0;if(c){for(;s<e.length&&typeof e[s]=="string";s+=2)if(e[s]==="class"&&gm(e[s+1].toLowerCase(),n,0)!==-1)return!0}else if(I7(t))return!1;if(s=e.indexOf(1,s),s>-1){let o;for(;++s<e.length&&typeof(o=e[s])=="string";)if(o.toLowerCase()===n)return!0}return!1}function I7(t){return t.type===4&&t.value!==cs}function zm(t,e,n){let c=t.type===4&&!n?cs:t.value;return e===c}function Lm(t,e,n){let c=4,s=t.attrs,o=s!==null?bm(s):0,i=!1;for(let r=0;r<e.length;r++){let a=e[r];if(typeof a=="number"){if(!i&&!J1(c)&&!J1(a))return!1;if(i&&J1(a))continue;i=!1,c=a|c&1;continue}if(!i)if(c&4){if(c=2|c&1,a!==""&&!zm(t,a,n)||a===""&&e.length===1){if(J1(c))return!1;i=!0}}else if(c&8){if(s===null||!Cm(t,s,a,n)){if(J1(c))return!1;i=!0}}else{let l=e[++r],f=vm(a,s,I7(t),n);if(f===-1){if(J1(c))return!1;i=!0;continue}if(l!==""){let u;if(f>o?u="":u=s[f+1].toLowerCase(),c&2&&l!==u){if(J1(c))return!1;i=!0}}}}return J1(c)||i}function J1(t){return(t&1)===0}function vm(t,e,n,c){if(e===null)return-1;let s=0;if(c||!n){let o=!1;for(;s<e.length;){let i=e[s];if(i===t)return s;if(i===3||i===6)o=!0;else if(i===1||i===2){let r=e[++s];for(;typeof r=="string";)r=e[++s];continue}else{if(i===4)break;if(i===0){s+=4;continue}}s+=o?1:2}return-1}else return xm(e,t)}function ns(t,e,n=!1){for(let c=0;c<e.length;c++)if(Lm(t,e[c],n))return!0;return!1}function ym(t){let e=t.attrs;if(e!=null){let n=e.indexOf(5);if(!(n&1))return e[n+1]}return null}function bm(t){for(let e=0;e<t.length;e++){let n=t[e];if(ts(n))return e}return t.length}function xm(t,e){let n=t.indexOf(4);if(n>-1)for(n++;n<t.length;){let c=t[n];if(typeof c=="number")return-1;if(c===e)return n;n++}return-1}function Nm(t,e){e:for(let n=0;n<e.length;n++){let c=e[n];if(t.length===c.length){for(let s=0;s<t.length;s++)if(t[s]!==c[s])continue e;return!0}}return!1}function n9(t,e){return t?":not("+e.trim()+")":e}function wm(t){let e=t[0],n=1,c=2,s="",o=!1;for(;n<t.length;){let i=t[n];if(typeof i=="string")if(c&2){let r=t[++n];s+="["+i+(r.length>0?'="'+r+'"':"")+"]"}else c&8?s+="."+i:c&4&&(s+=" "+i);else s!==""&&!J1(i)&&(e+=n9(o,s),s=""),c=i,o=o||!J1(c);n++}return s!==""&&(e+=n9(o,s)),e}function Dm(t){return t.map(wm).join(",")}function Sm(t){let e=[],n=[],c=1,s=2;for(;c<t.length;){let o=t[c];if(typeof o=="string")s===2?o!==""&&e.push(o,t[++c]):s===8&&n.push(o);else{if(!J1(s))break;s=o}c++}return{attrs:e,classes:n}}function T7(t){return h0(()=>{var s;let e=rs(t),n=m1(Z({},e),{decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===es.OnPush,directiveDefs:null,pipeDefs:null,dependencies:e.standalone&&t.dependencies||null,getStandaloneInjector:null,signals:(s=t.signals)!=null?s:!1,data:t.data||{},encapsulation:t.encapsulation||d2.Emulated,styles:t.styles||v1,_:null,schemas:t.schemas||null,tView:null,id:""});as(n);let c=t.dependencies;return n.directiveDefs=o9(c,!1),n.pipeDefs=o9(c,!0),n.id=Tm(n),n})}function Em(t){return N2(t)||ss(t)}function Am(t){return t!==null}function I1(t){return h0(()=>({type:t.type,bootstrap:t.bootstrap||v1,declarations:t.declarations||v1,imports:t.imports||v1,exports:t.exports||v1,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function s9(t,e){var c;if(t==null)return a4;let n={};for(let s in t)if(t.hasOwnProperty(s)){let o=t[s],i,r,a=d1.None;Array.isArray(o)?(a=o[0],i=o[1],r=(c=o[2])!=null?c:i):(i=o,r=o),e?(n[i]=a!==d1.None?[s,a]:s,e[i]=r):n[i]=s}return n}function H(t){return h0(()=>{let e=rs(t);return as(e),e})}function S2(t){return{type:t.type,name:t.name,factory:null,pure:t.pure!==!1,standalone:t.standalone===!0,onDestroy:t.type.prototype.ngOnDestroy||null}}function N2(t){return t[Qh]||null}function ss(t){return t[Kh]||null}function os(t){return t[Xh]||null}function Im(t){let e=N2(t)||ss(t)||os(t);return e!==null?e.standalone:!1}function is(t,e){let n=t[Jh]||null;if(!n&&e===!0)throw new Error(`Type ${y1(t)} does not have '\u0275mod' property.`);return n}function rs(t){let e={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputTransforms:null,inputConfig:t.inputs||a4,exportAs:t.exportAs||null,standalone:t.standalone===!0,signals:t.signals===!0,selectors:t.selectors||v1,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:s9(t.inputs,e),outputs:s9(t.outputs),debugInfo:null}}function as(t){var e;(e=t.features)==null||e.forEach(n=>n(t))}function o9(t,e){if(!t)return null;let n=e?os:Em;return()=>(typeof t=="function"?t():t).map(c=>n(c)).filter(Am)}function Tm(t){let e=0,n=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,t.consts,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery].join("|");for(let s of n)e=Math.imul(31,e)+s.charCodeAt(0)<<0;return e+=**********,"c"+e}function F3(t){return{\u0275providers:t}}function _m(...t){return{\u0275providers:ls(!0,t),\u0275fromNgModule:!0}}function ls(t,...e){let n=[],c=new Set,s,o=i=>{n.push(i)};return E7(e,i=>{let r=i;b5(r,o,[],c)&&(s||(s=[]),s.push(r))}),s!==void 0&&fs(s,o),n}function fs(t,e){for(let n=0;n<t.length;n++){let{ngModule:c,providers:s}=t[n];_7(s,o=>{e(o,c)})}}function b5(t,e,n,c){if(t=L1(t),!t)return!1;let s=null,o=Xn(t),i=!o&&N2(t);if(!o&&!i){let a=t.ngModule;if(o=Xn(a),o)s=a;else return!1}else{if(i&&!i.standalone)return!1;s=t}let r=c.has(s);if(i){if(r)return!1;if(c.add(s),i.dependencies){let a=typeof i.dependencies=="function"?i.dependencies():i.dependencies;for(let l of a)b5(l,e,n,c)}}else if(o){if(o.imports!=null&&!r){c.add(s);let l;try{E7(o.imports,f=>{b5(f,e,n,c)&&(l||(l=[]),l.push(f))})}finally{}l!==void 0&&fs(l,e)}if(!r){let l=w3(s)||(()=>new s);e({provide:s,useFactory:l,deps:v1},s),e({provide:J9,useValue:s,multi:!0},s),e({provide:l4,useValue:()=>L(s),multi:!0},s)}let a=o.providers;if(a!=null&&!r){let l=t;_7(a,f=>{e(f,l)})}}else return!1;return s!==t&&t.providers!==void 0}function _7(t,e){for(let n of t)G9(n)&&(n=n.\u0275providers),Array.isArray(n)?_7(n,e):e(n)}var km=q({provide:String,useValue:q});function us(t){return t!==null&&typeof t=="object"&&km in t}function Fm(t){return!!(t&&t.useExisting)}function Pm(t){return!!(t&&t.useFactory)}function f4(t){return typeof t=="function"}function Vm(t){return!!t.useClass}var Me=new w(""),O6={},Om={},t5;function Ce(){return t5===void 0&&(t5=new W6),t5}var h2=class{},o0=class extends h2{get destroyed(){return this._destroyed}constructor(e,n,c,s){super(),this.parent=n,this.source=c,this.scopes=s,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,N5(e,i=>this.processProvider(i)),this.records.set(X9,c4(void 0,this)),s.has("environment")&&this.records.set(h2,c4(void 0,this));let o=this.records.get(Me);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(J9,v1,R.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let e=O(null);try{for(let c of this._ngOnDestroyHooks)c.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let c of n)c()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),O(e)}}onDestroy(e){return this.assertNotDestroyed(),this._onDestroyHooks.push(e),()=>this.removeOnDestroy(e)}runInContext(e){this.assertNotDestroyed();let n=W2(this),c=S1(void 0),s;try{return e()}finally{W2(n),S1(c)}}get(e,n=n0,c=R.Default){if(this.assertNotDestroyed(),e.hasOwnProperty(e9))return e[e9](this);c=ge(c);let s,o=W2(this),i=S1(void 0);try{if(!(c&R.SkipSelf)){let a=this.records.get(e);if(a===void 0){let l=Um(e)&&pe(e);l&&this.injectableDefInScope(l)?a=c4(x5(e),O6):a=null,this.records.set(e,a)}if(a!=null)return this.hydrate(e,a)}let r=c&R.Self?Ce():this.parent;return n=c&R.Optional&&n===n0?null:n,r.get(e,n)}catch(r){if(r.name==="NullInjectorError"){if((r[q6]=r[q6]||[]).unshift(y1(e)),o)throw r;return lm(r,e,"R3InjectorError",this.source)}else throw r}finally{S1(i),W2(o)}}resolveInjectorInitializers(){let e=O(null),n=W2(this),c=S1(void 0),s;try{let o=this.get(l4,v1,R.Self);for(let i of o)i()}finally{W2(n),S1(c),O(e)}}toString(){let e=[],n=this.records;for(let c of n.keys())e.push(y1(c));return`R3Injector[${e.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new S(205,!1)}processProvider(e){e=L1(e);let n=f4(e)?e:L1(e&&e.provide),c=Hm(e);if(!f4(e)&&e.multi===!0){let s=this.records.get(n);s||(s=c4(void 0,O6,!0),s.factory=()=>v5(s.multi),this.records.set(n,s)),n=e,s.multi.push(e)}this.records.set(n,c)}hydrate(e,n){let c=O(null);try{return n.value===O6&&(n.value=Om,n.value=n.factory()),typeof n.value=="object"&&n.value&&Bm(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{O(c)}}injectableDefInScope(e){if(!e.providedIn)return!1;let n=L1(e.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(e){let n=this._onDestroyHooks.indexOf(e);n!==-1&&this._onDestroyHooks.splice(n,1)}};function x5(t){let e=pe(t),n=e!==null?e.factory:w3(t);if(n!==null)return n;if(t instanceof w)throw new S(204,!1);if(t instanceof Function)return Rm(t);throw new S(204,!1)}function Rm(t){if(t.length>0)throw new S(204,!1);let n=Zh(t);return n!==null?()=>n.factory(t):()=>new t}function Hm(t){if(us(t))return c4(void 0,t.useValue);{let e=ds(t);return c4(e,O6)}}function ds(t,e,n){let c;if(f4(t)){let s=L1(t);return w3(s)||x5(s)}else if(us(t))c=()=>L1(t.useValue);else if(Pm(t))c=()=>t.useFactory(...v5(t.deps||[]));else if(Fm(t))c=()=>L(L1(t.useExisting));else{let s=L1(t&&(t.useClass||t.provide));if(jm(t))c=()=>new s(...v5(t.deps));else return w3(s)||x5(s)}return c}function c4(t,e,n=!1){return{factory:t,value:e,multi:n?[]:void 0}}function jm(t){return!!t.deps}function Bm(t){return t!==null&&typeof t=="object"&&typeof t.ngOnDestroy=="function"}function Um(t){return typeof t=="function"||typeof t=="object"&&t instanceof w}function N5(t,e){for(let n of t)Array.isArray(n)?N5(n,e):n&&G9(n)?N5(n.\u0275providers,e):e(n)}function k7(t,e){t instanceof o0&&t.assertNotDestroyed();let n,c=W2(t),s=S1(void 0);try{return e()}finally{W2(c),S1(s)}}function hs(){return W9()!==void 0||im()!=null}function $m(t){if(!hs())throw new S(-203,!1)}function qm(t){return typeof t=="function"}var P1=0,T=1,E=2,f1=3,t2=4,O1=5,$1=6,i0=7,q1=8,u4=9,n2=10,$=11,r0=12,i9=13,g4=14,V1=15,p0=16,n4=17,c2=18,ze=19,ms=20,Z2=21,c5=22,D3=23,e1=25,ps=1,a0=6,w2=7,Z6=8,d4=9,b1=10,F7=function(t){return t[t.None=0]="None",t[t.HasTransplantedViews=2]="HasTransplantedViews",t}(F7||{});function x2(t){return Array.isArray(t)&&typeof t[ps]=="object"}function g2(t){return Array.isArray(t)&&t[ps]===!0}function P7(t){return(t.flags&4)!==0}function g0(t){return t.componentOffset>-1}function Le(t){return(t.flags&1)===1}function Y2(t){return!!t.template}function gs(t){return(t[E]&512)!==0}var w5=class{constructor(e,n,c){this.previousValue=e,this.currentValue=n,this.firstChange=c}isFirstChange(){return this.firstChange}};function Ms(t,e,n,c){e!==null?e.applyValueToInputSignal(e,c):t[n]=c}function o2(){return Cs}function Cs(t){return t.type.prototype.ngOnChanges&&(t.setInput=Wm),Gm}o2.ngInherit=!0;function Gm(){let t=Ls(this),e=t==null?void 0:t.current;if(e){let n=t.previous;if(n===a4)t.previous=e;else for(let c in e)n[c]=e[c];t.current=null,this.ngOnChanges(e)}}function Wm(t,e,n,c,s){let o=this.declaredInputs[c],i=Ls(t)||Zm(t,{previous:a4,current:null}),r=i.current||(i.current={}),a=i.previous,l=a[o];r[o]=new w5(l&&l.currentValue,n,a===a4),Ms(t,e,s,n)}var zs="__ngSimpleChanges__";function Ls(t){return t[zs]||null}function Zm(t,e){return t[zs]=e}var r9=null;var f2=function(t,e,n){r9!=null&&r9(t,e,n)},vs="svg",Ym="math",Qm=!1;function Km(){return Qm}function s2(t){for(;Array.isArray(t);)t=t[P1];return t}function ys(t,e){return s2(e[t])}function R1(t,e){return s2(e[t.index])}function V7(t,e){return t.data[e]}function ve(t,e){return t[e]}function X2(t,e){let n=e[t];return x2(n)?n:n[P1]}function Xm(t){return(t[E]&4)===4}function O7(t){return(t[E]&128)===128}function Jm(t){return g2(t[f1])}function h4(t,e){return e==null?null:t[e]}function bs(t){t[n4]=0}function ep(t){t[E]&1024||(t[E]|=1024,O7(t)&&l0(t))}function tp(t,e){for(;t>0;)e=e[g4],t--;return e}function R7(t){var e;return!!(t[E]&9216||(e=t[D3])!=null&&e.dirty)}function D5(t){var e,n;(e=t[n2].changeDetectionScheduler)==null||e.notify(1),R7(t)?l0(t):t[E]&64&&(Km()?(t[E]|=1024,l0(t)):(n=t[n2].changeDetectionScheduler)==null||n.notify())}function l0(t){var n;(n=t[n2].changeDetectionScheduler)==null||n.notify();let e=f0(t);for(;e!==null&&!(e[E]&8192||(e[E]|=8192,!O7(e)));)e=f0(e)}function xs(t,e){if((t[E]&256)===256)throw new S(911,!1);t[Z2]===null&&(t[Z2]=[]),t[Z2].push(e)}function cp(t,e){if(t[Z2]===null)return;let n=t[Z2].indexOf(e);n!==-1&&t[Z2].splice(n,1)}function f0(t){let e=t[f1];return g2(e)?e[f1]:e}var _={lFrame:As(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function np(){return _.lFrame.elementDepthCount}function sp(){_.lFrame.elementDepthCount++}function op(){_.lFrame.elementDepthCount--}function Ns(){return _.bindingsEnabled}function M4(){return _.skipHydrationRootTNode!==null}function ip(t){return _.skipHydrationRootTNode===t}function rp(t){_.skipHydrationRootTNode=t}function ap(){_.skipHydrationRootTNode=null}function D(){return _.lFrame.lView}function K(){return _.lFrame.tView}function G_(t){return _.lFrame.contextLView=t,t[q1]}function W_(t){return _.lFrame.contextLView=null,t}function p1(){let t=ws();for(;t!==null&&t.type===64;)t=t.parent;return t}function ws(){return _.lFrame.currentTNode}function lp(){let t=_.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function P3(t,e){let n=_.lFrame;n.currentTNode=t,n.isParent=e}function H7(){return _.lFrame.isParent}function j7(){_.lFrame.isParent=!1}function fp(){return _.lFrame.contextLView}function E2(){let t=_.lFrame,e=t.bindingRootIndex;return e===-1&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function M0(){return _.lFrame.bindingIndex}function up(t){return _.lFrame.bindingIndex=t}function C4(){return _.lFrame.bindingIndex++}function V3(t){let e=_.lFrame,n=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,n}function dp(){return _.lFrame.inI18n}function hp(t,e){let n=_.lFrame;n.bindingIndex=n.bindingRootIndex=t,S5(e)}function mp(){return _.lFrame.currentDirectiveIndex}function S5(t){_.lFrame.currentDirectiveIndex=t}function pp(t){let e=_.lFrame.currentDirectiveIndex;return e===-1?null:t[e]}function Ds(){return _.lFrame.currentQueryIndex}function B7(t){_.lFrame.currentQueryIndex=t}function gp(t){let e=t[T];return e.type===2?e.declTNode:e.type===1?t[O1]:null}function Ss(t,e,n){if(n&R.SkipSelf){let s=e,o=t;for(;s=s.parent,s===null&&!(n&R.Host);)if(s=gp(o),s===null||(o=o[g4],s.type&10))break;if(s===null)return!1;e=s,t=o}let c=_.lFrame=Es();return c.currentTNode=e,c.lView=t,!0}function U7(t){let e=Es(),n=t[T];_.lFrame=e,e.currentTNode=n.firstChild,e.lView=t,e.tView=n,e.contextLView=t,e.bindingIndex=n.bindingStartIndex,e.inI18n=!1}function Es(){let t=_.lFrame,e=t===null?null:t.child;return e===null?As(t):e}function As(t){let e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return t!==null&&(t.child=e),e}function Is(){let t=_.lFrame;return _.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}var Ts=Is;function $7(){let t=Is();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function Mp(t){return(_.lFrame.contextLView=tp(t,_.lFrame.contextLView))[q1]}function G1(){return _.lFrame.selectedIndex}function S3(t){_.lFrame.selectedIndex=t}function C0(){let t=_.lFrame;return V7(t.tView,t.selectedIndex)}function Z_(){_.lFrame.currentNamespace=vs}function Y_(){Cp()}function Cp(){_.lFrame.currentNamespace=null}function _s(){return _.lFrame.currentNamespace}var ks=!0;function ye(){return ks}function M2(t){ks=t}function zp(t,e,n){var i,r,a,l,f;let{ngOnChanges:c,ngOnInit:s,ngDoCheck:o}=e.type.prototype;if(c){let u=Cs(e);((i=n.preOrderHooks)!=null?i:n.preOrderHooks=[]).push(t,u),((r=n.preOrderCheckHooks)!=null?r:n.preOrderCheckHooks=[]).push(t,u)}s&&((a=n.preOrderHooks)!=null?a:n.preOrderHooks=[]).push(0-t,s),o&&(((l=n.preOrderHooks)!=null?l:n.preOrderHooks=[]).push(t,o),((f=n.preOrderCheckHooks)!=null?f:n.preOrderCheckHooks=[]).push(t,o))}function be(t,e){var n,c,s,o,i,r,a;for(let l=e.directiveStart,f=e.directiveEnd;l<f;l++){let d=t.data[l].type.prototype,{ngAfterContentInit:h,ngAfterContentChecked:m,ngAfterViewInit:C,ngAfterViewChecked:M,ngOnDestroy:g}=d;h&&((n=t.contentHooks)!=null?n:t.contentHooks=[]).push(-l,h),m&&(((c=t.contentHooks)!=null?c:t.contentHooks=[]).push(l,m),((s=t.contentCheckHooks)!=null?s:t.contentCheckHooks=[]).push(l,m)),C&&((o=t.viewHooks)!=null?o:t.viewHooks=[]).push(-l,C),M&&(((i=t.viewHooks)!=null?i:t.viewHooks=[]).push(l,M),((r=t.viewCheckHooks)!=null?r:t.viewCheckHooks=[]).push(l,M)),g!=null&&((a=t.destroyHooks)!=null?a:t.destroyHooks=[]).push(l,g)}}function R6(t,e,n){Fs(t,e,3,n)}function H6(t,e,n,c){(t[E]&3)===n&&Fs(t,e,n,c)}function n5(t,e){let n=t[E];(n&3)===e&&(n&=16383,n+=1,t[E]=n)}function Fs(t,e,n,c){let s=c!==void 0?t[n4]&65535:0,o=c!=null?c:-1,i=e.length-1,r=0;for(let a=s;a<i;a++)if(typeof e[a+1]=="number"){if(r=e[a],c!=null&&r>=c)break}else e[a]<0&&(t[n4]+=65536),(r<o||o==-1)&&(Lp(t,n,e,a),t[n4]=(t[n4]&**********)+a+2),a++}function a9(t,e){f2(4,t,e);let n=O(null);try{e.call(t)}finally{O(n),f2(5,t,e)}}function Lp(t,e,n,c){let s=n[c]<0,o=n[c+1],i=s?-n[c]:n[c],r=t[i];s?t[E]>>14<t[n4]>>16&&(t[E]&3)===e&&(t[E]+=16384,a9(r,o)):a9(r,o)}var r4=-1,E3=class{constructor(e,n,c){this.factory=e,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=c}};function vp(t){return t instanceof E3}function yp(t){return(t.flags&8)!==0}function bp(t){return(t.flags&16)!==0}function Ps(t){return t!==r4}function Y6(t){return t&32767}function xp(t){return t>>16}function Q6(t,e){let n=xp(t),c=e;for(;n>0;)c=c[g4],n--;return c}var E5=!0;function K6(t){let e=E5;return E5=t,e}var Np=256,Vs=Np-1,Os=5,wp=0,u2={};function Dp(t,e,n){let c;typeof n=="string"?c=n.charCodeAt(0)||0:n.hasOwnProperty(c0)&&(c=n[c0]),c==null&&(c=n[c0]=wp++);let s=c&Vs,o=1<<s;e.data[t+(s>>Os)]|=o}function X6(t,e){let n=Rs(t,e);if(n!==-1)return n;let c=e[T];c.firstCreatePass&&(t.injectorIndex=e.length,s5(c.data,t),s5(e,null),s5(c.blueprint,null));let s=q7(t,e),o=t.injectorIndex;if(Ps(s)){let i=Y6(s),r=Q6(s,e),a=r[T].data;for(let l=0;l<8;l++)e[o+l]=r[i+l]|a[i+l]}return e[o+8]=s,o}function s5(t,e){t.push(0,0,0,0,0,0,0,0,e)}function Rs(t,e){return t.injectorIndex===-1||t.parent&&t.parent.injectorIndex===t.injectorIndex||e[t.injectorIndex+8]===null?-1:t.injectorIndex}function q7(t,e){if(t.parent&&t.parent.injectorIndex!==-1)return t.parent.injectorIndex;let n=0,c=null,s=e;for(;s!==null;){if(c=$s(s),c===null)return r4;if(n++,s=s[g4],c.injectorIndex!==-1)return c.injectorIndex|n<<16}return r4}function A5(t,e,n){Dp(t,e,n)}function Sp(t,e){if(e==="class")return t.classes;if(e==="style")return t.styles;let n=t.attrs;if(n){let c=n.length,s=0;for(;s<c;){let o=n[s];if(ts(o))break;if(o===0)s=s+2;else if(typeof o=="number")for(s++;s<c&&typeof n[s]=="string";)s++;else{if(o===e)return n[s+1];s=s+2}}}return null}function Hs(t,e,n){if(n&R.Optional||t!==void 0)return t;D7(e,"NodeInjector")}function js(t,e,n,c){if(n&R.Optional&&c===void 0&&(c=null),!(n&(R.Self|R.Host))){let s=t[u4],o=S1(void 0);try{return s?s.get(e,c,n&R.Optional):Z9(e,c,n&R.Optional)}finally{S1(o)}}return Hs(c,e,n)}function Bs(t,e,n,c=R.Default,s){if(t!==null){if(e[E]&2048&&!(c&R.Self)){let i=Tp(t,e,n,c,u2);if(i!==u2)return i}let o=Us(t,e,n,c,u2);if(o!==u2)return o}return js(e,n,c,s)}function Us(t,e,n,c,s){let o=Ap(n);if(typeof o=="function"){if(!Ss(e,t,c))return c&R.Host?Hs(s,n,c):js(e,n,c,s);try{let i;if(i=o(c),i==null&&!(c&R.Optional))D7(n);else return i}finally{Ts()}}else if(typeof o=="number"){let i=null,r=Rs(t,e),a=r4,l=c&R.Host?e[V1][O1]:null;for((r===-1||c&R.SkipSelf)&&(a=r===-1?q7(t,e):e[r+8],a===r4||!f9(c,!1)?r=-1:(i=e[T],r=Y6(a),e=Q6(a,e)));r!==-1;){let f=e[T];if(l9(o,r,f.data)){let u=Ep(r,e,n,i,c,l);if(u!==u2)return u}a=e[r+8],a!==r4&&f9(c,e[T].data[r+8]===l)&&l9(o,r,e)?(i=f,r=Y6(a),e=Q6(a,e)):r=-1}}return s}function Ep(t,e,n,c,s,o){let i=e[T],r=i.data[t+8],a=c==null?g0(r)&&E5:c!=i&&(r.type&3)!==0,l=s&R.Host&&o===r,f=j6(r,i,n,a,l);return f!==null?A3(e,i,f,r):u2}function j6(t,e,n,c,s){let o=t.providerIndexes,i=e.data,r=o&1048575,a=t.directiveStart,l=t.directiveEnd,f=o>>20,u=c?r:r+f,d=s?r+f:l;for(let h=u;h<d;h++){let m=i[h];if(h<a&&n===m||h>=a&&m.type===n)return h}if(s){let h=i[a];if(h&&Y2(h)&&h.type===n)return a}return null}function A3(t,e,n,c){let s=t[n],o=e.data;if(vp(s)){let i=s;i.resolving&&tm(em(o[n]));let r=K6(i.canSeeViewProviders);i.resolving=!0;let a,l=i.injectImpl?S1(i.injectImpl):null,f=Ss(t,c,R.Default);try{s=t[n]=i.factory(void 0,o,t,c),e.firstCreatePass&&n>=c.directiveStart&&zp(n,o[n],e)}finally{l!==null&&S1(l),K6(r),i.resolving=!1,Ts()}}return s}function Ap(t){if(typeof t=="string")return t.charCodeAt(0)||0;let e=t.hasOwnProperty(c0)?t[c0]:void 0;return typeof e=="number"?e>=0?e&Vs:Ip:e}function l9(t,e,n){let c=1<<t;return!!(n[e+(t>>Os)]&c)}function f9(t,e){return!(t&R.Self)&&!(t&R.Host&&e)}var N3=class{constructor(e,n){this._tNode=e,this._lView=n}get(e,n,c){return Bs(this._tNode,this._lView,e,ge(c),n)}};function Ip(){return new N3(p1(),D())}function g1(t){return h0(()=>{let e=t.prototype.constructor,n=e[$6]||I5(e),c=Object.prototype,s=Object.getPrototypeOf(t.prototype).constructor;for(;s&&s!==c;){let o=s[$6]||I5(s);if(o&&o!==n)return o;s=Object.getPrototypeOf(s)}return o=>new o})}function I5(t){return U9(t)?()=>{let e=I5(L1(t));return e&&e()}:w3(t)}function Tp(t,e,n,c,s){let o=t,i=e;for(;o!==null&&i!==null&&i[E]&2048&&!(i[E]&512);){let r=Us(o,i,n,c|R.Self,u2);if(r!==u2)return r;let a=o.parent;if(!a){let l=i[ms];if(l){let f=l.get(n,u2,c);if(f!==u2)return f}a=$s(i),i=i[g4]}o=a}return s}function $s(t){let e=t[T],n=e.type;return n===2?e.declTNode:n===1?t[O1]:null}function _p(t){return Sp(p1(),t)}function u9(t,e=null,n=null,c){let s=qs(t,e,n,c);return s.resolveInjectorInitializers(),s}function qs(t,e=null,n=null,c,s=new Set){let o=[n||v1,_m(t)];return c=c||(typeof t=="object"?void 0:y1(t)),new o0(o,e||Ce(),c||null,s)}var J2=(()=>{let e=class e{static create(c,s){var o;if(Array.isArray(c))return u9({name:""},s,c,"");{let i=(o=c.name)!=null?o:"";return u9({name:i},c.parent,c.providers,i)}}};e.THROW_IF_NOT_FOUND=n0,e.NULL=new W6,e.\u0275prov=y({token:e,providedIn:"any",factory:()=>L(X9)}),e.__NG_ELEMENT_ID__=-1;let t=e;return t})();var kp="ngOriginalError";function o5(t){return t[kp]}var m2=class{constructor(){this._console=console}handleError(e){let n=this._findOriginalError(e);this._console.error("ERROR",e),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(e){let n=e&&o5(e);for(;n&&o5(n);)n=o5(n);return n||null}},Gs=new w("",{providedIn:"root",factory:()=>x(m2).handleError.bind(void 0)}),G7=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=Fp,e.__NG_ENV_ID__=c=>c;let t=e;return t})(),T5=class extends G7{constructor(e){super(),this._lView=e}onDestroy(e){return xs(this._lView,e),()=>cp(this._lView,e)}};function Fp(){return new T5(D())}function d9(t,e){return j9(t,e)}function Pp(t){return j9(H9,t)}var Q_=(d9.required=Pp,d9);function Vp(){return z4(p1(),D())}function z4(t,e){return new h1(R1(t,e))}var h1=(()=>{let e=class e{constructor(c){this.nativeElement=c}};e.__NG_ELEMENT_ID__=Vp;let t=e;return t})();function Op(t){return t instanceof h1?t.nativeElement:t}var _5=class extends U1{constructor(e=!1){var n;super(),this.destroyRef=void 0,this.__isAsync=e,hs()&&(this.destroyRef=(n=x(G7,{optional:!0}))!=null?n:void 0)}emit(e){let n=O(null);try{super.next(e)}finally{O(n)}}subscribe(e,n,c){var a,l,f;let s=e,o=n||(()=>null),i=c;if(e&&typeof e=="object"){let u=e;s=(a=u.next)==null?void 0:a.bind(u),o=(l=u.error)==null?void 0:l.bind(u),i=(f=u.complete)==null?void 0:f.bind(u)}this.__isAsync&&(o=i5(o),s&&(s=i5(s)),i&&(i=i5(i)));let r=super.subscribe({next:s,error:o,complete:i});return e instanceof t1&&e.add(r),r}};function i5(t){return e=>{setTimeout(t,void 0,e)}}var l1=_5;function Rp(){return this._results[Symbol.iterator]()}var k5=class t{get changes(){var e;return(e=this._changes)!=null?e:this._changes=new l1}constructor(e=!1){this._emitDistinctChangesOnly=e,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=t.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=Rp)}get(e){return this._results[e]}map(e){return this._results.map(e)}filter(e){return this._results.filter(e)}find(e){return this._results.find(e)}reduce(e,n){return this._results.reduce(e,n)}forEach(e){this._results.forEach(e)}some(e){return this._results.some(e)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(e,n){this.dirty=!1;let c=dm(e);(this._changesDetected=!um(this._results,c,n))&&(this._results=c,this.length=c.length,this.last=c[this.length-1],this.first=c[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(e){this._onDirty=e}setDirty(){var e;this.dirty=!0,(e=this._onDirty)==null||e.call(this)}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}},Hp="ngSkipHydration",jp="ngskiphydration";function Ws(t){let e=t.mergedAttrs;if(e===null)return!1;for(let n=0;n<e.length;n+=2){let c=e[n];if(typeof c=="number")return!1;if(typeof c=="string"&&c.toLowerCase()===jp)return!0}return!1}function Zs(t){return t.hasAttribute(Hp)}function J6(t){return(t.flags&128)===128}function Bp(t){if(J6(t))return!0;let e=t.parent;for(;e;){if(J6(t)||Ws(e))return!0;e=e.parent}return!1}var Ys=new Map,Up=0;function $p(){return Up++}function qp(t){Ys.set(t[ze],t)}function Gp(t){Ys.delete(t[ze])}var h9="__ngContext__";function Q2(t,e){x2(e)?(t[h9]=e[ze],qp(e)):t[h9]=e}function Qs(t){return Xs(t[r0])}function Ks(t){return Xs(t[t2])}function Xs(t){for(;t!==null&&!g2(t);)t=t[t2];return t}var F5;function Js(t){F5=t}function z0(){if(F5!==void 0)return F5;if(typeof document<"u")return document;throw new S(210,!1)}var xe=new w("",{providedIn:"root",factory:()=>Wp}),Wp="ng",W7=new w(""),T1=new w("",{providedIn:"platform",factory:()=>"unknown"});var Z7=new w("",{providedIn:"root",factory:()=>{var t,e;return((e=(t=z0().body)==null?void 0:t.querySelector("[ngCspNonce]"))==null?void 0:e.getAttribute("ngCspNonce"))||null}});function Zp(){let t=new O3;return x(T1)==="browser"&&(t.store=Yp(z0(),x(xe))),t}var O3=(()=>{let e=class e{constructor(){this.store={},this.onSerializeCallbacks={}}get(c,s){return this.store[c]!==void 0?this.store[c]:s}set(c,s){this.store[c]=s}remove(c){delete this.store[c]}hasKey(c){return this.store.hasOwnProperty(c)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(c,s){this.onSerializeCallbacks[c]=s}toJson(){for(let c in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(c))try{this.store[c]=this.onSerializeCallbacks[c]()}catch(s){console.warn("Exception in onSerialize callback: ",s)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}};e.\u0275prov=y({token:e,providedIn:"root",factory:Zp});let t=e;return t})();function Yp(t,e){let n=t.getElementById(e+"-state");if(n!=null&&n.textContent)try{return JSON.parse(n.textContent)}catch(c){console.warn("Exception while restoring TransferState for app "+e,c)}return{}}var eo="h",to="b",P5=function(t){return t.FirstChild="f",t.NextSibling="n",t}(P5||{}),Qp="e",Kp="t",Y7="c",co="x",ee="r",Xp="i",Jp="n",eg="d",tg="__nghData__",no=tg,r5="ngh",cg="nghm",so=()=>null;function ng(t,e,n=!1){var f;let c=t.getAttribute(r5);if(c==null)return null;let[s,o]=c.split("|");if(c=n?o:s,!c)return null;let i=o?`|${o}`:"",r=n?s:i,a={};if(c!==""){let u=e.get(O3,null,{optional:!0});u!==null&&(a=u.get(no,[])[Number(c)])}let l={data:a,firstChild:(f=t.firstChild)!=null?f:null};return n&&(l.firstChild=t,Ne(l,0,t.nextSibling)),r?t.setAttribute(r5,r):t.removeAttribute(r5),l}function sg(){so=ng}function Q7(t,e,n=!1){return so(t,e,n)}function og(t){let e=t._lView;return e[T].type===2?null:(gs(e)&&(e=e[e1]),e)}function ig(t){var e;return(e=t.textContent)==null?void 0:e.replace(/\s/gm,"")}function rg(t){let e=z0(),n=e.createNodeIterator(t,NodeFilter.SHOW_COMMENT,{acceptNode(o){let i=ig(o);return i==="ngetn"||i==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),c,s=[];for(;c=n.nextNode();)s.push(c);for(let o of s)o.textContent==="ngetn"?o.replaceWith(e.createTextNode("")):o.remove()}function Ne(t,e,n){var c;(c=t.segmentHeads)!=null||(t.segmentHeads={}),t.segmentHeads[e]=n}function V5(t,e){var n,c;return(c=(n=t.segmentHeads)==null?void 0:n[e])!=null?c:null}function ag(t,e){var s,o,i;let n=t.data,c=(o=(s=n[Qp])==null?void 0:s[e])!=null?o:null;return c===null&&((i=n[Y7])!=null&&i[e])&&(c=K7(t,e)),c}function oo(t,e){var n,c;return(c=(n=t.data[Y7])==null?void 0:n[e])!=null?c:null}function K7(t,e){var s,o;let n=(s=oo(t,e))!=null?s:[],c=0;for(let i of n)c+=i[ee]*((o=i[co])!=null?o:1);return c}function we(t,e){var n;if(typeof t.disconnectedNodes>"u"){let c=t.data[eg];t.disconnectedNodes=c?new Set(c):null}return!!((n=t.disconnectedNodes)!=null&&n.has(e))}var T6=new w(""),io=!1,ro=new w("",{providedIn:"root",factory:()=>io}),lg=new w(""),_6;function fg(){if(_6===void 0&&(_6=null,F1.trustedTypes))try{_6=F1.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return _6}function De(t){var e;return((e=fg())==null?void 0:e.createHTML(t))||t}var k6;function ao(){if(k6===void 0&&(k6=null,F1.trustedTypes))try{k6=F1.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return k6}function m9(t){var e;return((e=ao())==null?void 0:e.createHTML(t))||t}function p9(t){var e;return((e=ao())==null?void 0:e.createScriptURL(t))||t}var D2=class{constructor(e){this.changingThisBreaksApplicationSecurity=e}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${R9})`}},O5=class extends D2{getTypeName(){return"HTML"}},R5=class extends D2{getTypeName(){return"Style"}},H5=class extends D2{getTypeName(){return"Script"}},j5=class extends D2{getTypeName(){return"URL"}},B5=class extends D2{getTypeName(){return"ResourceURL"}};function W1(t){return t instanceof D2?t.changingThisBreaksApplicationSecurity:t}function A2(t,e){let n=ug(t);if(n!=null&&n!==e){if(n==="ResourceURL"&&e==="URL")return!0;throw new Error(`Required a safe ${e}, got a ${n} (see ${R9})`)}return n===e}function ug(t){return t instanceof D2&&t.getTypeName()||null}function lo(t){return new O5(t)}function fo(t){return new R5(t)}function uo(t){return new H5(t)}function ho(t){return new j5(t)}function mo(t){return new B5(t)}function dg(t){let e=new $5(t);return hg()?new U5(e):e}var U5=class{constructor(e){this.inertDocumentHelper=e}getInertBodyElement(e){e="<body><remove></remove>"+e;try{let n=new window.DOMParser().parseFromString(De(e),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(e):(n.removeChild(n.firstChild),n)}catch{return null}}},$5=class{constructor(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(e){let n=this.inertDocument.createElement("template");return n.innerHTML=De(e),n}};function hg(){try{return!!new window.DOMParser().parseFromString(De(""),"text/html")}catch{return!1}}var mg=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Se(t){return t=String(t),t.match(mg)?t:"unsafe:"+t}function I2(t){let e={};for(let n of t.split(","))e[n]=!0;return e}function L0(...t){let e={};for(let n of t)for(let c in n)n.hasOwnProperty(c)&&(e[c]=!0);return e}var po=I2("area,br,col,hr,img,wbr"),go=I2("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Mo=I2("rp,rt"),pg=L0(Mo,go),gg=L0(go,I2("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Mg=L0(Mo,I2("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),g9=L0(po,gg,Mg,pg),Co=I2("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Cg=I2("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),zg=I2("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Lg=L0(Co,Cg,zg),vg=I2("script,style,template"),q5=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(e){let n=e.firstChild,c=!0,s=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?c=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,c&&n.firstChild){s.push(n),n=xg(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let o=bg(n);if(o){n=o;break}n=s.pop()}}return this.buf.join("")}startElement(e){let n=M9(e).toLowerCase();if(!g9.hasOwnProperty(n))return this.sanitizedSomething=!0,!vg.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let c=e.attributes;for(let s=0;s<c.length;s++){let o=c.item(s),i=o.name,r=i.toLowerCase();if(!Lg.hasOwnProperty(r)){this.sanitizedSomething=!0;continue}let a=o.value;Co[r]&&(a=Se(a)),this.buf.push(" ",i,'="',C9(a),'"')}return this.buf.push(">"),!0}endElement(e){let n=M9(e).toLowerCase();g9.hasOwnProperty(n)&&!po.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(e){this.buf.push(C9(e))}};function yg(t,e){return(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function bg(t){let e=t.nextSibling;if(e&&t!==e.previousSibling)throw zo(e);return e}function xg(t){let e=t.firstChild;if(e&&yg(t,e))throw zo(e);return e}function M9(t){let e=t.nodeName;return typeof e=="string"?e:"FORM"}function zo(t){return new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`)}var Ng=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,wg=/([^\#-~ |!])/g;function C9(t){return t.replace(/&/g,"&amp;").replace(Ng,function(e){let n=e.charCodeAt(0),c=e.charCodeAt(1);return"&#"+((n-55296)*1024+(c-56320)+65536)+";"}).replace(wg,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var F6;function X7(t,e){let n=null;try{F6=F6||dg(t);let c=e?String(e):"";n=F6.getInertBodyElement(c);let s=5,o=c;do{if(s===0)throw new Error("Failed to sanitize html because the input is unstable");s--,c=o,o=n.innerHTML,n=F6.getInertBodyElement(c)}while(c!==o);let r=new q5().sanitizeChildren(z9(n)||n);return De(r)}finally{if(n){let c=z9(n)||n;for(;c.firstChild;)c.removeChild(c.firstChild)}}}function z9(t){return"content"in t&&Dg(t)?t.content:null}function Dg(t){return t.nodeType===Node.ELEMENT_NODE&&t.nodeName==="TEMPLATE"}var i2=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(i2||{});function Lo(t){let e=J7();return e?m9(e.sanitize(i2.HTML,t)||""):A2(t,"HTML")?m9(W1(t)):X7(z0(),Y(t))}function Sg(t){let e=J7();return e?e.sanitize(i2.URL,t)||"":A2(t,"URL")?W1(t):Se(Y(t))}function Eg(t){let e=J7();if(e)return p9(e.sanitize(i2.RESOURCE_URL,t)||"");if(A2(t,"ResourceURL"))return p9(W1(t));throw new S(904,!1)}function Ag(t,e){return e==="src"&&(t==="embed"||t==="frame"||t==="iframe"||t==="media"||t==="script")||e==="href"&&(t==="base"||t==="link")?Eg:Sg}function K_(t,e,n){return Ag(e,n)(t)}function J7(){let t=D();return t&&t[n2].sanitizer}var Ig=/^>|^->|<!--|-->|--!>|<!-$/g,Tg=/(<|>)/g,_g="\u200B$1\u200B";function kg(t){return t.replace(Ig,e=>e.replace(Tg,_g))}function X_(t){return t.ownerDocument.defaultView}function J_(t){return t.ownerDocument}function Fg(t){return t.ownerDocument.body}function vo(t){return t instanceof Function?t():t}function t0(t){return(t!=null?t:x(J2)).get(T1)==="browser"}var p2=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(p2||{}),Pg;function et(t,e){return Pg(t,e)}function s4(t,e,n,c,s){if(c!=null){let o,i=!1;g2(c)?o=c:x2(c)&&(i=!0,c=c[P1]);let r=s2(c);t===0&&n!==null?s==null?No(e,n,r):ce(e,n,r,s||null,!0):t===1&&n!==null?ce(e,n,r,s||null,!0):t===2?ot(e,r,i):t===3&&e.destroyNode(r),o!=null&&Kg(e,t,o,n,s)}}function tt(t,e){return t.createText(e)}function Vg(t,e,n){t.setValue(e,n)}function ct(t,e){return t.createComment(kg(e))}function Ee(t,e,n){return t.createElement(e,n)}function Og(t,e){yo(t,e),e[P1]=null,e[O1]=null}function Rg(t,e,n,c,s,o){c[P1]=s,c[O1]=e,Ie(t,c,n,1,s,o)}function yo(t,e){var n;(n=e[n2].changeDetectionScheduler)==null||n.notify(1),Ie(t,e,e[$],2,null,null)}function Hg(t){let e=t[r0];if(!e)return a5(t[T],t);for(;e;){let n=null;if(x2(e))n=e[r0];else{let c=e[b1];c&&(n=c)}if(!n){for(;e&&!e[t2]&&e!==t;)x2(e)&&a5(e[T],e),e=e[f1];e===null&&(e=t),x2(e)&&a5(e[T],e),n=e&&e[t2]}e=n}}function jg(t,e,n,c){let s=b1+c,o=n.length;c>0&&(n[s-1][t2]=e),c<o-b1?(e[t2]=n[s],K9(n,b1+c,e)):(n.push(e),e[t2]=null),e[f1]=n;let i=e[p0];i!==null&&n!==i&&Bg(i,e);let r=e[c2];r!==null&&r.insertView(t),D5(e),e[E]|=128}function Bg(t,e){let n=t[d4],s=e[f1][f1][V1];e[V1]!==s&&(t[E]|=F7.HasTransplantedViews),n===null?t[d4]=[e]:n.push(e)}function bo(t,e){let n=t[d4],c=n.indexOf(e);n.splice(c,1)}function te(t,e){if(t.length<=b1)return;let n=b1+e,c=t[n];if(c){let s=c[p0];s!==null&&s!==t&&bo(s,c),e>0&&(t[n-1][t2]=c[t2]);let o=G6(t,b1+e);Og(c[T],c);let i=o[c2];i!==null&&i.detachView(o[T]),c[f1]=null,c[t2]=null,c[E]&=-129}return c}function nt(t,e){if(!(e[E]&256)){let n=e[$];n.destroyNode&&Ie(t,e,n,3,null,null),Hg(e)}}function a5(t,e){if(e[E]&256)return;let n=O(null);try{e[E]&=-129,e[E]|=256,e[D3]&&Bn(e[D3]),$g(t,e),Ug(t,e),e[T].type===1&&e[$].destroy();let c=e[p0];if(c!==null&&g2(e[f1])){c!==e[f1]&&bo(c,e);let s=e[c2];s!==null&&s.detachView(t)}Gp(e)}finally{O(n)}}function Ug(t,e){let n=t.cleanup,c=e[i0];if(n!==null)for(let o=0;o<n.length-1;o+=2)if(typeof n[o]=="string"){let i=n[o+3];i>=0?c[i]():c[-i].unsubscribe(),o+=2}else{let i=c[n[o+1]];n[o].call(i)}c!==null&&(e[i0]=null);let s=e[Z2];if(s!==null){e[Z2]=null;for(let o=0;o<s.length;o++){let i=s[o];i()}}}function $g(t,e){let n;if(t!=null&&(n=t.destroyHooks)!=null)for(let c=0;c<n.length;c+=2){let s=e[n[c]];if(!(s instanceof E3)){let o=n[c+1];if(Array.isArray(o))for(let i=0;i<o.length;i+=2){let r=s[o[i]],a=o[i+1];f2(4,r,a);try{a.call(r)}finally{f2(5,r,a)}}else{f2(4,s,o);try{o.call(s)}finally{f2(5,s,o)}}}}}function xo(t,e,n){return qg(t,e.parent,n)}function qg(t,e,n){let c=e;for(;c!==null&&c.type&40;)e=c,c=e.parent;if(c===null)return n[P1];{let{componentOffset:s}=c;if(s>-1){let{encapsulation:o}=t.data[c.directiveStart+s];if(o===d2.None||o===d2.Emulated)return null}return R1(c,n)}}function ce(t,e,n,c,s){t.insertBefore(e,n,c,s)}function No(t,e,n){t.appendChild(e,n)}function L9(t,e,n,c,s){c!==null?ce(t,e,n,c,s):No(t,e,n)}function Gg(t,e,n,c){t.removeChild(e,n,c)}function st(t,e){return t.parentNode(e)}function Wg(t,e){return t.nextSibling(e)}function wo(t,e,n){return Yg(t,e,n)}function Zg(t,e,n){return t.type&40?R1(t,n):null}var Yg=Zg,v9;function Ae(t,e,n,c){let s=xo(t,c,e),o=e[$],i=c.parent||e[O1],r=wo(i,c,e);if(s!=null)if(Array.isArray(n))for(let a=0;a<n.length;a++)L9(o,s,n[a],r,!1);else L9(o,s,n,r,!1);v9!==void 0&&v9(o,c,e,n,s)}function B6(t,e){if(e!==null){let n=e.type;if(n&3)return R1(e,t);if(n&4)return G5(-1,t[e.index]);if(n&8){let c=e.child;if(c!==null)return B6(t,c);{let s=t[e.index];return g2(s)?G5(-1,s):s2(s)}}else{if(n&32)return et(e,t)()||s2(t[e.index]);{let c=Do(t,e);if(c!==null){if(Array.isArray(c))return c[0];let s=f0(t[V1]);return B6(s,c)}else return B6(t,e.next)}}}return null}function Do(t,e){if(e!==null){let c=t[V1][O1],s=e.projection;return c.projection[s]}return null}function G5(t,e){let n=b1+t+1;if(n<e.length){let c=e[n],s=c[T].firstChild;if(s!==null)return B6(c,s)}return e[w2]}function ot(t,e,n){let c=st(t,e);c&&Gg(t,c,e,n)}function So(t){t.textContent=""}function it(t,e,n,c,s,o,i){for(;n!=null;){let r=c[n.index],a=n.type;if(i&&e===0&&(r&&Q2(s2(r),c),n.flags|=2),(n.flags&32)!==32)if(a&8)it(t,e,n.child,c,s,o,!1),s4(e,t,s,r,o);else if(a&32){let l=et(n,c),f;for(;f=l();)s4(e,t,s,f,o);s4(e,t,s,r,o)}else a&16?Eo(t,e,c,n,s,o):s4(e,t,s,r,o);n=i?n.projectionNext:n.next}}function Ie(t,e,n,c,s,o){it(n,c,t.firstChild,e,s,o,!1)}function Qg(t,e,n){let c=e[$],s=xo(t,n,e),o=n.parent||e[O1],i=wo(o,n,e);Eo(c,0,e,n,s,i)}function Eo(t,e,n,c,s,o){let i=n[V1],a=i[O1].projection[c.projection];if(Array.isArray(a))for(let l=0;l<a.length;l++){let f=a[l];s4(e,t,s,f,o)}else{let l=a,f=i[f1];J6(c)&&(l.flags|=128),it(t,e,l,f,s,o,!0)}}function Kg(t,e,n,c,s){let o=n[w2],i=s2(n);o!==i&&s4(e,t,c,o,s);for(let r=b1;r<n.length;r++){let a=n[r];Ie(a[T],a,t,e,c,o)}}function Xg(t,e,n,c,s){if(e)s?t.addClass(n,c):t.removeClass(n,c);else{let o=c.indexOf("-")===-1?void 0:p2.DashCase;s==null?t.removeStyle(n,c,o):(typeof s=="string"&&s.endsWith("!important")&&(s=s.slice(0,-10),o|=p2.Important),t.setStyle(n,c,s,o))}}function Jg(t,e,n){t.setAttribute(e,"style",n)}function Ao(t,e,n){n===""?t.removeAttribute(e,"class"):t.setAttribute(e,"class",n)}function Io(t,e,n){let{mergedAttrs:c,classes:s,styles:o}=n;c!==null&&y5(t,e,c),s!==null&&Ao(t,e,s),o!==null&&Jg(t,e,o)}var c1={};function ek(t=1){To(K(),D(),G1()+t,!1)}function To(t,e,n,c){if(!c)if((e[E]&3)===3){let o=t.preOrderCheckHooks;o!==null&&R6(e,o,n)}else{let o=t.preOrderHooks;o!==null&&H6(e,o,0,n)}S3(n)}function z(t,e=R.Default){let n=D();if(n===null)return L(t,e);let c=p1();return Bs(c,n,L1(t),e)}function tk(){let t="invalid";throw new Error(t)}function _o(t,e,n,c,s,o){let i=O(null);try{let r=null;s&d1.SignalBased&&(r=e[c][q2]),r!==null&&r.transformFn!==void 0&&(o=r.transformFn(o)),s&d1.HasDecoratorInputTransform&&(o=t.inputTransforms[c].call(e,o)),t.setInput!==null?t.setInput(e,r,o,n,c):Ms(e,r,c,o)}finally{O(i)}}function eM(t,e){let n=t.hostBindingOpCodes;if(n!==null)try{for(let c=0;c<n.length;c++){let s=n[c];if(s<0)S3(~s);else{let o=s,i=n[++c],r=n[++c];hp(i,o);let a=e[o];r(2,a)}}}finally{S3(-1)}}function Te(t,e,n,c,s,o,i,r,a,l,f){let u=e.blueprint.slice();return u[P1]=s,u[E]=c|4|128|8|64,(l!==null||t&&t[E]&2048)&&(u[E]|=2048),bs(u),u[f1]=u[g4]=t,u[q1]=n,u[n2]=i||t&&t[n2],u[$]=r||t&&t[$],u[u4]=a||t&&t[u4]||null,u[O1]=o,u[ze]=$p(),u[$1]=f,u[ms]=l,u[V1]=e.type==2?t[V1]:u,u}function L4(t,e,n,c,s){let o=t.data[e];if(o===null)o=tM(t,e,n,c,s),dp()&&(o.flags|=32);else if(o.type&64){o.type=n,o.value=c,o.attrs=s;let i=lp();o.injectorIndex=i===null?-1:i.injectorIndex}return P3(o,!0),o}function tM(t,e,n,c,s){let o=ws(),i=H7(),r=i?o:o&&o.parent,a=t.data[e]=aM(t,r,n,e,c,s);return t.firstChild===null&&(t.firstChild=a),o!==null&&(i?o.child==null&&a.parent!==null&&(o.child=a):o.next===null&&(o.next=a,a.prev=o)),a}function ko(t,e,n,c){if(n===0)return-1;let s=e.length;for(let o=0;o<n;o++)e.push(c),t.blueprint.push(c),t.data.push(null);return s}function Fo(t,e,n,c,s){let o=G1(),i=c&2;try{S3(-1),i&&e.length>e1&&To(t,e,e1,!1),f2(i?2:0,s),n(c,s)}finally{S3(o),f2(i?3:1,s)}}function rt(t,e,n){if(P7(e)){let c=O(null);try{let s=e.directiveStart,o=e.directiveEnd;for(let i=s;i<o;i++){let r=t.data[i];if(r.contentQueries){let a=n[i];r.contentQueries(1,a,i)}}}finally{O(c)}}}function at(t,e,n){Ns()&&(mM(t,e,n,R1(n,e)),(n.flags&64)===64&&Ro(t,e,n))}function lt(t,e,n=R1){let c=e.localNames;if(c!==null){let s=e.index+1;for(let o=0;o<c.length;o+=2){let i=c[o+1],r=i===-1?n(e,t):t[i];t[s++]=r}}}function Po(t){let e=t.tView;return e===null||e.incompleteFirstPass?t.tView=ft(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):e}function ft(t,e,n,c,s,o,i,r,a,l,f){let u=e1+c,d=u+s,h=cM(u,d),m=typeof l=="function"?l():l;return h[T]={type:t,blueprint:h,template:n,queries:null,viewQuery:r,declTNode:e,data:h.slice().fill(null,u),bindingStartIndex:u,expandoStartIndex:d,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof i=="function"?i():i,firstChild:null,schemas:a,consts:m,incompleteFirstPass:!1,ssrId:f}}function cM(t,e){let n=[];for(let c=0;c<e;c++)n.push(c<t?null:c1);return n}function nM(t,e,n,c){let o=c.get(ro,io)||n===d2.ShadowDom,i=t.selectRootElement(e,o);return sM(i),i}function sM(t){Vo(t)}var Vo=()=>null;function oM(t){Zs(t)?So(t):rg(t)}function iM(){Vo=oM}function rM(t,e,n,c){let s=Bo(e);s.push(n),t.firstCreatePass&&Uo(t).push(c,s.length-1)}function aM(t,e,n,c,s,o){let i=e?e.injectorIndex:-1,r=0;return M4()&&(r|=128),{type:n,index:c,insertBeforeIndex:null,injectorIndex:i,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:r,providerIndexes:0,value:s,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function y9(t,e,n,c,s){for(let o in e){if(!e.hasOwnProperty(o))continue;let i=e[o];if(i===void 0)continue;c!=null||(c={});let r,a=d1.None;Array.isArray(i)?(r=i[0],a=i[1]):r=i;let l=o;if(s!==null){if(!s.hasOwnProperty(o))continue;l=s[o]}t===0?b9(c,n,l,r,a):b9(c,n,l,r)}return c}function b9(t,e,n,c,s){let o;t.hasOwnProperty(n)?(o=t[n]).push(e,c):o=t[n]=[e,c],s!==void 0&&o.push(s)}function lM(t,e,n){let c=e.directiveStart,s=e.directiveEnd,o=t.data,i=e.attrs,r=[],a=null,l=null;for(let f=c;f<s;f++){let u=o[f],d=n?n.get(u):null,h=d?d.inputs:null,m=d?d.outputs:null;a=y9(0,u.inputs,f,a,h),l=y9(1,u.outputs,f,l,m);let C=a!==null&&i!==null&&!I7(e)?NM(a,f,i):null;r.push(C)}a!==null&&(a.hasOwnProperty("class")&&(e.flags|=8),a.hasOwnProperty("style")&&(e.flags|=16)),e.initialInputs=r,e.inputs=a,e.outputs=l}function fM(t){return t==="class"?"className":t==="for"?"htmlFor":t==="formaction"?"formAction":t==="innerHtml"?"innerHTML":t==="readonly"?"readOnly":t==="tabindex"?"tabIndex":t}function _e(t,e,n,c,s,o,i,r){let a=R1(e,n),l=e.inputs,f;!r&&l!=null&&(f=l[c])?(dt(t,n,f,c,s),g0(e)&&uM(n,e.index)):e.type&3?(c=fM(c),s=i!=null?i(s,e.value||"",c):s,o.setProperty(a,c,s)):e.type&12}function uM(t,e){let n=X2(e,t);n[E]&16||(n[E]|=64)}function ut(t,e,n,c){if(Ns()){let s=c===null?null:{"":-1},o=gM(t,n),i,r;o===null?i=r=null:[i,r]=o,i!==null&&Oo(t,e,n,i,s,r),s&&MM(n,c,s)}n.mergedAttrs=s0(n.mergedAttrs,n.attrs)}function Oo(t,e,n,c,s,o){var l,f;for(let u=0;u<c.length;u++)A5(X6(n,e),t,c[u].type);zM(n,t.data.length,c.length);for(let u=0;u<c.length;u++){let d=c[u];d.providersResolver&&d.providersResolver(d)}let i=!1,r=!1,a=ko(t,e,c.length,null);for(let u=0;u<c.length;u++){let d=c[u];n.mergedAttrs=s0(n.mergedAttrs,d.hostAttrs),LM(t,n,e,a,d),CM(a,d,s),d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let h=d.type.prototype;!i&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&(((l=t.preOrderHooks)!=null?l:t.preOrderHooks=[]).push(n.index),i=!0),!r&&(h.ngOnChanges||h.ngDoCheck)&&(((f=t.preOrderCheckHooks)!=null?f:t.preOrderCheckHooks=[]).push(n.index),r=!0),a++}lM(t,n,o)}function dM(t,e,n,c,s){let o=s.hostBindings;if(o){let i=t.hostBindingOpCodes;i===null&&(i=t.hostBindingOpCodes=[]);let r=~e.index;hM(i)!=r&&i.push(r),i.push(n,c,o)}}function hM(t){let e=t.length;for(;e>0;){let n=t[--e];if(typeof n=="number"&&n<0)return n}return 0}function mM(t,e,n,c){let s=n.directiveStart,o=n.directiveEnd;g0(n)&&vM(e,n,t.data[s+n.componentOffset]),t.firstCreatePass||X6(n,e),Q2(c,e);let i=n.initialInputs;for(let r=s;r<o;r++){let a=t.data[r],l=A3(e,t,r,n);if(Q2(l,e),i!==null&&xM(e,r-s,l,a,n,i),Y2(a)){let f=X2(n.index,e);f[q1]=A3(e,t,r,n)}}}function Ro(t,e,n){let c=n.directiveStart,s=n.directiveEnd,o=n.index,i=mp();try{S3(o);for(let r=c;r<s;r++){let a=t.data[r],l=e[r];S5(r),(a.hostBindings!==null||a.hostVars!==0||a.hostAttrs!==null)&&pM(a,l)}}finally{S3(-1),S5(i)}}function pM(t,e){t.hostBindings!==null&&t.hostBindings(1,e)}function gM(t,e){var o;let n=t.directiveRegistry,c=null,s=null;if(n)for(let i=0;i<n.length;i++){let r=n[i];if(ns(e,r.selectors,!1))if(c||(c=[]),Y2(r))if(r.findHostDirectiveDefs!==null){let a=[];s=s||new Map,r.findHostDirectiveDefs(r,a,s),c.unshift(...a,r);let l=a.length;W5(t,e,l)}else c.unshift(r),W5(t,e,0);else s=s||new Map,(o=r.findHostDirectiveDefs)==null||o.call(r,r,c,s),c.push(r)}return c===null?null:[c,s]}function W5(t,e,n){var c;e.componentOffset=n,((c=t.components)!=null?c:t.components=[]).push(e.index)}function MM(t,e,n){if(e){let c=t.localNames=[];for(let s=0;s<e.length;s+=2){let o=n[e[s+1]];if(o==null)throw new S(-301,!1);c.push(e[s],o)}}}function CM(t,e,n){if(n){if(e.exportAs)for(let c=0;c<e.exportAs.length;c++)n[e.exportAs[c]]=t;Y2(e)&&(n[""]=t)}}function zM(t,e,n){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+n,t.providerIndexes=e}function LM(t,e,n,c,s){t.data[c]=s;let o=s.factory||(s.factory=w3(s.type,!0)),i=new E3(o,Y2(s),z);t.blueprint[c]=i,n[c]=i,dM(t,e,c,ko(t,n,s.hostVars,c1),s)}function vM(t,e,n){let c=R1(e,t),s=Po(n),o=t[n2].rendererFactory,i=16;n.signals?i=4096:n.onPush&&(i=64);let r=ke(t,Te(t,s,null,i,c,e,null,o.createRenderer(c,n),null,null,null));t[e.index]=r}function yM(t,e,n,c,s,o){let i=R1(t,e);bM(e[$],i,o,t.value,n,c,s)}function bM(t,e,n,c,s,o,i){if(o==null)t.removeAttribute(e,s,n);else{let r=i==null?Y(o):i(o,c||"",s);t.setAttribute(e,s,r,n)}}function xM(t,e,n,c,s,o){let i=o[e];if(i!==null)for(let r=0;r<i.length;){let a=i[r++],l=i[r++],f=i[r++],u=i[r++];_o(c,n,a,l,f,u)}}function NM(t,e,n){let c=null,s=0;for(;s<n.length;){let o=n[s];if(o===0){s+=4;continue}else if(o===5){s+=2;continue}if(typeof o=="number")break;if(t.hasOwnProperty(o)){c===null&&(c=[]);let i=t[o];for(let r=0;r<i.length;r+=3)if(i[r]===e){c.push(o,i[r+1],i[r+2],n[s+1]);break}}s+=2}return c}function Ho(t,e,n,c){return[t,!0,0,e,null,c,null,n,null,null]}function jo(t,e){let n=t.contentQueries;if(n!==null){let c=O(null);try{for(let s=0;s<n.length;s+=2){let o=n[s],i=n[s+1];if(i!==-1){let r=t.data[i];B7(o),r.contentQueries(2,e[i],i)}}}finally{O(c)}}}function ke(t,e){return t[r0]?t[i9][t2]=e:t[r0]=e,t[i9]=e,e}function Z5(t,e,n){B7(0);let c=O(null);try{e(t,n)}finally{O(c)}}function Bo(t){return t[i0]||(t[i0]=[])}function Uo(t){return t.cleanup||(t.cleanup=[])}function $o(t,e){let n=t[u4],c=n?n.get(m2,null):null;c&&c.handleError(e)}function dt(t,e,n,c,s){for(let o=0;o<n.length;){let i=n[o++],r=n[o++],a=n[o++],l=e[i],f=t.data[i];_o(f,l,c,r,a,s)}}function v4(t,e,n){let c=ys(e,t);Vg(t[$],c,n)}function wM(t,e){let n=X2(e,t),c=n[T];DM(c,n);let s=n[P1];s!==null&&n[$1]===null&&(n[$1]=Q7(s,n[u4])),ht(c,n,n[q1])}function DM(t,e){for(let n=e.length;n<t.blueprint.length;n++)e.push(t.blueprint[n])}function ht(t,e,n){var c;U7(e);try{let s=t.viewQuery;s!==null&&Z5(1,s,n);let o=t.template;o!==null&&Fo(t,e,o,1,n),t.firstCreatePass&&(t.firstCreatePass=!1),(c=e[c2])==null||c.finishViewCreation(t),t.staticContentQueries&&jo(t,e),t.staticViewQueries&&Z5(2,t.viewQuery,n);let i=t.components;i!==null&&SM(e,i)}catch(s){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),s}finally{e[E]&=-5,$7()}}function SM(t,e){for(let n=0;n<e.length;n++)wM(t,e[n])}function qo(t,e,n,c){var o,i,r;let s=O(null);try{let a=e.tView,f=t[E]&4096?4096:16,u=Te(t,a,n,f,null,e,null,null,(o=c==null?void 0:c.injector)!=null?o:null,(i=c==null?void 0:c.embeddedViewInjector)!=null?i:null,(r=c==null?void 0:c.dehydratedView)!=null?r:null),d=t[e.index];u[p0]=d;let h=t[c2];return h!==null&&(u[c2]=h.createEmbeddedView(a)),ht(a,u,n),u}finally{O(s)}}function EM(t,e){let n=b1+e;if(n<t.length)return t[n]}function Y5(t,e){return!e||e.firstChild===null||J6(t)}function Go(t,e,n,c=!0){let s=e[T];if(jg(s,e,t,n),c){let i=G5(n,t),r=e[$],a=st(r,t[w2]);a!==null&&Rg(s,t[O1],r,e,a,i)}let o=e[$1];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function AM(t,e){let n=te(t,e);return n!==void 0&&nt(n[T],n),n}function ne(t,e,n,c,s=!1){for(;n!==null;){let o=e[n.index];o!==null&&c.push(s2(o)),g2(o)&&IM(o,c);let i=n.type;if(i&8)ne(t,e,n.child,c);else if(i&32){let r=et(n,e),a;for(;a=r();)c.push(a)}else if(i&16){let r=Do(e,n);if(Array.isArray(r))c.push(...r);else{let a=f0(e[V1]);ne(a[T],a,r,c,!0)}}n=s?n.projectionNext:n.next}return c}function IM(t,e){for(let n=b1;n<t.length;n++){let c=t[n],s=c[T].firstChild;s!==null&&ne(c[T],c,s,e)}t[w2]!==t[P1]&&e.push(t[w2])}var Wo=[];function TM(t){var e;return(e=t[D3])!=null?e:_M(t)}function _M(t){var n;let e=(n=Wo.pop())!=null?n:Object.create(FM);return e.lView=t,e}function kM(t){t.lView[D3]!==t&&(t.lView=null,Wo.push(t))}var FM=m1(Z({},Y8),{consumerIsAlwaysLive:!0,consumerMarkedDirty:t=>{l0(t.lView)},consumerOnSignalRead(){this.lView[D3]=this}}),Zo=100;function Yo(t,e=!0,n=0){var i,r,a;let c=t[n2],s=c.rendererFactory,o=!1;o||(i=s.begin)==null||i.call(s);try{PM(t,n)}catch(l){throw e&&$o(t,l),l}finally{o||((r=s.end)==null||r.call(s),(a=c.inlineEffectRunner)==null||a.flush())}}function PM(t,e){Q5(t,e);let n=0;for(;R7(t);){if(n===Zo)throw new S(103,!1);n++,Q5(t,1)}}function VM(t,e,n,c){var a;let s=e[E];if((s&256)===256)return;let o=!1;!o&&((a=e[n2].inlineEffectRunner)==null||a.flush()),U7(e);let i=null,r=null;!o&&OM(t)&&(r=TM(e),i=Hn(r));try{bs(e),up(t.bindingStartIndex),n!==null&&Fo(t,e,n,2,c);let l=(s&3)===3;if(!o)if(l){let d=t.preOrderCheckHooks;d!==null&&R6(e,d,null)}else{let d=t.preOrderHooks;d!==null&&H6(e,d,0,null),n5(e,0)}if(RM(e),Qo(e,0),t.contentQueries!==null&&jo(t,e),!o)if(l){let d=t.contentCheckHooks;d!==null&&R6(e,d)}else{let d=t.contentHooks;d!==null&&H6(e,d,1),n5(e,1)}eM(t,e);let f=t.components;f!==null&&Xo(e,f,0);let u=t.viewQuery;if(u!==null&&Z5(2,u,c),!o)if(l){let d=t.viewCheckHooks;d!==null&&R6(e,d)}else{let d=t.viewHooks;d!==null&&H6(e,d,2),n5(e,2)}if(t.firstUpdatePass===!0&&(t.firstUpdatePass=!1),e[c5]){for(let d of e[c5])d();e[c5]=null}o||(e[E]&=-73)}catch(l){throw l0(e),l}finally{r!==null&&(jn(r,i),kM(r)),$7()}}function OM(t){return t.type!==2}function Qo(t,e){for(let n=Qs(t);n!==null;n=Ks(n))for(let c=b1;c<n.length;c++){let s=n[c];Ko(s,e)}}function RM(t){for(let e=Qs(t);e!==null;e=Ks(e)){if(!(e[E]&F7.HasTransplantedViews))continue;let n=e[d4];for(let c=0;c<n.length;c++){let s=n[c],o=s[f1];ep(s)}}}function HM(t,e,n){let c=X2(e,t);Ko(c,n)}function Ko(t,e){O7(t)&&Q5(t,e)}function Q5(t,e){let c=t[T],s=t[E],o=t[D3],i=!!(e===0&&s&16);if(i||(i=!!(s&64&&e===0)),i||(i=!!(s&1024)),i||(i=!!(o!=null&&o.dirty&&K8(o))),o&&(o.dirty=!1),t[E]&=-9217,i)VM(c,t,c.template,t[q1]);else if(s&8192){Qo(t,1);let r=c.components;r!==null&&Xo(t,r,1)}}function Xo(t,e,n){for(let c=0;c<e.length;c++)HM(t,e[c],n)}function mt(t){var e;for((e=t[n2].changeDetectionScheduler)==null||e.notify();t;){t[E]|=64;let n=f0(t);if(gs(t)&&!n)return t;t=n}return null}var I3=class{get rootNodes(){let e=this._lView,n=e[T];return ne(n,e,n.firstChild,[])}constructor(e,n,c=!0){this._lView=e,this._cdRefInjectingView=n,this.notifyErrorHandler=c,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[q1]}set context(e){this._lView[q1]=e}get destroyed(){return(this._lView[E]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let e=this._lView[f1];if(g2(e)){let n=e[Z6],c=n?n.indexOf(this):-1;c>-1&&(te(e,c),G6(n,c))}this._attachedToViewContainer=!1}nt(this._lView[T],this._lView)}onDestroy(e){xs(this._lView,e)}markForCheck(){mt(this._cdRefInjectingView||this._lView)}detach(){this._lView[E]&=-129}reattach(){D5(this._lView),this._lView[E]|=128}detectChanges(){this._lView[E]|=1024,Yo(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new S(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,yo(this._lView[T],this._lView)}attachToAppRef(e){if(this._attachedToViewContainer)throw new S(902,!1);this._appRef=e,D5(this._lView)}},T3=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=UM;let t=e;return t})(),jM=T3,BM=class extends jM{constructor(e,n,c){super(),this._declarationLView=e,this._declarationTContainer=n,this.elementRef=c}get ssrId(){var e;return((e=this._declarationTContainer.tView)==null?void 0:e.ssrId)||null}createEmbeddedView(e,n){return this.createEmbeddedViewImpl(e,n)}createEmbeddedViewImpl(e,n,c){let s=qo(this._declarationLView,this._declarationTContainer,e,{embeddedViewInjector:n,dehydratedView:c});return new I3(s)}};function UM(){return Fe(p1(),D())}function Fe(t,e){return t.type&4?new BM(e,t,z4(t,e)):null}function Jo(t){var s;let e=(s=t[a0])!=null?s:[],c=t[f1][$];for(let o of e)$M(o,c);t[a0]=v1}function $M(t,e){let n=0,c=t.firstChild;if(c){let s=t.data[ee];for(;n<s;){let o=c.nextSibling;ot(e,c,!1),c=o,n++}}}function ei(t){Jo(t);for(let e=b1;e<t.length;e++)se(t[e])}function qM(t){var n;let e=(n=t[$1])==null?void 0:n.i18nNodes;if(e){let c=t[$];for(let s of e.values())ot(c,s,!1);t[$1].i18nNodes=void 0}}function se(t){qM(t);let e=t[T];for(let n=e1;n<e.bindingStartIndex;n++)if(g2(t[n])){let c=t[n];ei(c)}else x2(t[n])&&se(t[n])}function GM(t){let e=t._views;for(let n of e){let c=og(n);if(c!==null&&c[P1]!==null)if(x2(c))se(c);else{let s=c[P1];se(s),ei(c)}}}var WM=new RegExp(`^(\\d+)*(${to}|${eo})*(.*)`);function ZM(t){let e=t.match(WM),[n,c,s,o]=e,i=c?parseInt(c,10):s,r=[];for(let[a,l,f]of o.matchAll(/(f|n)(\d*)/g)){let u=parseInt(f,10)||1;r.push(l,u)}return[i,...r]}function YM(t){var e;return!t.prev&&((e=t.parent)==null?void 0:e.type)===8}function l5(t){return t.index-e1}function QM(t,e){let n=t.i18nNodes;if(n){let c=n.get(e);return c&&n.delete(e),c}return null}function Pe(t,e,n,c){var i;let s=l5(c),o=QM(t,s);if(!o){let r=t.data[Jp];if(r!=null&&r[s])o=XM(r[s],n);else if(e.firstChild===c)o=t.firstChild;else{let a=c.prev===null,l=(i=c.prev)!=null?i:c.parent;if(YM(c)){let f=l5(c.parent);o=V5(t,f)}else{let f=R1(l,n);if(a)o=f.firstChild;else{let u=l5(l),d=V5(t,u);if(l.type===2&&d){let m=K7(t,u)+1;o=Ve(m,d)}else o=f.nextSibling}}}}return o}function Ve(t,e){let n=e;for(let c=0;c<t;c++)n=n.nextSibling;return n}function KM(t,e){let n=t;for(let c=0;c<e.length;c+=2){let s=e[c],o=e[c+1];for(let i=0;i<o;i++)switch(s){case P5.FirstChild:n=n.firstChild;break;case P5.NextSibling:n=n.nextSibling;break}}return n}function XM(t,e){let[n,...c]=ZM(t),s;if(n===eo)s=e[V1][P1];else if(n===to)s=Fg(e[V1][P1]);else{let o=Number(n);s=s2(e[o+e1])}return KM(s,c)}function JM(t,e){var c;let n=[];for(let s of e)for(let o=0;o<((c=s[co])!=null?c:1);o++){let i={data:s,firstChild:null};s[ee]>0&&(i.firstChild=t,t=Ve(s[ee],t)),n.push(i)}return[t,n]}var ti=()=>null;function eC(t,e){let n=t[a0];return!e||n===null||n.length===0?null:n[0].data[Xp]===e?n.shift():(Jo(t),null)}function tC(){ti=eC}function K5(t,e){return ti(t,e)}var oe=class{},X5=class{},ie=class{};function cC(t){let e=Error(`No component factory found for ${y1(t)}.`);return e[nC]=t,e}var nC="ngComponent";var J5=class{resolveComponentFactory(e){throw cC(e)}},Oe=(()=>{let e=class e{};e.NULL=new J5;let t=e;return t})(),u0=class{},Z1=(()=>{let e=class e{constructor(){this.destroyNode=null}};e.__NG_ELEMENT_ID__=()=>sC();let t=e;return t})();function sC(){let t=D(),e=p1(),n=X2(e.index,t);return(x2(n)?n:t)[$]}var oC=(()=>{let e=class e{};e.\u0275prov=y({token:e,providedIn:"root",factory:()=>null});let t=e;return t})(),f5={};var x9=new Set;function e3(t){var e;x9.has(t)||(x9.add(t),(e=performance==null?void 0:performance.mark)==null||e.call(performance,"mark_feature_usage",{detail:{feature:t}}))}function N9(...t){}function iC(){let t=typeof F1.requestAnimationFrame=="function",e=F1[t?"requestAnimationFrame":"setTimeout"],n=F1[t?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&e&&n){let c=e[Zone.__symbol__("OriginalDelegate")];c&&(e=c);let s=n[Zone.__symbol__("OriginalDelegate")];s&&(n=s)}return{nativeRequestAnimationFrame:e,nativeCancelAnimationFrame:n}}var a1=class t{constructor({enableLongStackTrace:e=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:c=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new l1(!1),this.onMicrotaskEmpty=new l1(!1),this.onStable=new l1(!1),this.onError=new l1(!1),typeof Zone>"u")throw new S(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),e&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!c&&n,s.shouldCoalesceRunChangeDetection=c,s.lastRequestAnimationFrameId=-1,s.nativeRequestAnimationFrame=iC().nativeRequestAnimationFrame,lC(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!t.isInAngularZone())throw new S(909,!1)}static assertNotInAngularZone(){if(t.isInAngularZone())throw new S(909,!1)}run(e,n,c){return this._inner.run(e,n,c)}runTask(e,n,c,s){let o=this._inner,i=o.scheduleEventTask("NgZoneEvent: "+s,e,rC,N9,N9);try{return o.runTask(i,n,c)}finally{o.cancelTask(i)}}runGuarded(e,n,c){return this._inner.runGuarded(e,n,c)}runOutsideAngular(e){return this._outer.run(e)}},rC={};function pt(t){if(t._nesting==0&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function aC(t){t.isCheckStableRunning||t.lastRequestAnimationFrameId!==-1||(t.lastRequestAnimationFrameId=t.nativeRequestAnimationFrame.call(F1,()=>{t.fakeTopEventTask||(t.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{t.lastRequestAnimationFrameId=-1,e7(t),t.isCheckStableRunning=!0,pt(t),t.isCheckStableRunning=!1},void 0,()=>{},()=>{})),t.fakeTopEventTask.invoke()}),e7(t))}function lC(t){let e=()=>{aC(t)};t._inner=t._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,c,s,o,i,r)=>{if(fC(r))return n.invokeTask(s,o,i,r);try{return w9(t),n.invokeTask(s,o,i,r)}finally{(t.shouldCoalesceEventChangeDetection&&o.type==="eventTask"||t.shouldCoalesceRunChangeDetection)&&e(),D9(t)}},onInvoke:(n,c,s,o,i,r,a)=>{try{return w9(t),n.invoke(s,o,i,r,a)}finally{t.shouldCoalesceRunChangeDetection&&e(),D9(t)}},onHasTask:(n,c,s,o)=>{n.hasTask(s,o),c===s&&(o.change=="microTask"?(t._hasPendingMicrotasks=o.microTask,e7(t),pt(t)):o.change=="macroTask"&&(t.hasPendingMacrotasks=o.macroTask))},onHandleError:(n,c,s,o)=>(n.handleError(s,o),t.runOutsideAngular(()=>t.onError.emit(o)),!1)})}function e7(t){t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&t.lastRequestAnimationFrameId!==-1?t.hasPendingMicrotasks=!0:t.hasPendingMicrotasks=!1}function w9(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function D9(t){t._nesting--,pt(t)}function fC(t){var e;return!Array.isArray(t)||t.length!==1?!1:((e=t[0].data)==null?void 0:e.__ignore_ng_zone__)===!0}var o4=function(t){return t[t.EarlyRead=0]="EarlyRead",t[t.Write=1]="Write",t[t.MixedReadWrite=2]="MixedReadWrite",t[t.Read=3]="Read",t}(o4||{}),uC={destroy(){}};function dC(t,e){var l,f,u;!e&&$m(dC);let n=(l=e==null?void 0:e.injector)!=null?l:x(J2);if(!t0(n))return uC;e3("NgAfterNextRender");let c=n.get(gt),s=(f=c.handler)!=null?f:c.handler=new c7,o=(u=e==null?void 0:e.phase)!=null?u:o4.MixedReadWrite,i=()=>{s.unregister(a),r()},r=n.get(G7).onDestroy(i),a=k7(n,()=>new t7(o,()=>{i(),t()}));return s.register(a),{destroy:i}}var t7=class{constructor(e,n){var c;this.phase=e,this.callbackFn=n,this.zone=x(a1),this.errorHandler=x(m2,{optional:!0}),(c=x(oe,{optional:!0}))==null||c.notify(1)}invoke(){var e;try{this.zone.runOutsideAngular(this.callbackFn)}catch(n){(e=this.errorHandler)==null||e.handleError(n)}}},c7=class{constructor(){this.executingCallbacks=!1,this.buckets={[o4.EarlyRead]:new Set,[o4.Write]:new Set,[o4.MixedReadWrite]:new Set,[o4.Read]:new Set},this.deferredCallbacks=new Set}register(e){(this.executingCallbacks?this.deferredCallbacks:this.buckets[e.phase]).add(e)}unregister(e){this.buckets[e.phase].delete(e),this.deferredCallbacks.delete(e)}execute(){this.executingCallbacks=!0;for(let e of Object.values(this.buckets))for(let n of e)n.invoke();this.executingCallbacks=!1;for(let e of this.deferredCallbacks)this.buckets[e.phase].add(e);this.deferredCallbacks.clear()}destroy(){for(let e of Object.values(this.buckets))e.clear();this.deferredCallbacks.clear()}},gt=(()=>{let e=class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){var c;this.executeInternalCallbacks(),(c=this.handler)==null||c.execute()}executeInternalCallbacks(){let c=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let s of c)s()}ngOnDestroy(){var c;(c=this.handler)==null||c.destroy(),this.handler=null,this.internalCallbacks.length=0}};e.\u0275prov=y({token:e,providedIn:"root",factory:()=>new e});let t=e;return t})();function re(t,e,n){let c=n?t.styles:null,s=n?t.classes:null,o=0;if(e!==null)for(let i=0;i<e.length;i++){let r=e[i];if(typeof r=="number")o=r;else if(o==1)s=C5(s,r);else if(o==2){let a=r,l=e[++i];c=C5(c,a+": "+l+";")}}n?t.styles=c:t.stylesWithoutHost=c,n?t.classes=s:t.classesWithoutHost=s}var ae=class extends Oe{constructor(e){super(),this.ngModule=e}resolveComponentFactory(e){let n=N2(e);return new _3(n,this.ngModule)}};function S9(t){let e=[];for(let n in t){if(!t.hasOwnProperty(n))continue;let c=t[n];c!==void 0&&e.push({propName:Array.isArray(c)?c[0]:c,templateName:n})}return e}function hC(t){let e=t.toLowerCase();return e==="svg"?vs:e==="math"?Ym:null}var n7=class{constructor(e,n){this.injector=e,this.parentInjector=n}get(e,n,c){c=ge(c);let s=this.injector.get(e,f5,c);return s!==f5||n===f5?s:this.parentInjector.get(e,n,c)}},_3=class extends ie{get inputs(){let e=this.componentDef,n=e.inputTransforms,c=S9(e.inputs);if(n!==null)for(let s of c)n.hasOwnProperty(s.propName)&&(s.transform=n[s.propName]);return c}get outputs(){return S9(this.componentDef.outputs)}constructor(e,n){super(),this.componentDef=e,this.ngModule=n,this.componentType=e.type,this.selector=Dm(e.selectors),this.ngContentSelectors=e.ngContentSelectors?e.ngContentSelectors:[],this.isBoundToModule=!!n}create(e,n,c,s){let o=O(null);try{s=s||this.ngModule;let r=s instanceof h2?s:s==null?void 0:s.injector;r&&this.componentDef.getStandaloneInjector!==null&&(r=this.componentDef.getStandaloneInjector(r)||r);let a=r?new n7(e,r):e,l=a.get(u0,null);if(l===null)throw new S(407,!1);let f=a.get(oC,null),u=a.get(gt,null),d=a.get(oe,null),h={rendererFactory:l,sanitizer:f,inlineEffectRunner:null,afterRenderEventManager:u,changeDetectionScheduler:d},m=l.createRenderer(null,this.componentDef),C=this.componentDef.selectors[0][0]||"div",M=c?nM(m,c,this.componentDef.encapsulation,a):Ee(m,C,hC(C)),g=512;this.componentDef.signals?g|=4096:this.componentDef.onPush||(g|=16);let b=null;M!==null&&(b=Q7(M,a,!0));let V=ft(0,null,null,1,0,null,null,null,null,null,null),A=Te(null,V,null,g,null,null,h,m,a,null,b);U7(A);let J,z1;try{let s1=this.componentDef,Q1,U4=null;s1.findHostDirectiveDefs?(Q1=[],U4=new Map,s1.findHostDirectiveDefs(s1,Q1,U4),Q1.push(s1)):Q1=[s1];let h3=mC(A,M),$4=pC(h3,M,s1,Q1,A,h,m);z1=V7(V,e1),M&&CC(m,s1,M,c),n!==void 0&&zC(z1,this.ngContentSelectors,n),J=MC($4,s1,Q1,U4,A,[LC]),ht(V,A,null)}finally{$7()}return new s7(this.componentType,J,z4(z1,A),A,z1)}finally{O(o)}}},s7=class extends X5{constructor(e,n,c,s,o){super(),this.location=c,this._rootLView=s,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new I3(s,void 0,!1),this.componentType=e}setInput(e,n){var o;let c=this._tNode.inputs,s;if(c!==null&&(s=c[e])){if((o=this.previousInputValues)!=null||(this.previousInputValues=new Map),this.previousInputValues.has(e)&&Object.is(this.previousInputValues.get(e),n))return;let i=this._rootLView;dt(i[T],i,s,e,n),this.previousInputValues.set(e,n);let r=X2(this._tNode.index,i);mt(r)}}get injector(){return new N3(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(e){this.hostView.onDestroy(e)}};function mC(t,e){let n=t[T],c=e1;return t[c]=e,L4(n,c,2,"#host",null)}function pC(t,e,n,c,s,o,i){let r=s[T];gC(c,t,e,i);let a=null;e!==null&&(a=Q7(e,s[u4]));let l=o.rendererFactory.createRenderer(e,n),f=16;n.signals?f=4096:n.onPush&&(f=64);let u=Te(s,Po(n),null,f,s[t.index],t,o,l,null,null,a);return r.firstCreatePass&&W5(r,t,c.length-1),ke(s,u),s[t.index]=u}function gC(t,e,n,c){for(let s of t)e.mergedAttrs=s0(e.mergedAttrs,s.hostAttrs);e.mergedAttrs!==null&&(re(e,e.mergedAttrs,!0),n!==null&&Io(c,n,e))}function MC(t,e,n,c,s,o){let i=p1(),r=s[T],a=R1(i,s);Oo(r,s,i,n,null,c);for(let f=0;f<n.length;f++){let u=i.directiveStart+f,d=A3(s,r,u,i);Q2(d,s)}Ro(r,s,i),a&&Q2(a,s);let l=A3(s,r,i.directiveStart+i.componentOffset,i);if(t[q1]=s[q1]=l,o!==null)for(let f of o)f(l,e);return rt(r,i,s),l}function CC(t,e,n,c){if(c)y5(t,n,["ng-version","17.3.12"]);else{let{attrs:s,classes:o}=Sm(e.selectors[0]);s&&y5(t,n,s),o&&o.length>0&&Ao(t,n,o.join(" "))}}function zC(t,e,n){let c=t.projection=[];for(let s=0;s<e.length;s++){let o=n[s];c.push(o!=null?Array.from(o):null)}}function LC(){let t=p1();be(D()[T],t)}var y4=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=vC;let t=e;return t})();function vC(){let t=p1();return ni(t,D())}var yC=y4,ci=class extends yC{constructor(e,n,c){super(),this._lContainer=e,this._hostTNode=n,this._hostLView=c}get element(){return z4(this._hostTNode,this._hostLView)}get injector(){return new N3(this._hostTNode,this._hostLView)}get parentInjector(){let e=q7(this._hostTNode,this._hostLView);if(Ps(e)){let n=Q6(e,this._hostLView),c=Y6(e),s=n[T].data[c+8];return new N3(s,n)}else return new N3(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(e){let n=E9(this._lContainer);return n!==null&&n[e]||null}get length(){return this._lContainer.length-b1}createEmbeddedView(e,n,c){let s,o;typeof c=="number"?s=c:c!=null&&(s=c.index,o=c.injector);let i=K5(this._lContainer,e.ssrId),r=e.createEmbeddedViewImpl(n||{},o,i);return this.insertImpl(r,s,Y5(this._hostTNode,i)),r}createComponent(e,n,c,s,o){var m,C,M;let i=e&&!qm(e),r;if(i)r=n;else{let g=n||{};r=g.index,c=g.injector,s=g.projectableNodes,o=g.environmentInjector||g.ngModuleRef}let a=i?e:new _3(N2(e)),l=c||this.parentInjector;if(!o&&a.ngModule==null){let b=(i?l:this.parentInjector).get(h2,null);b&&(o=b)}let f=N2((m=a.componentType)!=null?m:{}),u=K5(this._lContainer,(C=f==null?void 0:f.id)!=null?C:null),d=(M=u==null?void 0:u.firstChild)!=null?M:null,h=a.create(l,s,d,o);return this.insertImpl(h.hostView,r,Y5(this._hostTNode,u)),h}insert(e,n){return this.insertImpl(e,n,!0)}insertImpl(e,n,c){let s=e._lView;if(Jm(s)){let r=this.indexOf(e);if(r!==-1)this.detach(r);else{let a=s[f1],l=new ci(a,a[O1],a[f1]);l.detach(l.indexOf(e))}}let o=this._adjustIndex(n),i=this._lContainer;return Go(i,s,o,c),e.attachToViewContainerRef(),K9(u5(i),o,e),e}move(e,n){return this.insert(e,n)}indexOf(e){let n=E9(this._lContainer);return n!==null?n.indexOf(e):-1}remove(e){let n=this._adjustIndex(e,-1),c=te(this._lContainer,n);c&&(G6(u5(this._lContainer),n),nt(c[T],c))}detach(e){let n=this._adjustIndex(e,-1),c=te(this._lContainer,n);return c&&G6(u5(this._lContainer),n)!=null?new I3(c):null}_adjustIndex(e,n=0){return e==null?this.length+n:e}};function E9(t){return t[Z6]}function u5(t){return t[Z6]||(t[Z6]=[])}function ni(t,e){let n,c=e[t.index];return g2(c)?n=c:(n=Ho(c,e,null,t),e[t.index]=n,ke(e,n)),si(n,e,t,c),new ci(n,t,e)}function bC(t,e){let n=t[$],c=n.createComment(""),s=R1(e,t),o=st(n,s);return ce(n,o,c,Wg(n,s),!1),c}var si=oi,Mt=()=>!1;function xC(t,e,n){return Mt(t,e,n)}function oi(t,e,n,c){if(t[w2])return;let s;n.type&8?s=s2(c):s=bC(e,n),t[w2]=s}function NC(t,e,n){var f;if(t[w2]&&t[a0])return!0;let c=n[$1],s=e.index-e1;if(!c||Bp(e)||we(c,s))return!1;let i=V5(c,s),r=(f=c.data[Y7])==null?void 0:f[s],[a,l]=JM(i,r);return t[w2]=a,t[a0]=l,!0}function wC(t,e,n,c){Mt(t,n,e)||oi(t,e,n,c)}function DC(){si=wC,Mt=NC}var o7=class t{constructor(e){this.queryList=e,this.matches=null}clone(){return new t(this.queryList)}setDirty(){this.queryList.setDirty()}},i7=class t{constructor(e=[]){this.queries=e}createEmbeddedView(e){let n=e.queries;if(n!==null){let c=e.contentQueries!==null?e.contentQueries[0]:n.length,s=[];for(let o=0;o<c;o++){let i=n.getByIndex(o),r=this.queries[i.indexInDeclarationView];s.push(r.clone())}return new t(s)}return null}insertView(e){this.dirtyQueriesWithMatches(e)}detachView(e){this.dirtyQueriesWithMatches(e)}finishViewCreation(e){this.dirtyQueriesWithMatches(e)}dirtyQueriesWithMatches(e){for(let n=0;n<this.queries.length;n++)Ct(e,n).matches!==null&&this.queries[n].setDirty()}},le=class{constructor(e,n,c=null){this.flags=n,this.read=c,typeof e=="string"?this.predicate=FC(e):this.predicate=e}},r7=class t{constructor(e=[]){this.queries=e}elementStart(e,n){for(let c=0;c<this.queries.length;c++)this.queries[c].elementStart(e,n)}elementEnd(e){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(e)}embeddedTView(e){let n=null;for(let c=0;c<this.length;c++){let s=n!==null?n.length:0,o=this.getByIndex(c).embeddedTView(e,s);o&&(o.indexInDeclarationView=c,n!==null?n.push(o):n=[o])}return n!==null?new t(n):null}template(e,n){for(let c=0;c<this.queries.length;c++)this.queries[c].template(e,n)}getByIndex(e){return this.queries[e]}get length(){return this.queries.length}track(e){this.queries.push(e)}},a7=class t{constructor(e,n=-1){this.metadata=e,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(e,n){this.isApplyingToNode(n)&&this.matchTNode(e,n)}elementEnd(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)}template(e,n){this.elementStart(e,n)}embeddedTView(e,n){return this.isApplyingToNode(e)?(this.crossesNgTemplate=!0,this.addMatch(-e.index,n),new t(this.metadata)):null}isApplyingToNode(e){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,c=e.parent;for(;c!==null&&c.type&8&&c.index!==n;)c=c.parent;return n===(c!==null?c.index:-1)}return this._appliesToNextNode}matchTNode(e,n){let c=this.metadata.predicate;if(Array.isArray(c))for(let s=0;s<c.length;s++){let o=c[s];this.matchTNodeWithReadOption(e,n,SC(n,o)),this.matchTNodeWithReadOption(e,n,j6(n,e,o,!1,!1))}else c===T3?n.type&4&&this.matchTNodeWithReadOption(e,n,-1):this.matchTNodeWithReadOption(e,n,j6(n,e,c,!1,!1))}matchTNodeWithReadOption(e,n,c){if(c!==null){let s=this.metadata.read;if(s!==null)if(s===h1||s===y4||s===T3&&n.type&4)this.addMatch(n.index,-2);else{let o=j6(n,e,s,!1,!1);o!==null&&this.addMatch(n.index,o)}else this.addMatch(n.index,c)}}addMatch(e,n){this.matches===null?this.matches=[e,n]:this.matches.push(e,n)}};function SC(t,e){let n=t.localNames;if(n!==null){for(let c=0;c<n.length;c+=2)if(n[c]===e)return n[c+1]}return null}function EC(t,e){return t.type&11?z4(t,e):t.type&4?Fe(t,e):null}function AC(t,e,n,c){return n===-1?EC(e,t):n===-2?IC(t,e,c):A3(t,t[T],n,e)}function IC(t,e,n){if(n===h1)return z4(e,t);if(n===T3)return Fe(e,t);if(n===y4)return ni(e,t)}function ii(t,e,n,c){let s=e[c2].queries[c];if(s.matches===null){let o=t.data,i=n.matches,r=[];for(let a=0;i!==null&&a<i.length;a+=2){let l=i[a];if(l<0)r.push(null);else{let f=o[l];r.push(AC(e,f,i[a+1],n.metadata.read))}}s.matches=r}return s.matches}function l7(t,e,n,c){let s=t.queries.getByIndex(n),o=s.matches;if(o!==null){let i=ii(t,e,s,n);for(let r=0;r<o.length;r+=2){let a=o[r];if(a>0)c.push(i[r/2]);else{let l=o[r+1],f=e[-a];for(let u=b1;u<f.length;u++){let d=f[u];d[p0]===d[f1]&&l7(d[T],d,l,c)}if(f[d4]!==null){let u=f[d4];for(let d=0;d<u.length;d++){let h=u[d];l7(h[T],h,l,c)}}}}}return c}function TC(t,e){return t[c2].queries[e].queryList}function ri(t,e,n){var o;let c=new k5((n&4)===4);return rM(t,e,c,c.destroy),((o=e[c2])!=null?o:e[c2]=new i7).queries.push(new o7(c))-1}function _C(t,e,n){let c=K();return c.firstCreatePass&&(ai(c,new le(t,e,n),-1),(e&2)===2&&(c.staticViewQueries=!0)),ri(c,D(),e)}function kC(t,e,n,c){let s=K();if(s.firstCreatePass){let o=p1();ai(s,new le(e,n,c),o.index),PC(s,t),(n&2)===2&&(s.staticContentQueries=!0)}return ri(s,D(),n)}function FC(t){return t.split(",").map(e=>e.trim())}function ai(t,e,n){t.queries===null&&(t.queries=new r7),t.queries.track(new a7(e,n))}function PC(t,e){let n=t.contentQueries||(t.contentQueries=[]),c=n.length?n[n.length-1]:-1;e!==c&&n.push(t.queries.length-1,e)}function Ct(t,e){return t.queries.getByIndex(e)}function VC(t,e){let n=t[T],c=Ct(n,e);return c.crossesNgTemplate?l7(n,t,e,[]):ii(n,t,c,e)}function OC(t){return typeof t=="function"&&t[q2]!==void 0}function sk(t,e){e3("NgSignals");let n=Zn(t),c=n[q2];return e!=null&&e.equal&&(c.equal=e.equal),n.set=s=>D6(c,s),n.update=s=>Yn(c,s),n.asReadonly=RC.bind(n),n}function RC(){let t=this[q2];if(t.readonlyFn===void 0){let e=()=>this();e[q2]=t,t.readonlyFn=e}return t.readonlyFn}function li(t){return OC(t)&&typeof t.set=="function"}function HC(t){return Object.getPrototypeOf(t.prototype).constructor}function n1(t){let e=HC(t.type),n=!0,c=[t];for(;e;){let s;if(Y2(t))s=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new S(903,!1);s=e.\u0275dir}if(s){if(n){c.push(s);let i=t;i.inputs=P6(t.inputs),i.inputTransforms=P6(t.inputTransforms),i.declaredInputs=P6(t.declaredInputs),i.outputs=P6(t.outputs);let r=s.hostBindings;r&&qC(t,r);let a=s.viewQuery,l=s.contentQueries;if(a&&UC(t,a),l&&$C(t,l),jC(t,s),Gh(t.outputs,s.outputs),Y2(s)&&s.data.animation){let f=t.data;f.animation=(f.animation||[]).concat(s.data.animation)}}let o=s.features;if(o)for(let i=0;i<o.length;i++){let r=o[i];r&&r.ngInherit&&r(t),r===n1&&(n=!1)}}e=Object.getPrototypeOf(e)}BC(c)}function jC(t,e){var n;for(let c in e.inputs){if(!e.inputs.hasOwnProperty(c)||t.inputs.hasOwnProperty(c))continue;let s=e.inputs[c];if(s!==void 0&&(t.inputs[c]=s,t.declaredInputs[c]=e.declaredInputs[c],e.inputTransforms!==null)){let o=Array.isArray(s)?s[0]:s;if(!e.inputTransforms.hasOwnProperty(o))continue;(n=t.inputTransforms)!=null||(t.inputTransforms={}),t.inputTransforms[o]=e.inputTransforms[o]}}}function BC(t){let e=0,n=null;for(let c=t.length-1;c>=0;c--){let s=t[c];s.hostVars=e+=s.hostVars,s.hostAttrs=s0(s.hostAttrs,n=s0(n,s.hostAttrs))}}function P6(t){return t===a4?{}:t===v1?[]:t}function UC(t,e){let n=t.viewQuery;n?t.viewQuery=(c,s)=>{e(c,s),n(c,s)}:t.viewQuery=e}function $C(t,e){let n=t.contentQueries;n?t.contentQueries=(c,s,o)=>{e(c,s,o),n(c,s,o)}:t.contentQueries=e}function qC(t,e){let n=t.hostBindings;n?t.hostBindings=(c,s)=>{e(c,s),n(c,s)}:t.hostBindings=e}function GC(t){let e=t.inputConfig,n={};for(let c in e)if(e.hasOwnProperty(c)){let s=e[c];Array.isArray(s)&&s[3]&&(n[c]=s[3])}t.inputTransforms=n}var K2=class{},f7=class{};var u7=class extends K2{constructor(e,n,c){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new ae(this);let s=is(e);this._bootstrapComponents=vo(s.bootstrap),this._r3Injector=qs(e,n,[{provide:K2,useValue:this},{provide:Oe,useValue:this.componentFactoryResolver},...c],y1(e),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(e)}get injector(){return this._r3Injector}destroy(){let e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(e){this.destroyCbs.push(e)}},d7=class extends f7{constructor(e){super(),this.moduleType=e}create(e){return new u7(this.moduleType,e,[])}};var fe=class extends K2{constructor(e){super(),this.componentFactoryResolver=new ae(this),this.instance=null;let n=new o0([...e.providers,{provide:K2,useValue:this},{provide:Oe,useValue:this.componentFactoryResolver}],e.parent||Ce(),e.debugName,new Set(["environment"]));this.injector=n,e.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(e){this.injector.onDestroy(e)}};function WC(t,e,n=null){return new fe({providers:t,parent:e,debugName:n,runEnvironmentInitializers:!0}).injector}var Re=(()=>{let e=class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new K4(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let c=this.taskId++;return this.pendingTasks.add(c),c}remove(c){this.pendingTasks.delete(c),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function fi(t){return zt(t)?Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t:!1}function ZC(t,e){if(Array.isArray(t))for(let n=0;n<t.length;n++)e(t[n]);else{let n=t[Symbol.iterator](),c;for(;!(c=n.next()).done;)e(c.value)}}function zt(t){return t!==null&&(typeof t=="function"||typeof t=="object")}function b4(t,e,n){return t[e]=n}function ui(t,e){return t[e]}function E1(t,e,n){let c=t[e];return Object.is(c,n)?!1:(t[e]=n,!0)}function m4(t,e,n,c){let s=E1(t,e,n);return E1(t,e+1,c)||s}function di(t,e,n,c,s){let o=m4(t,e,n,c);return E1(t,e+2,s)||o}function v0(t,e,n,c,s,o){let i=m4(t,e,n,c);return m4(t,e+2,s,o)||i}function y0(t){return(t.flags&32)===32}function YC(t,e,n,c,s,o,i,r,a){let l=e.consts,f=L4(e,t,4,i||null,h4(l,r));ut(e,n,f,h4(l,a)),be(e,f);let u=f.tView=ft(2,f,c,s,o,e.directiveRegistry,e.pipeRegistry,null,e.schemas,l,null);return e.queries!==null&&(e.queries.template(e,f),u.queries=e.queries.embeddedTView(f)),f}function QC(t,e,n,c,s,o,i,r){let a=D(),l=K(),f=t+e1,u=l.firstCreatePass?YC(f,l,a,e,n,c,s,o,i):l.data[f];P3(u,!1);let d=hi(l,a,u,t);ye()&&Ae(l,a,d,u),Q2(d,a);let h=Ho(d,a,d,u);return a[f]=h,ke(a,h),xC(h,u,a),Le(u)&&at(l,a,u),i!=null&&lt(a,u,r),QC}var hi=mi;function mi(t,e,n,c){return M2(!0),e[$].createComment("")}function KC(t,e,n,c){var f,u;let s=e[$1],o=!s||M4()||y0(n)||we(s,c);if(M2(o),o)return mi(t,e,n,c);let i=(u=(f=s.data[Kp])==null?void 0:f[c])!=null?u:null;i!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=i);let r=Pe(s,t,e,n);Ne(s,c,r);let a=K7(s,c);return Ve(a,r)}function XC(){hi=KC}function T2(t,e,n,c){let s=D(),o=C4();if(E1(s,o,e)){let i=K(),r=C0();yM(r,s,t,e,n,c)}return T2}function pi(t,e,n,c){return E1(t,C4(),n)?e+Y(n)+c:c1}function JC(t,e,n,c,s,o){let i=M0(),r=m4(t,i,n,s);return V3(2),r?e+Y(n)+c+Y(s)+o:c1}function ez(t,e,n,c,s,o,i,r){let a=M0(),l=di(t,a,n,s,i);return V3(3),l?e+Y(n)+c+Y(s)+o+Y(i)+r:c1}function tz(t,e,n,c,s,o,i,r,a,l){let f=M0(),u=v0(t,f,n,s,i,a);return V3(4),u?e+Y(n)+c+Y(s)+o+Y(i)+r+Y(a)+l:c1}function cz(t,e,n,c,s,o,i,r,a,l,f,u){let d=M0(),h=v0(t,d,n,s,i,a);return h=E1(t,d+4,f)||h,V3(5),h?e+Y(n)+c+Y(s)+o+Y(i)+r+Y(a)+l+Y(f)+u:c1}function nz(t,e,n,c,s,o,i,r,a,l,f,u,d,h){let m=M0(),C=v0(t,m,n,s,i,a);return C=m4(t,m+4,f,d)||C,V3(6),C?e+Y(n)+c+Y(s)+o+Y(i)+r+Y(a)+l+Y(f)+u+Y(d)+h:c1}function V6(t,e){return t<<17|e<<2}function k3(t){return t>>17&32767}function sz(t){return(t&2)==2}function oz(t,e){return t&131071|e<<17}function h7(t){return t|2}function p4(t){return(t&131068)>>2}function d5(t,e){return t&-131069|e<<2}function iz(t){return(t&1)===1}function m7(t){return t|1}function rz(t,e,n,c,s,o){let i=o?e.classBindings:e.styleBindings,r=k3(i),a=p4(i);t[c]=n;let l=!1,f;if(Array.isArray(n)){let u=n;f=u[1],(f===null||m0(u,f)>0)&&(l=!0)}else f=n;if(s)if(a!==0){let d=k3(t[r+1]);t[c+1]=V6(d,r),d!==0&&(t[d+1]=d5(t[d+1],c)),t[r+1]=oz(t[r+1],c)}else t[c+1]=V6(r,0),r!==0&&(t[r+1]=d5(t[r+1],c)),r=c;else t[c+1]=V6(a,0),r===0?r=c:t[a+1]=d5(t[a+1],c),a=c;l&&(t[c+1]=h7(t[c+1])),A9(t,f,c,!0),A9(t,f,c,!1),az(e,f,t,c,o),i=V6(r,a),o?e.classBindings=i:e.styleBindings=i}function az(t,e,n,c,s){let o=s?t.residualClasses:t.residualStyles;o!=null&&typeof e=="string"&&m0(o,e)>=0&&(n[c+1]=m7(n[c+1]))}function A9(t,e,n,c){let s=t[n+1],o=e===null,i=c?k3(s):p4(s),r=!1;for(;i!==0&&(r===!1||o);){let a=t[i],l=t[i+1];lz(a,e)&&(r=!0,t[i+1]=c?m7(l):h7(l)),i=c?k3(l):p4(l)}r&&(t[n+1]=c?h7(s):m7(s))}function lz(t,e){return t===null||e==null||(Array.isArray(t)?t[1]:t)===e?!0:Array.isArray(t)&&typeof e=="string"?m0(t,e)>=0:!1}var e2={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function fz(t){return t.substring(e2.key,e2.keyEnd)}function uz(t){return dz(t),gi(t,Mi(t,0,e2.textEnd))}function gi(t,e){let n=e2.textEnd;return n===e?-1:(e=e2.keyEnd=hz(t,e2.key=e,n),Mi(t,e,n))}function dz(t){e2.key=0,e2.keyEnd=0,e2.value=0,e2.valueEnd=0,e2.textEnd=t.length}function Mi(t,e,n){for(;e<n&&t.charCodeAt(e)<=32;)e++;return e}function hz(t,e,n){for(;e<n&&t.charCodeAt(e)>32;)e++;return e}function mz(t,e,n){let c=D(),s=C4();if(E1(c,s,e)){let o=K(),i=C0();_e(o,i,c,t,e,c[$],n,!1)}return mz}function p7(t,e,n,c,s){let o=e.inputs,i=s?"class":"style";dt(t,n,o[i],i,c)}function Ci(t,e,n){return zi(t,e,n,!1),Ci}function b0(t,e){return zi(t,e,null,!0),b0}function ok(t){gz(yz,pz,t,!0)}function pz(t,e){for(let n=uz(e);n>=0;n=gi(e,n))A7(t,fz(e),!0)}function zi(t,e,n,c){let s=D(),o=K(),i=V3(2);if(o.firstUpdatePass&&vi(o,t,i,c),e!==c1&&E1(s,i,e)){let r=o.data[G1()];yi(o,r,s,s[$],t,s[i+1]=xz(e,n),c,i)}}function gz(t,e,n,c){let s=K(),o=V3(2);s.firstUpdatePass&&vi(s,null,o,c);let i=D();if(n!==c1&&E1(i,o,n)){let r=s.data[G1()];if(bi(r,c)&&!Li(s,o)){let a=c?r.classesWithoutHost:r.stylesWithoutHost;a!==null&&(n=C5(a,n||"")),p7(s,r,i,n,c)}else bz(s,r,i,i[$],i[o+1],i[o+1]=vz(t,e,n),c,o)}}function Li(t,e){return e>=t.expandoStartIndex}function vi(t,e,n,c){let s=t.data;if(s[n+1]===null){let o=s[G1()],i=Li(t,n);bi(o,c)&&e===null&&!i&&(e=!1),e=Mz(s,o,e,c),rz(s,o,e,n,i,c)}}function Mz(t,e,n,c){let s=pp(t),o=c?e.residualClasses:e.residualStyles;if(s===null)(c?e.classBindings:e.styleBindings)===0&&(n=h5(null,t,e,n,c),n=d0(n,e.attrs,c),o=null);else{let i=e.directiveStylingLast;if(i===-1||t[i]!==s)if(n=h5(s,t,e,n,c),o===null){let a=Cz(t,e,c);a!==void 0&&Array.isArray(a)&&(a=h5(null,t,e,a[1],c),a=d0(a,e.attrs,c),zz(t,e,c,a))}else o=Lz(t,e,c)}return o!==void 0&&(c?e.residualClasses=o:e.residualStyles=o),n}function Cz(t,e,n){let c=n?e.classBindings:e.styleBindings;if(p4(c)!==0)return t[k3(c)]}function zz(t,e,n,c){let s=n?e.classBindings:e.styleBindings;t[k3(s)]=c}function Lz(t,e,n){let c,s=e.directiveEnd;for(let o=1+e.directiveStylingLast;o<s;o++){let i=t[o].hostAttrs;c=d0(c,i,n)}return d0(c,e.attrs,n)}function h5(t,e,n,c,s){let o=null,i=n.directiveEnd,r=n.directiveStylingLast;for(r===-1?r=n.directiveStart:r++;r<i&&(o=e[r],c=d0(c,o.hostAttrs,s),o!==t);)r++;return t!==null&&(n.directiveStylingLast=r),c}function d0(t,e,n){let c=n?1:2,s=-1;if(e!==null)for(let o=0;o<e.length;o++){let i=e[o];typeof i=="number"?s=i:s===c&&(Array.isArray(t)||(t=t===void 0?[]:["",t]),A7(t,i,n?!0:e[++o]))}return t===void 0?null:t}function vz(t,e,n){if(n==null||n==="")return v1;let c=[],s=W1(n);if(Array.isArray(s))for(let o=0;o<s.length;o++)t(c,s[o],!0);else if(typeof s=="object")for(let o in s)s.hasOwnProperty(o)&&t(c,o,s[o]);else typeof s=="string"&&e(c,s);return c}function yz(t,e,n){let c=String(e);c!==""&&!c.includes(" ")&&A7(t,c,n)}function bz(t,e,n,c,s,o,i,r){s===c1&&(s=v1);let a=0,l=0,f=0<s.length?s[0]:null,u=0<o.length?o[0]:null;for(;f!==null||u!==null;){let d=a<s.length?s[a+1]:void 0,h=l<o.length?o[l+1]:void 0,m=null,C;f===u?(a+=2,l+=2,d!==h&&(m=u,C=h)):u===null||f!==null&&f<u?(a+=2,m=f):(l+=2,m=u,C=h),m!==null&&yi(t,e,n,c,m,C,i,r),f=a<s.length?s[a]:null,u=l<o.length?o[l]:null}}function yi(t,e,n,c,s,o,i,r){if(!(e.type&3))return;let a=t.data,l=a[r+1],f=iz(l)?I9(a,e,n,s,p4(l),i):void 0;if(!ue(f)){ue(o)||sz(l)&&(o=I9(a,null,n,s,r,i));let u=ys(G1(),n);Xg(c,i,u,s,o)}}function I9(t,e,n,c,s,o){let i=e===null,r;for(;s>0;){let a=t[s],l=Array.isArray(a),f=l?a[1]:a,u=f===null,d=n[s+1];d===c1&&(d=u?v1:void 0);let h=u?e5(d,c):f===c?d:void 0;if(l&&!ue(h)&&(h=e5(a,c)),ue(h)&&(r=h,i))return r;let m=t[s+1];s=i?k3(m):p4(m)}if(e!==null){let a=o?e.residualClasses:e.residualStyles;a!=null&&(r=e5(a,c))}return r}function ue(t){return t!==void 0}function xz(t,e){return t==null||t===""||(typeof e=="string"?t=t+e:typeof t=="object"&&(t=y1(W1(t)))),t}function bi(t,e){return(t.flags&(e?8:16))!==0}function ik(t,e,n){e3("NgControlFlow");let c=D(),s=C4(),o=Nz(c,e1+t),i=0;if(E1(c,s,e)){let r=O(null);try{if(AM(o,i),e!==-1){let a=wz(c[T],e1+e),l=K5(o,a.tView.ssrId),f=qo(c,a,n,{dehydratedView:l});Go(o,f,i,Y5(a,l))}}finally{O(r)}}else{let r=EM(o,i);r!==void 0&&(r[q1]=n)}}function Nz(t,e){return t[e]}function wz(t,e){return V7(t,e)}function Dz(t,e,n,c,s,o){let i=e.consts,r=h4(i,s),a=L4(e,t,2,c,r);return ut(e,n,a,h4(i,o)),a.attrs!==null&&re(a,a.attrs,!1),a.mergedAttrs!==null&&re(a,a.mergedAttrs,!0),e.queries!==null&&e.queries.elementStart(e,a),a}function xi(t,e,n,c){let s=D(),o=K(),i=e1+t,r=s[$],a=o.firstCreatePass?Dz(i,o,s,e,n,c):o.data[i],l=wi(o,s,a,r,e,t);s[i]=l;let f=Le(a);return P3(a,!0),Io(r,l,a),!y0(a)&&ye()&&Ae(o,s,l,a),np()===0&&Q2(l,s),sp(),f&&(at(o,s,a),rt(o,a,s)),c!==null&&lt(s,a),xi}function Ni(){let t=p1();H7()?j7():(t=t.parent,P3(t,!1));let e=t;ip(e)&&ap(),op();let n=K();return n.firstCreatePass&&(be(n,t),P7(t)&&n.queries.elementEnd(t)),e.classesWithoutHost!=null&&yp(e)&&p7(n,e,D(),e.classesWithoutHost,!0),e.stylesWithoutHost!=null&&bp(e)&&p7(n,e,D(),e.stylesWithoutHost,!1),Ni}function Sz(t,e,n,c){return xi(t,e,n,c),Ni(),Sz}var wi=(t,e,n,c,s,o)=>(M2(!0),Ee(c,s,_s()));function Ez(t,e,n,c,s,o){let i=e[$1],r=!i||M4()||y0(n)||we(i,o);if(M2(r),r)return Ee(c,s,_s());let a=Pe(i,t,e,n);return oo(i,o)&&Ne(i,o,a.nextSibling),i&&(Ws(n)||Zs(a))&&g0(n)&&(rp(n),So(a)),a}function Az(){wi=Ez}function Iz(t,e,n,c,s){let o=e.consts,i=h4(o,c),r=L4(e,t,8,"ng-container",i);i!==null&&re(r,i,!0);let a=h4(o,s);return ut(e,n,r,a),e.queries!==null&&e.queries.elementStart(e,r),r}function Tz(t,e,n){let c=D(),s=K(),o=t+e1,i=s.firstCreatePass?Iz(o,s,c,e,n):s.data[o];P3(i,!0);let r=Di(s,c,i,t);return c[o]=r,ye()&&Ae(s,c,r,i),Q2(r,c),Le(i)&&(at(s,c,i),rt(s,i,c)),n!=null&&lt(c,i),Tz}function _z(){let t=p1(),e=K();return H7()?j7():(t=t.parent,P3(t,!1)),e.firstCreatePass&&(be(e,t),P7(t)&&e.queries.elementEnd(t)),_z}var Di=(t,e,n,c)=>(M2(!0),ct(e[$],""));function kz(t,e,n,c){let s,o=e[$1],i=!o||M4()||y0(n);if(M2(i),i)return ct(e[$],"");let r=Pe(o,t,e,n),a=ag(o,c);return Ne(o,c,r),s=Ve(a,r),s}function Fz(){Di=kz}function rk(){return D()}function Lt(t,e,n){let c=D(),s=C4();if(E1(c,s,e)){let o=K(),i=C0();_e(o,i,c,t,e,c[$],n,!0)}return Lt}var x3=void 0;function Pz(t){let e=t,n=Math.floor(Math.abs(t)),c=t.toString().replace(/^[^.]*\.?/,"").length;return n===1&&c===0?1:5}var Vz=["en",[["a","p"],["AM","PM"],x3],[["AM","PM"],x3,x3],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],x3,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],x3,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",x3,"{1} 'at' {0}",x3],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Pz],m5={};function _1(t){let e=Oz(t),n=T9(e);if(n)return n;let c=e.split("-")[0];if(n=T9(c),n)return n;if(c==="en")return Vz;throw new S(701,!1)}function T9(t){return t in m5||(m5[t]=F1.ng&&F1.ng.common&&F1.ng.common.locales&&F1.ng.common.locales[t]),m5[t]}var X=function(t){return t[t.LocaleId=0]="LocaleId",t[t.DayPeriodsFormat=1]="DayPeriodsFormat",t[t.DayPeriodsStandalone=2]="DayPeriodsStandalone",t[t.DaysFormat=3]="DaysFormat",t[t.DaysStandalone=4]="DaysStandalone",t[t.MonthsFormat=5]="MonthsFormat",t[t.MonthsStandalone=6]="MonthsStandalone",t[t.Eras=7]="Eras",t[t.FirstDayOfWeek=8]="FirstDayOfWeek",t[t.WeekendRange=9]="WeekendRange",t[t.DateFormat=10]="DateFormat",t[t.TimeFormat=11]="TimeFormat",t[t.DateTimeFormat=12]="DateTimeFormat",t[t.NumberSymbols=13]="NumberSymbols",t[t.NumberFormats=14]="NumberFormats",t[t.CurrencyCode=15]="CurrencyCode",t[t.CurrencySymbol=16]="CurrencySymbol",t[t.CurrencyName=17]="CurrencyName",t[t.Currencies=18]="Currencies",t[t.Directionality=19]="Directionality",t[t.PluralCase=20]="PluralCase",t[t.ExtraData=21]="ExtraData",t}(X||{});function Oz(t){return t.toLowerCase().replace(/_/g,"-")}var de="en-US",Rz="USD";var Hz=de;function jz(t){typeof t=="string"&&(Hz=t.toLowerCase().replace(/_/g,"-"))}function Si(t,e,n){let c=t[$];switch(n){case Node.COMMENT_NODE:return ct(c,e);case Node.TEXT_NODE:return tt(c,e);case Node.ELEMENT_NODE:return Ee(c,e,null)}}var Bz=(t,e,n,c)=>(M2(!0),Si(t,n,c));function Uz(t,e,n,c){return M2(!0),Si(t,n,c)}function $z(){Bz=Uz}function C2(t,e,n,c){let s=D(),o=K(),i=p1();return Ei(o,s,s[$],i,t,e,c),C2}function qz(t,e,n,c){let s=t.cleanup;if(s!=null)for(let o=0;o<s.length-1;o+=2){let i=s[o];if(i===n&&s[o+1]===c){let r=e[i0],a=s[o+2];return r.length>a?r[a]:null}typeof i=="string"&&(o+=2)}return null}function Ei(t,e,n,c,s,o,i){let r=Le(c),l=t.firstCreatePass&&Uo(t),f=e[q1],u=Bo(e),d=!0;if(c.type&3||i){let C=R1(c,e),M=i?i(C):C,g=u.length,b=i?A=>i(s2(A[c.index])):c.index,V=null;if(!i&&r&&(V=qz(t,e,s,c.index)),V!==null){let A=V.__ngLastListenerFn__||V;A.__ngNextListenerFn__=o,V.__ngLastListenerFn__=o,d=!1}else{o=k9(c,e,f,o,!1);let A=n.listen(M,s,o);u.push(o,A),l&&l.push(s,b,g,g+1)}}else o=k9(c,e,f,o,!1);let h=c.outputs,m;if(d&&h!==null&&(m=h[s])){let C=m.length;if(C)for(let M=0;M<C;M+=2){let g=m[M],b=m[M+1],J=e[g][b].subscribe(o),z1=u.length;u.push(o,J),l&&l.push(s,c.index,z1,-(z1+1))}}}function _9(t,e,n,c){let s=O(null);try{return f2(6,e,n),n(c)!==!1}catch(o){return $o(t,o),!1}finally{f2(7,e,n),O(s)}}function k9(t,e,n,c,s){return function o(i){if(i===Function)return c;let r=t.componentOffset>-1?X2(t.index,e):e;mt(r);let a=_9(e,n,c,i),l=o.__ngNextListenerFn__;for(;l;)a=_9(e,n,l,i)&&a,l=l.__ngNextListenerFn__;return s&&a===!1&&i.preventDefault(),a}}function ak(t=1){return Mp(t)}function Gz(t,e){let n=null,c=ym(t);for(let s=0;s<e.length;s++){let o=e[s];if(o==="*"){n=s;continue}if(c===null?ns(t,o,!0):Nm(c,o))return s}return n}function Ai(t){let e=D()[V1][O1];if(!e.projection){let n=t?t.length:1,c=e.projection=hm(n,null),s=c.slice(),o=e.child;for(;o!==null;){let i=t?Gz(o,t):0;i!==null&&(s[i]?s[i].projectionNext=o:c[i]=o,s[i]=o),o=o.next}}}function Ii(t,e=0,n){let c=D(),s=K(),o=L4(s,e1+t,16,null,n||null);o.projection===null&&(o.projection=e),j7(),(!c[$1]||M4())&&(o.flags&32)!==32&&Qg(s,c,o)}function Wz(t,e,n){return Ti(t,"",e,"",n),Wz}function Ti(t,e,n,c,s){let o=D(),i=pi(o,e,n,c);if(i!==c1){let r=K(),a=C0();_e(r,a,o,t,i,o[$],s,!1)}return Ti}function lk(t,e,n,c){kC(t,e,n,c)}function fk(t,e,n){_C(t,e,n)}function uk(t){let e=D(),n=K(),c=Ds();B7(c+1);let s=Ct(n,c);if(t.dirty&&Xm(e)===((s.metadata.flags&2)===2)){if(s.matches===null)t.reset([]);else{let o=VC(e,c);t.reset(o,Op),t.notifyOnChanges()}return!0}return!1}function dk(){return TC(D(),Ds())}function Zz(t,e,n,c){n>=t.data.length&&(t.data[n]=null,t.blueprint[n]=null),e[n]=c}function hk(t){let e=fp();return ve(e,e1+t)}function mk(t,e=""){let n=D(),c=K(),s=t+e1,o=c.firstCreatePass?L4(c,s,1,e,null):c.data[s],i=_i(c,n,o,e,t);n[s]=i,ye()&&Ae(c,n,i,o),P3(o,!1)}var _i=(t,e,n,c,s)=>(M2(!0),tt(e[$],c));function Yz(t,e,n,c,s){let o=e[$1],i=!o||M4()||y0(n)||we(o,s);return M2(i),i?tt(e[$],c):Pe(o,t,e,n)}function Qz(){_i=Yz}function Kz(t){return ki("",t,""),Kz}function ki(t,e,n){let c=D(),s=pi(c,t,e,n);return s!==c1&&v4(c,G1(),s),ki}function Xz(t,e,n,c,s){let o=D(),i=JC(o,t,e,n,c,s);return i!==c1&&v4(o,G1(),i),Xz}function Jz(t,e,n,c,s,o,i){let r=D(),a=ez(r,t,e,n,c,s,o,i);return a!==c1&&v4(r,G1(),a),Jz}function eL(t,e,n,c,s,o,i,r,a){let l=D(),f=tz(l,t,e,n,c,s,o,i,r,a);return f!==c1&&v4(l,G1(),f),eL}function tL(t,e,n,c,s,o,i,r,a,l,f){let u=D(),d=cz(u,t,e,n,c,s,o,i,r,a,l,f);return d!==c1&&v4(u,G1(),d),tL}function cL(t,e,n,c,s,o,i,r,a,l,f,u,d){let h=D(),m=nz(h,t,e,n,c,s,o,i,r,a,l,f,u,d);return m!==c1&&v4(h,G1(),m),cL}function nL(t,e,n){li(e)&&(e=e());let c=D(),s=C4();if(E1(c,s,e)){let o=K(),i=C0();_e(o,i,c,t,e,c[$],n,!1)}return nL}function pk(t,e){let n=li(t);return n&&t.set(e),n}function sL(t,e){let n=D(),c=K(),s=p1();return Ei(c,n,n[$],s,t,e),sL}function oL(t,e,n){let c=K();if(c.firstCreatePass){let s=Y2(t);g7(n,c.data,c.blueprint,s,!0),g7(e,c.data,c.blueprint,s,!1)}}function g7(t,e,n,c,s){if(t=L1(t),Array.isArray(t))for(let o=0;o<t.length;o++)g7(t[o],e,n,c,s);else{let o=K(),i=D(),r=p1(),a=f4(t)?t:L1(t.provide),l=ds(t),f=r.providerIndexes&1048575,u=r.directiveStart,d=r.providerIndexes>>20;if(f4(t)||!t.multi){let h=new E3(l,s,z),m=g5(a,e,s?f:f+d,u);m===-1?(A5(X6(r,i),o,a),p5(o,t,e.length),e.push(a),r.directiveStart++,r.directiveEnd++,s&&(r.providerIndexes+=1048576),n.push(h),i.push(h)):(n[m]=h,i[m]=h)}else{let h=g5(a,e,f+d,u),m=g5(a,e,f,f+d),C=h>=0&&n[h],M=m>=0&&n[m];if(s&&!M||!s&&!C){A5(X6(r,i),o,a);let g=aL(s?rL:iL,n.length,s,c,l);!s&&M&&(n[m].providerFactory=g),p5(o,t,e.length,0),e.push(a),r.directiveStart++,r.directiveEnd++,s&&(r.providerIndexes+=1048576),n.push(g),i.push(g)}else{let g=Fi(n[s?m:h],l,!s&&c);p5(o,t,h>-1?h:m,g)}!s&&c&&M&&n[m].componentProviders++}}}function p5(t,e,n,c){let s=f4(e),o=Vm(e);if(s||o){let a=(o?L1(e.useClass):e).prototype.ngOnDestroy;if(a){let l=t.destroyHooks||(t.destroyHooks=[]);if(!s&&e.multi){let f=l.indexOf(n);f===-1?l.push(n,[c,a]):l[f+1].push(c,a)}else l.push(n,a)}}}function Fi(t,e,n){return n&&t.componentProviders++,t.multi.push(e)-1}function g5(t,e,n,c){for(let s=n;s<c;s++)if(e[s]===t)return s;return-1}function iL(t,e,n,c){return M7(this.multi,[])}function rL(t,e,n,c){let s=this.multi,o;if(this.providerFactory){let i=this.providerFactory.componentProviders,r=A3(n,n[T],this.providerFactory.index,c);o=r.slice(0,i),M7(s,o);for(let a=i;a<r.length;a++)o.push(r[a])}else o=[],M7(s,o);return o}function M7(t,e){for(let n=0;n<t.length;n++){let c=t[n];e.push(c())}return e}function aL(t,e,n,c,s){let o=new E3(t,n,z);return o.multi=[],o.index=e,o.componentProviders=0,Fi(o,s,c&&!n),o}function M1(t,e=[]){return n=>{n.providersResolver=(c,s)=>oL(c,s?s(t):t,e)}}var lL=(()=>{let e=class e{constructor(c){this._injector=c,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(c){if(!c.standalone)return null;if(!this.cachedInjectors.has(c)){let s=ls(!1,c.type),o=s.length>0?WC([s],this._injector,`Standalone[${c.type.name}]`):null;this.cachedInjectors.set(c,o)}return this.cachedInjectors.get(c)}ngOnDestroy(){try{for(let c of this.cachedInjectors.values())c!==null&&c.destroy()}finally{this.cachedInjectors.clear()}}};e.\u0275prov=y({token:e,providedIn:"environment",factory:()=>new e(L(h2))});let t=e;return t})();function vt(t){e3("NgStandalone"),t.getStandaloneInjector=e=>e.get(lL).getOrCreateStandaloneInjector(t)}function gk(t,e,n){let c=E2()+t,s=D();return s[c]===c1?b4(s,c,n?e.call(n):e()):ui(s,c)}function Mk(t,e,n,c){return Pi(D(),E2(),t,e,n,c)}function Ck(t,e,n,c,s){return Vi(D(),E2(),t,e,n,c,s)}function zk(t,e,n,c,s,o){return Oi(D(),E2(),t,e,n,c,s,o)}function Lk(t,e,n,c,s,o,i){return fL(D(),E2(),t,e,n,c,s,o,i)}function vk(t,e,n,c,s,o,i,r){let a=E2()+t,l=D(),f=v0(l,a,n,c,s,o);return E1(l,a+4,i)||f?b4(l,a+5,r?e.call(r,n,c,s,o,i):e(n,c,s,o,i)):ui(l,a+5)}function He(t,e){let n=t[e];return n===c1?void 0:n}function Pi(t,e,n,c,s,o){let i=e+n;return E1(t,i,s)?b4(t,i+1,o?c.call(o,s):c(s)):He(t,i+1)}function Vi(t,e,n,c,s,o,i){let r=e+n;return m4(t,r,s,o)?b4(t,r+2,i?c.call(i,s,o):c(s,o)):He(t,r+2)}function Oi(t,e,n,c,s,o,i,r){let a=e+n;return di(t,a,s,o,i)?b4(t,a+3,r?c.call(r,s,o,i):c(s,o,i)):He(t,a+3)}function fL(t,e,n,c,s,o,i,r,a){let l=e+n;return v0(t,l,s,o,i,r)?b4(t,l+4,a?c.call(a,s,o,i,r):c(s,o,i,r)):He(t,l+4)}function yk(t,e){var a;let n=K(),c,s=t+e1;n.firstCreatePass?(c=uL(e,n.pipeRegistry),n.data[s]=c,c.onDestroy&&((a=n.destroyHooks)!=null?a:n.destroyHooks=[]).push(s,c.onDestroy)):c=n.data[s];let o=c.factory||(c.factory=w3(c.type,!0)),i,r=S1(z);try{let l=K6(!1),f=o();return K6(l),Zz(n,D(),s,f),f}finally{S1(r)}}function uL(t,e){if(e)for(let n=e.length-1;n>=0;n--){let c=e[n];if(t===c.name)return c}}function bk(t,e,n){let c=t+e1,s=D(),o=ve(s,c);return yt(s,c)?Pi(s,E2(),e,o.transform,n,o):o.transform(n)}function xk(t,e,n,c){let s=t+e1,o=D(),i=ve(o,s);return yt(o,s)?Vi(o,E2(),e,i.transform,n,c,i):i.transform(n,c)}function Nk(t,e,n,c,s){let o=t+e1,i=D(),r=ve(i,o);return yt(i,o)?Oi(i,E2(),e,r.transform,n,c,s,r):r.transform(n,c,s)}function yt(t,e){return t[T].data[e].pure}function wk(t,e){return Fe(t,e)}var Ri=(()=>{let e=class e{log(c){console.log(c)}warn(c){console.warn(c)}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"platform"});let t=e;return t})();var Hi=new w("");function x4(t){return!!t&&typeof t.then=="function"}function bt(t){return!!t&&typeof t.subscribe=="function"}var dL=new w(""),ji=(()=>{let e=class e{constructor(){var c;this.initialized=!1,this.done=!1,this.donePromise=new Promise((s,o)=>{this.resolve=s,this.reject=o}),this.appInits=(c=x(dL,{optional:!0}))!=null?c:[]}runInitializers(){if(this.initialized)return;let c=[];for(let o of this.appInits){let i=o();if(x4(i))c.push(i);else if(bt(i)){let r=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});c.push(r)}}let s=()=>{this.done=!0,this.resolve()};Promise.all(c).then(()=>{s()}).catch(o=>{this.reject(o)}),c.length===0&&s(),this.initialized=!0}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),je=new w("");function hL(){Wn(()=>{throw new S(600,!1)})}function mL(t){return t.isBoundToModule}function pL(t,e,n){try{let c=n();return x4(c)?c.catch(s=>{throw e.runOutsideAngular(()=>t.handleError(s)),s}):c}catch(c){throw e.runOutsideAngular(()=>t.handleError(c)),c}}var N4=(()=>{let e=class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=x(Gs),this.afterRenderEffectManager=x(gt),this.externalTestViews=new Set,this.beforeRender=new U1,this.afterTick=new U1,this.componentTypes=[],this.components=[],this.isStable=x(Re).hasPendingTasks.pipe(o1(c=>!c)),this._injector=x(h2)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(c,s){let o=c instanceof ie;if(!this._injector.get(ji).done){let h=!o&&Im(c),m=!1;throw new S(405,m)}let r;o?r=c:r=this._injector.get(Oe).resolveComponentFactory(c),this.componentTypes.push(r.componentType);let a=mL(r)?void 0:this._injector.get(K2),l=s||r.selector,f=r.create(J2.NULL,[],l,a),u=f.location.nativeElement,d=f.injector.get(Hi,null);return d==null||d.registerApplication(u),f.onDestroy(()=>{this.detachView(f.hostView),M5(this.components,f),d==null||d.unregisterApplication(u)}),this._loadComponent(f),f}tick(){this._tick(!0)}_tick(c){if(this._runningTick)throw new S(101,!1);let s=O(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(c)}catch(o){this.internalErrorHandler(o)}finally{this.afterTick.next(),this._runningTick=!1,O(s)}}detectChangesInAttachedViews(c){let s=0,o=this.afterRenderEffectManager;for(;;){if(s===Zo)throw new S(103,!1);if(c){let i=s===0;this.beforeRender.next(i);for(let{_lView:r,notifyErrorHandler:a}of this._views)gL(r,i,a)}if(s++,o.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:i})=>C7(i))&&(o.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:i})=>C7(i))))break}}attachView(c){let s=c;this._views.push(s),s.attachToAppRef(this)}detachView(c){let s=c;M5(this._views,s),s.detachFromAppRef()}_loadComponent(c){this.attachView(c.hostView),this.tick(),this.components.push(c);let s=this._injector.get(je,[]);[...this._bootstrapListeners,...s].forEach(o=>o(c))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(c=>c()),this._views.slice().forEach(c=>c.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(c){return this._destroyListeners.push(c),()=>M5(this._destroyListeners,c)}destroy(){if(this._destroyed)throw new S(406,!1);let c=this._injector;c.destroy&&!c.destroyed&&c.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function M5(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}var G2;function xt(t){G2!=null||(G2=new WeakMap);let e=G2.get(t);if(e)return e;let n=t.isStable.pipe(U8(c=>c)).toPromise().then(()=>{});return G2.set(t,n),t.onDestroy(()=>G2==null?void 0:G2.delete(t)),n}function gL(t,e,n){!e&&!C7(t)||ML(t,n,e)}function C7(t){return R7(t)}function ML(t,e,n){let c;n?(c=0,t[E]|=1024):t[E]&64?c=0:c=1,Yo(t,e,c)}var z7=class{constructor(e,n){this.ngModuleFactory=e,this.componentFactories=n}},Dk=(()=>{let e=class e{compileModuleSync(c){return new d7(c)}compileModuleAsync(c){return Promise.resolve(this.compileModuleSync(c))}compileModuleAndAllComponentsSync(c){let s=this.compileModuleSync(c),o=is(c),i=vo(o.declarations).reduce((r,a)=>{let l=N2(a);return l&&r.push(new _3(l)),r},[]);return new z7(s,i)}compileModuleAndAllComponentsAsync(c){return Promise.resolve(this.compileModuleAndAllComponentsSync(c))}clearCache(){}clearCacheFor(c){}getModuleId(c){}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();var CL=(()=>{let e=class e{constructor(){this.zone=x(a1),this.applicationRef=x(N4)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){var c;(c=this._onMicrotaskEmptySubscription)==null||c.unsubscribe()}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function zL(t){return[{provide:a1,useFactory:t},{provide:l4,multi:!0,useFactory:()=>{let e=x(CL,{optional:!0});return()=>e.initialize()}},{provide:l4,multi:!0,useFactory:()=>{let e=x(bL);return()=>{e.initialize()}}},{provide:Gs,useFactory:LL}]}function LL(){let t=x(a1),e=x(m2);return n=>t.runOutsideAngular(()=>e.handleError(n))}function vL(t){let e=zL(()=>new a1(yL(t)));return F3([[],e])}function yL(t){var e,n;return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:(e=t==null?void 0:t.eventCoalescing)!=null?e:!1,shouldCoalesceRunChangeDetection:(n=t==null?void 0:t.runCoalescing)!=null?n:!1}}var bL=(()=>{let e=class e{constructor(){this.subscription=new t1,this.initialized=!1,this.zone=x(a1),this.pendingTasks=x(Re)}initialize(){if(this.initialized)return;this.initialized=!0;let c=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(c=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{a1.assertNotInAngularZone(),queueMicrotask(()=>{c!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(c),c=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{a1.assertInAngularZone(),c!=null||(c=this.pendingTasks.add())}))}ngOnDestroy(){this.subscription.unsubscribe()}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();function xL(){return typeof $localize<"u"&&$localize.locale||de}var x0=new w("",{providedIn:"root",factory:()=>x(x0,R.Optional|R.SkipSelf)||xL()}),Bi=new w("",{providedIn:"root",factory:()=>Rz});var Ui=new w("");var U6=null;function NL(t=[],e){return J2.create({name:e,providers:[{provide:Me,useValue:"platform"},{provide:Ui,useValue:new Set([()=>U6=null])},...t]})}function wL(t=[]){if(U6)return U6;let e=NL(t);return U6=e,hL(),DL(e),e}function DL(t){let e=t.get(W7,null);e==null||e.forEach(n=>n())}var R3=(()=>{let e=class e{};e.__NG_ELEMENT_ID__=SL;let t=e;return t})();function SL(t){return EL(p1(),D(),(t&16)===16)}function EL(t,e,n){if(g0(t)&&!n){let c=X2(t.index,e);return new I3(c,c)}else if(t.type&47){let c=e[V1];return new I3(c,e)}return null}var L7=class{constructor(){}supports(e){return fi(e)}create(e){return new v7(e)}},AL=(t,e)=>e,v7=class{constructor(e){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=e||AL}forEachItem(e){let n;for(n=this._itHead;n!==null;n=n._next)e(n)}forEachOperation(e){let n=this._itHead,c=this._removalsHead,s=0,o=null;for(;n||c;){let i=!c||n&&n.currentIndex<F9(c,s,o)?n:c,r=F9(i,s,o),a=i.currentIndex;if(i===c)s--,c=c._nextRemoved;else if(n=n._next,i.previousIndex==null)s++;else{o||(o=[]);let l=r-s,f=a-s;if(l!=f){for(let d=0;d<l;d++){let h=d<o.length?o[d]:o[d]=0,m=h+d;f<=m&&m<l&&(o[d]=h+1)}let u=i.previousIndex;o[u]=f-l}}r!==a&&e(i,r,a)}}forEachPreviousItem(e){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachMovedItem(e){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}forEachIdentityChange(e){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)e(n)}diff(e){if(e==null&&(e=[]),!fi(e))throw new S(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._itHead,c=!1,s,o,i;if(Array.isArray(e)){this.length=e.length;for(let r=0;r<this.length;r++)o=e[r],i=this._trackByFn(r,o),n===null||!Object.is(n.trackById,i)?(n=this._mismatch(n,o,i,r),c=!0):(c&&(n=this._verifyReinsertion(n,o,i,r)),Object.is(n.item,o)||this._addIdentityChange(n,o)),n=n._next}else s=0,ZC(e,r=>{i=this._trackByFn(s,r),n===null||!Object.is(n.trackById,i)?(n=this._mismatch(n,r,i,s),c=!0):(c&&(n=this._verifyReinsertion(n,r,i,s)),Object.is(n.item,r)||this._addIdentityChange(n,r)),n=n._next,s++}),this.length=s;return this._truncate(n),this.collection=e,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let e;for(e=this._previousItHead=this._itHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;e!==null;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;e!==null;e=e._nextMoved)e.previousIndex=e.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(e,n,c,s){let o;return e===null?o=this._itTail:(o=e._prev,this._remove(e)),e=this._unlinkedRecords===null?null:this._unlinkedRecords.get(c,null),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._reinsertAfter(e,o,s)):(e=this._linkedRecords===null?null:this._linkedRecords.get(c,s),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._moveAfter(e,o,s)):e=this._addAfter(new y7(n,c),o,s)),e}_verifyReinsertion(e,n,c,s){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(c,null);return o!==null?e=this._reinsertAfter(o,e._prev,s):e.currentIndex!=s&&(e.currentIndex=s,this._addToMoves(e,s)),e}_truncate(e){for(;e!==null;){let n=e._next;this._addToRemovals(this._unlink(e)),e=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(e,n,c){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(e);let s=e._prevRemoved,o=e._nextRemoved;return s===null?this._removalsHead=o:s._nextRemoved=o,o===null?this._removalsTail=s:o._prevRemoved=s,this._insertAfter(e,n,c),this._addToMoves(e,c),e}_moveAfter(e,n,c){return this._unlink(e),this._insertAfter(e,n,c),this._addToMoves(e,c),e}_addAfter(e,n,c){return this._insertAfter(e,n,c),this._additionsTail===null?this._additionsTail=this._additionsHead=e:this._additionsTail=this._additionsTail._nextAdded=e,e}_insertAfter(e,n,c){let s=n===null?this._itHead:n._next;return e._next=s,e._prev=n,s===null?this._itTail=e:s._prev=e,n===null?this._itHead=e:n._next=e,this._linkedRecords===null&&(this._linkedRecords=new he),this._linkedRecords.put(e),e.currentIndex=c,e}_remove(e){return this._addToRemovals(this._unlink(e))}_unlink(e){this._linkedRecords!==null&&this._linkedRecords.remove(e);let n=e._prev,c=e._next;return n===null?this._itHead=c:n._next=c,c===null?this._itTail=n:c._prev=n,e}_addToMoves(e,n){return e.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=e:this._movesTail=this._movesTail._nextMoved=e),e}_addToRemovals(e){return this._unlinkedRecords===null&&(this._unlinkedRecords=new he),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e}_addIdentityChange(e,n){return e.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=e:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=e,e}},y7=class{constructor(e,n){this.item=e,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},b7=class{constructor(){this._head=null,this._tail=null}add(e){this._head===null?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)}get(e,n){let c;for(c=this._head;c!==null;c=c._nextDup)if((n===null||n<=c.currentIndex)&&Object.is(c.trackById,e))return c;return null}remove(e){let n=e._prevDup,c=e._nextDup;return n===null?this._head=c:n._nextDup=c,c===null?this._tail=n:c._prevDup=n,this._head===null}},he=class{constructor(){this.map=new Map}put(e){let n=e.trackById,c=this.map.get(n);c||(c=new b7,this.map.set(n,c)),c.add(e)}get(e,n){let c=e,s=this.map.get(c);return s?s.get(e,n):null}remove(e){let n=e.trackById;return this.map.get(n).remove(e)&&this.map.delete(n),e}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function F9(t,e,n){let c=t.previousIndex;if(c===null)return c;let s=0;return n&&c<n.length&&(s=n[c]),c+e+s}var x7=class{constructor(){}supports(e){return e instanceof Map||zt(e)}create(){return new N7}},N7=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(e){let n;for(n=this._mapHead;n!==null;n=n._next)e(n)}forEachPreviousItem(e){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)e(n)}forEachChangedItem(e){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}diff(e){if(!e)e=new Map;else if(!(e instanceof Map||zt(e)))throw new S(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(e,(c,s)=>{if(n&&n.key===s)this._maybeAddToChanges(n,c),this._appendAfter=n,n=n._next;else{let o=this._getOrCreateRecordForKey(s,c);n=this._insertBeforeOrAppend(n,o)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let c=n;c!==null;c=c._nextRemoved)c===this._mapHead&&(this._mapHead=null),this._records.delete(c.key),c._nextRemoved=c._next,c.previousValue=c.currentValue,c.currentValue=null,c._prev=null,c._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(e,n){if(e){let c=e._prev;return n._next=e,n._prev=c,e._prev=n,c&&(c._next=n),e===this._mapHead&&(this._mapHead=n),this._appendAfter=e,e}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(e,n){if(this._records.has(e)){let s=this._records.get(e);this._maybeAddToChanges(s,n);let o=s._prev,i=s._next;return o&&(o._next=i),i&&(i._prev=o),s._next=null,s._prev=null,s}let c=new w7(e);return this._records.set(e,c),c.currentValue=n,this._addToAdditions(c),c}_reset(){if(this.isDirty){let e;for(this._previousMapHead=this._mapHead,e=this._previousMapHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._changesHead;e!==null;e=e._nextChanged)e.previousValue=e.currentValue;for(e=this._additionsHead;e!=null;e=e._nextAdded)e.previousValue=e.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(e,n){Object.is(n,e.currentValue)||(e.previousValue=e.currentValue,e.currentValue=n,this._addToChanges(e))}_addToAdditions(e){this._additionsHead===null?this._additionsHead=this._additionsTail=e:(this._additionsTail._nextAdded=e,this._additionsTail=e)}_addToChanges(e){this._changesHead===null?this._changesHead=this._changesTail=e:(this._changesTail._nextChanged=e,this._changesTail=e)}_forEach(e,n){e instanceof Map?e.forEach(n):Object.keys(e).forEach(c=>n(e[c],c))}},w7=class{constructor(e){this.key=e,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function P9(){return new Nt([new L7])}var Nt=(()=>{let e=class e{constructor(c){this.factories=c}static create(c,s){if(s!=null){let o=s.factories.slice();c=c.concat(o)}return new e(c)}static extend(c){return{provide:e,useFactory:s=>e.create(c,s||P9()),deps:[[e,new Q9,new S7]]}}find(c){let s=this.factories.find(o=>o.supports(c));if(s!=null)return s;throw new S(901,!1)}};e.\u0275prov=y({token:e,providedIn:"root",factory:P9});let t=e;return t})();function V9(){return new wt([new x7])}var wt=(()=>{let e=class e{constructor(c){this.factories=c}static create(c,s){if(s){let o=s.factories.slice();c=c.concat(o)}return new e(c)}static extend(c){return{provide:e,useFactory:s=>e.create(c,s||V9()),deps:[[e,new Q9,new S7]]}}find(c){let s=this.factories.find(o=>o.supports(c));if(s)return s;throw new S(901,!1)}};e.\u0275prov=y({token:e,providedIn:"root",factory:V9});let t=e;return t})();function $i(t){try{let{rootComponent:e,appProviders:n,platformProviders:c}=t,s=wL(c),o=[vL(),...n||[]],r=new fe({providers:o,parent:s,debugName:"",runEnvironmentInitializers:!1}).injector,a=r.get(a1);return a.run(()=>{r.resolveInjectorInitializers();let l=r.get(m2,null),f;a.runOutsideAngular(()=>{f=a.onError.subscribe({next:h=>{l.handleError(h)}})});let u=()=>r.destroy(),d=s.get(Ui);return d.add(u),r.onDestroy(()=>{f.unsubscribe(),d.delete(u)}),pL(l,a,()=>{let h=r.get(ji);return h.runInitializers(),h.donePromise.then(()=>{let m=r.get(x0,de);jz(m||de);let C=r.get(N4);return e!==void 0&&C.bootstrap(e),C})})})}catch(e){return Promise.reject(e)}}var O9=!1,IL=!1;function TL(){O9||(O9=!0,sg(),Az(),Qz(),Fz(),XC(),DC(),tC(),iM(),$z())}function _L(t,e){return xt(t)}function qi(){return F3([{provide:T6,useFactory:()=>{let t=!0;if(t0()){let e=x(O3,{optional:!0});t=!!(e!=null&&e.get(no,null))}return t&&e3("NgHydration"),t}},{provide:l4,useValue:()=>{IL=!!x(lg,{optional:!0}),t0()&&x(T6)&&(kL(),TL())},multi:!0},{provide:ro,useFactory:()=>t0()&&x(T6)},{provide:je,useFactory:()=>{if(t0()&&x(T6)){let t=x(N4),e=x(J2);return()=>{_L(t,e).then(()=>{GM(t)})}}return()=>{}},multi:!0}])}function kL(){var n;let t=z0(),e;for(let c of t.body.childNodes)if(c.nodeType===Node.COMMENT_NODE&&((n=c.textContent)==null?void 0:n.trim())===cg){e=c;break}if(!e)throw new S(-507,!1)}function Be(t){return typeof t=="boolean"?t:t!=null&&t!=="false"}function Dt(t){let e=O(null);try{return t()}finally{O(e)}}function Sk(t,e){let n=N2(t),c=e.elementInjector||Ce();return new _3(n).create(c,e.projectableNodes,e.hostElement,e.environmentInjector)}function Ek(t){let e=N2(t);if(!e)return null;let n=new _3(e);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return e.standalone},get isSignal(){return e.signals}}}var Ye=null;function O2(){return Ye}function tr(t){Ye!=null||(Ye=t)}var Ke=class{};var N1=new w(""),Ht=(()=>{let e=class e{historyGo(c){throw new Error("")}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:()=>x(FL),providedIn:"platform"});let t=e;return t})(),Zk=new w(""),FL=(()=>{let e=class e extends Ht{constructor(){super(),this._doc=x(N1),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return O2().getBaseHref(this._doc)}onPopState(c){let s=O2().getGlobalEventTarget(this._doc,"window");return s.addEventListener("popstate",c,!1),()=>s.removeEventListener("popstate",c)}onHashChange(c){let s=O2().getGlobalEventTarget(this._doc,"window");return s.addEventListener("hashchange",c,!1),()=>s.removeEventListener("hashchange",c)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(c){this._location.pathname=c}pushState(c,s,o){this._history.pushState(c,s,o)}replaceState(c,s,o){this._history.replaceState(c,s,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(c=0){this._history.go(c)}getState(){return this._history.state}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:()=>new e,providedIn:"platform"});let t=e;return t})();function jt(t,e){if(t.length==0)return e;if(e.length==0)return t;let n=0;return t.endsWith("/")&&n++,e.startsWith("/")&&n++,n==2?t+e.substring(1):n==1?t+e:t+"/"+e}function Gi(t){let e=t.match(/#|\?|$/),n=e&&e.index||t.length,c=n-(t[n-1]==="/"?1:0);return t.slice(0,c)+t.slice(n)}function F2(t){return t&&t[0]!=="?"?"?"+t:t}var e8=(()=>{let e=class e{historyGo(c){throw new Error("")}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:()=>x(PL),providedIn:"root"});let t=e;return t})(),cr=new w(""),PL=(()=>{let e=class e extends e8{constructor(c,s){var o,i,r;super(),this._platformLocation=c,this._removeListenerFns=[],this._baseHref=(r=(i=s!=null?s:this._platformLocation.getBaseHrefFromDOM())!=null?i:(o=x(N1).location)==null?void 0:o.origin)!=null?r:""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(c){this._removeListenerFns.push(this._platformLocation.onPopState(c),this._platformLocation.onHashChange(c))}getBaseHref(){return this._baseHref}prepareExternalUrl(c){return jt(this._baseHref,c)}path(c=!1){let s=this._platformLocation.pathname+F2(this._platformLocation.search),o=this._platformLocation.hash;return o&&c?`${s}${o}`:s}pushState(c,s,o,i){let r=this.prepareExternalUrl(o+F2(i));this._platformLocation.pushState(c,s,r)}replaceState(c,s,o,i){let r=this.prepareExternalUrl(o+F2(i));this._platformLocation.replaceState(c,s,r)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(c=0){var s,o;(o=(s=this._platformLocation).historyGo)==null||o.call(s,c)}};e.\u0275fac=function(s){return new(s||e)(L(Ht),L(cr,8))},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),Yk=(()=>{let e=class e extends e8{constructor(c,s){super(),this._platformLocation=c,this._baseHref="",this._removeListenerFns=[],s!=null&&(this._baseHref=s)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(c){this._removeListenerFns.push(this._platformLocation.onPopState(c),this._platformLocation.onHashChange(c))}getBaseHref(){return this._baseHref}path(c=!1){var o;let s=(o=this._platformLocation.hash)!=null?o:"#";return s.length>0?s.substring(1):s}prepareExternalUrl(c){let s=jt(this._baseHref,c);return s.length>0?"#"+s:s}pushState(c,s,o,i){let r=this.prepareExternalUrl(o+F2(i));r.length==0&&(r=this._platformLocation.pathname),this._platformLocation.pushState(c,s,r)}replaceState(c,s,o,i){let r=this.prepareExternalUrl(o+F2(i));r.length==0&&(r=this._platformLocation.pathname),this._platformLocation.replaceState(c,s,r)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(c=0){var s,o;(o=(s=this._platformLocation).historyGo)==null||o.call(s,c)}};e.\u0275fac=function(s){return new(s||e)(L(Ht),L(cr,8))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),VL=(()=>{let e=class e{constructor(c){this._subject=new l1,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=c;let s=this._locationStrategy.getBaseHref();this._basePath=HL(Gi(Wi(s))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){var c;(c=this._urlChangeSubscription)==null||c.unsubscribe(),this._urlChangeListeners=[]}path(c=!1){return this.normalize(this._locationStrategy.path(c))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(c,s=""){return this.path()==this.normalize(c+F2(s))}normalize(c){return e.stripTrailingSlash(RL(this._basePath,Wi(c)))}prepareExternalUrl(c){return c&&c[0]!=="/"&&(c="/"+c),this._locationStrategy.prepareExternalUrl(c)}go(c,s="",o=null){this._locationStrategy.pushState(o,"",c,s),this._notifyUrlChangeListeners(this.prepareExternalUrl(c+F2(s)),o)}replaceState(c,s="",o=null){this._locationStrategy.replaceState(o,"",c,s),this._notifyUrlChangeListeners(this.prepareExternalUrl(c+F2(s)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(c=0){var s,o;(o=(s=this._locationStrategy).historyGo)==null||o.call(s,c)}onUrlChange(c){var s;return this._urlChangeListeners.push(c),(s=this._urlChangeSubscription)!=null||(this._urlChangeSubscription=this.subscribe(o=>{this._notifyUrlChangeListeners(o.url,o.state)})),()=>{var i;let o=this._urlChangeListeners.indexOf(c);this._urlChangeListeners.splice(o,1),this._urlChangeListeners.length===0&&((i=this._urlChangeSubscription)==null||i.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(c="",s){this._urlChangeListeners.forEach(o=>o(c,s))}subscribe(c,s,o){return this._subject.subscribe({next:c,error:s,complete:o})}};e.normalizeQueryParams=F2,e.joinWithSlash=jt,e.stripTrailingSlash=Gi,e.\u0275fac=function(s){return new(s||e)(L(e8))},e.\u0275prov=y({token:e,factory:()=>OL(),providedIn:"root"});let t=e;return t})();function OL(){return new VL(L(e8))}function RL(t,e){if(!t||!e.startsWith(t))return e;let n=e.substring(t.length);return n===""||["/",";","?","#"].includes(n[0])?n:e}function Wi(t){return t.replace(/\/index.html$/,"")}function HL(t){if(new RegExp("^(https?:)?//").test(t)){let[,n]=t.split(/\/\/[^\/]+/);return n}return t}var nr={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},sr=function(t){return t[t.Decimal=0]="Decimal",t[t.Percent=1]="Percent",t[t.Currency=2]="Currency",t[t.Scientific=3]="Scientific",t}(sr||{});var x1=function(t){return t[t.Format=0]="Format",t[t.Standalone=1]="Standalone",t}(x1||{}),G=function(t){return t[t.Narrow=0]="Narrow",t[t.Abbreviated=1]="Abbreviated",t[t.Wide=2]="Wide",t[t.Short=3]="Short",t}(G||{}),H1=function(t){return t[t.Short=0]="Short",t[t.Medium=1]="Medium",t[t.Long=2]="Long",t[t.Full=3]="Full",t}(H1||{}),j1={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function jL(t){return _1(t)[X.LocaleId]}function BL(t,e,n){let c=_1(t),s=[c[X.DayPeriodsFormat],c[X.DayPeriodsStandalone]],o=Y1(s,e);return Y1(o,n)}function UL(t,e,n){let c=_1(t),s=[c[X.DaysFormat],c[X.DaysStandalone]],o=Y1(s,e);return Y1(o,n)}function $L(t,e,n){let c=_1(t),s=[c[X.MonthsFormat],c[X.MonthsStandalone]],o=Y1(s,e);return Y1(o,n)}function qL(t,e){let c=_1(t)[X.Eras];return Y1(c,e)}function Ue(t,e){let n=_1(t);return Y1(n[X.DateFormat],e)}function $e(t,e){let n=_1(t);return Y1(n[X.TimeFormat],e)}function qe(t,e){let c=_1(t)[X.DateTimeFormat];return Y1(c,e)}function P2(t,e){let n=_1(t),c=n[X.NumberSymbols][e];if(typeof c>"u"){if(e===j1.CurrencyDecimal)return n[X.NumberSymbols][j1.Decimal];if(e===j1.CurrencyGroup)return n[X.NumberSymbols][j1.Group]}return c}function GL(t,e){return _1(t)[X.NumberFormats][e]}function WL(t){return _1(t)[X.Currencies]}function or(t){if(!t[X.ExtraData])throw new Error(`Missing extra locale data for the locale "${t[X.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function ZL(t){let e=_1(t);return or(e),(e[X.ExtraData][2]||[]).map(c=>typeof c=="string"?St(c):[St(c[0]),St(c[1])])}function YL(t,e,n){let c=_1(t);or(c);let s=[c[X.ExtraData][0],c[X.ExtraData][1]],o=Y1(s,e)||[];return Y1(o,n)||[]}function Y1(t,e){for(let n=e;n>-1;n--)if(typeof t[n]<"u")return t[n];throw new Error("Locale data API: locale data undefined")}function St(t){let[e,n]=t.split(":");return{hours:+e,minutes:+n}}function QL(t,e,n="en"){let c=WL(n)[t]||nr[t]||[],s=c[1];return e==="narrow"&&typeof s=="string"?s:c[0]||t}var KL=2;function XL(t){let e,n=nr[t];return n&&(e=n[2]),typeof e=="number"?e:KL}var JL=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,w4={},ev=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,V2=function(t){return t[t.Short=0]="Short",t[t.ShortGMT=1]="ShortGMT",t[t.Long=2]="Long",t[t.Extended=3]="Extended",t}(V2||{}),B=function(t){return t[t.FullYear=0]="FullYear",t[t.Month=1]="Month",t[t.Date=2]="Date",t[t.Hours=3]="Hours",t[t.Minutes=4]="Minutes",t[t.Seconds=5]="Seconds",t[t.FractionalSeconds=6]="FractionalSeconds",t[t.Day=7]="Day",t}(B||{}),j=function(t){return t[t.DayPeriods=0]="DayPeriods",t[t.Days=1]="Days",t[t.Months=2]="Months",t[t.Eras=3]="Eras",t}(j||{});function tv(t,e,n,c){let s=fv(t);e=k2(n,e)||e;let i=[],r;for(;e;)if(r=ev.exec(e),r){i=i.concat(r.slice(1));let f=i.pop();if(!f)break;e=f}else{i.push(e);break}let a=s.getTimezoneOffset();c&&(a=rr(c,a),s=lv(s,c,!0));let l="";return i.forEach(f=>{let u=rv(f);l+=u?u(s,n,a):f==="''"?"'":f.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function Xe(t,e,n){let c=new Date(0);return c.setFullYear(t,e,n),c.setHours(0,0,0),c}function k2(t,e){var s;let n=jL(t);if((s=w4[n])!=null||(w4[n]={}),w4[n][e])return w4[n][e];let c="";switch(e){case"shortDate":c=Ue(t,H1.Short);break;case"mediumDate":c=Ue(t,H1.Medium);break;case"longDate":c=Ue(t,H1.Long);break;case"fullDate":c=Ue(t,H1.Full);break;case"shortTime":c=$e(t,H1.Short);break;case"mediumTime":c=$e(t,H1.Medium);break;case"longTime":c=$e(t,H1.Long);break;case"fullTime":c=$e(t,H1.Full);break;case"short":let o=k2(t,"shortTime"),i=k2(t,"shortDate");c=Ge(qe(t,H1.Short),[o,i]);break;case"medium":let r=k2(t,"mediumTime"),a=k2(t,"mediumDate");c=Ge(qe(t,H1.Medium),[r,a]);break;case"long":let l=k2(t,"longTime"),f=k2(t,"longDate");c=Ge(qe(t,H1.Long),[l,f]);break;case"full":let u=k2(t,"fullTime"),d=k2(t,"fullDate");c=Ge(qe(t,H1.Full),[u,d]);break}return c&&(w4[n][e]=c),c}function Ge(t,e){return e&&(t=t.replace(/\{([^}]+)}/g,function(n,c){return e!=null&&c in e?e[c]:n})),t}function r2(t,e,n="-",c,s){let o="";(t<0||s&&t<=0)&&(s?t=-t+1:(t=-t,o=n));let i=String(t);for(;i.length<e;)i="0"+i;return c&&(i=i.slice(i.length-e)),o+i}function cv(t,e){return r2(t,3).substring(0,e)}function r1(t,e,n=0,c=!1,s=!1){return function(o,i){let r=nv(t,o);if((n>0||r>-n)&&(r+=n),t===B.Hours)r===0&&n===-12&&(r=12);else if(t===B.FractionalSeconds)return cv(r,e);let a=P2(i,j1.MinusSign);return r2(r,e,a,c,s)}}function nv(t,e){switch(t){case B.FullYear:return e.getFullYear();case B.Month:return e.getMonth();case B.Date:return e.getDate();case B.Hours:return e.getHours();case B.Minutes:return e.getMinutes();case B.Seconds:return e.getSeconds();case B.FractionalSeconds:return e.getMilliseconds();case B.Day:return e.getDay();default:throw new Error(`Unknown DateType value "${t}".`)}}function W(t,e,n=x1.Format,c=!1){return function(s,o){return sv(s,o,t,e,n,c)}}function sv(t,e,n,c,s,o){switch(n){case j.Months:return $L(e,s,c)[t.getMonth()];case j.Days:return UL(e,s,c)[t.getDay()];case j.DayPeriods:let i=t.getHours(),r=t.getMinutes();if(o){let l=ZL(e),f=YL(e,s,c),u=l.findIndex(d=>{if(Array.isArray(d)){let[h,m]=d,C=i>=h.hours&&r>=h.minutes,M=i<m.hours||i===m.hours&&r<m.minutes;if(h.hours<m.hours){if(C&&M)return!0}else if(C||M)return!0}else if(d.hours===i&&d.minutes===r)return!0;return!1});if(u!==-1)return f[u]}return BL(e,s,c)[i<12?0:1];case j.Eras:return qL(e,c)[t.getFullYear()<=0?0:1];default:let a=n;throw new Error(`unexpected translation type ${a}`)}}function We(t){return function(e,n,c){let s=-1*c,o=P2(n,j1.MinusSign),i=s>0?Math.floor(s/60):Math.ceil(s/60);switch(t){case V2.Short:return(s>=0?"+":"")+r2(i,2,o)+r2(Math.abs(s%60),2,o);case V2.ShortGMT:return"GMT"+(s>=0?"+":"")+r2(i,1,o);case V2.Long:return"GMT"+(s>=0?"+":"")+r2(i,2,o)+":"+r2(Math.abs(s%60),2,o);case V2.Extended:return c===0?"Z":(s>=0?"+":"")+r2(i,2,o)+":"+r2(Math.abs(s%60),2,o);default:throw new Error(`Unknown zone width "${t}"`)}}}var ov=0,Qe=4;function iv(t){let e=Xe(t,ov,1).getDay();return Xe(t,0,1+(e<=Qe?Qe:Qe+7)-e)}function ir(t){let e=t.getDay(),n=e===0?-3:Qe-e;return Xe(t.getFullYear(),t.getMonth(),t.getDate()+n)}function Et(t,e=!1){return function(n,c){let s;if(e){let o=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,i=n.getDate();s=1+Math.floor((i+o)/7)}else{let o=ir(n),i=iv(o.getFullYear()),r=o.getTime()-i.getTime();s=1+Math.round(r/6048e5)}return r2(s,t,P2(c,j1.MinusSign))}}function Ze(t,e=!1){return function(n,c){let o=ir(n).getFullYear();return r2(o,t,P2(c,j1.MinusSign),e)}}var At={};function rv(t){if(At[t])return At[t];let e;switch(t){case"G":case"GG":case"GGG":e=W(j.Eras,G.Abbreviated);break;case"GGGG":e=W(j.Eras,G.Wide);break;case"GGGGG":e=W(j.Eras,G.Narrow);break;case"y":e=r1(B.FullYear,1,0,!1,!0);break;case"yy":e=r1(B.FullYear,2,0,!0,!0);break;case"yyy":e=r1(B.FullYear,3,0,!1,!0);break;case"yyyy":e=r1(B.FullYear,4,0,!1,!0);break;case"Y":e=Ze(1);break;case"YY":e=Ze(2,!0);break;case"YYY":e=Ze(3);break;case"YYYY":e=Ze(4);break;case"M":case"L":e=r1(B.Month,1,1);break;case"MM":case"LL":e=r1(B.Month,2,1);break;case"MMM":e=W(j.Months,G.Abbreviated);break;case"MMMM":e=W(j.Months,G.Wide);break;case"MMMMM":e=W(j.Months,G.Narrow);break;case"LLL":e=W(j.Months,G.Abbreviated,x1.Standalone);break;case"LLLL":e=W(j.Months,G.Wide,x1.Standalone);break;case"LLLLL":e=W(j.Months,G.Narrow,x1.Standalone);break;case"w":e=Et(1);break;case"ww":e=Et(2);break;case"W":e=Et(1,!0);break;case"d":e=r1(B.Date,1);break;case"dd":e=r1(B.Date,2);break;case"c":case"cc":e=r1(B.Day,1);break;case"ccc":e=W(j.Days,G.Abbreviated,x1.Standalone);break;case"cccc":e=W(j.Days,G.Wide,x1.Standalone);break;case"ccccc":e=W(j.Days,G.Narrow,x1.Standalone);break;case"cccccc":e=W(j.Days,G.Short,x1.Standalone);break;case"E":case"EE":case"EEE":e=W(j.Days,G.Abbreviated);break;case"EEEE":e=W(j.Days,G.Wide);break;case"EEEEE":e=W(j.Days,G.Narrow);break;case"EEEEEE":e=W(j.Days,G.Short);break;case"a":case"aa":case"aaa":e=W(j.DayPeriods,G.Abbreviated);break;case"aaaa":e=W(j.DayPeriods,G.Wide);break;case"aaaaa":e=W(j.DayPeriods,G.Narrow);break;case"b":case"bb":case"bbb":e=W(j.DayPeriods,G.Abbreviated,x1.Standalone,!0);break;case"bbbb":e=W(j.DayPeriods,G.Wide,x1.Standalone,!0);break;case"bbbbb":e=W(j.DayPeriods,G.Narrow,x1.Standalone,!0);break;case"B":case"BB":case"BBB":e=W(j.DayPeriods,G.Abbreviated,x1.Format,!0);break;case"BBBB":e=W(j.DayPeriods,G.Wide,x1.Format,!0);break;case"BBBBB":e=W(j.DayPeriods,G.Narrow,x1.Format,!0);break;case"h":e=r1(B.Hours,1,-12);break;case"hh":e=r1(B.Hours,2,-12);break;case"H":e=r1(B.Hours,1);break;case"HH":e=r1(B.Hours,2);break;case"m":e=r1(B.Minutes,1);break;case"mm":e=r1(B.Minutes,2);break;case"s":e=r1(B.Seconds,1);break;case"ss":e=r1(B.Seconds,2);break;case"S":e=r1(B.FractionalSeconds,1);break;case"SS":e=r1(B.FractionalSeconds,2);break;case"SSS":e=r1(B.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":e=We(V2.Short);break;case"ZZZZZ":e=We(V2.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":e=We(V2.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":e=We(V2.Long);break;default:return null}return At[t]=e,e}function rr(t,e){t=t.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+t)/6e4;return isNaN(n)?e:n}function av(t,e){return t=new Date(t.getTime()),t.setMinutes(t.getMinutes()+e),t}function lv(t,e,n){let c=n?-1:1,s=t.getTimezoneOffset(),o=rr(e,s);return av(t,c*(o-s))}function fv(t){if(Zi(t))return t;if(typeof t=="number"&&!isNaN(t))return new Date(t);if(typeof t=="string"){if(t=t.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(t)){let[s,o=1,i=1]=t.split("-").map(r=>+r);return Xe(s,o-1,i)}let n=parseFloat(t);if(!isNaN(t-n))return new Date(n);let c;if(c=t.match(JL))return uv(c)}let e=new Date(t);if(!Zi(e))throw new Error(`Unable to convert "${t}" into a date`);return e}function uv(t){let e=new Date(0),n=0,c=0,s=t[8]?e.setUTCFullYear:e.setFullYear,o=t[8]?e.setUTCHours:e.setHours;t[9]&&(n=Number(t[9]+t[10]),c=Number(t[9]+t[11])),s.call(e,Number(t[1]),Number(t[2])-1,Number(t[3]));let i=Number(t[4]||0)-n,r=Number(t[5]||0)-c,a=Number(t[6]||0),l=Math.floor(parseFloat("0."+(t[7]||0))*1e3);return o.call(e,i,r,a,l),e}function Zi(t){return t instanceof Date&&!isNaN(t.valueOf())}var dv=/^(\d+)?\.((\d+)(-(\d+))?)?$/,Yi=22,Je=".",N0="0",hv=";",mv=",",It="#",Qi="\xA4";function pv(t,e,n,c,s,o,i=!1){let r="",a=!1;if(!isFinite(t))r=P2(n,j1.Infinity);else{let l=zv(t);i&&(l=Cv(l));let f=e.minInt,u=e.minFrac,d=e.maxFrac;if(o){let b=o.match(dv);if(b===null)throw new Error(`${o} is not a valid digit info`);let V=b[1],A=b[3],J=b[5];V!=null&&(f=Tt(V)),A!=null&&(u=Tt(A)),J!=null?d=Tt(J):A!=null&&u>d&&(d=u)}Lv(l,u,d);let h=l.digits,m=l.integerLen,C=l.exponent,M=[];for(a=h.every(b=>!b);m<f;m++)h.unshift(0);for(;m<0;m++)h.unshift(0);m>0?M=h.splice(m,h.length):(M=h,h=[0]);let g=[];for(h.length>=e.lgSize&&g.unshift(h.splice(-e.lgSize,h.length).join(""));h.length>e.gSize;)g.unshift(h.splice(-e.gSize,h.length).join(""));h.length&&g.unshift(h.join("")),r=g.join(P2(n,c)),M.length&&(r+=P2(n,s)+M.join("")),C&&(r+=P2(n,j1.Exponential)+"+"+C)}return t<0&&!a?r=e.negPre+r+e.negSuf:r=e.posPre+r+e.posSuf,r}function gv(t,e,n,c,s){let o=GL(e,sr.Currency),i=Mv(o,P2(e,j1.MinusSign));return i.minFrac=XL(c),i.maxFrac=i.minFrac,pv(t,i,e,j1.CurrencyGroup,j1.CurrencyDecimal,s).replace(Qi,n).replace(Qi,"").trim()}function Mv(t,e="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},c=t.split(hv),s=c[0],o=c[1],i=s.indexOf(Je)!==-1?s.split(Je):[s.substring(0,s.lastIndexOf(N0)+1),s.substring(s.lastIndexOf(N0)+1)],r=i[0],a=i[1]||"";n.posPre=r.substring(0,r.indexOf(It));for(let f=0;f<a.length;f++){let u=a.charAt(f);u===N0?n.minFrac=n.maxFrac=f+1:u===It?n.maxFrac=f+1:n.posSuf+=u}let l=r.split(mv);if(n.gSize=l[1]?l[1].length:0,n.lgSize=l[2]||l[1]?(l[2]||l[1]).length:0,o){let f=s.length-n.posPre.length-n.posSuf.length,u=o.indexOf(It);n.negPre=o.substring(0,u).replace(/'/g,""),n.negSuf=o.slice(u+f).replace(/'/g,"")}else n.negPre=e+n.posPre,n.negSuf=n.posSuf;return n}function Cv(t){if(t.digits[0]===0)return t;let e=t.digits.length-t.integerLen;return t.exponent?t.exponent+=2:(e===0?t.digits.push(0,0):e===1&&t.digits.push(0),t.integerLen+=2),t}function zv(t){let e=Math.abs(t)+"",n=0,c,s,o,i,r;for((s=e.indexOf(Je))>-1&&(e=e.replace(Je,"")),(o=e.search(/e/i))>0?(s<0&&(s=o),s+=+e.slice(o+1),e=e.substring(0,o)):s<0&&(s=e.length),o=0;e.charAt(o)===N0;o++);if(o===(r=e.length))c=[0],s=1;else{for(r--;e.charAt(r)===N0;)r--;for(s-=o,c=[],i=0;o<=r;o++,i++)c[i]=Number(e.charAt(o))}return s>Yi&&(c=c.splice(0,Yi-1),n=s-1,s=1),{digits:c,exponent:n,integerLen:s}}function Lv(t,e,n){if(e>n)throw new Error(`The minimum number of digits after fraction (${e}) is higher than the maximum (${n}).`);let c=t.digits,s=c.length-t.integerLen,o=Math.min(Math.max(e,s),n),i=o+t.integerLen,r=c[i];if(i>0){c.splice(Math.max(t.integerLen,i));for(let u=i;u<c.length;u++)c[u]=0}else{s=Math.max(0,s),t.integerLen=1,c.length=Math.max(1,i=o+1),c[0]=0;for(let u=1;u<i;u++)c[u]=0}if(r>=5)if(i-1<0){for(let u=0;u>i;u--)c.unshift(0),t.integerLen++;c.unshift(1),t.integerLen++}else c[i-1]++;for(;s<Math.max(0,o);s++)c.push(0);let a=o!==0,l=e+t.integerLen,f=c.reduceRight(function(u,d,h,m){return d=d+u,m[h]=d<10?d:d-10,a&&(m[h]===0&&h>=l?m.pop():a=!1),d>=10?1:0},0);f&&(c.unshift(f),t.integerLen++)}function Tt(t){let e=parseInt(t);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+t);return e}function t8(t,e){e=encodeURIComponent(e);for(let n of t.split(";")){let c=n.indexOf("="),[s,o]=c==-1?[n,""]:[n.slice(0,c),n.slice(c+1)];if(s.trim()===e)return decodeURIComponent(o)}return null}var _t=/\s+/,Ki=[],Qk=(()=>{let e=class e{constructor(c,s){this._ngEl=c,this._renderer=s,this.initialClasses=Ki,this.stateMap=new Map}set klass(c){this.initialClasses=c!=null?c.trim().split(_t):Ki}set ngClass(c){this.rawClass=typeof c=="string"?c.trim().split(_t):c}ngDoCheck(){for(let s of this.initialClasses)this._updateState(s,!0);let c=this.rawClass;if(Array.isArray(c)||c instanceof Set)for(let s of c)this._updateState(s,!0);else if(c!=null)for(let s of Object.keys(c))this._updateState(s,!!c[s]);this._applyStateDiff()}_updateState(c,s){let o=this.stateMap.get(c);o!==void 0?(o.enabled!==s&&(o.changed=!0,o.enabled=s),o.touched=!0):this.stateMap.set(c,{enabled:s,changed:!0,touched:!0})}_applyStateDiff(){for(let c of this.stateMap){let s=c[0],o=c[1];o.changed?(this._toggleClass(s,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(s,!1),this.stateMap.delete(s)),o.touched=!1}}_toggleClass(c,s){c=c.trim(),c.length>0&&c.split(_t).forEach(o=>{s?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}};e.\u0275fac=function(s){return new(s||e)(z(h1),z(Z1))},e.\u0275dir=H({type:e,selectors:[["","ngClass",""]],inputs:{klass:[d1.None,"class","klass"],ngClass:"ngClass"},standalone:!0});let t=e;return t})();var kt=class{constructor(e,n,c,s){this.$implicit=e,this.ngForOf=n,this.index=c,this.count=s}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Kk=(()=>{let e=class e{set ngForOf(c){this._ngForOf=c,this._ngForOfDirty=!0}set ngForTrackBy(c){this._trackByFn=c}get ngForTrackBy(){return this._trackByFn}constructor(c,s,o){this._viewContainer=c,this._template=s,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(c){c&&(this._template=c)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let c=this._ngForOf;if(!this._differ&&c)if(0)try{}catch{}else this._differ=this._differs.find(c).create(this.ngForTrackBy)}if(this._differ){let c=this._differ.diff(this._ngForOf);c&&this._applyChanges(c)}}_applyChanges(c){let s=this._viewContainer;c.forEachOperation((o,i,r)=>{if(o.previousIndex==null)s.createEmbeddedView(this._template,new kt(o.item,this._ngForOf,-1,-1),r===null?void 0:r);else if(r==null)s.remove(i===null?void 0:i);else if(i!==null){let a=s.get(i);s.move(a,r),Xi(a,o)}});for(let o=0,i=s.length;o<i;o++){let a=s.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}c.forEachIdentityChange(o=>{let i=s.get(o.currentIndex);Xi(i,o)})}static ngTemplateContextGuard(c,s){return!0}};e.\u0275fac=function(s){return new(s||e)(z(y4),z(T3),z(Nt))},e.\u0275dir=H({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0});let t=e;return t})();function Xi(t,e){t.context.$implicit=e.item}var Xk=(()=>{let e=class e{constructor(c,s){this._viewContainer=c,this._context=new Ft,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=s}set ngIf(c){this._context.$implicit=this._context.ngIf=c,this._updateView()}set ngIfThen(c){Ji("ngIfThen",c),this._thenTemplateRef=c,this._thenViewRef=null,this._updateView()}set ngIfElse(c){Ji("ngIfElse",c),this._elseTemplateRef=c,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(c,s){return!0}};e.\u0275fac=function(s){return new(s||e)(z(y4),z(T3))},e.\u0275dir=H({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0});let t=e;return t})(),Ft=class{constructor(){this.$implicit=null,this.ngIf=null}};function Ji(t,e){if(!!!(!e||e.createEmbeddedView))throw new Error(`${t} must be a TemplateRef, but received '${y1(e)}'.`)}var Jk=(()=>{let e=class e{constructor(c,s,o){this._ngEl=c,this._differs=s,this._renderer=o,this._ngStyle=null,this._differ=null}set ngStyle(c){this._ngStyle=c,!this._differ&&c&&(this._differ=this._differs.find(c).create())}ngDoCheck(){if(this._differ){let c=this._differ.diff(this._ngStyle);c&&this._applyChanges(c)}}_setStyle(c,s){let[o,i]=c.split("."),r=o.indexOf("-")===-1?void 0:p2.DashCase;s!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${s}${i}`:s,r):this._renderer.removeStyle(this._ngEl.nativeElement,o,r)}_applyChanges(c){c.forEachRemovedItem(s=>this._setStyle(s.key,null)),c.forEachAddedItem(s=>this._setStyle(s.key,s.currentValue)),c.forEachChangedItem(s=>this._setStyle(s.key,s.currentValue))}};e.\u0275fac=function(s){return new(s||e)(z(h1),z(wt),z(Z1))},e.\u0275dir=H({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0});let t=e;return t})();function S4(t,e){return new S(2100,!1)}var Pt=class{createSubscription(e,n){return Dt(()=>e.subscribe({next:n,error:c=>{throw c}}))}dispose(e){Dt(()=>e.unsubscribe())}},Vt=class{createSubscription(e,n){return e.then(n,c=>{throw c})}dispose(e){}},vv=new Vt,yv=new Pt,eF=(()=>{let e=class e{constructor(c){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=c}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(c){if(!this._obj){if(c)try{this.markForCheckOnValueUpdate=!1,this._subscribe(c)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return c!==this._obj?(this._dispose(),this.transform(c)):this._latestValue}_subscribe(c){this._obj=c,this._strategy=this._selectStrategy(c),this._subscription=this._strategy.createSubscription(c,s=>this._updateLatestValue(c,s))}_selectStrategy(c){if(x4(c))return vv;if(bt(c))return yv;throw S4(e,c)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(c,s){var o;c===this._obj&&(this._latestValue=s,this.markForCheckOnValueUpdate&&((o=this._ref)==null||o.markForCheck()))}};e.\u0275fac=function(s){return new(s||e)(z(R3,16))},e.\u0275pipe=S2({name:"async",type:e,pure:!1,standalone:!0});let t=e;return t})(),tF=(()=>{let e=class e{transform(c){if(c==null)return null;if(typeof c!="string")throw S4(e,c);return c.toLowerCase()}};e.\u0275fac=function(s){return new(s||e)},e.\u0275pipe=S2({name:"lowercase",type:e,pure:!0,standalone:!0});let t=e;return t})(),bv=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,cF=(()=>{let e=class e{transform(c){if(c==null)return null;if(typeof c!="string")throw S4(e,c);return c.replace(bv,s=>s[0].toUpperCase()+s.slice(1).toLowerCase())}};e.\u0275fac=function(s){return new(s||e)},e.\u0275pipe=S2({name:"titlecase",type:e,pure:!0,standalone:!0});let t=e;return t})();var xv="mediumDate",Nv=new w(""),wv=new w(""),nF=(()=>{let e=class e{constructor(c,s,o){this.locale=c,this.defaultTimezone=s,this.defaultOptions=o}transform(c,s,o,i){var r,a,l,f,u;if(c==null||c===""||c!==c)return null;try{let d=(a=s!=null?s:(r=this.defaultOptions)==null?void 0:r.dateFormat)!=null?a:xv,h=(u=(f=o!=null?o:(l=this.defaultOptions)==null?void 0:l.timezone)!=null?f:this.defaultTimezone)!=null?u:void 0;return tv(c,d,i||this.locale,h)}catch(d){throw S4(e,d.message)}}};e.\u0275fac=function(s){return new(s||e)(z(x0,16),z(Nv,24),z(wv,24))},e.\u0275pipe=S2({name:"date",type:e,pure:!0,standalone:!0});let t=e;return t})();var sF=(()=>{let e=class e{constructor(c,s="USD"){this._locale=c,this._defaultCurrencyCode=s}transform(c,s=this._defaultCurrencyCode,o="symbol",i,r){if(!Dv(c))return null;r||(r=this._locale),typeof o=="boolean"&&(o=o?"symbol":"code");let a=s||this._defaultCurrencyCode;o!=="code"&&(o==="symbol"||o==="symbol-narrow"?a=QL(a,o==="symbol"?"wide":"narrow",r):a=o);try{let l=Sv(c);return gv(l,r,a,s,i)}catch(l){throw S4(e,l.message)}}};e.\u0275fac=function(s){return new(s||e)(z(x0,16),z(Bi,16))},e.\u0275pipe=S2({name:"currency",type:e,pure:!0,standalone:!0});let t=e;return t})();function Dv(t){return!(t==null||t===""||t!==t)}function Sv(t){if(typeof t=="string"&&!isNaN(Number(t)-parseFloat(t)))return Number(t);if(typeof t!="number")throw new Error(`${t} is not a number`);return t}var oF=(()=>{let e=class e{transform(c,s,o){if(c==null)return null;if(!this.supports(c))throw S4(e,c);return c.slice(s,o)}supports(c){return typeof c=="string"||Array.isArray(c)}};e.\u0275fac=function(s){return new(s||e)},e.\u0275pipe=S2({name:"slice",type:e,pure:!1,standalone:!0});let t=e;return t})();var Ev=(()=>{let e=class e{};e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=I1({type:e}),e.\u0275inj=A1({});let t=e;return t})(),Bt="browser",Av="server";function E4(t){return t===Bt}function H3(t){return t===Av}var iF=(()=>{let e=class e{};e.\u0275prov=y({token:e,providedIn:"root",factory:()=>E4(x(T1))?new Ot(x(N1),window):new Rt});let t=e;return t})(),Ot=class{constructor(e,n){this.document=e,this.window=n,this.offset=()=>[0,0]}setOffset(e){Array.isArray(e)?this.offset=()=>e:this.offset=e}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(e){this.window.scrollTo(e[0],e[1])}scrollToAnchor(e){let n=Iv(this.document,e);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(e){this.window.history.scrollRestoration=e}scrollToElement(e){let n=e.getBoundingClientRect(),c=n.left+this.window.pageXOffset,s=n.top+this.window.pageYOffset,o=this.offset();this.window.scrollTo(c-o[0],s-o[1])}};function Iv(t,e){let n=t.getElementById(e)||t.getElementsByName(e)[0];if(n)return n;if(typeof t.createTreeWalker=="function"&&t.body&&typeof t.body.attachShadow=="function"){let c=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT),s=c.currentNode;for(;s;){let o=s.shadowRoot;if(o){let i=o.getElementById(e)||o.querySelector(`[name="${e}"]`);if(i)return i}s=c.nextNode()}}return null}var Rt=class{setOffset(e){}getScrollPosition(){return[0,0]}scrollToPosition(e){}scrollToAnchor(e){}setHistoryScrollRestoration(e){}},D4=class{};var D0=class{},S0=class{},z2=class t{constructor(e){this.normalizedNames=new Map,this.lazyUpdate=null,e?typeof e=="string"?this.lazyInit=()=>{this.headers=new Map,e.split(`
`).forEach(n=>{let c=n.indexOf(":");if(c>0){let s=n.slice(0,c),o=s.toLowerCase(),i=n.slice(c+1).trim();this.maybeSetNormalizedName(s,o),this.headers.has(o)?this.headers.get(o).push(i):this.headers.set(o,[i])}})}:typeof Headers<"u"&&e instanceof Headers?(this.headers=new Map,e.forEach((n,c)=>{this.setHeaderEntries(c,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(e).forEach(([n,c])=>{this.setHeaderEntries(n,c)})}:this.headers=new Map}has(e){return this.init(),this.headers.has(e.toLowerCase())}get(e){this.init();let n=this.headers.get(e.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(e){return this.init(),this.headers.get(e.toLowerCase())||null}append(e,n){return this.clone({name:e,value:n,op:"a"})}set(e,n){return this.clone({name:e,value:n,op:"s"})}delete(e,n){return this.clone({name:e,value:n,op:"d"})}maybeSetNormalizedName(e,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,e)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(e=>this.applyUpdate(e)),this.lazyUpdate=null))}copyFrom(e){e.init(),Array.from(e.headers.keys()).forEach(n=>{this.headers.set(n,e.headers.get(n)),this.normalizedNames.set(n,e.normalizedNames.get(n))})}clone(e){let n=new t;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([e]),n}applyUpdate(e){let n=e.name.toLowerCase();switch(e.op){case"a":case"s":let c=e.value;if(typeof c=="string"&&(c=[c]),c.length===0)return;this.maybeSetNormalizedName(e.name,n);let s=(e.op==="a"?this.headers.get(n):void 0)||[];s.push(...c),this.headers.set(n,s);break;case"d":let o=e.value;if(!o)this.headers.delete(n),this.normalizedNames.delete(n);else{let i=this.headers.get(n);if(!i)return;i=i.filter(r=>o.indexOf(r)===-1),i.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,i)}break}}setHeaderEntries(e,n){let c=(Array.isArray(n)?n:[n]).map(o=>o.toString()),s=e.toLowerCase();this.headers.set(s,c),this.maybeSetNormalizedName(e,s)}forEach(e){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>e(this.normalizedNames.get(n),this.headers.get(n)))}};var qt=class{encodeKey(e){return ar(e)}encodeValue(e){return ar(e)}decodeKey(e){return decodeURIComponent(e)}decodeValue(e){return decodeURIComponent(e)}};function kv(t,e){let n=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(s=>{let o=s.indexOf("="),[i,r]=o==-1?[e.decodeKey(s),""]:[e.decodeKey(s.slice(0,o)),e.decodeValue(s.slice(o+1))],a=n.get(i)||[];a.push(r),n.set(i,a)}),n}var Fv=/%(\d[a-f0-9])/gi,Pv={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ar(t){return encodeURIComponent(t).replace(Fv,(e,n)=>{var c;return(c=Pv[n])!=null?c:e})}function c8(t){return`${t}`}var c3=class t{constructor(e={}){if(this.updates=null,this.cloneFrom=null,this.encoder=e.encoder||new qt,e.fromString){if(e.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=kv(e.fromString,this.encoder)}else e.fromObject?(this.map=new Map,Object.keys(e.fromObject).forEach(n=>{let c=e.fromObject[n],s=Array.isArray(c)?c.map(c8):[c8(c)];this.map.set(n,s)})):this.map=null}has(e){return this.init(),this.map.has(e)}get(e){this.init();let n=this.map.get(e);return n?n[0]:null}getAll(e){return this.init(),this.map.get(e)||null}keys(){return this.init(),Array.from(this.map.keys())}append(e,n){return this.clone({param:e,value:n,op:"a"})}appendAll(e){let n=[];return Object.keys(e).forEach(c=>{let s=e[c];Array.isArray(s)?s.forEach(o=>{n.push({param:c,value:o,op:"a"})}):n.push({param:c,value:s,op:"a"})}),this.clone(n)}set(e,n){return this.clone({param:e,value:n,op:"s"})}delete(e,n){return this.clone({param:e,value:n,op:"d"})}toString(){return this.init(),this.keys().map(e=>{let n=this.encoder.encodeKey(e);return this.map.get(e).map(c=>n+"="+this.encoder.encodeValue(c)).join("&")}).filter(e=>e!=="").join("&")}clone(e){let n=new t({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(e),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(e=>this.map.set(e,this.cloneFrom.map.get(e))),this.updates.forEach(e=>{switch(e.op){case"a":case"s":let n=(e.op==="a"?this.map.get(e.param):void 0)||[];n.push(c8(e.value)),this.map.set(e.param,n);break;case"d":if(e.value!==void 0){let c=this.map.get(e.param)||[],s=c.indexOf(c8(e.value));s!==-1&&c.splice(s,1),c.length>0?this.map.set(e.param,c):this.map.delete(e.param)}else{this.map.delete(e.param);break}}}),this.cloneFrom=this.updates=null)}};var Gt=class{constructor(){this.map=new Map}set(e,n){return this.map.set(e,n),this}get(e){return this.map.has(e)||this.map.set(e,e.defaultValue()),this.map.get(e)}delete(e){return this.map.delete(e),this}has(e){return this.map.has(e)}keys(){return this.map.keys()}};function Vv(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function lr(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function fr(t){return typeof Blob<"u"&&t instanceof Blob}function ur(t){return typeof FormData<"u"&&t instanceof FormData}function Ov(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var w0=class t{constructor(e,n,c,s){var i,r;this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=e.toUpperCase();let o;if(Vv(this.method)||s?(this.body=c!==void 0?c:null,o=s):o=c,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),(i=this.headers)!=null||(this.headers=new z2),(r=this.context)!=null||(this.context=new Gt),!this.params)this.params=new c3,this.urlWithParams=n;else{let a=this.params.toString();if(a.length===0)this.urlWithParams=n;else{let l=n.indexOf("?"),f=l===-1?"?":l<n.length-1?"&":"";this.urlWithParams=n+f+a}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||lr(this.body)||fr(this.body)||ur(this.body)||Ov(this.body)?this.body:this.body instanceof c3?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||ur(this.body)?null:fr(this.body)?this.body.type||null:lr(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof c3?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(e={}){var d,h,m,C;let n=e.method||this.method,c=e.url||this.url,s=e.responseType||this.responseType,o=(d=e.transferCache)!=null?d:this.transferCache,i=e.body!==void 0?e.body:this.body,r=(h=e.withCredentials)!=null?h:this.withCredentials,a=(m=e.reportProgress)!=null?m:this.reportProgress,l=e.headers||this.headers,f=e.params||this.params,u=(C=e.context)!=null?C:this.context;return e.setHeaders!==void 0&&(l=Object.keys(e.setHeaders).reduce((M,g)=>M.set(g,e.setHeaders[g]),l)),e.setParams&&(f=Object.keys(e.setParams).reduce((M,g)=>M.set(g,e.setParams[g]),f)),new t(n,c,i,{params:f,headers:l,context:u,reportProgress:a,responseType:s,withCredentials:r,transferCache:o})}},n3=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(n3||{}),E0=class{constructor(e,n=A0.Ok,c="OK"){this.headers=e.headers||new z2,this.status=e.status!==void 0?e.status:n,this.statusText=e.statusText||c,this.url=e.url||null,this.ok=this.status>=200&&this.status<300}},s8=class t extends E0{constructor(e={}){super(e),this.type=n3.ResponseHeader}clone(e={}){return new t({headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},j3=class t extends E0{constructor(e={}){super(e),this.type=n3.Response,this.body=e.body!==void 0?e.body:null}clone(e={}){return new t({body:e.body!==void 0?e.body:this.body,headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},t3=class extends E0{constructor(e){super(e,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${e.url||"(unknown url)"}`:this.message=`Http failure response for ${e.url||"(unknown url)"}: ${e.status} ${e.statusText}`,this.error=e.error||null}},A0=function(t){return t[t.Continue=100]="Continue",t[t.SwitchingProtocols=101]="SwitchingProtocols",t[t.Processing=102]="Processing",t[t.EarlyHints=103]="EarlyHints",t[t.Ok=200]="Ok",t[t.Created=201]="Created",t[t.Accepted=202]="Accepted",t[t.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",t[t.NoContent=204]="NoContent",t[t.ResetContent=205]="ResetContent",t[t.PartialContent=206]="PartialContent",t[t.MultiStatus=207]="MultiStatus",t[t.AlreadyReported=208]="AlreadyReported",t[t.ImUsed=226]="ImUsed",t[t.MultipleChoices=300]="MultipleChoices",t[t.MovedPermanently=301]="MovedPermanently",t[t.Found=302]="Found",t[t.SeeOther=303]="SeeOther",t[t.NotModified=304]="NotModified",t[t.UseProxy=305]="UseProxy",t[t.Unused=306]="Unused",t[t.TemporaryRedirect=307]="TemporaryRedirect",t[t.PermanentRedirect=308]="PermanentRedirect",t[t.BadRequest=400]="BadRequest",t[t.Unauthorized=401]="Unauthorized",t[t.PaymentRequired=402]="PaymentRequired",t[t.Forbidden=403]="Forbidden",t[t.NotFound=404]="NotFound",t[t.MethodNotAllowed=405]="MethodNotAllowed",t[t.NotAcceptable=406]="NotAcceptable",t[t.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",t[t.RequestTimeout=408]="RequestTimeout",t[t.Conflict=409]="Conflict",t[t.Gone=410]="Gone",t[t.LengthRequired=411]="LengthRequired",t[t.PreconditionFailed=412]="PreconditionFailed",t[t.PayloadTooLarge=413]="PayloadTooLarge",t[t.UriTooLong=414]="UriTooLong",t[t.UnsupportedMediaType=415]="UnsupportedMediaType",t[t.RangeNotSatisfiable=416]="RangeNotSatisfiable",t[t.ExpectationFailed=417]="ExpectationFailed",t[t.ImATeapot=418]="ImATeapot",t[t.MisdirectedRequest=421]="MisdirectedRequest",t[t.UnprocessableEntity=422]="UnprocessableEntity",t[t.Locked=423]="Locked",t[t.FailedDependency=424]="FailedDependency",t[t.TooEarly=425]="TooEarly",t[t.UpgradeRequired=426]="UpgradeRequired",t[t.PreconditionRequired=428]="PreconditionRequired",t[t.TooManyRequests=429]="TooManyRequests",t[t.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",t[t.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",t[t.InternalServerError=500]="InternalServerError",t[t.NotImplemented=501]="NotImplemented",t[t.BadGateway=502]="BadGateway",t[t.ServiceUnavailable=503]="ServiceUnavailable",t[t.GatewayTimeout=504]="GatewayTimeout",t[t.HttpVersionNotSupported=505]="HttpVersionNotSupported",t[t.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",t[t.InsufficientStorage=507]="InsufficientStorage",t[t.LoopDetected=508]="LoopDetected",t[t.NotExtended=510]="NotExtended",t[t.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired",t}(A0||{});function Ut(t,e){return{body:e,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var Rv=(()=>{let e=class e{constructor(c){this.handler=c}request(c,s,o={}){let i;if(c instanceof w0)i=c;else{let l;o.headers instanceof z2?l=o.headers:l=new z2(o.headers);let f;o.params&&(o.params instanceof c3?f=o.params:f=new c3({fromObject:o.params})),i=new w0(c,s,o.body!==void 0?o.body:null,{headers:l,context:o.context,params:f,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let r=l2(i).pipe(Y4(l=>this.handler.handle(l)));if(c instanceof w0||o.observe==="events")return r;let a=r.pipe(v3(l=>l instanceof j3));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(o1(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(o1(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(o1(l=>{if(l.body!==null&&typeof l.body!="string")throw new Error("Response is not a string.");return l.body}));case"json":default:return a.pipe(o1(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(c,s={}){return this.request("DELETE",c,s)}get(c,s={}){return this.request("GET",c,s)}head(c,s={}){return this.request("HEAD",c,s)}jsonp(c,s){return this.request("JSONP",c,{params:new c3().append(s,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(c,s={}){return this.request("OPTIONS",c,s)}patch(c,s,o={}){return this.request("PATCH",c,Ut(o,s))}post(c,s,o={}){return this.request("POST",c,Ut(o,s))}put(c,s,o={}){return this.request("PUT",c,Ut(o,s))}};e.\u0275fac=function(s){return new(s||e)(L(D0))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),Hv=/^\)\]\}',?\n/,jv="X-Request-URL";function dr(t){if(t.url)return t.url;let e=jv.toLocaleLowerCase();return t.headers.get(e)}var $t=(()=>{let e=class e{constructor(){var c,s;this.fetchImpl=(s=(c=x(Wt,{optional:!0}))==null?void 0:c.fetch)!=null?s:fetch.bind(globalThis),this.ngZone=x(a1)}handle(c){return new P(s=>{let o=new AbortController;return this.doRequest(c,o.signal,s).then(Zt,i=>s.error(new t3({error:i}))),()=>o.abort()})}doRequest(c,s,o){return K0(this,null,function*(){var m,C,M,g;let i=this.createRequestInit(c),r;try{let b=this.fetchImpl(c.urlWithParams,Z({signal:s},i));Bv(b),o.next({type:n3.Sent}),r=yield b}catch(b){o.error(new t3({error:b,status:(m=b.status)!=null?m:0,statusText:b.statusText,url:c.urlWithParams,headers:b.headers}));return}let a=new z2(r.headers),l=r.statusText,f=(C=dr(r))!=null?C:c.urlWithParams,u=r.status,d=null;if(c.reportProgress&&o.next(new s8({headers:a,status:u,statusText:l,url:f})),r.body){let b=r.headers.get("content-length"),V=[],A=r.body.getReader(),J=0,z1,s1,Q1=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>K0(this,null,function*(){for(;;){let{done:h3,value:$4}=yield A.read();if(h3)break;if(V.push($4),J+=$4.length,c.reportProgress){s1=c.responseType==="text"?(s1!=null?s1:"")+(z1!=null?z1:z1=new TextDecoder).decode($4,{stream:!0}):void 0;let ln=()=>o.next({type:n3.DownloadProgress,total:b?+b:void 0,loaded:J,partialText:s1});Q1?Q1.run(ln):ln()}}}));let U4=this.concatChunks(V,J);try{let h3=(M=r.headers.get("Content-Type"))!=null?M:"";d=this.parseBody(c,U4,h3)}catch(h3){o.error(new t3({error:h3,headers:new z2(r.headers),status:r.status,statusText:r.statusText,url:(g=dr(r))!=null?g:c.urlWithParams}));return}}u===0&&(u=d?A0.Ok:0),u>=200&&u<300?(o.next(new j3({body:d,headers:a,status:u,statusText:l,url:f})),o.complete()):o.error(new t3({error:d,headers:a,status:u,statusText:l,url:f}))})}parseBody(c,s,o){switch(c.responseType){case"json":let i=new TextDecoder().decode(s).replace(Hv,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(s);case"blob":return new Blob([s],{type:o});case"arraybuffer":return s.buffer}}createRequestInit(c){var i;let s={},o=c.withCredentials?"include":void 0;if(c.headers.forEach((r,a)=>s[r]=a.join(",")),(i=s.Accept)!=null||(s.Accept="application/json, text/plain, */*"),!s["Content-Type"]){let r=c.detectContentTypeHeader();r!==null&&(s["Content-Type"]=r)}return{body:c.serializeBody(),method:c.method,headers:s,credentials:o}}concatChunks(c,s){let o=new Uint8Array(s),i=0;for(let r of c)o.set(r,i),i+=r.length;return o}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),Wt=class{};function Zt(){}function Bv(t){t.then(Zt,Zt)}function Uv(t,e){return e(t)}function $v(t,e,n){return(c,s)=>k7(n,()=>e(c,o=>t(o,s)))}var Yt=new w(""),yr=new w(""),br=new w("");var hr=(()=>{let e=class e extends D0{constructor(c,s){super(),this.backend=c,this.injector=s,this.chain=null,this.pendingTasks=x(Re);let o=x(br,{optional:!0});this.backend=o!=null?o:c}handle(c){if(this.chain===null){let o=Array.from(new Set([...this.injector.get(Yt),...this.injector.get(yr,[])]));this.chain=o.reduceRight((i,r)=>$v(i,r,this.injector),Uv)}let s=this.pendingTasks.add();return this.chain(c,o=>this.backend.handle(o)).pipe(X0(()=>this.pendingTasks.remove(s)))}};e.\u0275fac=function(s){return new(s||e)(L(S0),L(h2))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})();var qv=/^\)\]\}',?\n/;function Gv(t){return"responseURL"in t&&t.responseURL?t.responseURL:/^X-Request-URL:/m.test(t.getAllResponseHeaders())?t.getResponseHeader("X-Request-URL"):null}var mr=(()=>{let e=class e{constructor(c){this.xhrFactory=c}handle(c){if(c.method==="JSONP")throw new S(-2800,!1);let s=this.xhrFactory;return(s.\u0275loadImpl?D1(s.\u0275loadImpl()):l2(null)).pipe(y3(()=>new P(i=>{let r=s.build();if(r.open(c.method,c.urlWithParams),c.withCredentials&&(r.withCredentials=!0),c.headers.forEach((M,g)=>r.setRequestHeader(M,g.join(","))),c.headers.has("Accept")||r.setRequestHeader("Accept","application/json, text/plain, */*"),!c.headers.has("Content-Type")){let M=c.detectContentTypeHeader();M!==null&&r.setRequestHeader("Content-Type",M)}if(c.responseType){let M=c.responseType.toLowerCase();r.responseType=M!=="json"?M:"text"}let a=c.serializeBody(),l=null,f=()=>{if(l!==null)return l;let M=r.statusText||"OK",g=new z2(r.getAllResponseHeaders()),b=Gv(r)||c.url;return l=new s8({headers:g,status:r.status,statusText:M,url:b}),l},u=()=>{let{headers:M,status:g,statusText:b,url:V}=f(),A=null;g!==A0.NoContent&&(A=typeof r.response>"u"?r.responseText:r.response),g===0&&(g=A?A0.Ok:0);let J=g>=200&&g<300;if(c.responseType==="json"&&typeof A=="string"){let z1=A;A=A.replace(qv,"");try{A=A!==""?JSON.parse(A):null}catch(s1){A=z1,J&&(J=!1,A={error:s1,text:A})}}J?(i.next(new j3({body:A,headers:M,status:g,statusText:b,url:V||void 0})),i.complete()):i.error(new t3({error:A,headers:M,status:g,statusText:b,url:V||void 0}))},d=M=>{let{url:g}=f(),b=new t3({error:M,status:r.status||0,statusText:r.statusText||"Unknown Error",url:g||void 0});i.error(b)},h=!1,m=M=>{h||(i.next(f()),h=!0);let g={type:n3.DownloadProgress,loaded:M.loaded};M.lengthComputable&&(g.total=M.total),c.responseType==="text"&&r.responseText&&(g.partialText=r.responseText),i.next(g)},C=M=>{let g={type:n3.UploadProgress,loaded:M.loaded};M.lengthComputable&&(g.total=M.total),i.next(g)};return r.addEventListener("load",u),r.addEventListener("error",d),r.addEventListener("timeout",d),r.addEventListener("abort",d),c.reportProgress&&(r.addEventListener("progress",m),a!==null&&r.upload&&r.upload.addEventListener("progress",C)),r.send(a),i.next({type:n3.Sent}),()=>{r.removeEventListener("error",d),r.removeEventListener("abort",d),r.removeEventListener("load",u),r.removeEventListener("timeout",d),c.reportProgress&&(r.removeEventListener("progress",m),a!==null&&r.upload&&r.upload.removeEventListener("progress",C)),r.readyState!==r.DONE&&r.abort()}})))}};e.\u0275fac=function(s){return new(s||e)(L(D4))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),xr=new w(""),Wv="XSRF-TOKEN",Zv=new w("",{providedIn:"root",factory:()=>Wv}),Yv="X-XSRF-TOKEN",Qv=new w("",{providedIn:"root",factory:()=>Yv}),o8=class{},Kv=(()=>{let e=class e{constructor(c,s,o){this.doc=c,this.platform=s,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let c=this.doc.cookie||"";return c!==this.lastCookieString&&(this.parseCount++,this.lastToken=t8(c,this.cookieName),this.lastCookieString=c),this.lastToken}};e.\u0275fac=function(s){return new(s||e)(L(N1),L(T1),L(Zv))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})();function Xv(t,e){let n=t.url.toLowerCase();if(!x(xr)||t.method==="GET"||t.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return e(t);let c=x(o8).getToken(),s=x(Qv);return c!=null&&!t.headers.has(s)&&(t=t.clone({headers:t.headers.set(s,c)})),e(t)}var Qt=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(Qt||{});function Nr(t,e){return{\u0275kind:t,\u0275providers:e}}function MF(...t){let e=[Rv,mr,hr,{provide:D0,useExisting:hr},{provide:S0,useExisting:mr},{provide:Yt,useValue:Xv,multi:!0},{provide:xr,useValue:!0},{provide:o8,useClass:Kv}];for(let n of t)e.push(...n.\u0275providers);return F3(e)}function CF(t){return Nr(Qt.Interceptors,t.map(e=>({provide:Yt,useValue:e,multi:!0})))}function zF(){return Nr(Qt.Fetch,[$t,{provide:S0,useExisting:$t},{provide:br,useExisting:$t}])}var pr="b",gr="h",Mr="s",Cr="st",zr="u",Lr="rt",n8=new w(""),Jv=["GET","HEAD"];function ey(t,e){var d;let u=x(n8),{isCacheActive:n}=u,c=fn(u,["isCacheActive"]),{transferCache:s,method:o}=t;if(!n||o==="POST"&&!c.includePostRequests&&!s||o!=="POST"&&!Jv.includes(o)||s===!1||((d=c.filter)==null?void 0:d.call(c,t))===!1)return e(t);let i=x(O3),r=cy(t),a=i.get(r,null),l=c.includeHeaders;if(typeof s=="object"&&s.includeHeaders&&(l=s.includeHeaders),a){let{[pr]:h,[Lr]:m,[gr]:C,[Mr]:M,[Cr]:g,[zr]:b}=a,V=h;switch(m){case"arraybuffer":V=new TextEncoder().encode(h).buffer;break;case"blob":V=new Blob([h]);break}let A=new z2(C);return l2(new j3({body:V,headers:A,status:M,statusText:g,url:b}))}let f=H3(x(T1));return e(t).pipe(b6(h=>{h instanceof j3&&f&&i.set(r,{[pr]:h.body,[gr]:ty(h.headers,l),[Mr]:h.status,[Cr]:h.statusText,[zr]:h.url||"",[Lr]:t.responseType})}))}function ty(t,e){if(!e)return{};let n={};for(let c of e){let s=t.getAll(c);s!==null&&(n[c]=s)}return n}function vr(t){return[...t.keys()].sort().map(e=>`${e}=${t.getAll(e)}`).join("&")}function cy(t){let{params:e,method:n,responseType:c,url:s}=t,o=vr(e),i=t.serializeBody();i instanceof URLSearchParams?i=vr(i):typeof i!="string"&&(i="");let r=[n,c,s,i,o].join("|"),a=ny(r);return a}function ny(t){let e=0;for(let n of t)e=Math.imul(31,e)+n.charCodeAt(0)<<0;return e+=**********,e.toString()}function wr(t){return[{provide:n8,useFactory:()=>(e3("NgHttpTransferCache"),Z({isCacheActive:!0},t))},{provide:yr,useValue:ey,multi:!0,deps:[O3,n8]},{provide:je,multi:!0,useFactory:()=>{let e=x(N4),n=x(n8);return()=>{xt(e).then(()=>{n.isCacheActive=!1})}}}]}var Jt=class extends Ke{constructor(){super(...arguments),this.supportsDOMEvents=!0}},ec=class t extends Jt{static makeCurrent(){tr(new t)}onAndCancel(e,n,c){return e.addEventListener(n,c),()=>{e.removeEventListener(n,c)}}dispatchEvent(e,n){e.dispatchEvent(n)}remove(e){e.parentNode&&e.parentNode.removeChild(e)}createElement(e,n){return n=n||this.getDefaultDocument(),n.createElement(e)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(e){return e.nodeType===Node.ELEMENT_NODE}isShadowRoot(e){return e instanceof DocumentFragment}getGlobalEventTarget(e,n){return n==="window"?window:n==="document"?e:n==="body"?e.body:null}getBaseHref(e){let n=sy();return n==null?null:oy(n)}resetBaseElement(){I0=null}getUserAgent(){return window.navigator.userAgent}getCookie(e){return t8(document.cookie,e)}},I0=null;function sy(){return I0=I0||document.querySelector("base"),I0?I0.getAttribute("href"):null}function oy(t){return new URL(t,document.baseURI).pathname}var iy=(()=>{let e=class e{build(){return new XMLHttpRequest}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),tc=new w(""),Ar=(()=>{let e=class e{constructor(c,s){this._zone=s,this._eventNameToPlugin=new Map,c.forEach(o=>{o.manager=this}),this._plugins=c.slice().reverse()}addEventListener(c,s,o){return this._findPluginFor(s).addEventListener(c,s,o)}getZone(){return this._zone}_findPluginFor(c){let s=this._eventNameToPlugin.get(c);if(s)return s;if(s=this._plugins.find(i=>i.supports(c)),!s)throw new S(5101,!1);return this._eventNameToPlugin.set(c,s),s}};e.\u0275fac=function(s){return new(s||e)(L(tc),L(a1))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),i8=class{constructor(e){this._doc=e}},Kt="ng-app-id",Ir=(()=>{let e=class e{constructor(c,s,o,i={}){this.doc=c,this.appId=s,this.nonce=o,this.platformId=i,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=H3(i),this.resetHostNodes()}addStyles(c){for(let s of c)this.changeUsageCount(s,1)===1&&this.onStyleAdded(s)}removeStyles(c){for(let s of c)this.changeUsageCount(s,-1)<=0&&this.onStyleRemoved(s)}ngOnDestroy(){let c=this.styleNodesInDOM;c&&(c.forEach(s=>s.remove()),c.clear());for(let s of this.getAllStyles())this.onStyleRemoved(s);this.resetHostNodes()}addHost(c){this.hostNodes.add(c);for(let s of this.getAllStyles())this.addStyleToHost(c,s)}removeHost(c){this.hostNodes.delete(c)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(c){for(let s of this.hostNodes)this.addStyleToHost(s,c)}onStyleRemoved(c){var o,i;let s=this.styleRef;(i=(o=s.get(c))==null?void 0:o.elements)==null||i.forEach(r=>r.remove()),s.delete(c)}collectServerRenderedStyles(){var s;let c=(s=this.doc.head)==null?void 0:s.querySelectorAll(`style[${Kt}="${this.appId}"]`);if(c!=null&&c.length){let o=new Map;return c.forEach(i=>{i.textContent!=null&&o.set(i.textContent,i)}),o}return null}changeUsageCount(c,s){let o=this.styleRef;if(o.has(c)){let i=o.get(c);return i.usage+=s,i.usage}return o.set(c,{usage:s,elements:[]}),s}getStyleElement(c,s){let o=this.styleNodesInDOM,i=o==null?void 0:o.get(s);if((i==null?void 0:i.parentNode)===c)return o.delete(s),i.removeAttribute(Kt),i;{let r=this.doc.createElement("style");return this.nonce&&r.setAttribute("nonce",this.nonce),r.textContent=s,this.platformIsServer&&r.setAttribute(Kt,this.appId),c.appendChild(r),r}}addStyleToHost(c,s){var a;let o=this.getStyleElement(c,s),i=this.styleRef,r=(a=i.get(s))==null?void 0:a.elements;r?r.push(o):i.set(s,{elements:[o],usage:1})}resetHostNodes(){let c=this.hostNodes;c.clear(),c.add(this.doc.head)}};e.\u0275fac=function(s){return new(s||e)(L(N1),L(xe),L(Z7,8),L(T1))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),Xt={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},sc=/%COMP%/g,Tr="%COMP%",ry=`_nghost-${Tr}`,ay=`_ngcontent-${Tr}`,ly=!0,fy=new w("",{providedIn:"root",factory:()=>ly});function uy(t){return ay.replace(sc,t)}function dy(t){return ry.replace(sc,t)}function _r(t,e){return e.map(n=>n.replace(sc,t))}var Dr=(()=>{let e=class e{constructor(c,s,o,i,r,a,l,f=null){this.eventManager=c,this.sharedStylesHost=s,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=r,this.platformId=a,this.ngZone=l,this.nonce=f,this.rendererByCompId=new Map,this.platformIsServer=H3(a),this.defaultRenderer=new T0(c,r,l,this.platformIsServer)}createRenderer(c,s){if(!c||!s)return this.defaultRenderer;this.platformIsServer&&s.encapsulation===d2.ShadowDom&&(s=m1(Z({},s),{encapsulation:d2.Emulated}));let o=this.getOrCreateRenderer(c,s);return o instanceof r8?o.applyToHost(c):o instanceof _0&&o.applyStyles(),o}getOrCreateRenderer(c,s){let o=this.rendererByCompId,i=o.get(s.id);if(!i){let r=this.doc,a=this.ngZone,l=this.eventManager,f=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(s.encapsulation){case d2.Emulated:i=new r8(l,f,s,this.appId,u,r,a,d);break;case d2.ShadowDom:return new cc(l,f,c,s,r,a,this.nonce,d);default:i=new _0(l,f,s,u,r,a,d);break}o.set(s.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}};e.\u0275fac=function(s){return new(s||e)(L(Ar),L(Ir),L(xe),L(fy),L(N1),L(T1),L(a1),L(Z7))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),T0=class{constructor(e,n,c,s){this.eventManager=e,this.doc=n,this.ngZone=c,this.platformIsServer=s,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(e,n){return n?this.doc.createElementNS(Xt[n]||n,e):this.doc.createElement(e)}createComment(e){return this.doc.createComment(e)}createText(e){return this.doc.createTextNode(e)}appendChild(e,n){(Sr(e)?e.content:e).appendChild(n)}insertBefore(e,n,c){e&&(Sr(e)?e.content:e).insertBefore(n,c)}removeChild(e,n){e&&e.removeChild(n)}selectRootElement(e,n){let c=typeof e=="string"?this.doc.querySelector(e):e;if(!c)throw new S(-5104,!1);return n||(c.textContent=""),c}parentNode(e){return e.parentNode}nextSibling(e){return e.nextSibling}setAttribute(e,n,c,s){if(s){n=s+":"+n;let o=Xt[s];o?e.setAttributeNS(o,n,c):e.setAttribute(n,c)}else e.setAttribute(n,c)}removeAttribute(e,n,c){if(c){let s=Xt[c];s?e.removeAttributeNS(s,n):e.removeAttribute(`${c}:${n}`)}else e.removeAttribute(n)}addClass(e,n){e.classList.add(n)}removeClass(e,n){e.classList.remove(n)}setStyle(e,n,c,s){s&(p2.DashCase|p2.Important)?e.style.setProperty(n,c,s&p2.Important?"important":""):e.style[n]=c}removeStyle(e,n,c){c&p2.DashCase?e.style.removeProperty(n):e.style[n]=""}setProperty(e,n,c){e!=null&&(e[n]=c)}setValue(e,n){e.nodeValue=n}listen(e,n,c){if(typeof e=="string"&&(e=O2().getGlobalEventTarget(this.doc,e),!e))throw new Error(`Unsupported event target ${e} for event ${n}`);return this.eventManager.addEventListener(e,n,this.decoratePreventDefault(c))}decoratePreventDefault(e){return n=>{if(n==="__ngUnwrap__")return e;(this.platformIsServer?this.ngZone.runGuarded(()=>e(n)):e(n))===!1&&n.preventDefault()}}};function Sr(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var cc=class extends T0{constructor(e,n,c,s,o,i,r,a){super(e,o,i,a),this.sharedStylesHost=n,this.hostEl=c,this.shadowRoot=c.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=_r(s.id,s.styles);for(let f of l){let u=document.createElement("style");r&&u.setAttribute("nonce",r),u.textContent=f,this.shadowRoot.appendChild(u)}}nodeOrShadowRoot(e){return e===this.hostEl?this.shadowRoot:e}appendChild(e,n){return super.appendChild(this.nodeOrShadowRoot(e),n)}insertBefore(e,n,c){return super.insertBefore(this.nodeOrShadowRoot(e),n,c)}removeChild(e,n){return super.removeChild(this.nodeOrShadowRoot(e),n)}parentNode(e){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(e)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},_0=class extends T0{constructor(e,n,c,s,o,i,r,a){super(e,o,i,r),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=s,this.styles=a?_r(a,c.styles):c.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},r8=class extends _0{constructor(e,n,c,s,o,i,r,a){let l=s+"-"+c.id;super(e,n,c,o,i,r,a,l),this.contentAttr=uy(l),this.hostAttr=dy(l)}applyToHost(e){this.applyStyles(),this.setAttribute(e,this.hostAttr,"")}createElement(e,n){let c=super.createElement(e,n);return super.setAttribute(c,this.contentAttr,""),c}},hy=(()=>{let e=class e extends i8{constructor(c){super(c)}supports(c){return!0}addEventListener(c,s,o){return c.addEventListener(s,o,!1),()=>this.removeEventListener(c,s,o)}removeEventListener(c,s,o){return c.removeEventListener(s,o)}};e.\u0275fac=function(s){return new(s||e)(L(N1))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})(),Er=["alt","control","meta","shift"],my={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},py={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},gy=(()=>{let e=class e extends i8{constructor(c){super(c)}supports(c){return e.parseEventName(c)!=null}addEventListener(c,s,o){let i=e.parseEventName(s),r=e.eventCallback(i.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>O2().onAndCancel(c,i.domEventName,r))}static parseEventName(c){let s=c.toLowerCase().split("."),o=s.shift();if(s.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(s.pop()),r="",a=s.indexOf("code");if(a>-1&&(s.splice(a,1),r="code."),Er.forEach(f=>{let u=s.indexOf(f);u>-1&&(s.splice(u,1),r+=f+".")}),r+=i,s.length!=0||i.length===0)return null;let l={};return l.domEventName=o,l.fullKey=r,l}static matchEventFullKeyCode(c,s){let o=my[c.key]||c.key,i="";return s.indexOf("code.")>-1&&(o=c.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Er.forEach(r=>{if(r!==o){let a=py[r];a(c)&&(i+=r+".")}}),i+=o,i===s)}static eventCallback(c,s,o){return i=>{e.matchEventFullKeyCode(i,c)&&o.runGuarded(()=>s(i))}}static _normalizeKey(c){return c==="esc"?"escape":c}};e.\u0275fac=function(s){return new(s||e)(L(N1))},e.\u0275prov=y({token:e,factory:e.\u0275fac});let t=e;return t})();function VF(t,e){return $i(Z({rootComponent:t},My(e)))}function My(t){var e;return{appProviders:[...yy,...(e=t==null?void 0:t.providers)!=null?e:[]],platformProviders:vy}}function Cy(){ec.makeCurrent()}function zy(){return new m2}function Ly(){return Js(document),document}var vy=[{provide:T1,useValue:Bt},{provide:W7,useValue:Cy,multi:!0},{provide:N1,useFactory:Ly,deps:[]}];var yy=[{provide:Me,useValue:"root"},{provide:m2,useFactory:zy,deps:[]},{provide:tc,useClass:hy,multi:!0,deps:[N1,a1,T1]},{provide:tc,useClass:gy,multi:!0,deps:[N1]},Dr,Ir,Ar,{provide:u0,useExisting:Dr},{provide:D4,useClass:iy,deps:[]},[]];var OF=(()=>{let e=class e{constructor(c){this._doc=c}getTitle(){return this._doc.title}setTitle(c){this._doc.title=c||""}};e.\u0275fac=function(s){return new(s||e)(L(N1))},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();var oc=(()=>{let e=class e{};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:function(s){let o=null;return s?o=new(s||e):o=L(by),o},providedIn:"root"});let t=e;return t})(),by=(()=>{let e=class e extends oc{constructor(c){super(),this._doc=c}sanitize(c,s){if(s==null)return null;switch(c){case i2.NONE:return s;case i2.HTML:return A2(s,"HTML")?W1(s):X7(this._doc,String(s)).toString();case i2.STYLE:return A2(s,"Style")?W1(s):s;case i2.SCRIPT:if(A2(s,"Script"))return W1(s);throw new S(5200,!1);case i2.URL:return A2(s,"URL")?W1(s):Se(String(s));case i2.RESOURCE_URL:if(A2(s,"ResourceURL"))return W1(s);throw new S(5201,!1);default:throw new S(5202,!1)}}bypassSecurityTrustHtml(c){return lo(c)}bypassSecurityTrustStyle(c){return fo(c)}bypassSecurityTrustScript(c){return uo(c)}bypassSecurityTrustUrl(c){return ho(c)}bypassSecurityTrustResourceUrl(c){return mo(c)}};e.\u0275fac=function(s){return new(s||e)(L(N1))},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),nc=function(t){return t[t.NoHttpTransferCache=0]="NoHttpTransferCache",t[t.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",t}(nc||{});function RF(...t){let e=[],n=new Set,c=n.has(nc.HttpTransferCacheOptions);for(let{\u0275providers:s,\u0275kind:o}of t)n.add(o),s.length&&e.push(s);return F3([[],qi(),n.has(nc.NoHttpTransferCache)||c?[]:wr({}),e])}function Ny(t,e,n){return(e=Dy(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function kr(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(t);e&&(c=c.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),n.push.apply(n,c)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?kr(Object(n),!0).forEach(function(c){Ny(t,c,n[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):kr(Object(n)).forEach(function(c){Object.defineProperty(t,c,Object.getOwnPropertyDescriptor(n,c))})}return t}function wy(t,e){if(typeof t!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var c=n.call(t,e||"default");if(typeof c!="object")return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Dy(t){var e=wy(t,"string");return typeof e=="symbol"?e:e+""}var Fr=()=>{},Sc={},ra={},aa=null,la={mark:Fr,measure:Fr};try{typeof window<"u"&&(Sc=window),typeof document<"u"&&(ra=document),typeof MutationObserver<"u"&&(aa=MutationObserver),typeof performance<"u"&&(la=performance)}catch{}var{userAgent:Pr=""}=Sc.navigator||{},o3=Sc,Q=ra,Vr=aa,a8=la,jF=!!o3.document,j2=!!Q.documentElement&&!!Q.head&&typeof Q.addEventListener=="function"&&typeof Q.createElement=="function",fa=~Pr.indexOf("MSIE")||~Pr.indexOf("Trident/"),Sy=/fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,Ey=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i,ua={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}},Ay={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},da=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],C1="classic",m8="duotone",Iy="sharp",Ty="sharp-duotone",ha=[C1,m8,Iy,Ty],_y={classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}},ky={"Font Awesome 6 Free":{900:"fas",400:"far"},"Font Awesome 6 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 6 Brands":{400:"fab",normal:"fab"},"Font Awesome 6 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 6 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 6 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"}},Fy=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}]]),Py={classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}},Vy=["fak","fa-kit","fakd","fa-kit-duotone"],Or={kit:{fak:"kit","fa-kit":"kit"},"kit-duotone":{fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"}},Oy=["kit"],Ry={kit:{"fa-kit":"fak"},"kit-duotone":{"fa-kit-duotone":"fakd"}},Hy=["fak","fakd"],jy={kit:{fak:"fa-kit"},"kit-duotone":{fakd:"fa-kit-duotone"}},Rr={kit:{kit:"fak"},"kit-duotone":{"kit-duotone":"fakd"}},l8={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},By=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],Uy=["fak","fa-kit","fakd","fa-kit-duotone"],$y={"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}},qy={classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}},Gy={classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"]},uc={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}},Wy=["fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands"],dc=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt",...By,...Wy],Zy=["solid","regular","light","thin","duotone","brands"],ma=[1,2,3,4,5,6,7,8,9,10],Yy=ma.concat([11,12,13,14,15,16,17,18,19,20]),Qy=[...Object.keys(Gy),...Zy,"2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",l8.GROUP,l8.SWAP_OPACITY,l8.PRIMARY,l8.SECONDARY].concat(ma.map(t=>"".concat(t,"x"))).concat(Yy.map(t=>"w-".concat(t))),Ky={"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}},R2="___FONT_AWESOME___",hc=16,pa="fa",ga="svg-inline--fa",U3="data-fa-i2svg",mc="data-fa-pseudo-element",Xy="data-fa-pseudo-element-pending",Ec="data-prefix",Ac="data-icon",Hr="fontawesome-i2svg",Jy="async",eb=["HTML","HEAD","STYLE","SCRIPT"],Ma=(()=>{try{return!0}catch{return!1}})();function R0(t){return new Proxy(t,{get(e,n){return n in e?e[n]:e[C1]}})}var Ca=p({},ua);Ca[C1]=p(p(p(p({},{"fa-duotone":"duotone"}),ua[C1]),Or.kit),Or["kit-duotone"]);var tb=R0(Ca),pc=p({},Py);pc[C1]=p(p(p(p({},{duotone:"fad"}),pc[C1]),Rr.kit),Rr["kit-duotone"]);var jr=R0(pc),gc=p({},uc);gc[C1]=p(p({},gc[C1]),jy.kit);var Ic=R0(gc),Mc=p({},qy);Mc[C1]=p(p({},Mc[C1]),Ry.kit);var BF=R0(Mc),cb=Sy,za="fa-layers-text",nb=Ey,sb=p({},_y),UF=R0(sb),ob=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],ic=Ay,ib=[...Oy,...Qy],F0=o3.FontAwesomeConfig||{};function rb(t){var e=Q.querySelector("script["+t+"]");if(e)return e.getAttribute(t)}function ab(t){return t===""?!0:t==="false"?!1:t==="true"?!0:t}Q&&typeof Q.querySelector=="function"&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(e=>{let[n,c]=e,s=ab(rb(n));s!=null&&(F0[c]=s)});var La={styleDefault:"solid",familyDefault:C1,cssPrefix:pa,replacementClass:ga,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};F0.familyPrefix&&(F0.cssPrefix=F0.familyPrefix);var T4=p(p({},La),F0);T4.autoReplaceSvg||(T4.observeMutations=!1);var v={};Object.keys(La).forEach(t=>{Object.defineProperty(v,t,{enumerable:!0,set:function(e){T4[t]=e,P0.forEach(n=>n(v))},get:function(){return T4[t]}})});Object.defineProperty(v,"familyPrefix",{enumerable:!0,set:function(t){T4.cssPrefix=t,P0.forEach(e=>e(v))},get:function(){return T4.cssPrefix}});o3.FontAwesomeConfig=v;var P0=[];function lb(t){return P0.push(t),()=>{P0.splice(P0.indexOf(t),1)}}var s3=hc,L2={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function fb(t){if(!t||!j2)return;let e=Q.createElement("style");e.setAttribute("type","text/css"),e.innerHTML=t;let n=Q.head.childNodes,c=null;for(let s=n.length-1;s>-1;s--){let o=n[s],i=(o.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(i)>-1&&(c=o)}return Q.head.insertBefore(e,c),t}var ub="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function V0(){let t=12,e="";for(;t-- >0;)e+=ub[Math.random()*62|0];return e}function _4(t){let e=[];for(let n=(t||[]).length>>>0;n--;)e[n]=t[n];return e}function Tc(t){return t.classList?_4(t.classList):(t.getAttribute("class")||"").split(" ").filter(e=>e)}function va(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function db(t){return Object.keys(t||{}).reduce((e,n)=>e+"".concat(n,'="').concat(va(t[n]),'" '),"").trim()}function p8(t){return Object.keys(t||{}).reduce((e,n)=>e+"".concat(n,": ").concat(t[n].trim(),";"),"")}function _c(t){return t.size!==L2.size||t.x!==L2.x||t.y!==L2.y||t.rotate!==L2.rotate||t.flipX||t.flipY}function hb(t){let{transform:e,containerWidth:n,iconWidth:c}=t,s={transform:"translate(".concat(n/2," 256)")},o="translate(".concat(e.x*32,", ").concat(e.y*32,") "),i="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),r="rotate(".concat(e.rotate," 0 0)"),a={transform:"".concat(o," ").concat(i," ").concat(r)},l={transform:"translate(".concat(c/2*-1," -256)")};return{outer:s,inner:a,path:l}}function mb(t){let{transform:e,width:n=hc,height:c=hc,startCentered:s=!1}=t,o="";return s&&fa?o+="translate(".concat(e.x/s3-n/2,"em, ").concat(e.y/s3-c/2,"em) "):s?o+="translate(calc(-50% + ".concat(e.x/s3,"em), calc(-50% + ").concat(e.y/s3,"em)) "):o+="translate(".concat(e.x/s3,"em, ").concat(e.y/s3,"em) "),o+="scale(".concat(e.size/s3*(e.flipX?-1:1),", ").concat(e.size/s3*(e.flipY?-1:1),") "),o+="rotate(".concat(e.rotate,"deg) "),o}var pb=`:root, :host {
  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";
  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";
  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";
  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";
  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";
  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 6 Duotone";
  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 6 Duotone";
  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 6 Duotone";
  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";
  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";
  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 6 Sharp Duotone";
  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 6 Sharp Duotone";
  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 6 Sharp Duotone";
}

svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {
  overflow: visible;
  box-sizing: content-box;
}

.svg-inline--fa {
  display: var(--fa-display, inline-block);
  height: 1em;
  overflow: visible;
  vertical-align: -0.125em;
}
.svg-inline--fa.fa-2xs {
  vertical-align: 0.1em;
}
.svg-inline--fa.fa-xs {
  vertical-align: 0em;
}
.svg-inline--fa.fa-sm {
  vertical-align: -0.0714285705em;
}
.svg-inline--fa.fa-lg {
  vertical-align: -0.2em;
}
.svg-inline--fa.fa-xl {
  vertical-align: -0.25em;
}
.svg-inline--fa.fa-2xl {
  vertical-align: -0.3125em;
}
.svg-inline--fa.fa-pull-left {
  margin-right: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-pull-right {
  margin-left: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-li {
  width: var(--fa-li-width, 2em);
  top: 0.25em;
}
.svg-inline--fa.fa-fw {
  width: var(--fa-fw-width, 1.25em);
}

.fa-layers svg.svg-inline--fa {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.fa-layers-counter, .fa-layers-text {
  display: inline-block;
  position: absolute;
  text-align: center;
}

.fa-layers {
  display: inline-block;
  height: 1em;
  position: relative;
  text-align: center;
  vertical-align: -0.125em;
  width: 1em;
}
.fa-layers svg.svg-inline--fa {
  transform-origin: center center;
}

.fa-layers-text {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: center center;
}

.fa-layers-counter {
  background-color: var(--fa-counter-background-color, #ff253a);
  border-radius: var(--fa-counter-border-radius, 1em);
  box-sizing: border-box;
  color: var(--fa-inverse, #fff);
  line-height: var(--fa-counter-line-height, 1);
  max-width: var(--fa-counter-max-width, 5em);
  min-width: var(--fa-counter-min-width, 1.5em);
  overflow: hidden;
  padding: var(--fa-counter-padding, 0.25em 0.5em);
  right: var(--fa-right, 0);
  text-overflow: ellipsis;
  top: var(--fa-top, 0);
  transform: scale(var(--fa-counter-scale, 0.25));
  transform-origin: top right;
}

.fa-layers-bottom-right {
  bottom: var(--fa-bottom, 0);
  right: var(--fa-right, 0);
  top: auto;
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: bottom right;
}

.fa-layers-bottom-left {
  bottom: var(--fa-bottom, 0);
  left: var(--fa-left, 0);
  right: auto;
  top: auto;
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: bottom left;
}

.fa-layers-top-right {
  top: var(--fa-top, 0);
  right: var(--fa-right, 0);
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: top right;
}

.fa-layers-top-left {
  left: var(--fa-left, 0);
  right: auto;
  top: var(--fa-top, 0);
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: top left;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-2xs {
  font-size: 0.625em;
  line-height: 0.1em;
  vertical-align: 0.225em;
}

.fa-xs {
  font-size: 0.75em;
  line-height: 0.0833333337em;
  vertical-align: 0.125em;
}

.fa-sm {
  font-size: 0.875em;
  line-height: 0.0714285718em;
  vertical-align: 0.0535714295em;
}

.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em;
}

.fa-xl {
  font-size: 1.5em;
  line-height: 0.0416666682em;
  vertical-align: -0.125em;
}

.fa-2xl {
  font-size: 2em;
  line-height: 0.03125em;
  vertical-align: -0.1875em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  left: calc(-1 * var(--fa-li-width, 2em));
  position: absolute;
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit;
}

.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
}

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em);
}

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em);
}

.fa-beat {
  animation-name: fa-beat;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-bounce {
  animation-name: fa-bounce;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
}

.fa-fade {
  animation-name: fa-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-beat-fade {
  animation-name: fa-beat-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-flip {
  animation-name: fa-flip;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-shake {
  animation-name: fa-shake;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin {
  animation-name: fa-spin;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 2s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin-reverse {
  --fa-animation-direction: reverse;
}

.fa-pulse,
.fa-spin-pulse {
  animation-name: fa-spin;
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, steps(8));
}

@media (prefers-reduced-motion: reduce) {
  .fa-beat,
.fa-bounce,
.fa-fade,
.fa-beat-fade,
.fa-flip,
.fa-pulse,
.fa-shake,
.fa-spin,
.fa-spin-pulse {
    animation-delay: -1ms;
    animation-duration: 1ms;
    animation-iteration-count: 1;
    transition-delay: 0s;
    transition-duration: 0s;
  }
}
@keyframes fa-beat {
  0%, 90% {
    transform: scale(1);
  }
  45% {
    transform: scale(var(--fa-beat-scale, 1.25));
  }
}
@keyframes fa-bounce {
  0% {
    transform: scale(1, 1) translateY(0);
  }
  10% {
    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
  }
  30% {
    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
  }
  50% {
    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
  }
  57% {
    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
  }
  64% {
    transform: scale(1, 1) translateY(0);
  }
  100% {
    transform: scale(1, 1) translateY(0);
  }
}
@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4);
  }
}
@keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(var(--fa-beat-fade-scale, 1.125));
  }
}
@keyframes fa-flip {
  50% {
    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
  }
}
@keyframes fa-shake {
  0% {
    transform: rotate(-15deg);
  }
  4% {
    transform: rotate(15deg);
  }
  8%, 24% {
    transform: rotate(-18deg);
  }
  12%, 28% {
    transform: rotate(18deg);
  }
  16% {
    transform: rotate(-22deg);
  }
  20% {
    transform: rotate(22deg);
  }
  32% {
    transform: rotate(-12deg);
  }
  36% {
    transform: rotate(12deg);
  }
  40%, 100% {
    transform: rotate(0deg);
  }
}
@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.fa-rotate-90 {
  transform: rotate(90deg);
}

.fa-rotate-180 {
  transform: rotate(180deg);
}

.fa-rotate-270 {
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  transform: scale(1, -1);
}

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1, -1);
}

.fa-rotate-by {
  transform: rotate(var(--fa-rotate-angle, 0));
}

.fa-stack {
  display: inline-block;
  vertical-align: middle;
  height: 2em;
  position: relative;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
  z-index: var(--fa-stack-z-index, auto);
}

.svg-inline--fa.fa-stack-1x {
  height: 1em;
  width: 1.25em;
}
.svg-inline--fa.fa-stack-2x {
  height: 2em;
  width: 2.5em;
}

.fa-inverse {
  color: var(--fa-inverse, #fff);
}

.sr-only,
.fa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.sr-only-focusable:not(:focus),
.fa-sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.svg-inline--fa .fa-primary {
  fill: var(--fa-primary-color, currentColor);
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa .fa-secondary {
  fill: var(--fa-secondary-color, currentColor);
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-primary {
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-secondary {
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa mask .fa-primary,
.svg-inline--fa mask .fa-secondary {
  fill: black;
}`;function ya(){let t=pa,e=ga,n=v.cssPrefix,c=v.replacementClass,s=pb;if(n!==t||c!==e){let o=new RegExp("\\.".concat(t,"\\-"),"g"),i=new RegExp("\\--".concat(t,"\\-"),"g"),r=new RegExp("\\.".concat(e),"g");s=s.replace(o,".".concat(n,"-")).replace(i,"--".concat(n,"-")).replace(r,".".concat(c))}return s}var Br=!1;function rc(){v.autoAddCss&&!Br&&(fb(ya()),Br=!0)}var gb={mixout(){return{dom:{css:ya,insertCss:rc}}},hooks(){return{beforeDOMElementCreation(){rc()},beforeI2svg(){rc()}}}},H2=o3||{};H2[R2]||(H2[R2]={});H2[R2].styles||(H2[R2].styles={});H2[R2].hooks||(H2[R2].hooks={});H2[R2].shims||(H2[R2].shims=[]);var v2=H2[R2],ba=[],xa=function(){Q.removeEventListener("DOMContentLoaded",xa),d8=1,ba.map(t=>t())},d8=!1;j2&&(d8=(Q.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(Q.readyState),d8||Q.addEventListener("DOMContentLoaded",xa));function Mb(t){j2&&(d8?setTimeout(t,0):ba.push(t))}function H0(t){let{tag:e,attributes:n={},children:c=[]}=t;return typeof t=="string"?va(t):"<".concat(e," ").concat(db(n),">").concat(c.map(H0).join(""),"</").concat(e,">")}function Ur(t,e,n){if(t&&t[e]&&t[e][n])return{prefix:e,iconName:n,icon:t[e][n]}}var Cb=function(e,n){return function(c,s,o,i){return e.call(n,c,s,o,i)}},ac=function(e,n,c,s){var o=Object.keys(e),i=o.length,r=s!==void 0?Cb(n,s):n,a,l,f;for(c===void 0?(a=1,f=e[o[0]]):(a=0,f=c);a<i;a++)l=o[a],f=r(f,e[l],l,e);return f};function zb(t){let e=[],n=0,c=t.length;for(;n<c;){let s=t.charCodeAt(n++);if(s>=55296&&s<=56319&&n<c){let o=t.charCodeAt(n++);(o&64512)==56320?e.push(((s&1023)<<10)+(o&1023)+65536):(e.push(s),n--)}else e.push(s)}return e}function Cc(t){let e=zb(t);return e.length===1?e[0].toString(16):null}function Lb(t,e){let n=t.length,c=t.charCodeAt(e),s;return c>=55296&&c<=56319&&n>e+1&&(s=t.charCodeAt(e+1),s>=56320&&s<=57343)?(c-55296)*1024+s-56320+65536:c}function $r(t){return Object.keys(t).reduce((e,n)=>{let c=t[n];return!!c.icon?e[c.iconName]=c.icon:e[n]=c,e},{})}function zc(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},{skipHooks:c=!1}=n,s=$r(e);typeof v2.hooks.addPack=="function"&&!c?v2.hooks.addPack(t,$r(e)):v2.styles[t]=p(p({},v2.styles[t]||{}),s),t==="fas"&&zc("fa",e)}var{styles:O0,shims:vb}=v2,Na=Object.keys(Ic),yb=Na.reduce((t,e)=>(t[e]=Object.keys(Ic[e]),t),{}),kc=null,wa={},Da={},Sa={},Ea={},Aa={};function bb(t){return~ib.indexOf(t)}function xb(t,e){let n=e.split("-"),c=n[0],s=n.slice(1).join("-");return c===t&&s!==""&&!bb(s)?s:null}var Ia=()=>{let t=c=>ac(O0,(s,o,i)=>(s[i]=ac(o,c,{}),s),{});wa=t((c,s,o)=>(s[3]&&(c[s[3]]=o),s[2]&&s[2].filter(r=>typeof r=="number").forEach(r=>{c[r.toString(16)]=o}),c)),Da=t((c,s,o)=>(c[o]=o,s[2]&&s[2].filter(r=>typeof r=="string").forEach(r=>{c[r]=o}),c)),Aa=t((c,s,o)=>{let i=s[2];return c[o]=o,i.forEach(r=>{c[r]=o}),c});let e="far"in O0||v.autoFetchSvg,n=ac(vb,(c,s)=>{let o=s[0],i=s[1],r=s[2];return i==="far"&&!e&&(i="fas"),typeof o=="string"&&(c.names[o]={prefix:i,iconName:r}),typeof o=="number"&&(c.unicodes[o.toString(16)]={prefix:i,iconName:r}),c},{names:{},unicodes:{}});Sa=n.names,Ea=n.unicodes,kc=g8(v.styleDefault,{family:v.familyDefault})};lb(t=>{kc=g8(t.styleDefault,{family:v.familyDefault})});Ia();function Fc(t,e){return(wa[t]||{})[e]}function Nb(t,e){return(Da[t]||{})[e]}function B3(t,e){return(Aa[t]||{})[e]}function Ta(t){return Sa[t]||{prefix:null,iconName:null}}function wb(t){let e=Ea[t],n=Fc("fas",t);return e||(n?{prefix:"fas",iconName:n}:null)||{prefix:null,iconName:null}}function i3(){return kc}var _a=()=>({prefix:null,iconName:null,rest:[]});function Db(t){let e=C1,n=Na.reduce((c,s)=>(c[s]="".concat(v.cssPrefix,"-").concat(s),c),{});return ha.forEach(c=>{(t.includes(n[c])||t.some(s=>yb[c].includes(s)))&&(e=c)}),e}function g8(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{family:n=C1}=e,c=tb[n][t];if(n===m8&&!t)return"fad";let s=jr[n][t]||jr[n][c],o=t in v2.styles?t:null;return s||o||null}function Sb(t){let e=[],n=null;return t.forEach(c=>{let s=xb(v.cssPrefix,c);s?n=s:c&&e.push(c)}),{iconName:n,rest:e}}function qr(t){return t.sort().filter((e,n,c)=>c.indexOf(e)===n)}function M8(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{skipLookups:n=!1}=e,c=null,s=dc.concat(Uy),o=qr(t.filter(u=>s.includes(u))),i=qr(t.filter(u=>!dc.includes(u))),r=o.filter(u=>(c=u,!da.includes(u))),[a=null]=r,l=Db(o),f=p(p({},Sb(i)),{},{prefix:g8(a,{family:l})});return p(p(p({},f),Tb({values:t,family:l,styles:O0,config:v,canonical:f,givenPrefix:c})),Eb(n,c,f))}function Eb(t,e,n){let{prefix:c,iconName:s}=n;if(t||!c||!s)return{prefix:c,iconName:s};let o=e==="fa"?Ta(s):{},i=B3(c,s);return s=o.iconName||i||s,c=o.prefix||c,c==="far"&&!O0.far&&O0.fas&&!v.autoFetchSvg&&(c="fas"),{prefix:c,iconName:s}}var Ab=ha.filter(t=>t!==C1||t!==m8),Ib=Object.keys(uc).filter(t=>t!==C1).map(t=>Object.keys(uc[t])).flat();function Tb(t){let{values:e,family:n,canonical:c,givenPrefix:s="",styles:o={},config:i={}}=t,r=n===m8,a=e.includes("fa-duotone")||e.includes("fad"),l=i.familyDefault==="duotone",f=c.prefix==="fad"||c.prefix==="fa-duotone";if(!r&&(a||l||f)&&(c.prefix="fad"),(e.includes("fa-brands")||e.includes("fab"))&&(c.prefix="fab"),!c.prefix&&Ab.includes(n)&&(Object.keys(o).find(d=>Ib.includes(d))||i.autoFetchSvg)){let d=Fy.get(n).defaultShortPrefixId;c.prefix=d,c.iconName=B3(c.prefix,c.iconName)||c.iconName}return(c.prefix==="fa"||s==="fa")&&(c.prefix=i3()||"fas"),c}var Lc=class{constructor(){this.definitions={}}add(){for(var e=arguments.length,n=new Array(e),c=0;c<e;c++)n[c]=arguments[c];let s=n.reduce(this._pullDefinitions,{});Object.keys(s).forEach(o=>{this.definitions[o]=p(p({},this.definitions[o]||{}),s[o]),zc(o,s[o]);let i=Ic[C1][o];i&&zc(i,s[o]),Ia()})}reset(){this.definitions={}}_pullDefinitions(e,n){let c=n.prefix&&n.iconName&&n.icon?{0:n}:n;return Object.keys(c).map(s=>{let{prefix:o,iconName:i,icon:r}=c[s],a=r[2];e[o]||(e[o]={}),a.length>0&&a.forEach(l=>{typeof l=="string"&&(e[o][l]=r)}),e[o][i]=r}),e}},Gr=[],A4={},I4={},_b=Object.keys(I4);function kb(t,e){let{mixoutsTo:n}=e;return Gr=t,A4={},Object.keys(I4).forEach(c=>{_b.indexOf(c)===-1&&delete I4[c]}),Gr.forEach(c=>{let s=c.mixout?c.mixout():{};if(Object.keys(s).forEach(o=>{typeof s[o]=="function"&&(n[o]=s[o]),typeof s[o]=="object"&&Object.keys(s[o]).forEach(i=>{n[o]||(n[o]={}),n[o][i]=s[o][i]})}),c.hooks){let o=c.hooks();Object.keys(o).forEach(i=>{A4[i]||(A4[i]=[]),A4[i].push(o[i])})}c.provides&&c.provides(I4)}),n}function vc(t,e){for(var n=arguments.length,c=new Array(n>2?n-2:0),s=2;s<n;s++)c[s-2]=arguments[s];return(A4[t]||[]).forEach(i=>{e=i.apply(null,[e,...c])}),e}function $3(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),c=1;c<e;c++)n[c-1]=arguments[c];(A4[t]||[]).forEach(o=>{o.apply(null,n)})}function r3(){let t=arguments[0],e=Array.prototype.slice.call(arguments,1);return I4[t]?I4[t].apply(null,e):void 0}function yc(t){t.prefix==="fa"&&(t.prefix="fas");let{iconName:e}=t,n=t.prefix||i3();if(e)return e=B3(n,e)||e,Ur(ka.definitions,n,e)||Ur(v2.styles,n,e)}var ka=new Lc,Fb=()=>{v.autoReplaceSvg=!1,v.observeMutations=!1,$3("noAuto")},Pb={i2svg:function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return j2?($3("beforeI2svg",t),r3("pseudoElements2svg",t),r3("i2svg",t)):Promise.reject(new Error("Operation requires a DOM of some kind."))},watch:function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{autoReplaceSvgRoot:e}=t;v.autoReplaceSvg===!1&&(v.autoReplaceSvg=!0),v.observeMutations=!0,Mb(()=>{Ob({autoReplaceSvgRoot:e}),$3("watch",t)})}},Vb={icon:t=>{if(t===null)return null;if(typeof t=="object"&&t.prefix&&t.iconName)return{prefix:t.prefix,iconName:B3(t.prefix,t.iconName)||t.iconName};if(Array.isArray(t)&&t.length===2){let e=t[1].indexOf("fa-")===0?t[1].slice(3):t[1],n=g8(t[0]);return{prefix:n,iconName:B3(n,e)||e}}if(typeof t=="string"&&(t.indexOf("".concat(v.cssPrefix,"-"))>-1||t.match(cb))){let e=M8(t.split(" "),{skipLookups:!0});return{prefix:e.prefix||i3(),iconName:B3(e.prefix,e.iconName)||e.iconName}}if(typeof t=="string"){let e=i3();return{prefix:e,iconName:B3(e,t)||t}}}},B1={noAuto:Fb,config:v,dom:Pb,parse:Vb,library:ka,findIconDefinition:yc,toHtml:H0},Ob=function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{autoReplaceSvgRoot:e=Q}=t;(Object.keys(v2.styles).length>0||v.autoFetchSvg)&&j2&&v.autoReplaceSvg&&B1.dom.i2svg({node:e})};function C8(t,e){return Object.defineProperty(t,"abstract",{get:e}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map(n=>H0(n))}}),Object.defineProperty(t,"node",{get:function(){if(!j2)return;let n=Q.createElement("div");return n.innerHTML=t.html,n.children}}),t}function Rb(t){let{children:e,main:n,mask:c,attributes:s,styles:o,transform:i}=t;if(_c(i)&&n.found&&!c.found){let{width:r,height:a}=n,l={x:r/a/2,y:.5};s.style=p8(p(p({},o),{},{"transform-origin":"".concat(l.x+i.x/16,"em ").concat(l.y+i.y/16,"em")}))}return[{tag:"svg",attributes:s,children:e}]}function Hb(t){let{prefix:e,iconName:n,children:c,attributes:s,symbol:o}=t,i=o===!0?"".concat(e,"-").concat(v.cssPrefix,"-").concat(n):o;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:p(p({},s),{},{id:i}),children:c}]}]}function Pc(t){let{icons:{main:e,mask:n},prefix:c,iconName:s,transform:o,symbol:i,title:r,maskId:a,titleId:l,extra:f,watchable:u=!1}=t,{width:d,height:h}=n.found?n:e,m=Hy.includes(c),C=[v.replacementClass,s?"".concat(v.cssPrefix,"-").concat(s):""].filter(J=>f.classes.indexOf(J)===-1).filter(J=>J!==""||!!J).concat(f.classes).join(" "),M={children:[],attributes:p(p({},f.attributes),{},{"data-prefix":c,"data-icon":s,class:C,role:f.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(d," ").concat(h)})},g=m&&!~f.classes.indexOf("fa-fw")?{width:"".concat(d/h*16*.0625,"em")}:{};u&&(M.attributes[U3]=""),r&&(M.children.push({tag:"title",attributes:{id:M.attributes["aria-labelledby"]||"title-".concat(l||V0())},children:[r]}),delete M.attributes.title);let b=p(p({},M),{},{prefix:c,iconName:s,main:e,mask:n,maskId:a,transform:o,symbol:i,styles:p(p({},g),f.styles)}),{children:V,attributes:A}=n.found&&e.found?r3("generateAbstractMask",b)||{children:[],attributes:{}}:r3("generateAbstractIcon",b)||{children:[],attributes:{}};return b.children=V,b.attributes=A,i?Hb(b):Rb(b)}function Wr(t){let{content:e,width:n,height:c,transform:s,title:o,extra:i,watchable:r=!1}=t,a=p(p(p({},i.attributes),o?{title:o}:{}),{},{class:i.classes.join(" ")});r&&(a[U3]="");let l=p({},i.styles);_c(s)&&(l.transform=mb({transform:s,startCentered:!0,width:n,height:c}),l["-webkit-transform"]=l.transform);let f=p8(l);f.length>0&&(a.style=f);let u=[];return u.push({tag:"span",attributes:a,children:[e]}),o&&u.push({tag:"span",attributes:{class:"sr-only"},children:[o]}),u}function jb(t){let{content:e,title:n,extra:c}=t,s=p(p(p({},c.attributes),n?{title:n}:{}),{},{class:c.classes.join(" ")}),o=p8(c.styles);o.length>0&&(s.style=o);let i=[];return i.push({tag:"span",attributes:s,children:[e]}),n&&i.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),i}var{styles:lc}=v2;function bc(t){let e=t[0],n=t[1],[c]=t.slice(4),s=null;return Array.isArray(c)?s={tag:"g",attributes:{class:"".concat(v.cssPrefix,"-").concat(ic.GROUP)},children:[{tag:"path",attributes:{class:"".concat(v.cssPrefix,"-").concat(ic.SECONDARY),fill:"currentColor",d:c[0]}},{tag:"path",attributes:{class:"".concat(v.cssPrefix,"-").concat(ic.PRIMARY),fill:"currentColor",d:c[1]}}]}:s={tag:"path",attributes:{fill:"currentColor",d:c}},{found:!0,width:e,height:n,icon:s}}var Bb={found:!1,width:512,height:512};function Ub(t,e){!Ma&&!v.showMissingIcons&&t&&console.error('Icon with name "'.concat(t,'" and prefix "').concat(e,'" is missing.'))}function xc(t,e){let n=e;return e==="fa"&&v.styleDefault!==null&&(e=i3()),new Promise((c,s)=>{if(n==="fa"){let o=Ta(t)||{};t=o.iconName||t,e=o.prefix||e}if(t&&e&&lc[e]&&lc[e][t]){let o=lc[e][t];return c(bc(o))}Ub(t,e),c(p(p({},Bb),{},{icon:v.showMissingIcons&&t?r3("missingIconAbstract")||{}:{}}))})}var Zr=()=>{},Nc=v.measurePerformance&&a8&&a8.mark&&a8.measure?a8:{mark:Zr,measure:Zr},k0='FA "6.7.2"',$b=t=>(Nc.mark("".concat(k0," ").concat(t," begins")),()=>Fa(t)),Fa=t=>{Nc.mark("".concat(k0," ").concat(t," ends")),Nc.measure("".concat(k0," ").concat(t),"".concat(k0," ").concat(t," begins"),"".concat(k0," ").concat(t," ends"))},Vc={begin:$b,end:Fa},f8=()=>{};function Yr(t){return typeof(t.getAttribute?t.getAttribute(U3):null)=="string"}function qb(t){let e=t.getAttribute?t.getAttribute(Ec):null,n=t.getAttribute?t.getAttribute(Ac):null;return e&&n}function Gb(t){return t&&t.classList&&t.classList.contains&&t.classList.contains(v.replacementClass)}function Wb(){return v.autoReplaceSvg===!0?u8.replace:u8[v.autoReplaceSvg]||u8.replace}function Zb(t){return Q.createElementNS("http://www.w3.org/2000/svg",t)}function Yb(t){return Q.createElement(t)}function Pa(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{ceFn:n=t.tag==="svg"?Zb:Yb}=e;if(typeof t=="string")return Q.createTextNode(t);let c=n(t.tag);return Object.keys(t.attributes||[]).forEach(function(o){c.setAttribute(o,t.attributes[o])}),(t.children||[]).forEach(function(o){c.appendChild(Pa(o,{ceFn:n}))}),c}function Qb(t){let e=" ".concat(t.outerHTML," ");return e="".concat(e,"Font Awesome fontawesome.com "),e}var u8={replace:function(t){let e=t[0];if(e.parentNode)if(t[1].forEach(n=>{e.parentNode.insertBefore(Pa(n),e)}),e.getAttribute(U3)===null&&v.keepOriginalSource){let n=Q.createComment(Qb(e));e.parentNode.replaceChild(n,e)}else e.remove()},nest:function(t){let e=t[0],n=t[1];if(~Tc(e).indexOf(v.replacementClass))return u8.replace(t);let c=new RegExp("".concat(v.cssPrefix,"-.*"));if(delete n[0].attributes.id,n[0].attributes.class){let o=n[0].attributes.class.split(" ").reduce((i,r)=>(r===v.replacementClass||r.match(c)?i.toSvg.push(r):i.toNode.push(r),i),{toNode:[],toSvg:[]});n[0].attributes.class=o.toSvg.join(" "),o.toNode.length===0?e.removeAttribute("class"):e.setAttribute("class",o.toNode.join(" "))}let s=n.map(o=>H0(o)).join(`
`);e.setAttribute(U3,""),e.innerHTML=s}};function Qr(t){t()}function Va(t,e){let n=typeof e=="function"?e:f8;if(t.length===0)n();else{let c=Qr;v.mutateApproach===Jy&&(c=o3.requestAnimationFrame||Qr),c(()=>{let s=Wb(),o=Vc.begin("mutate");t.map(s),o(),n()})}}var Oc=!1;function Oa(){Oc=!0}function wc(){Oc=!1}var h8=null;function Kr(t){if(!Vr||!v.observeMutations)return;let{treeCallback:e=f8,nodeCallback:n=f8,pseudoElementsCallback:c=f8,observeMutationsRoot:s=Q}=t;h8=new Vr(o=>{if(Oc)return;let i=i3();_4(o).forEach(r=>{if(r.type==="childList"&&r.addedNodes.length>0&&!Yr(r.addedNodes[0])&&(v.searchPseudoElements&&c(r.target),e(r.target)),r.type==="attributes"&&r.target.parentNode&&v.searchPseudoElements&&c(r.target.parentNode),r.type==="attributes"&&Yr(r.target)&&~ob.indexOf(r.attributeName))if(r.attributeName==="class"&&qb(r.target)){let{prefix:a,iconName:l}=M8(Tc(r.target));r.target.setAttribute(Ec,a||i),l&&r.target.setAttribute(Ac,l)}else Gb(r.target)&&n(r.target)})}),j2&&h8.observe(s,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function Kb(){h8&&h8.disconnect()}function Xb(t){let e=t.getAttribute("style"),n=[];return e&&(n=e.split(";").reduce((c,s)=>{let o=s.split(":"),i=o[0],r=o.slice(1);return i&&r.length>0&&(c[i]=r.join(":").trim()),c},{})),n}function Jb(t){let e=t.getAttribute("data-prefix"),n=t.getAttribute("data-icon"),c=t.innerText!==void 0?t.innerText.trim():"",s=M8(Tc(t));return s.prefix||(s.prefix=i3()),e&&n&&(s.prefix=e,s.iconName=n),s.iconName&&s.prefix||(s.prefix&&c.length>0&&(s.iconName=Nb(s.prefix,t.innerText)||Fc(s.prefix,Cc(t.innerText))),!s.iconName&&v.autoFetchSvg&&t.firstChild&&t.firstChild.nodeType===Node.TEXT_NODE&&(s.iconName=t.firstChild.data)),s}function ex(t){let e=_4(t.attributes).reduce((s,o)=>(s.name!=="class"&&s.name!=="style"&&(s[o.name]=o.value),s),{}),n=t.getAttribute("title"),c=t.getAttribute("data-fa-title-id");return v.autoA11y&&(n?e["aria-labelledby"]="".concat(v.replacementClass,"-title-").concat(c||V0()):(e["aria-hidden"]="true",e.focusable="false")),e}function tx(){return{iconName:null,title:null,titleId:null,prefix:null,transform:L2,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}}}function Xr(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{styleParser:!0},{iconName:n,prefix:c,rest:s}=Jb(t),o=ex(t),i=vc("parseNodeAttributes",{},t),r=e.styleParser?Xb(t):[];return p({iconName:n,title:t.getAttribute("title"),titleId:t.getAttribute("data-fa-title-id"),prefix:c,transform:L2,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:s,styles:r,attributes:o}},i)}var{styles:cx}=v2;function Ra(t){let e=v.autoReplaceSvg==="nest"?Xr(t,{styleParser:!1}):Xr(t);return~e.extra.classes.indexOf(za)?r3("generateLayersText",t,e):r3("generateSvgReplacementMutation",t,e)}function nx(){return[...Vy,...dc]}function Jr(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(!j2)return Promise.resolve();let n=Q.documentElement.classList,c=f=>n.add("".concat(Hr,"-").concat(f)),s=f=>n.remove("".concat(Hr,"-").concat(f)),o=v.autoFetchSvg?nx():da.concat(Object.keys(cx));o.includes("fa")||o.push("fa");let i=[".".concat(za,":not([").concat(U3,"])")].concat(o.map(f=>".".concat(f,":not([").concat(U3,"])"))).join(", ");if(i.length===0)return Promise.resolve();let r=[];try{r=_4(t.querySelectorAll(i))}catch{}if(r.length>0)c("pending"),s("complete");else return Promise.resolve();let a=Vc.begin("onTree"),l=r.reduce((f,u)=>{try{let d=Ra(u);d&&f.push(d)}catch(d){Ma||d.name==="MissingIcon"&&console.error(d)}return f},[]);return new Promise((f,u)=>{Promise.all(l).then(d=>{Va(d,()=>{c("active"),c("complete"),s("pending"),typeof e=="function"&&e(),a(),f()})}).catch(d=>{a(),u(d)})})}function sx(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;Ra(t).then(n=>{n&&Va([n],e)})}function ox(t){return function(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},c=(e||{}).icon?e:yc(e||{}),{mask:s}=n;return s&&(s=(s||{}).icon?s:yc(s||{})),t(c,p(p({},n),{},{mask:s}))}}var ix=function(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{transform:n=L2,symbol:c=!1,mask:s=null,maskId:o=null,title:i=null,titleId:r=null,classes:a=[],attributes:l={},styles:f={}}=e;if(!t)return;let{prefix:u,iconName:d,icon:h}=t;return C8(p({type:"icon"},t),()=>($3("beforeDOMElementCreation",{iconDefinition:t,params:e}),v.autoA11y&&(i?l["aria-labelledby"]="".concat(v.replacementClass,"-title-").concat(r||V0()):(l["aria-hidden"]="true",l.focusable="false")),Pc({icons:{main:bc(h),mask:s?bc(s.icon):{found:!1,width:null,height:null,icon:{}}},prefix:u,iconName:d,transform:p(p({},L2),n),symbol:c,title:i,maskId:o,titleId:r,extra:{attributes:l,styles:f,classes:a}})))},rx={mixout(){return{icon:ox(ix)}},hooks(){return{mutationObserverCallbacks(t){return t.treeCallback=Jr,t.nodeCallback=sx,t}}},provides(t){t.i2svg=function(e){let{node:n=Q,callback:c=()=>{}}=e;return Jr(n,c)},t.generateSvgReplacementMutation=function(e,n){let{iconName:c,title:s,titleId:o,prefix:i,transform:r,symbol:a,mask:l,maskId:f,extra:u}=n;return new Promise((d,h)=>{Promise.all([xc(c,i),l.iconName?xc(l.iconName,l.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(m=>{let[C,M]=m;d([e,Pc({icons:{main:C,mask:M},prefix:i,iconName:c,transform:r,symbol:a,maskId:f,title:s,titleId:o,extra:u,watchable:!0})])}).catch(h)})},t.generateAbstractIcon=function(e){let{children:n,attributes:c,main:s,transform:o,styles:i}=e,r=p8(i);r.length>0&&(c.style=r);let a;return _c(o)&&(a=r3("generateAbstractTransformGrouping",{main:s,transform:o,containerWidth:s.width,iconWidth:s.width})),n.push(a||s.icon),{children:n,attributes:c}}}},ax={mixout(){return{layer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{classes:n=[]}=e;return C8({type:"layer"},()=>{$3("beforeDOMElementCreation",{assembler:t,params:e});let c=[];return t(s=>{Array.isArray(s)?s.map(o=>{c=c.concat(o.abstract)}):c=c.concat(s.abstract)}),[{tag:"span",attributes:{class:["".concat(v.cssPrefix,"-layers"),...n].join(" ")},children:c}]})}}}},lx={mixout(){return{counter(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{title:n=null,classes:c=[],attributes:s={},styles:o={}}=e;return C8({type:"counter",content:t},()=>($3("beforeDOMElementCreation",{content:t,params:e}),jb({content:t.toString(),title:n,extra:{attributes:s,styles:o,classes:["".concat(v.cssPrefix,"-layers-counter"),...c]}})))}}}},fx={mixout(){return{text(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{transform:n=L2,title:c=null,classes:s=[],attributes:o={},styles:i={}}=e;return C8({type:"text",content:t},()=>($3("beforeDOMElementCreation",{content:t,params:e}),Wr({content:t,transform:p(p({},L2),n),title:c,extra:{attributes:o,styles:i,classes:["".concat(v.cssPrefix,"-layers-text"),...s]}})))}}},provides(t){t.generateLayersText=function(e,n){let{title:c,transform:s,extra:o}=n,i=null,r=null;if(fa){let a=parseInt(getComputedStyle(e).fontSize,10),l=e.getBoundingClientRect();i=l.width/a,r=l.height/a}return v.autoA11y&&!c&&(o.attributes["aria-hidden"]="true"),Promise.resolve([e,Wr({content:e.innerHTML,width:i,height:r,transform:s,title:c,extra:o,watchable:!0})])}}},ux=new RegExp('"',"ug"),ea=[1105920,1112319],ta=p(p(p(p({},{FontAwesome:{normal:"fas",400:"fas"}}),ky),Ky),$y),Dc=Object.keys(ta).reduce((t,e)=>(t[e.toLowerCase()]=ta[e],t),{}),dx=Object.keys(Dc).reduce((t,e)=>{let n=Dc[e];return t[e]=n[900]||[...Object.entries(n)][0][1],t},{});function hx(t){let e=t.replace(ux,""),n=Lb(e,0),c=n>=ea[0]&&n<=ea[1],s=e.length===2?e[0]===e[1]:!1;return{value:Cc(s?e[0]:e),isSecondary:c||s}}function mx(t,e){let n=t.replace(/^['"]|['"]$/g,"").toLowerCase(),c=parseInt(e),s=isNaN(c)?"normal":c;return(Dc[n]||{})[s]||dx[n]}function ca(t,e){let n="".concat(Xy).concat(e.replace(":","-"));return new Promise((c,s)=>{if(t.getAttribute(n)!==null)return c();let i=_4(t.children).filter(d=>d.getAttribute(mc)===e)[0],r=o3.getComputedStyle(t,e),a=r.getPropertyValue("font-family"),l=a.match(nb),f=r.getPropertyValue("font-weight"),u=r.getPropertyValue("content");if(i&&!l)return t.removeChild(i),c();if(l&&u!=="none"&&u!==""){let d=r.getPropertyValue("content"),h=mx(a,f),{value:m,isSecondary:C}=hx(d),M=l[0].startsWith("FontAwesome"),g=Fc(h,m),b=g;if(M){let V=wb(m);V.iconName&&V.prefix&&(g=V.iconName,h=V.prefix)}if(g&&!C&&(!i||i.getAttribute(Ec)!==h||i.getAttribute(Ac)!==b)){t.setAttribute(n,b),i&&t.removeChild(i);let V=tx(),{extra:A}=V;A.attributes[mc]=e,xc(g,h).then(J=>{let z1=Pc(p(p({},V),{},{icons:{main:J,mask:_a()},prefix:h,iconName:b,extra:A,watchable:!0})),s1=Q.createElementNS("http://www.w3.org/2000/svg","svg");e==="::before"?t.insertBefore(s1,t.firstChild):t.appendChild(s1),s1.outerHTML=z1.map(Q1=>H0(Q1)).join(`
`),t.removeAttribute(n),c()}).catch(s)}else c()}else c()})}function px(t){return Promise.all([ca(t,"::before"),ca(t,"::after")])}function gx(t){return t.parentNode!==document.head&&!~eb.indexOf(t.tagName.toUpperCase())&&!t.getAttribute(mc)&&(!t.parentNode||t.parentNode.tagName!=="svg")}function na(t){if(j2)return new Promise((e,n)=>{let c=_4(t.querySelectorAll("*")).filter(gx).map(px),s=Vc.begin("searchPseudoElements");Oa(),Promise.all(c).then(()=>{s(),wc(),e()}).catch(()=>{s(),wc(),n()})})}var Mx={hooks(){return{mutationObserverCallbacks(t){return t.pseudoElementsCallback=na,t}}},provides(t){t.pseudoElements2svg=function(e){let{node:n=Q}=e;v.searchPseudoElements&&na(n)}}},sa=!1,Cx={mixout(){return{dom:{unwatch(){Oa(),sa=!0}}}},hooks(){return{bootstrap(){Kr(vc("mutationObserverCallbacks",{}))},noAuto(){Kb()},watch(t){let{observeMutationsRoot:e}=t;sa?wc():Kr(vc("mutationObserverCallbacks",{observeMutationsRoot:e}))}}}},oa=t=>{let e={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return t.toLowerCase().split(" ").reduce((n,c)=>{let s=c.toLowerCase().split("-"),o=s[0],i=s.slice(1).join("-");if(o&&i==="h")return n.flipX=!0,n;if(o&&i==="v")return n.flipY=!0,n;if(i=parseFloat(i),isNaN(i))return n;switch(o){case"grow":n.size=n.size+i;break;case"shrink":n.size=n.size-i;break;case"left":n.x=n.x-i;break;case"right":n.x=n.x+i;break;case"up":n.y=n.y-i;break;case"down":n.y=n.y+i;break;case"rotate":n.rotate=n.rotate+i;break}return n},e)},zx={mixout(){return{parse:{transform:t=>oa(t)}}},hooks(){return{parseNodeAttributes(t,e){let n=e.getAttribute("data-fa-transform");return n&&(t.transform=oa(n)),t}}},provides(t){t.generateAbstractTransformGrouping=function(e){let{main:n,transform:c,containerWidth:s,iconWidth:o}=e,i={transform:"translate(".concat(s/2," 256)")},r="translate(".concat(c.x*32,", ").concat(c.y*32,") "),a="scale(".concat(c.size/16*(c.flipX?-1:1),", ").concat(c.size/16*(c.flipY?-1:1),") "),l="rotate(".concat(c.rotate," 0 0)"),f={transform:"".concat(r," ").concat(a," ").concat(l)},u={transform:"translate(".concat(o/2*-1," -256)")},d={outer:i,inner:f,path:u};return{tag:"g",attributes:p({},d.outer),children:[{tag:"g",attributes:p({},d.inner),children:[{tag:n.icon.tag,children:n.icon.children,attributes:p(p({},n.icon.attributes),d.path)}]}]}}}},fc={x:0,y:0,width:"100%",height:"100%"};function ia(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return t.attributes&&(t.attributes.fill||e)&&(t.attributes.fill="black"),t}function Lx(t){return t.tag==="g"?t.children:[t]}var vx={hooks(){return{parseNodeAttributes(t,e){let n=e.getAttribute("data-fa-mask"),c=n?M8(n.split(" ").map(s=>s.trim())):_a();return c.prefix||(c.prefix=i3()),t.mask=c,t.maskId=e.getAttribute("data-fa-mask-id"),t}}},provides(t){t.generateAbstractMask=function(e){let{children:n,attributes:c,main:s,mask:o,maskId:i,transform:r}=e,{width:a,icon:l}=s,{width:f,icon:u}=o,d=hb({transform:r,containerWidth:f,iconWidth:a}),h={tag:"rect",attributes:p(p({},fc),{},{fill:"white"})},m=l.children?{children:l.children.map(ia)}:{},C={tag:"g",attributes:p({},d.inner),children:[ia(p({tag:l.tag,attributes:p(p({},l.attributes),d.path)},m))]},M={tag:"g",attributes:p({},d.outer),children:[C]},g="mask-".concat(i||V0()),b="clip-".concat(i||V0()),V={tag:"mask",attributes:p(p({},fc),{},{id:g,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[h,M]},A={tag:"defs",children:[{tag:"clipPath",attributes:{id:b},children:Lx(u)},V]};return n.push(A,{tag:"rect",attributes:p({fill:"currentColor","clip-path":"url(#".concat(b,")"),mask:"url(#".concat(g,")")},fc)}),{children:n,attributes:c}}}},yx={provides(t){let e=!1;o3.matchMedia&&(e=o3.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){let n=[],c={fill:"currentColor"},s={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};n.push({tag:"path",attributes:p(p({},c),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});let o=p(p({},s),{},{attributeName:"opacity"}),i={tag:"circle",attributes:p(p({},c),{},{cx:"256",cy:"364",r:"28"}),children:[]};return e||i.children.push({tag:"animate",attributes:p(p({},s),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:p(p({},o),{},{values:"1;0;1;1;0;1;"})}),n.push(i),n.push({tag:"path",attributes:p(p({},c),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:e?[]:[{tag:"animate",attributes:p(p({},o),{},{values:"1;0;0;0;0;1;"})}]}),e||n.push({tag:"path",attributes:p(p({},c),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:p(p({},o),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:n}}}},bx={hooks(){return{parseNodeAttributes(t,e){let n=e.getAttribute("data-fa-symbol"),c=n===null?!1:n===""?!0:n;return t.symbol=c,t}}}},xx=[gb,rx,ax,lx,fx,Mx,Cx,zx,vx,yx,bx];kb(xx,{mixoutsTo:B1});var $F=B1.noAuto,qF=B1.config,GF=B1.library,WF=B1.dom,Ha=B1.parse,ZF=B1.findIconDefinition,YF=B1.toHtml,ja=B1.icon,QF=B1.layer,Nx=B1.text,wx=B1.counter;var Dx=["*"],Sx=t=>{throw new Error(`Could not find icon with iconName=${t.iconName} and prefix=${t.prefix} in the icon library.`)},Ex=()=>{throw new Error("Property `icon` is required for `fa-icon`/`fa-duotone-icon` components.")},Ax=t=>{let e={[`fa-${t.animation}`]:t.animation!=null&&!t.animation.startsWith("spin"),"fa-spin":t.animation==="spin"||t.animation==="spin-reverse","fa-spin-pulse":t.animation==="spin-pulse"||t.animation==="spin-pulse-reverse","fa-spin-reverse":t.animation==="spin-reverse"||t.animation==="spin-pulse-reverse","fa-pulse":t.animation==="spin-pulse"||t.animation==="spin-pulse-reverse","fa-fw":t.fixedWidth,"fa-border":t.border,"fa-inverse":t.inverse,"fa-layers-counter":t.counter,"fa-flip-horizontal":t.flip==="horizontal"||t.flip==="both","fa-flip-vertical":t.flip==="vertical"||t.flip==="both",[`fa-${t.size}`]:t.size!==null,[`fa-rotate-${t.rotate}`]:t.rotate!==null,[`fa-pull-${t.pull}`]:t.pull!==null,[`fa-stack-${t.stackItemSize}`]:t.stackItemSize!=null};return Object.keys(e).map(n=>e[n]?n:null).filter(n=>n)},Ix=t=>t.prefix!==void 0&&t.iconName!==void 0,Tx=(t,e)=>Ix(t)?t:typeof t=="string"?{prefix:e,iconName:t}:{prefix:t[0],iconName:t[1]},_x=(()=>{let e=class e{constructor(){this.defaultPrefix="fas",this.fallbackIcon=null}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),Rc=(()=>{let e=class e{constructor(){this.definitions={}}addIcons(...c){for(let s of c){s.prefix in this.definitions||(this.definitions[s.prefix]={}),this.definitions[s.prefix][s.iconName]=s;for(let o of s.icon[2])typeof o=="string"&&(this.definitions[s.prefix][o]=s)}}addIconPacks(...c){for(let s of c){let o=Object.keys(s).map(i=>s[i]);this.addIcons(...o)}}getIconDefinition(c,s){return c in this.definitions&&s in this.definitions[c]?this.definitions[c][s]:null}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),kx=(()=>{let e=class e{constructor(){this.stackItemSize="1x"}ngOnChanges(c){if("size"in c)throw new Error('fa-icon is not allowed to customize size when used inside fa-stack. Set size on the enclosing fa-stack instead: <fa-stack size="4x">...</fa-stack>.')}};e.\u0275fac=function(s){return new(s||e)},e.\u0275dir=H({type:e,selectors:[["fa-icon","stackItemSize",""],["fa-duotone-icon","stackItemSize",""]],inputs:{stackItemSize:"stackItemSize",size:"size"},standalone:!0,features:[o2]});let t=e;return t})(),Fx=(()=>{let e=class e{constructor(c,s){this.renderer=c,this.elementRef=s}ngOnInit(){this.renderer.addClass(this.elementRef.nativeElement,"fa-stack")}ngOnChanges(c){"size"in c&&(c.size.currentValue!=null&&this.renderer.addClass(this.elementRef.nativeElement,`fa-${c.size.currentValue}`),c.size.previousValue!=null&&this.renderer.removeClass(this.elementRef.nativeElement,`fa-${c.size.previousValue}`))}};e.\u0275fac=function(s){return new(s||e)(z(Z1),z(h1))},e.\u0275cmp=T7({type:e,selectors:[["fa-stack"]],inputs:{size:"size"},standalone:!0,features:[o2,vt],ngContentSelectors:Dx,decls:1,vars:0,template:function(s,o){s&1&&(Ai(),Ii(0))},encapsulation:2});let t=e;return t})(),JF=(()=>{let e=class e{set spin(c){this.animation=c?"spin":void 0}set pulse(c){this.animation=c?"spin-pulse":void 0}constructor(c,s,o,i,r){this.sanitizer=c,this.config=s,this.iconLibrary=o,this.stackItem=i,this.classes=[],r!=null&&i==null&&console.error('FontAwesome: fa-icon and fa-duotone-icon elements must specify stackItemSize attribute when wrapped into fa-stack. Example: <fa-icon stackItemSize="2x"></fa-icon>.')}ngOnChanges(c){if(this.icon==null&&this.config.fallbackIcon==null){Ex();return}if(c){let s=this.icon!=null?this.icon:this.config.fallbackIcon,o=this.findIconDefinition(s);if(o!=null){let i=this.buildParams();this.renderIcon(o,i)}}}render(){this.ngOnChanges({})}findIconDefinition(c){let s=Tx(c,this.config.defaultPrefix);if("icon"in s)return s;let o=this.iconLibrary.getIconDefinition(s.prefix,s.iconName);return o!=null?o:(Sx(s),null)}buildParams(){let c={flip:this.flip,animation:this.animation,border:this.border,inverse:this.inverse,size:this.size||null,pull:this.pull||null,rotate:this.rotate||null,fixedWidth:typeof this.fixedWidth=="boolean"?this.fixedWidth:this.config.fixedWidth,stackItemSize:this.stackItem!=null?this.stackItem.stackItemSize:null},s=typeof this.transform=="string"?Ha.transform(this.transform):this.transform;return{title:this.title,transform:s,classes:[...Ax(c),...this.classes],mask:this.mask!=null?this.findIconDefinition(this.mask):null,styles:this.styles!=null?this.styles:{},symbol:this.symbol,attributes:{role:this.a11yRole}}}renderIcon(c,s){let o=ja(c,s);this.renderedIconHTML=this.sanitizer.bypassSecurityTrustHtml(o.html.join(`
`))}};e.\u0275fac=function(s){return new(s||e)(z(oc),z(_x),z(Rc),z(kx,8),z(Fx,8))},e.\u0275cmp=T7({type:e,selectors:[["fa-icon"]],hostAttrs:[1,"ng-fa-icon"],hostVars:2,hostBindings:function(s,o){s&2&&(Lt("innerHTML",o.renderedIconHTML,Lo),T2("title",o.title))},inputs:{icon:"icon",title:"title",animation:"animation",spin:"spin",pulse:"pulse",mask:"mask",styles:"styles",flip:"flip",size:"size",pull:"pull",border:"border",inverse:"inverse",symbol:"symbol",rotate:"rotate",fixedWidth:"fixedWidth",classes:"classes",transform:"transform",a11yRole:"a11yRole"},standalone:!0,features:[o2,vt],decls:0,vars:0,template:function(s,o){},encapsulation:2});let t=e;return t})();var Ba=(()=>{let e=class e{};e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=I1({type:e}),e.\u0275inj=A1({});let t=e;return t})();var Ua={prefix:"fas",iconName:"trash-can",icon:[448,512,[61460,"trash-alt"],"f2ed","M135.2 17.7C140.6 6.8 151.7 0 163.8 0L284.2 0c12.1 0 23.2 6.8 28.6 17.7L320 32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 96C14.3 96 0 81.7 0 64S14.3 32 32 32l96 0 7.2-14.3zM32 128l384 0 0 320c0 35.3-28.7 64-64 64L96 512c-35.3 0-64-28.7-64-64l0-320zm96 64c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16z"]};var Vx={prefix:"fas",iconName:"file-lines",icon:[384,512,[128441,128462,61686,"file-alt","file-text"],"f15c","M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM112 256l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16z"]},$a=Vx;var Ox={prefix:"fas",iconName:"circle-minus",icon:[512,512,["minus-circle"],"f056","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM184 232l144 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-144 0c-13.3 0-24-10.7-24-24s10.7-24 24-24z"]},qa=Ox;var Ga={prefix:"fas",iconName:"comments",icon:[640,512,[128490,61670],"f086","M208 352c114.9 0 208-78.8 208-176S322.9 0 208 0S0 78.8 0 176c0 38.6 14.7 74.3 39.6 103.4c-3.5 9.4-8.7 17.7-14.2 24.7c-4.8 6.2-9.7 11-13.3 14.3c-1.8 1.6-3.3 2.9-4.3 3.7c-.5 .4-.9 .7-1.1 .8l-.2 .2s0 0 0 0s0 0 0 0C1 327.2-1.4 334.4 .8 340.9S9.1 352 16 352c21.8 0 43.8-5.6 62.1-12.5c9.2-3.5 17.8-7.4 25.2-11.4C134.1 343.3 169.8 352 208 352zM448 176c0 112.3-99.1 196.9-216.5 207C255.8 457.4 336.4 512 432 512c38.2 0 73.9-8.7 104.7-23.9c7.5 4 16 7.9 25.2 11.4c18.3 6.9 40.3 12.5 62.1 12.5c6.9 0 13.1-4.5 15.2-11.1c2.1-6.6-.2-13.8-5.8-17.9c0 0 0 0 0 0s0 0 0 0l-.2-.2c-.2-.2-.6-.4-1.1-.8c-1-.8-2.5-2-4.3-3.7c-3.6-3.3-8.5-8.1-13.3-14.3c-5.5-7-10.7-15.4-14.2-24.7c24.9-29 39.6-64.7 39.6-103.4c0-92.8-84.9-168.9-192.6-175.5c.4 5.1 .6 10.3 .6 15.5z"]};var Wa={prefix:"fas",iconName:"user-check",icon:[640,512,[],"f4fc","M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM625 177L497 305c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L591 143c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"]};var Za={prefix:"fas",iconName:"table",icon:[512,512,[],"f0ce","M64 256l0-96 160 0 0 96L64 256zm0 64l160 0 0 96L64 416l0-96zm224 96l0-96 160 0 0 96-160 0zM448 256l-160 0 0-96 160 0 0 96zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"]};var Ya={prefix:"fas",iconName:"bars",icon:[448,512,["navicon"],"f0c9","M0 96C0 78.3 14.3 64 32 64l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 288c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32L32 448c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z"]};var Rx={prefix:"fas",iconName:"heart-crack",icon:[512,512,[128148,"heart-broken"],"f7a9","M119.4 44.1c23.3-3.9 46.8-1.9 68.6 5.3l49.8 77.5-75.4 75.4c-1.5 1.5-2.4 3.6-2.3 5.8s1 4.2 2.6 5.7l112 104c2.9 2.7 7.4 2.9 10.5 .3s3.8-7 1.7-10.4l-60.4-98.1 90.7-75.6c2.6-2.1 3.5-5.7 2.4-8.8L296.8 61.8c28.5-16.7 62.4-23.2 95.7-17.6C461.5 55.6 512 115.2 512 185.1l0 5.8c0 41.5-17.2 81.2-47.6 109.5L283.7 469.1c-7.5 7-17.4 10.9-27.7 10.9s-20.2-3.9-27.7-10.9L47.6 300.4C17.2 272.1 0 232.4 0 190.9l0-5.8c0-69.9 50.5-129.5 119.4-141z"]},Qa=Rx;var Ka={prefix:"fas",iconName:"lightbulb",icon:[384,512,[128161],"f0eb","M272 384c9.6-31.9 29.5-59.1 49.2-86.2c0 0 0 0 0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4c0 0 0 0 0 0c19.8 27.1 39.7 54.4 49.2 86.2l160 0zM192 512c44.2 0 80-35.8 80-80l0-16-160 0 0 16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z"]};var Hc={prefix:"fas",iconName:"circle-exclamation",icon:[512,512,["exclamation-circle"],"f06a","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c13.3 0 24 10.7 24 24l0 112c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-112c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},Xa=Hc;var Ja={prefix:"fas",iconName:"flag",icon:[448,512,[127988,61725],"f024","M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32L0 64 0 368 0 480c0 17.7 14.3 32 32 32s32-14.3 32-32l0-128 64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30l0-247.7c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48l0-16z"]};var k4={prefix:"fas",iconName:"list",icon:[512,512,["list-squares"],"f03a","M40 48C26.7 48 16 58.7 16 72l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24L40 48zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM16 232l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0c-13.3 0-24 10.7-24 24zM40 368c-13.3 0-24 10.7-24 24l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0z"]};var el={prefix:"fas",iconName:"lock",icon:[448,512,[128274],"f023","M144 144l0 48 160 0 0-48c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192l0-48C80 64.5 144.5 0 224 0s144 64.5 144 144l0 48 16 0c35.3 0 64 28.7 64 64l0 192c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 256c0-35.3 28.7-64 64-64l16 0z"]};var Hx={prefix:"fas",iconName:"pen-to-square",icon:[512,512,["edit"],"f044","M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160L0 416c0 53 43 96 96 96l256 0c53 0 96-43 96-96l0-96c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7-14.3 32-32 32L96 448c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 64z"]},tl=Hx;var cl={prefix:"fas",iconName:"hourglass-half",icon:[384,512,["hourglass-2"],"f252","M32 0C14.3 0 0 14.3 0 32S14.3 64 32 64l0 11c0 42.4 16.9 83.1 46.9 113.1L146.7 256 78.9 323.9C48.9 353.9 32 394.6 32 437l0 11c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 256 0 32 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l0-11c0-42.4-16.9-83.1-46.9-113.1L237.3 256l67.9-67.9c30-30 46.9-70.7 46.9-113.1l0-11c17.7 0 32-14.3 32-32s-14.3-32-32-32L320 0 64 0 32 0zM96 75l0-11 192 0 0 11c0 19-5.6 37.4-16 53L112 128c-10.3-15.6-16-34-16-53zm16 309c3.5-5.3 7.6-10.3 12.1-14.9L192 301.3l67.9 67.9c4.6 4.6 8.6 9.6 12.1 14.9L112 384z"]};var nl={prefix:"fas",iconName:"users",icon:[640,512,[],"f0c0","M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192l42.7 0c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0L21.3 320C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7l42.7 0C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3l-213.3 0zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352l117.3 0C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7l-330.7 0c-14.7 0-26.7-11.9-26.7-26.7z"]};var jx={prefix:"fas",iconName:"hand",icon:[512,512,[129306,9995,"hand-paper"],"f256","M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 208c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-176c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 272c0 1.5 0 3.1 .1 4.6L67.6 283c-16-15.2-41.3-14.6-56.6 1.4s-14.6 41.3 1.4 56.6L124.8 448c43.1 41.1 100.4 64 160 64l19.2 0c97.2 0 176-78.8 176-176l0-208c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-176c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 176c0 8.8-7.2 16-16 16s-16-7.2-16-16l0-208z"]},sl=jx;var ol={prefix:"fas",iconName:"plug",icon:[384,512,[128268],"f1e6","M96 0C78.3 0 64 14.3 64 32l0 96 64 0 0-96c0-17.7-14.3-32-32-32zM288 0c-17.7 0-32 14.3-32 32l0 96 64 0 0-96c0-17.7-14.3-32-32-32zM32 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l0 32c0 77.4 55 142 128 156.8l0 67.2c0 17.7 14.3 32 32 32s32-14.3 32-32l0-67.2C297 398 352 333.4 352 256l0-32c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 160z"]},il={prefix:"fas",iconName:"chevron-up",icon:[512,512,[],"f077","M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"]};var rl={prefix:"fas",iconName:"angle-right",icon:[320,512,[8250],"f105","M278.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-160 160c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L210.7 256 73.4 118.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l160 160z"]};var al={prefix:"fas",iconName:"folder",icon:[512,512,[128193,128447,61716,"folder-blank"],"f07b","M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"]};var ll={prefix:"fas",iconName:"user",icon:[448,512,[128100,62144],"f007","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z"]};var fl={prefix:"fas",iconName:"key",icon:[512,512,[128273],"f084","M336 352c97.2 0 176-78.8 176-176S433.2 0 336 0S160 78.8 160 176c0 18.7 2.9 36.8 8.3 53.7L7 391c-4.5 4.5-7 10.6-7 17l0 80c0 13.3 10.7 24 24 24l80 0c13.3 0 24-10.7 24-24l0-40 40 0c13.3 0 24-10.7 24-24l0-40 40 0c6.4 0 12.5-2.5 17-7l33.3-33.3c16.9 5.4 35 8.3 53.7 8.3zM376 96a40 40 0 1 1 0 80 40 40 0 1 1 0-80z"]};var ul={prefix:"fas",iconName:"globe",icon:[512,512,[127760],"f0ac","M352 256c0 22.2-1.2 43.6-3.3 64l-185.3 0c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64l185.3 0c2.2 20.4 3.3 41.8 3.3 64zm28.8-64l123.1 0c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64l-123.1 0c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32l-116.7 0c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0l-176.6 0c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0L18.6 160C48.6 85.9 112.2 29.1 190.6 8.4C165.1 42.6 145.3 96.1 135.3 160zM8.1 192l123.1 0c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64L8.1 320C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64zM194.7 446.6c-11.6-26-20.9-58.2-27-94.6l176.6 0c-6.1 36.4-15.5 68.6-27 94.6c-10.5 23.6-22.2 40.7-33.5 51.5C272.6 508.8 263.3 512 256 512s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6C112.2 482.9 48.6 426.1 18.6 352l116.7 0zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6l116.7 0z"]};var dl={prefix:"fas",iconName:"money-bill-wave",icon:[576,512,[],"f53a","M0 112.5L0 422.3c0 18 10.1 35 27 41.3c87 32.5 174 10.3 261-11.9c79.8-20.3 159.6-40.7 239.3-18.9c23 6.3 48.7-9.5 48.7-33.4l0-309.9c0-18-10.1-35-27-41.3C462 15.9 375 38.1 288 60.3C208.2 80.6 128.4 100.9 48.7 79.1C25.6 72.8 0 88.6 0 112.5zM288 352c-44.2 0-80-43-80-96s35.8-96 80-96s80 43 80 96s-35.8 96-80 96zM64 352c35.3 0 64 28.7 64 64l-64 0 0-64zm64-208c0 35.3-28.7 64-64 64l0-64 64 0zM512 304l0 64-64 0c0-35.3 28.7-64 64-64zM448 96l64 0 0 64c-35.3 0-64-28.7-64-64z"]},hl={prefix:"fas",iconName:"chart-area",icon:[512,512,["area-chart"],"f1fe","M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64L0 400c0 44.2 35.8 80 80 80l400 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 416c-8.8 0-16-7.2-16-16L64 64zm96 288l288 0c17.7 0 32-14.3 32-32l0-68.2c0-7.6-2.7-15-7.7-20.8l-65.8-76.8c-12.1-14.2-33.7-15-46.9-1.8l-21 21c-10 10-26.4 9.2-35.4-1.6l-39.2-47c-12.6-15.1-35.7-15.4-48.7-.6L135.9 215c-5.1 5.8-7.9 13.3-7.9 21.1l0 84c0 17.7 14.3 32 32 32z"]};var ml={prefix:"fas",iconName:"ban",icon:[512,512,[128683,"cancel"],"f05e","M367.2 412.5L99.5 144.8C77.1 176.1 64 214.5 64 256c0 106 86 192 192 192c41.5 0 79.9-13.1 111.2-35.5zm45.3-45.3C434.9 335.9 448 297.5 448 256c0-106-86-192-192-192c-41.5 0-79.9 13.1-111.2 35.5L412.5 367.2zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"]};var pl={prefix:"fas",iconName:"star",icon:[576,512,[11088,61446],"f005","M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"]};var Bx={prefix:"fas",iconName:"arrow-pointer",icon:[320,512,["mouse-pointer"],"f245","M0 55.2L0 426c0 12.2 9.9 22 22 22c6.3 0 12.4-2.7 16.6-7.5L121.2 346l58.1 116.3c7.9 15.8 27.1 22.2 42.9 14.3s22.2-27.1 14.3-42.9L179.8 320l118.1 0c12.2 0 22.1-9.9 22.1-22.1c0-6.3-2.7-12.3-7.4-16.5L38.6 37.9C34.3 34.1 28.9 32 23.2 32C10.4 32 0 42.4 0 55.2z"]},gl=Bx;var Ml={prefix:"fas",iconName:"server",icon:[512,512,[],"f233","M64 32C28.7 32 0 60.7 0 96l0 64c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-64c0-35.3-28.7-64-64-64L64 32zm280 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm48 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zM64 288c-35.3 0-64 28.7-64 64l0 64c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-64c0-35.3-28.7-64-64-64L64 288zm280 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm56 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"]};var Ux={prefix:"fas",iconName:"right-to-bracket",icon:[512,512,["sign-in-alt"],"f2f6","M217.9 105.9L340.7 228.7c7.2 7.2 11.3 17.1 11.3 27.3s-4.1 20.1-11.3 27.3L217.9 406.1c-6.4 6.4-15 9.9-24 9.9c-18.7 0-33.9-15.2-33.9-33.9l0-62.1L32 320c-17.7 0-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32l128 0 0-62.1c0-18.7 15.2-33.9 33.9-33.9c9 0 17.6 3.6 24 9.9zM352 416l64 0c17.7 0 32-14.3 32-32l0-256c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32-14.3-32-32s14.3-32 32-32l64 0c53 0 96 43 96 96l0 256c0 53-43 96-96 96l-64 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]},Cl=Ux;var $x={prefix:"fas",iconName:"user-group",icon:[640,512,[128101,"user-friends"],"f500","M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM609.3 512l-137.8 0c5.4-9.4 8.6-20.3 8.6-32l0-8c0-60.7-27.1-115.2-69.8-151.8c2.4-.1 4.7-.2 7.1-.2l61.4 0C567.8 320 640 392.2 640 481.3c0 17-13.8 30.7-30.7 30.7zM432 256c-31 0-59-12.6-79.3-32.9C372.4 196.5 384 163.6 384 128c0-26.8-6.6-52.1-18.3-74.3C384.3 40.1 407.2 32 432 32c61.9 0 112 50.1 112 112s-50.1 112-112 112z"]},zl=$x;var Ll={prefix:"fas",iconName:"headset",icon:[512,512,[],"f590","M256 48C141.1 48 48 141.1 48 256l0 40c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-40C0 114.6 114.6 0 256 0S512 114.6 512 256l0 144.1c0 48.6-39.4 88-88.1 88L313.6 488c-8.3 14.3-23.8 24-41.6 24l-32 0c-26.5 0-48-21.5-48-48s21.5-48 48-48l32 0c17.8 0 33.3 9.7 41.6 24l110.4 .1c22.1 0 40-17.9 40-40L464 256c0-114.9-93.1-208-208-208zM144 208l16 0c17.7 0 32 14.3 32 32l0 112c0 17.7-14.3 32-32 32l-16 0c-35.3 0-64-28.7-64-64l0-48c0-35.3 28.7-64 64-64zm224 0c35.3 0 64 28.7 64 64l0 48c0 35.3-28.7 64-64 64l-16 0c-17.7 0-32-14.3-32-32l0-112c0-17.7 14.3-32 32-32l16 0z"]};var vl={prefix:"fas",iconName:"user-pen",icon:[640,512,["user-edit"],"f4ff","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l293.1 0c-3.1-8.8-3.7-18.4-1.4-27.8l15-60.1c2.8-11.3 8.6-21.5 16.8-29.7l40.3-40.3c-32.1-31-75.7-50.1-123.9-50.1l-91.4 0zm435.5-68.3c-15.6-15.6-40.9-15.6-56.6 0l-29.4 29.4 71 71 29.4-29.4c15.6-15.6 15.6-40.9 0-56.6l-14.4-14.4zM375.9 417c-4.1 4.1-7 9.2-8.4 14.9l-15 60.1c-1.4 5.5 .2 11.2 4.2 15.2s9.7 5.6 15.2 4.2l60.1-15c5.6-1.4 10.8-4.3 14.9-8.4L576.1 358.7l-71-71L375.9 417z"]};var yl={prefix:"fas",iconName:"gift",icon:[512,512,[127873],"f06b","M190.5 68.8L225.3 128l-1.3 0-72 0c-22.1 0-40-17.9-40-40s17.9-40 40-40l2.2 0c14.9 0 28.8 7.9 36.3 20.8zM64 88c0 14.4 3.5 28 9.6 40L32 128c-17.7 0-32 14.3-32 32l0 64c0 17.7 14.3 32 32 32l448 0c17.7 0 32-14.3 32-32l0-64c0-17.7-14.3-32-32-32l-41.6 0c6.1-12 9.6-25.6 9.6-40c0-48.6-39.4-88-88-88l-2.2 0c-31.9 0-61.5 16.9-77.7 44.4L256 85.5l-24.1-41C215.7 16.9 186.1 0 154.2 0L152 0C103.4 0 64 39.4 64 88zm336 0c0 22.1-17.9 40-40 40l-72 0-1.3 0 34.8-59.2C329.1 55.9 342.9 48 357.8 48l2.2 0c22.1 0 40 17.9 40 40zM32 288l0 176c0 26.5 21.5 48 48 48l144 0 0-224L32 288zM288 512l144 0c26.5 0 48-21.5 48-48l0-176-192 0 0 224z"]};var bl={prefix:"fas",iconName:"chart-bar",icon:[512,512,["bar-chart"],"f080","M32 32c17.7 0 32 14.3 32 32l0 336c0 8.8 7.2 16 16 16l400 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L80 480c-44.2 0-80-35.8-80-80L0 64C0 46.3 14.3 32 32 32zm96 96c0-17.7 14.3-32 32-32l192 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-192 0c-17.7 0-32-14.3-32-32zm32 64l128 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32zm0 96l256 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]};var xl={prefix:"fas",iconName:"image",icon:[512,512,[],"f03e","M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM323.8 202.5c-4.5-6.6-11.9-10.5-19.8-10.5s-15.4 3.9-19.8 10.5l-87 127.6L170.7 297c-4.6-5.7-11.5-9-18.7-9s-14.2 3.3-18.7 9l-64 80c-5.8 7.2-6.9 17.1-2.9 25.4s12.4 13.6 21.6 13.6l96 0 32 0 208 0c8.9 0 17.1-4.9 21.2-12.8s3.6-17.4-1.4-24.7l-120-176zM112 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"]};var qx={prefix:"fas",iconName:"table-columns",icon:[512,512,["columns"],"f0db","M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zm64 64l0 256 160 0 0-256L64 160zm384 0l-160 0 0 256 160 0 0-256z"]},Nl=qx;var Gx={prefix:"fas",iconName:"circle-play",icon:[512,512,[61469,"play-circle"],"f144","M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c-7.6 4.2-12.3 12.3-12.3 20.9l0 176c0 8.7 4.7 16.7 12.3 20.9s16.8 4.1 24.3-.5l144-88c7.1-4.4 11.5-12.1 11.5-20.5s-4.4-16.1-11.5-20.5l-144-88c-7.4-4.5-16.7-4.7-24.3-.5z"]},wl=Gx;var Wx={prefix:"fas",iconName:"circle-check",icon:[512,512,[61533,"check-circle"],"f058","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"]},Dl=Wx;var Sl={prefix:"fas",iconName:"file-import",icon:[512,512,["arrow-right-to-file"],"f56f","M128 64c0-35.3 28.7-64 64-64L352 0l0 128c0 17.7 14.3 32 32 32l128 0 0 288c0 35.3-28.7 64-64 64l-256 0c-35.3 0-64-28.7-64-64l0-112 174.1 0-39 39c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l39 39L128 288l0-224zm0 224l0 48L24 336c-13.3 0-24-10.7-24-24s10.7-24 24-24l104 0zM512 128l-128 0L384 0 512 128z"]};var El={prefix:"fas",iconName:"palette",icon:[512,512,[127912],"f53f","M512 256c0 .9 0 1.8 0 2.7c-.4 36.5-33.6 61.3-70.1 61.3L344 320c-26.5 0-48 21.5-48 48c0 3.4 .4 6.7 1 9.9c2.1 10.2 6.5 20 10.8 29.9c6.1 13.8 12.1 27.5 12.1 42c0 31.8-21.6 60.7-53.4 62c-3.5 .1-7 .2-10.6 .2C114.6 512 0 397.4 0 256S114.6 0 256 0S512 114.6 512 256zM128 288a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm0-96a32 32 0 1 0 0-64 32 32 0 1 0 0 64zM288 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm96 96a32 32 0 1 0 0-64 32 32 0 1 0 0 64z"]};var Zx={prefix:"fas",iconName:"arrow-down-wide-short",icon:[576,512,["sort-amount-asc","sort-amount-down"],"f160","M151.6 469.6C145.5 476.2 137 480 128 480s-17.5-3.8-23.6-10.4l-88-96c-11.9-13-11.1-33.3 2-45.2s33.3-11.1 45.2 2L96 365.7 96 64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 301.7 32.4-35.4c11.9-13 32.2-13.9 45.2-2s13.9 32.2 2 45.2l-88 96zM320 480c-17.7 0-32-14.3-32-32s14.3-32 32-32l32 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-32 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-96 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l160 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-160 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l224 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L320 96z"]};var Al=Zx;var Il={prefix:"fas",iconName:"envelope-open",icon:[512,512,[62135],"f2b6","M64 208.1L256 65.9 448 208.1l0 47.4L289.5 373c-9.7 7.2-21.4 11-33.5 11s-23.8-3.9-33.5-11L64 255.5l0-47.4zM256 0c-12.1 0-23.8 3.9-33.5 11L25.9 156.7C9.6 168.8 0 187.8 0 208.1L0 448c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-239.9c0-20.3-9.6-39.4-25.9-51.4L289.5 11C279.8 3.9 268.1 0 256 0z"]};var Yx={prefix:"fas",iconName:"arrows-rotate",icon:[512,512,[128472,"refresh","sync"],"f021","M105.1 202.6c7.7-21.8 20.2-42.3 37.8-59.8c62.5-62.5 163.8-62.5 226.3 0L386.3 160 352 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l111.5 0c0 0 0 0 0 0l.4 0c17.7 0 32-14.3 32-32l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 35.2L414.4 97.6c-87.5-87.5-229.3-87.5-316.8 0C73.2 122 55.6 150.7 44.8 181.4c-5.9 16.7 2.9 34.9 19.5 40.8s34.9-2.9 40.8-19.5zM39 289.3c-5 1.5-9.8 4.2-13.7 8.2c-4 4-6.7 8.8-8.1 14c-.3 1.2-.6 2.5-.8 3.8c-.3 1.7-.4 3.4-.4 5.1L16 432c0 17.7 14.3 32 32 32s32-14.3 32-32l0-35.1 17.6 17.5c0 0 0 0 0 0c87.5 87.4 229.3 87.4 316.7 0c24.4-24.4 42.1-53.1 52.9-83.8c5.9-16.7-2.9-34.9-19.5-40.8s-34.9 2.9-40.8 19.5c-7.7 21.8-20.2 42.3-37.8 59.8c-62.5 62.5-163.8 62.5-226.3 0l-.1-.1L125.6 352l34.4 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L48.4 288c-1.6 0-3.2 .1-4.8 .3s-3.1 .5-4.6 1z"]},Tl=Yx;var Qx={prefix:"fas",iconName:"shield-halved",icon:[512,512,["shield-alt"],"f3ed","M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8l0 378.1C394 378 431.1 230.1 432 141.4L256 66.8s0 0 0 0z"]},_l=Qx;var Kx={prefix:"fas",iconName:"rectangle-ad",icon:[576,512,["ad"],"f641","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l448 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM229.5 173.3l72 144c5.9 11.9 1.1 26.3-10.7 32.2s-26.3 1.1-32.2-10.7L253.2 328l-90.3 0-5.4 10.7c-5.9 11.9-20.3 16.7-32.2 10.7s-16.7-20.3-10.7-32.2l72-144c4.1-8.1 12.4-13.3 21.5-13.3s17.4 5.1 21.5 13.3zM208 237.7L186.8 280l42.3 0L208 237.7zM392 256a24 24 0 1 0 0 48 24 24 0 1 0 0-48zm24-43.9l0-28.1c0-13.3 10.7-24 24-24s24 10.7 24 24l0 96 0 48c0 13.3-10.7 24-24 24c-6.6 0-12.6-2.7-17-7c-9.4 4.5-19.9 7-31 7c-39.8 0-72-32.2-72-72s32.2-72 72-72c8.4 0 16.5 1.4 24 4.1z"]},kl=Kx;var Fl={prefix:"fas",iconName:"sort",icon:[320,512,["unsorted"],"f0dc","M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8L32 224c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8l256 0c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z"]};var Pl={prefix:"fas",iconName:"language",icon:[640,512,[],"f1ab","M0 128C0 92.7 28.7 64 64 64l192 0 48 0 16 0 256 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64l-256 0-16 0-48 0L64 448c-35.3 0-64-28.7-64-64L0 128zm320 0l0 256 256 0 0-256-256 0zM178.3 175.9c-3.2-7.2-10.4-11.9-18.3-11.9s-15.1 4.7-18.3 11.9l-64 144c-4.5 10.1 .1 21.9 10.2 26.4s21.9-.1 26.4-10.2l8.9-20.1 73.6 0 8.9 20.1c4.5 10.1 16.3 14.6 26.4 10.2s14.6-16.3 10.2-26.4l-64-144zM160 233.2L179 276l-38 0 19-42.8zM448 164c11 0 20 9 20 20l0 4 44 0 16 0c11 0 20 9 20 20s-9 20-20 20l-2 0-1.6 4.5c-8.9 24.4-22.4 46.6-39.6 65.4c.9 .6 1.8 1.1 2.7 1.6l18.9 11.3c9.5 5.7 12.5 18 6.9 27.4s-18 12.5-27.4 6.9l-18.9-11.3c-4.5-2.7-8.8-5.5-13.1-8.5c-10.6 7.5-21.9 14-34 19.4l-3.6 1.6c-10.1 4.5-21.9-.1-26.4-10.2s.1-21.9 10.2-26.4l3.6-1.6c6.4-2.9 12.6-6.1 18.5-9.8l-12.2-12.2c-7.8-7.8-7.8-20.5 0-28.3s20.5-7.8 28.3 0l14.6 14.6 .5 .5c12.4-13.1 22.5-28.3 29.8-45L448 228l-72 0c-11 0-20-9-20-20s9-20 20-20l52 0 0-4c0-11 9-20 20-20z"]};var Vl={prefix:"fas",iconName:"filter",icon:[512,512,[],"f0b0","M3.9 54.9C10.5 40.9 24.5 32 40 32l432 0c15.5 0 29.5 8.9 36.1 22.9s4.6 30.5-5.2 42.5L320 320.9 320 448c0 12.1-6.8 23.2-17.7 28.6s-23.8 4.3-33.5-3l-64-48c-8.1-6-12.8-15.5-12.8-25.6l0-79.1L9 97.3C-.7 85.4-2.8 68.8 3.9 54.9z"]};var Xx={prefix:"fas",iconName:"up-down-left-right",icon:[512,512,["arrows-alt"],"f0b2","M278.6 9.4c-12.5-12.5-32.8-12.5-45.3 0l-64 64c-9.2 9.2-11.9 22.9-6.9 34.9s16.6 19.8 29.6 19.8l32 0 0 96-96 0 0-32c0-12.9-7.8-24.6-19.8-29.6s-25.7-2.2-34.9 6.9l-64 64c-12.5 12.5-12.5 32.8 0 45.3l64 64c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6l0-32 96 0 0 96-32 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l64 64c12.5 12.5 32.8 12.5 45.3 0l64-64c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8l-32 0 0-96 96 0 0 32c0 12.9 7.8 24.6 19.8 29.6s25.7 2.2 34.9-6.9l64-64c12.5-12.5 12.5-32.8 0-45.3l-64-64c-9.2-9.2-22.9-11.9-34.9-6.9s-19.8 16.6-19.8 29.6l0 32-96 0 0-96 32 0c12.9 0 24.6-7.8 29.6-19.8s2.2-25.7-6.9-34.9l-64-64z"]},Ol=Xx;var Rl={prefix:"fas",iconName:"puzzle-piece",icon:[512,512,[129513],"f12e","M192 104.8c0-9.2-5.8-17.3-13.2-22.8C167.2 73.3 160 61.3 160 48c0-26.5 28.7-48 64-48s64 21.5 64 48c0 13.3-7.2 25.3-18.8 34c-7.4 5.5-13.2 13.6-13.2 22.8c0 12.8 10.4 23.2 23.2 23.2l56.8 0c26.5 0 48 21.5 48 48l0 56.8c0 12.8 10.4 23.2 23.2 23.2c9.2 0 17.3-5.8 22.8-13.2c8.7-11.6 20.7-18.8 34-18.8c26.5 0 48 28.7 48 64s-21.5 64-48 64c-13.3 0-25.3-7.2-34-18.8c-5.5-7.4-13.6-13.2-22.8-13.2c-12.8 0-23.2 10.4-23.2 23.2L384 464c0 26.5-21.5 48-48 48l-56.8 0c-12.8 0-23.2-10.4-23.2-23.2c0-9.2 5.8-17.3 13.2-22.8c11.6-8.7 18.8-20.7 18.8-34c0-26.5-28.7-48-64-48s-64 21.5-64 48c0 13.3 7.2 25.3 18.8 34c7.4 5.5 13.2 13.6 13.2 22.8c0 12.8-10.4 23.2-23.2 23.2L48 512c-26.5 0-48-21.5-48-48L0 343.2C0 330.4 10.4 320 23.2 320c9.2 0 17.3 5.8 22.8 13.2C54.7 344.8 66.7 352 80 352c26.5 0 48-28.7 48-64s-21.5-64-48-64c-13.3 0-25.3 7.2-34 18.8C40.5 250.2 32.4 256 23.2 256C10.4 256 0 245.6 0 232.8L0 176c0-26.5 21.5-48 48-48l120.8 0c12.8 0 23.2-10.4 23.2-23.2z"]};var Hl={prefix:"fas",iconName:"code",icon:[640,512,[],"f121","M392.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm80.6 120.1c-12.5 12.5-12.5 32.8 0 45.3L562.7 256l-89.4 89.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l112-112c12.5-12.5 12.5-32.8 0-45.3l-112-112c-12.5-12.5-32.8-12.5-45.3 0zm-306.7 0c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3l112 112c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256l89.4-89.4c12.5-12.5 12.5-32.8 0-45.3z"]};var jl={prefix:"fas",iconName:"chart-pie",icon:[576,512,["pie-chart"],"f200","M304 240l0-223.4c0-9 7-16.6 16-16.6C443.7 0 544 100.3 544 224c0 9-7.6 16-16.6 16L304 240zM32 272C32 150.7 122.1 50.3 239 34.3c9.2-1.3 17 6.1 17 15.4L256 288 412.5 444.5c6.7 6.7 6.2 17.7-1.5 23.1C371.8 495.6 323.8 512 272 512C139.5 512 32 404.6 32 272zm526.4 16c9.3 0 16.6 7.8 15.4 17c-7.7 55.9-34.6 105.6-73.9 142.3c-6 5.6-15.4 5.2-21.2-.7L320 288l238.4 0z"]};var Bl={prefix:"fas",iconName:"chart-line",icon:[512,512,["line-chart"],"f201","M64 64c0-17.7-14.3-32-32-32S0 46.3 0 64L0 400c0 44.2 35.8 80 80 80l400 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 416c-8.8 0-16-7.2-16-16L64 64zm406.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L320 210.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0l-112 112c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L240 221.3l57.4 57.4c12.5 12.5 32.8 12.5 45.3 0l128-128z"]};var Ul={prefix:"fas",iconName:"arrow-right",icon:[448,512,[8594],"f061","M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"]};var Jx={prefix:"fas",iconName:"screwdriver-wrench",icon:[512,512,["tools"],"f7d9","M78.6 5C69.1-2.4 55.6-1.5 47 7L7 47c-8.5 8.5-9.4 22-2.1 31.6l80 104c4.5 5.9 11.6 9.4 19 9.4l54.1 0 109 109c-14.7 29-10 65.4 14.3 89.6l112 112c12.5 12.5 32.8 12.5 45.3 0l64-64c12.5-12.5 12.5-32.8 0-45.3l-112-112c-24.2-24.2-60.6-29-89.6-14.3l-109-109 0-54.1c0-7.5-3.5-14.5-9.4-19L78.6 5zM19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L233.7 374.3c-7.8-20.9-9-43.6-3.6-65.1l-61.7-61.7L19.9 396.1zM512 144c0-10.5-1.1-20.7-3.2-30.5c-2.4-11.2-16.1-14.1-24.2-6l-63.9 63.9c-3 3-7.1 4.7-11.3 4.7L352 176c-8.8 0-16-7.2-16-16l0-57.4c0-4.2 1.7-8.3 4.7-11.3l63.9-63.9c8.1-8.1 5.2-21.8-6-24.2C388.7 1.1 378.5 0 368 0C288.5 0 224 64.5 224 144l0 .8 85.3 85.3c36-9.1 75.8 .5 104 28.7L429 274.5c49-23 83-72.8 83-130.5zM56 432a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"]},$l=Jx;var ql={prefix:"fas",iconName:"heart",icon:[512,512,[128153,128154,128155,128156,128420,129293,129294,129505,9829,10084,61578],"f004","M47.6 300.4L228.3 469.1c7.5 7 17.4 10.9 27.7 10.9s20.2-3.9 27.7-10.9L464.4 300.4c30.4-28.3 47.6-68 47.6-109.5v-5.8c0-69.9-50.5-129.5-119.4-141C347 36.5 300.6 51.4 268 84L256 96 244 84c-32.6-32.6-79-47.5-124.6-39.9C50.5 55.6 0 115.2 0 185.1v5.8c0 41.5 17.2 81.2 47.6 109.5z"]};var Gl={prefix:"fas",iconName:"cube",icon:[512,512,[],"f1b2","M234.5 5.7c13.9-5 29.1-5 43.1 0l192 68.6C495 83.4 512 107.5 512 134.6l0 242.9c0 27-17 51.2-42.5 60.3l-192 68.6c-13.9 5-29.1 5-43.1 0l-192-68.6C17 428.6 0 404.5 0 377.4L0 134.6c0-27 17-51.2 42.5-60.3l192-68.6zM256 66L82.3 128 256 190l173.7-62L256 66zm32 368.6l160-57.1 0-188L288 246.6l0 188z"]};var Wl={prefix:"fas",iconName:"circle",icon:[512,512,[128308,128309,128992,128993,128994,128995,128996,9679,9898,9899,11044,61708,61915],"f111","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512z"]};var Zl={prefix:"fas",iconName:"wallet",icon:[512,512,[],"f555","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-224c0-35.3-28.7-64-64-64L80 128c-8.8 0-16-7.2-16-16s7.2-16 16-16l368 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L64 32zM416 272a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]};var Yl={prefix:"fas",iconName:"wrench",icon:[512,512,[128295],"f0ad","M352 320c88.4 0 160-71.6 160-160c0-15.3-2.2-30.1-6.2-44.2c-3.1-10.8-16.4-13.2-24.3-5.3l-76.8 76.8c-3 3-7.1 4.7-11.3 4.7L336 192c-8.8 0-16-7.2-16-16l0-57.4c0-4.2 1.7-8.3 4.7-11.3l76.8-76.8c7.9-7.9 5.4-21.2-5.3-24.3C382.1 2.2 367.3 0 352 0C263.6 0 192 71.6 192 160c0 19.1 3.4 37.5 9.5 54.5L19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L297.5 310.5c17 6.2 35.4 9.5 54.5 9.5zM80 408a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"]};var eN={prefix:"fas",iconName:"circle-question",icon:[512,512,[62108,"question-circle"],"f059","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM169.8 165.3c7.9-22.3 29.1-37.3 52.8-37.3l58.3 0c34.9 0 63.1 28.3 63.1 63.1c0 22.6-12.1 43.5-31.7 54.8L280 264.4c-.2 13-10.9 23.6-24 23.6c-13.3 0-24-10.7-24-24l0-13.5c0-8.6 4.6-16.5 12.1-20.8l44.3-25.4c4.7-2.7 7.6-7.7 7.6-13.1c0-8.4-6.8-15.1-15.1-15.1l-58.3 0c-3.4 0-6.4 2.1-7.5 5.3l-.4 1.2c-4.4 12.5-18.2 19-30.6 14.6s-19-18.2-14.6-30.6l.4-1.2zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},Ql=eN;var Kl={prefix:"fas",iconName:"tags",icon:[512,512,[],"f02c","M345 39.1L472.8 168.4c52.4 53 52.4 138.2 0 191.2L360.8 472.9c-9.3 9.4-24.5 9.5-33.9 .2s-9.5-24.5-.2-33.9L438.6 325.9c33.9-34.3 33.9-89.4 0-123.7L310.9 72.9c-9.3-9.4-9.2-24.6 .2-33.9s24.6-9.2 33.9 .2zM0 229.5L0 80C0 53.5 21.5 32 48 32l149.5 0c17 0 33.3 6.7 45.3 18.7l168 168c25 25 25 65.5 0 90.5L277.3 442.7c-25 25-65.5 25-90.5 0l-168-168C6.7 262.7 0 246.5 0 229.5zM144 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]};var Xl={prefix:"fas",iconName:"eye",icon:[576,512,[128065],"f06e","M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z"]};var Jl={prefix:"fas",iconName:"pen",icon:[512,512,[128394],"f304","M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z"]};var ef={prefix:"fas",iconName:"window-maximize",icon:[512,512,[128470],"f2d0","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM96 96l320 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L96 160c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]};var tN={prefix:"fas",iconName:"floppy-disk",icon:[448,512,[128190,128426,"save"],"f0c7","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-242.7c0-17-6.7-33.3-18.7-45.3L352 50.7C340 38.7 323.7 32 306.7 32L64 32zm0 96c0-17.7 14.3-32 32-32l192 0c17.7 0 32 14.3 32 32l0 64c0 17.7-14.3 32-32 32L96 224c-17.7 0-32-14.3-32-32l0-64zM224 288a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"]},tf=tN;var cf={prefix:"fas",iconName:"phone",icon:[512,512,[128222,128379],"f095","M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"]};var cN={prefix:"fas",iconName:"user-gear",icon:[640,512,["user-cog"],"f4fe","M224 0a128 128 0 1 1 0 256A128 128 0 1 1 224 0zM178.3 304l91.4 0c11.8 0 23.4 1.2 34.5 3.3c-2.1 18.5 7.4 35.6 21.8 44.8c-16.6 10.6-26.7 31.6-20 53.3c4 12.9 9.4 25.5 16.4 37.6s15.2 23.1 24.4 33c15.7 16.9 39.6 18.4 57.2 8.7l0 .9c0 9.2 2.7 18.5 7.9 26.3L29.7 512C13.3 512 0 498.7 0 482.3C0 383.8 79.8 304 178.3 304zM436 218.2c0-7 4.5-13.3 11.3-14.8c10.5-2.4 21.5-3.7 32.7-3.7s22.2 1.3 32.7 3.7c6.8 1.5 11.3 7.8 11.3 14.8l0 30.6c7.9 3.4 15.4 7.7 22.3 12.8l24.9-14.3c6.1-3.5 13.7-2.7 18.5 2.4c7.6 8.1 14.3 17.2 20.1 27.2s10.3 20.4 13.5 31c2.1 6.7-1.1 13.7-7.2 17.2l-25 14.4c.4 4 .7 8.1 .7 12.3s-.2 8.2-.7 12.3l25 14.4c6.1 3.5 9.2 10.5 7.2 17.2c-3.3 10.6-7.8 21-13.5 31s-12.5 19.1-20.1 27.2c-4.8 5.1-12.5 5.9-18.5 2.4l-24.9-14.3c-6.9 5.1-14.3 9.4-22.3 12.8l0 30.6c0 7-4.5 13.3-11.3 14.8c-10.5 2.4-21.5 3.7-32.7 3.7s-22.2-1.3-32.7-3.7c-6.8-1.5-11.3-7.8-11.3-14.8l0-30.5c-8-3.4-15.6-7.7-22.5-12.9l-24.7 14.3c-6.1 3.5-13.7 2.7-18.5-2.4c-7.6-8.1-14.3-17.2-20.1-27.2s-10.3-20.4-13.5-31c-2.1-6.7 1.1-13.7 7.2-17.2l24.8-14.3c-.4-4.1-.7-8.2-.7-12.4s.2-8.3 .7-12.4L343.8 325c-6.1-3.5-9.2-10.5-7.2-17.2c3.3-10.6 7.7-21 13.5-31s12.5-19.1 20.1-27.2c4.8-5.1 12.4-5.9 18.5-2.4l24.8 14.3c6.9-5.1 14.5-9.4 22.5-12.9l0-30.5zm92.1 133.5a48.1 48.1 0 1 0 -96.1 0 48.1 48.1 0 1 0 96.1 0z"]},nf=cN;var sf={prefix:"fas",iconName:"trash",icon:[448,512,[],"f1f8","M135.2 17.7L128 32 32 32C14.3 32 0 46.3 0 64S14.3 96 32 96l384 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0-7.2-14.3C307.4 6.8 296.3 0 284.2 0L163.8 0c-12.1 0-23.2 6.8-28.6 17.7zM416 128L32 128 53.2 467c1.6 25.3 22.6 45 47.9 45l245.8 0c25.3 0 46.3-19.7 47.9-45L416 128z"]};var of={prefix:"fas",iconName:"headphones",icon:[512,512,[127911],"f025","M256 80C149.9 80 62.4 159.4 49.6 262c9.4-3.8 19.6-6 30.4-6c26.5 0 48 21.5 48 48l0 128c0 26.5-21.5 48-48 48c-44.2 0-80-35.8-80-80l0-16 0-48 0-48C0 146.6 114.6 32 256 32s256 114.6 256 256l0 48 0 48 0 16c0 44.2-35.8 80-80 80c-26.5 0-48-21.5-48-48l0-128c0-26.5 21.5-48 48-48c10.8 0 21 2.1 30.4 6C449.6 159.4 362.1 80 256 80z"]};var rf={prefix:"fas",iconName:"arrow-left",icon:[448,512,[8592],"f060","M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"]};var af={prefix:"fas",iconName:"align-left",icon:[448,512,[],"f036","M288 64c0 17.7-14.3 32-32 32L32 96C14.3 96 0 81.7 0 64S14.3 32 32 32l224 0c17.7 0 32 14.3 32 32zm0 256c0 17.7-14.3 32-32 32L32 352c-17.7 0-32-14.3-32-32s14.3-32 32-32l224 0c17.7 0 32 14.3 32 32zM0 192c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 224c-17.7 0-32-14.3-32-32zM448 448c0 17.7-14.3 32-32 32L32 480c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z"]};var nN={prefix:"fas",iconName:"up-right-from-square",icon:[512,512,["external-link-alt"],"f35d","M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6l0-128c0-17.7-14.3-32-32-32L352 0zM80 32C35.8 32 0 67.8 0 112L0 432c0 44.2 35.8 80 80 80l320 0c44.2 0 80-35.8 80-80l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16L80 448c-8.8 0-16-7.2-16-16l0-320c0-8.8 7.2-16 16-16l112 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 32z"]},lf=nN;var sN={prefix:"fas",iconName:"table-cells-large",icon:[512,512,["th-large"],"f009","M448 96l0 128-160 0 0-128 160 0zm0 192l0 128-160 0 0-128 160 0zM224 224L64 224 64 96l160 0 0 128zM64 288l160 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"]},ff=sN;var uf={prefix:"fas",iconName:"tag",icon:[448,512,[127991],"f02b","M0 80L0 229.5c0 17 6.7 33.3 18.7 45.3l176 176c25 25 65.5 25 90.5 0L418.7 317.3c25-25 25-65.5 0-90.5l-176-176c-12-12-28.3-18.7-45.3-18.7L48 32C21.5 32 0 53.5 0 80zm112 32a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]},df={prefix:"fas",iconName:"comment",icon:[512,512,[128489,61669],"f075","M512 240c0 114.9-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6C73.6 471.1 44.7 480 16 480c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4c0 0 0 0 0 0s0 0 0 0s0 0 0 0c0 0 0 0 0 0l.3-.3c.3-.3 .7-.7 1.3-1.4c1.1-1.2 2.8-3.1 4.9-5.7c4.1-5 9.6-12.4 15.2-21.6c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208z"]};var hf={prefix:"fas",iconName:"envelope",icon:[512,512,[128386,9993,61443],"f0e0","M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"]};var oN={prefix:"fas",iconName:"circle-info",icon:[512,512,["info-circle"],"f05a","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336l24 0 0-64-24 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l48 0c13.3 0 24 10.7 24 24l0 88 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"]},mf=oN;var pf={prefix:"fas",iconName:"check-double",icon:[448,512,[],"f560","M342.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 178.7l-57.4-57.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l80 80c12.5 12.5 32.8 12.5 45.3 0l160-160zm96 128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 402.7 54.6 297.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l256-256z"]};var iN={prefix:"fas",iconName:"arrow-rotate-left",icon:[512,512,[8634,"arrow-left-rotate","arrow-rotate-back","arrow-rotate-backward","undo"],"f0e2","M125.7 160l50.3 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L48 224c-17.7 0-32-14.3-32-32L16 64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"]};var gf=iN;var rN={prefix:"fas",iconName:"gear",icon:[512,512,[9881,"cog"],"f013","M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"]},Mf=rN;var jc={prefix:"fas",iconName:"cart-shopping",icon:[576,512,[128722,"shopping-cart"],"f07a","M0 24C0 10.7 10.7 0 24 0L69.5 0c22 0 41.5 12.8 50.6 32l411 0c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3l-288.5 0 5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5L488 336c13.3 0 24 10.7 24 24s-10.7 24-24 24l-288.3 0c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5L24 48C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"]},Cf=jc;var zf={prefix:"fas",iconName:"grip-vertical",icon:[320,512,[],"f58e","M40 352l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40zm192 0l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40zM40 320c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0zM232 192l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40zM40 160c-22.1 0-40-17.9-40-40L0 72C0 49.9 17.9 32 40 32l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0zM232 32l48 0c22.1 0 40 17.9 40 40l0 48c0 22.1-17.9 40-40 40l-48 0c-22.1 0-40-17.9-40-40l0-48c0-22.1 17.9-40 40-40z"]};var Lf={prefix:"fas",iconName:"clock",icon:[512,512,[128339,"clock-four"],"f017","M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120l0 136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2 280 120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"]};var vf={prefix:"fas",iconName:"coins",icon:[512,512,[],"f51e","M512 80c0 18-14.3 34.6-38.4 48c-29.1 16.1-72.5 27.5-122.3 30.9c-3.7-1.8-7.4-3.5-11.3-5C300.6 137.4 248.2 128 192 128c-8.3 0-16.4 .2-24.5 .6l-1.1-.6C142.3 114.6 128 98 128 80c0-44.2 86-80 192-80S512 35.8 512 80zM160.7 161.1c10.2-.7 20.7-1.1 31.3-1.1c62.2 0 117.4 12.3 152.5 31.4C369.3 204.9 384 221.7 384 240c0 4-.7 7.9-2.1 11.7c-4.6 13.2-17 25.3-35 35.5c0 0 0 0 0 0c-.1 .1-.3 .1-.4 .2c0 0 0 0 0 0s0 0 0 0c-.3 .2-.6 .3-.9 .5c-35 19.4-90.8 32-153.6 32c-59.6 0-112.9-11.3-148.2-29.1c-1.9-.9-3.7-1.9-5.5-2.9C14.3 274.6 0 258 0 240c0-34.8 53.4-64.5 128-75.4c10.5-1.5 21.4-2.7 32.7-3.5zM416 240c0-21.9-10.6-39.9-24.1-53.4c28.3-4.4 54.2-11.4 76.2-20.5c16.3-6.8 31.5-15.2 43.9-25.5l0 35.4c0 19.3-16.5 37.1-43.8 50.9c-14.6 7.4-32.4 13.7-52.4 18.5c.1-1.8 .2-3.5 .2-5.3zm-32 96c0 18-14.3 34.6-38.4 48c-1.8 1-3.6 1.9-5.5 2.9C304.9 404.7 251.6 416 192 416c-62.8 0-118.6-12.6-153.6-32C14.3 370.6 0 354 0 336l0-35.4c12.5 10.3 27.6 18.7 43.9 25.5C83.4 342.6 135.8 352 192 352s108.6-9.4 148.1-25.9c7.8-3.2 15.3-6.9 22.4-10.9c6.1-3.4 11.8-7.2 17.2-11.2c1.5-1.1 2.9-2.3 4.3-3.4l0 3.4 0 5.7 0 26.3zm32 0l0-32 0-25.9c19-4.2 36.5-9.5 52.1-16c16.3-6.8 31.5-15.2 43.9-25.5l0 35.4c0 10.5-5 21-14.9 30.9c-16.3 16.3-45 29.7-81.3 38.4c.1-1.7 .2-3.5 .2-5.3zM192 448c56.2 0 108.6-9.4 148.1-25.9c16.3-6.8 31.5-15.2 43.9-25.5l0 35.4c0 44.2-86 80-192 80S0 476.2 0 432l0-35.4c12.5 10.3 27.6 18.7 43.9 25.5C83.4 438.6 135.8 448 192 448z"]};var yf={prefix:"fas",iconName:"ellipsis-vertical",icon:[128,512,["ellipsis-v"],"f142","M64 360a56 56 0 1 0 0 112 56 56 0 1 0 0-112zm0-160a56 56 0 1 0 0 112 56 56 0 1 0 0-112zM120 96A56 56 0 1 0 8 96a56 56 0 1 0 112 0z"]};var aN={prefix:"fas",iconName:"right-long",icon:[512,512,["long-arrow-alt-right"],"f30b","M334.5 414c8.8 3.8 19 2 26-4.6l144-136c4.8-4.5 7.5-10.8 7.5-17.4s-2.7-12.9-7.5-17.4l-144-136c-7-6.6-17.2-8.4-26-4.6s-14.5 12.5-14.5 22l0 72L32 192c-17.7 0-32 14.3-32 32l0 64c0 17.7 14.3 32 32 32l288 0 0 72c0 9.6 5.7 18.2 14.5 22z"]},bf=aN;var xf={prefix:"fas",iconName:"download",icon:[512,512,[],"f019","M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 242.7-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 274.7 288 32zM64 352c-35.3 0-64 28.7-64 64l0 32c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-32c0-35.3-28.7-64-64-64l-101.5 0-45.3 45.3c-25 25-65.5 25-90.5 0L165.5 352 64 352zm368 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"]};var lN={prefix:"fas",iconName:"house",icon:[576,512,[127968,63498,63500,"home","home-alt","home-lg-alt"],"f015","M575.8 255.5c0 18-15 32.1-32 32.1l-32 0 .7 160.2c0 2.7-.2 5.4-.5 8.1l0 16.2c0 22.1-17.9 40-40 40l-16 0c-1.1 0-2.2 0-3.3-.1c-1.4 .1-2.8 .1-4.2 .1L416 512l-24 0c-22.1 0-40-17.9-40-40l0-24 0-64c0-17.7-14.3-32-32-32l-64 0c-17.7 0-32 14.3-32 32l0 64 0 24c0 22.1-17.9 40-40 40l-24 0-31.9 0c-1.5 0-3-.1-4.5-.2c-1.2 .1-2.4 .2-3.6 .2l-16 0c-22.1 0-40-17.9-40-40l0-112c0-.9 0-1.9 .1-2.8l0-69.7-32 0c-18 0-32-14-32-32.1c0-9 3-17 10-24L266.4 8c7-7 15-8 22-8s15 2 21 7L564.8 231.5c8 7 12 15 11 24z"]},Nf=lN;var fN={prefix:"fas",iconName:"arrow-up-wide-short",icon:[576,512,["sort-amount-up"],"f161","M151.6 42.4C145.5 35.8 137 32 128 32s-17.5 3.8-23.6 10.4l-88 96c-11.9 13-11.1 33.3 2 45.2s33.3 11.1 45.2-2L96 146.3 96 448c0 17.7 14.3 32 32 32s32-14.3 32-32l0-301.7 32.4 35.4c11.9 13 32.2 13.9 45.2 2s13.9-32.2 2-45.2l-88-96zM320 480l32 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l224 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32z"]},wf=fN;var Df={prefix:"fas",iconName:"upload",icon:[512,512,[],"f093","M288 109.3L288 352c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-242.7-73.4 73.4c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l128-128c12.5-12.5 32.8-12.5 45.3 0l128 128c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L288 109.3zM64 352l128 0c0 35.3 28.7 64 64 64s64-28.7 64-64l128 0c35.3 0 64 28.7 64 64l0 32c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64l0-32c0-35.3 28.7-64 64-64zM432 456a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"]};var Sf={prefix:"fas",iconName:"angle-down",icon:[448,512,[8964],"f107","M201.4 374.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 306.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"]};var Bc={prefix:"fas",iconName:"ellipsis",icon:[448,512,["ellipsis-h"],"f141","M8 256a56 56 0 1 1 112 0A56 56 0 1 1 8 256zm160 0a56 56 0 1 1 112 0 56 56 0 1 1 -112 0zm216-56a56 56 0 1 1 0 112 56 56 0 1 1 0-112z"]},Ef=Bc;var Af={prefix:"fas",iconName:"credit-card",icon:[576,512,[128179,62083,"credit-card-alt"],"f09d","M64 32C28.7 32 0 60.7 0 96l0 32 576 0 0-32c0-35.3-28.7-64-64-64L64 32zM576 224L0 224 0 416c0 35.3 28.7 64 64 64l448 0c35.3 0 64-28.7 64-64l0-192zM112 352l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm112 16c0-8.8 7.2-16 16-16l128 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-128 0c-8.8 0-16-7.2-16-16z"]};var If={prefix:"fas",iconName:"bell",icon:[448,512,[128276,61602],"f0f3","M224 0c-17.7 0-32 14.3-32 32l0 19.2C119 66 64 130.6 64 208l0 18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416l384 0c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8l0-18.8c0-77.4-55-142-128-156.8L256 32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3l-64 0-64 0c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z"]};var Tf={prefix:"fas",iconName:"file",icon:[384,512,[128196,128459,61462],"f15b","M0 64C0 28.7 28.7 0 64 0L224 0l0 128c0 17.7 14.3 32 32 32l128 0 0 288c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 64zm384 64l-128 0L256 0 384 128z"]};var _f={prefix:"fas",iconName:"arrow-down",icon:[384,512,[8595],"f063","M169.4 470.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 370.8 224 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 306.7L54.6 265.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"]};var kf={prefix:"fas",iconName:"inbox",icon:[512,512,[],"f01c","M121 32C91.6 32 66 52 58.9 80.5L1.9 308.4C.6 313.5 0 318.7 0 323.9L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-92.1c0-5.2-.6-10.4-1.9-15.5l-57-227.9C446 52 420.4 32 391 32L121 32zm0 64l270 0 48 192-51.2 0c-12.1 0-23.2 6.8-28.6 17.7l-14.3 28.6c-5.4 10.8-16.5 17.7-28.6 17.7l-120.4 0c-12.1 0-23.2-6.8-28.6-17.7l-14.3-28.6c-5.4-10.8-16.5-17.7-28.6-17.7L73 288 121 96z"]};var uN={prefix:"fas",iconName:"gauge-high",icon:[512,512,[62461,"tachometer-alt","tachometer-alt-fast"],"f625","M0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM288 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM256 416c35.3 0 64-28.7 64-64c0-17.4-6.9-33.1-18.1-44.6L366 161.7c5.3-12.1-.2-26.3-12.3-31.6s-26.3 .2-31.6 12.3L257.9 288c-.6 0-1.3 0-1.9 0c-35.3 0-64 28.7-64 64s28.7 64 64 64zM176 144a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM96 288a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm352-32a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},Ff=uN;var Pf={prefix:"fas",iconName:"play",icon:[384,512,[9654],"f04b","M73 39c-14.8-9.1-33.4-9.4-48.5-.9S0 62.6 0 80L0 432c0 17.4 9.4 33.4 24.5 41.9s33.7 8.1 48.5-.9L361 297c14.3-8.7 23-24.2 23-41s-8.7-32.2-23-41L73 39z"]};var dN={prefix:"fas",iconName:"magnifying-glass",icon:[512,512,[128269,"search"],"f002","M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"]},z8=dN;var Vf={prefix:"fas",iconName:"chevron-down",icon:[512,512,[],"f078","M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"]};var Of={prefix:"fas",iconName:"arrow-up",icon:[384,512,[8593],"f062","M214.6 41.4c-12.5-12.5-32.8-12.5-45.3 0l-160 160c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 141.2 160 448c0 17.7 14.3 32 32 32s32-14.3 32-32l0-306.7L329.4 246.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-160-160z"]};var hN={prefix:"fas",iconName:"circle-user",icon:[512,512,[62142,"user-circle"],"f2bd","M399 384.2C376.9 345.8 335.4 320 288 320l-64 0c-47.4 0-88.9 25.8-111 64.2c35.2 39.2 86.2 63.8 143 63.8s107.8-24.7 143-63.8zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zm256 16a72 72 0 1 0 0-144 72 72 0 1 0 0 144z"]},Rf=hN;var mN={prefix:"fas",iconName:"circle-half-stroke",icon:[512,512,[9680,"adjust"],"f042","M448 256c0-106-86-192-192-192l0 384c106 0 192-86 192-192zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"]},Hf=mN;var jf={prefix:"fas",iconName:"copy",icon:[448,512,[],"f0c5","M208 0L332.1 0c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9L448 336c0 26.5-21.5 48-48 48l-192 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48zM48 128l80 0 0 64-64 0 0 256 192 0 0-32 64 0 0 48c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 176c0-26.5 21.5-48 48-48z"]};var Bf={prefix:"fas",iconName:"plus",icon:[448,512,[10133,61543,"add"],"2b","M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 144L48 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l144 0 0 144c0 17.7 14.3 32 32 32s32-14.3 32-32l0-144 144 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-144 0 0-144z"]};var Uc={prefix:"fas",iconName:"xmark",icon:[384,512,[128473,10005,10006,10060,215,"close","multiply","remove","times"],"f00d","M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"]};var Uf=Uc;var $f={prefix:"fas",iconName:"rocket",icon:[512,512,[],"f135","M156.6 384.9L125.7 354c-8.5-8.5-11.5-20.8-7.7-32.2c3-8.9 7-20.5 11.8-33.8L24 288c-8.6 0-16.6-4.6-20.9-12.1s-4.2-16.7 .2-24.1l52.5-88.5c13-21.9 36.5-35.3 61.9-35.3l82.3 0c2.4-4 4.8-7.7 7.2-11.3C289.1-4.1 411.1-8.1 483.9 5.3c11.6 2.1 20.6 11.2 22.8 22.8c13.4 72.9 9.3 194.8-111.4 276.7c-3.5 2.4-7.3 4.8-11.3 7.2l0 82.3c0 25.4-13.4 49-35.3 61.9l-88.5 52.5c-7.4 4.4-16.6 4.5-24.1 .2s-12.1-12.2-12.1-20.9l0-107.2c-14.1 4.9-26.4 8.9-35.7 11.9c-11.2 3.6-23.4 .5-31.8-7.8zM384 168a40 40 0 1 0 0-80 40 40 0 1 0 0 80z"]};var qf={prefix:"fas",iconName:"angle-up",icon:[448,512,[8963],"f106","M201.4 137.4c12.5-12.5 32.8-12.5 45.3 0l160 160c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L224 205.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l160-160z"]};var Gf={prefix:"fas",iconName:"chevron-left",icon:[320,512,[9001],"f053","M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"]};var Wf={prefix:"fas",iconName:"chevron-right",icon:[320,512,[9002],"f054","M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"]};var pN={prefix:"fas",iconName:"percent",icon:[384,512,[62101,62785,"percentage"],"25","M374.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-320 320c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l320-320zM128 128A64 64 0 1 0 0 128a64 64 0 1 0 128 0zM384 384a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z"]},Zf=pN;var gN={prefix:"fas",iconName:"thumbtack",icon:[384,512,[128204,128392,"thumb-tack"],"f08d","M32 32C32 14.3 46.3 0 64 0L320 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-29.5 0 11.4 148.2c36.7 19.9 65.7 53.2 79.5 94.7l1 3c3.3 9.8 1.6 20.5-4.4 28.8s-15.7 13.3-26 13.3L32 352c-10.3 0-19.9-4.9-26-13.3s-7.7-19.1-4.4-28.8l1-3c13.8-41.5 42.8-74.8 79.5-94.7L93.5 64 64 64C46.3 64 32 49.7 32 32zM160 384l64 0 0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-96z"]},Yf=gN;var MN={prefix:"fas",iconName:"rotate",icon:[512,512,[128260,"sync-alt"],"f2f1","M142.9 142.9c-17.5 17.5-30.1 38-37.8 59.8c-5.9 16.7-24.2 25.4-40.8 19.5s-25.4-24.2-19.5-40.8C55.6 150.7 73.2 122 97.6 97.6c87.2-87.2 228.3-87.5 315.8-1L455 55c6.9-6.9 17.2-8.9 26.2-5.2s14.8 12.5 14.8 22.2l0 128c0 13.3-10.7 24-24 24l-8.4 0c0 0 0 0 0 0L344 224c-9.7 0-18.5-5.8-22.2-14.8s-1.7-19.3 5.2-26.2l41.1-41.1c-62.6-61.5-163.1-61.2-225.3 1zM16 312c0-13.3 10.7-24 24-24l7.6 0 .7 0L168 288c9.7 0 18.5 5.8 22.2 14.8s1.7 19.3-5.2 26.2l-41.1 41.1c62.6 61.5 163.1 61.2 225.3-1c17.5-17.5 30.1-38 37.8-59.8c5.9-16.7 24.2-25.4 40.8-19.5s25.4 24.2 19.5 40.8c-10.8 30.6-28.4 59.3-52.9 83.8c-87.2 87.2-228.3 87.5-315.8 1L57 457c-6.9 6.9-17.2 8.9-26.2 5.2S16 449.7 16 440l0-119.6 0-.7 0-7.6z"]},Qf=MN,Kf={prefix:"fas",iconName:"spinner",icon:[512,512,[],"f110","M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z"]},Xf={prefix:"fas",iconName:"robot",icon:[640,512,[129302],"f544","M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-304 0c-39.8 0-72-32.2-72-72l0-272c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32zM208 384c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z"]};var CN={prefix:"fas",iconName:"clock-rotate-left",icon:[512,512,["history"],"f1da","M75 75L41 41C25.9 25.9 0 36.6 0 57.9L0 168c0 13.3 10.7 24 24 24l110.1 0c21.4 0 32.1-25.9 17-41l-30.8-30.8C155 85.5 203 64 256 64c106 0 192 86 192 192s-86 192-192 192c-40.8 0-78.6-12.7-109.7-34.4c-14.5-10.1-34.4-6.6-44.6 7.9s-6.6 34.4 7.9 44.6C151.2 495 201.7 512 256 512c141.4 0 256-114.6 256-256S397.4 0 256 0C185.3 0 121.3 28.7 75 75zm181 53c-13.3 0-24 10.7-24 24l0 104c0 6.4 2.5 12.5 7 17l72 72c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-65-65 0-94.1c0-13.3-10.7-24-24-24z"]},Jf=CN;var eu={prefix:"fas",iconName:"shield",icon:[512,512,[128737,"shield-blank"],"f132","M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0z"]};var tu={prefix:"fas",iconName:"pen-nib",icon:[512,512,[10001],"f5ad","M368.4 18.3L312.7 74.1 437.9 199.3l55.7-55.7c21.9-21.9 21.9-57.3 0-79.2L447.6 18.3c-21.9-21.9-57.3-21.9-79.2 0zM288 94.6l-9.2 2.8L134.7 140.6c-19.9 6-35.7 21.2-42.3 41L3.8 445.8c-3.8 11.3-1 23.9 7.3 32.4L164.7 324.7c-3-6.3-4.7-13.3-4.7-20.7c0-26.5 21.5-48 48-48s48 21.5 48 48s-21.5 48-48 48c-7.4 0-14.4-1.7-20.7-4.7L33.7 500.9c8.6 8.3 21.1 11.2 32.4 7.3l264.3-88.6c19.7-6.6 35-22.4 41-42.3l43.2-144.1 2.7-9.2L288 94.6z"]};var cu={prefix:"fas",iconName:"moon",icon:[384,512,[127769,9214],"f186","M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z"]};var nu={prefix:"fas",iconName:"hashtag",icon:[448,512,[62098],"23","M181.3 32.4c17.4 2.9 29.2 19.4 26.3 36.8L197.8 128l95.1 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3s29.2 19.4 26.3 36.8L357.8 128l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0L325.8 320l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8l9.8-58.7-95.1 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8L90.2 384 32 384c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 21.3-128L64 192c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3zM187.1 192L165.8 320l95.1 0 21.3-128-95.1 0z"]};var zN={prefix:"fas",iconName:"up-down",icon:[256,512,[8597,11021,"arrows-alt-v"],"f338","M145.6 7.7C141 2.8 134.7 0 128 0s-13 2.8-17.6 7.7l-104 112c-6.5 7-8.2 17.2-4.4 25.9S14.5 160 24 160l56 0 0 192-56 0c-9.5 0-18.2 5.7-22 14.4s-2.1 18.9 4.4 25.9l104 112c4.5 4.9 10.9 7.7 17.6 7.7s13-2.8 17.6-7.7l104-112c6.5-7 8.2-17.2 4.4-25.9s-12.5-14.4-22-14.4l-56 0 0-192 56 0c9.5 0 18.2-5.7 22-14.4s2.1-18.9-4.4-25.9l-104-112z"]},su=zN;var ou={prefix:"fas",iconName:"calendar",icon:[448,512,[128197,128198],"f133","M96 32l0 32L48 64C21.5 64 0 85.5 0 112l0 48 448 0 0-48c0-26.5-21.5-48-48-48l-48 0 0-32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 32L160 64l0-32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192L0 192 0 464c0 26.5 21.5 48 48 48l352 0c26.5 0 48-21.5 48-48l0-272z"]};var $c={prefix:"fas",iconName:"circle-plus",icon:[512,512,["plus-circle"],"f055","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM232 344l0-64-64 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l64 0 0-64c0-13.3 10.7-24 24-24s24 10.7 24 24l0 64 64 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-64 0 0 64c0 13.3-10.7 24-24 24s-24-10.7-24-24z"]},iu=$c;var ru={prefix:"fas",iconName:"user-plus",icon:[640,512,[],"f234","M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM504 312l0-64-64 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l64 0 0-64c0-13.3 10.7-24 24-24s24 10.7 24 24l0 64 64 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-64 0 0 64c0 13.3-10.7 24-24 24s-24-10.7-24-24z"]},au={prefix:"fas",iconName:"check",icon:[448,512,[10003,10004],"f00c","M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"]};var lu={prefix:"fas",iconName:"book-open",icon:[576,512,[128214,128366],"f518","M249.6 471.5c10.8 3.8 22.4-4.1 22.4-15.5l0-377.4c0-4.2-1.6-8.4-5-11C247.4 52 202.4 32 144 32C93.5 32 46.3 45.3 18.1 56.1C6.8 60.5 0 71.7 0 83.8L0 454.1c0 11.9 12.8 20.2 24.1 16.5C55.6 460.1 105.5 448 144 448c33.9 0 79 14 105.6 23.5zm76.8 0C353 462 398.1 448 432 448c38.5 0 88.4 12.1 119.9 22.6c11.3 3.8 24.1-4.6 24.1-16.5l0-370.3c0-12.1-6.8-23.3-18.1-27.6C529.7 45.3 482.5 32 432 32c-58.4 0-103.4 20-123 35.6c-3.3 2.6-5 6.8-5 11L304 456c0 11.4 11.7 19.3 22.4 15.5z"]};var LN={prefix:"fas",iconName:"triangle-exclamation",icon:[512,512,[9888,"exclamation-triangle","warning"],"f071","M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480L40 480c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24l0 112c0 13.3 10.7 24 24 24s24-10.7 24-24l0-112c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},fu=LN;var vN={prefix:"fas",iconName:"right-left",icon:[512,512,["exchange-alt"],"f362","M32 96l320 0 0-64c0-12.9 7.8-24.6 19.8-29.6s25.7-2.2 34.9 6.9l96 96c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-96 96c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6l0-64L32 160c-17.7 0-32-14.3-32-32s14.3-32 32-32zM480 352c17.7 0 32 14.3 32 32s-14.3 32-32 32l-320 0 0 64c0 12.9-7.8 24.6-19.8 29.6s-25.7 2.2-34.9-6.9l-96-96c-6-6-9.4-14.1-9.4-22.6s3.4-16.6 9.4-22.6l96-96c9.2-9.2 22.9-11.9 34.9-6.9s19.8 16.6 19.8 29.6l0 64 320 0z"]},uu=vN,du={prefix:"fas",iconName:"paper-plane",icon:[512,512,[61913],"f1d8","M498.1 5.6c10.1 7 15.4 19.1 13.5 31.2l-64 416c-1.5 9.7-7.4 18.2-16 23s-18.9 5.4-28 1.6L284 427.7l-68.5 74.1c-8.9 9.7-22.9 12.9-35.2 8.1S160 493.2 160 480l0-83.6c0-4 1.5-7.8 4.2-10.8L331.8 202.8c5.8-6.3 5.6-16-.4-22s-15.7-6.4-22-.7L106 360.8 17.7 316.6C7.1 311.3 .3 300.7 0 288.9s5.9-22.8 16.1-28.7l448-256c10.7-6.1 23.9-5.5 34 1.4z"]};var yN={prefix:"fas",iconName:"circle-xmark",icon:[512,512,[61532,"times-circle","xmark-circle"],"f057","M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM175 175c9.4-9.4 24.6-9.4 33.9 0l47 47 47-47c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9l-47 47 47 47c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0l-47-47-47 47c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l47-47-47-47c-9.4-9.4-9.4-24.6 0-33.9z"]},hu=yN;var mu={prefix:"fas",iconName:"exclamation",icon:[128,512,[10069,10071,61738],"21","M96 64c0-17.7-14.3-32-32-32S32 46.3 32 64l0 256c0 17.7 14.3 32 32 32s32-14.3 32-32L96 64zM64 480a40 40 0 1 0 0-80 40 40 0 1 0 0 80z"]};var pu={prefix:"fas",iconName:"dollar-sign",icon:[320,512,[128178,61781,"dollar","usd"],"24","M160 0c17.7 0 32 14.3 32 32l0 35.7c1.6 .2 3.1 .4 4.7 .7c.4 .1 .7 .1 1.1 .2l48 8.8c17.4 3.2 28.9 19.9 25.7 37.2s-19.9 28.9-37.2 25.7l-47.5-8.7c-31.3-4.6-58.9-1.5-78.3 6.2s-27.2 18.3-29 28.1c-2 10.7-.5 16.7 1.2 20.4c1.8 3.9 5.5 8.3 12.8 13.2c16.3 10.7 41.3 17.7 73.7 26.3l2.9 .8c28.6 7.6 63.6 16.8 89.6 33.8c14.2 9.3 27.6 21.9 35.9 39.5c8.5 17.9 10.3 37.9 6.4 59.2c-6.9 38-33.1 63.4-65.6 76.7c-13.7 5.6-28.6 9.2-44.4 11l0 33.4c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-34.9c-.4-.1-.9-.1-1.3-.2l-.2 0s0 0 0 0c-24.4-3.8-64.5-14.3-91.5-26.3c-16.1-7.2-23.4-26.1-16.2-42.2s26.1-23.4 42.2-16.2c20.9 9.3 55.3 18.5 75.2 21.6c31.9 4.7 58.2 2 76-5.3c16.9-6.9 24.6-16.9 26.8-28.9c1.9-10.6 .4-16.7-1.3-20.4c-1.9-4-5.6-8.4-13-13.3c-16.4-10.7-41.5-17.7-74-26.3l-2.8-.7s0 0 0 0C119.4 279.3 84.4 270 58.4 253c-14.2-9.3-27.5-22-35.8-39.6c-8.4-17.9-10.1-37.9-6.1-59.2C23.7 116 52.3 91.2 84.8 78.3c13.3-5.3 27.9-8.9 43.2-11L128 32c0-17.7 14.3-32 32-32z"]};var bN={prefix:"fas",iconName:"users-gear",icon:[640,512,["users-cog"],"f509","M144 160A80 80 0 1 0 144 0a80 80 0 1 0 0 160zm368 0A80 80 0 1 0 512 0a80 80 0 1 0 0 160zM0 298.7C0 310.4 9.6 320 21.3 320l213.3 0c.2 0 .4 0 .7 0c-26.6-23.5-43.3-57.8-43.3-96c0-7.6 .7-15 1.9-22.3c-13.6-6.3-28.7-9.7-44.6-9.7l-42.7 0C47.8 192 0 239.8 0 298.7zM320 320c24 0 45.9-8.8 62.7-23.3c2.5-3.7 5.2-7.3 8-10.7c2.7-3.3 5.7-6.1 9-8.3C410 262.3 416 243.9 416 224c0-53-43-96-96-96s-96 43-96 96s43 96 96 96zm65.4 60.2c-10.3-5.9-18.1-16.2-20.8-28.2l-103.2 0C187.7 352 128 411.7 128 485.3c0 14.7 11.9 26.7 26.7 26.7l300.6 0c-2.1-5.2-3.2-10.9-3.2-16.4l0-3c-1.3-.7-2.7-1.5-4-2.3l-2.6 1.5c-16.8 9.7-40.5 8-54.7-9.7c-4.5-5.6-8.6-11.5-12.4-17.6l-.1-.2-.1-.2-2.4-4.1-.1-.2-.1-.2c-3.4-6.2-6.4-12.6-9-19.3c-8.2-21.2 2.2-42.6 19-52.3l2.7-1.5c0-.8 0-1.5 0-2.3s0-1.5 0-2.3l-2.7-1.5zM533.3 192l-42.7 0c-15.9 0-31 3.5-44.6 9.7c1.3 7.2 1.9 14.7 1.9 22.3c0 17.4-3.5 33.9-9.7 49c2.5 .9 4.9 2 7.1 3.3l2.6 1.5c1.3-.8 2.6-1.6 4-2.3l0-3c0-19.4 13.3-39.1 35.8-42.6c7.9-1.2 16-1.9 24.2-1.9s16.3 .6 24.2 1.9c22.5 3.5 35.8 23.2 35.8 42.6l0 3c1.3 .7 2.7 1.5 4 2.3l2.6-1.5c16.8-9.7 40.5-8 54.7 9.7c2.3 2.8 4.5 5.8 6.6 8.7c-2.1-57.1-49-102.7-106.6-102.7zm91.3 163.9c6.3-3.6 9.5-11.1 6.8-18c-2.1-5.5-4.6-10.8-7.4-15.9l-2.3-4c-3.1-5.1-6.5-9.9-10.2-14.5c-4.6-5.7-12.7-6.7-19-3l-2.9 1.7c-9.2 5.3-20.4 4-29.6-1.3s-16.1-14.5-16.1-25.1l0-3.4c0-7.3-4.9-13.8-12.1-14.9c-6.5-1-13.1-1.5-19.9-1.5s-13.4 .5-19.9 1.5c-7.2 1.1-12.1 7.6-12.1 14.9l0 3.4c0 10.6-6.9 19.8-16.1 25.1s-20.4 6.6-29.6 1.3l-2.9-1.7c-6.3-3.6-14.4-2.6-19 3c-3.7 4.6-7.1 9.5-10.2 14.6l-2.3 3.9c-2.8 5.1-5.3 10.4-7.4 15.9c-2.6 6.8 .5 14.3 6.8 17.9l2.9 1.7c9.2 5.3 13.7 15.8 13.7 26.4s-4.5 21.1-13.7 26.4l-3 1.7c-6.3 3.6-9.5 11.1-6.8 17.9c2.1 5.5 4.6 10.7 7.4 15.8l2.4 4.1c3 5.1 6.4 9.9 10.1 14.5c4.6 5.7 12.7 6.7 19 3l2.9-1.7c9.2-5.3 20.4-4 29.6 1.3s16.1 14.5 16.1 25.1l0 3.4c0 7.3 4.9 13.8 12.1 14.9c6.5 1 13.1 1.5 19.9 1.5s13.4-.5 19.9-1.5c7.2-1.1 12.1-7.6 12.1-14.9l0-3.4c0-10.6 6.9-19.8 16.1-25.1s20.4-6.6 29.6-1.3l2.9 1.7c6.3 3.6 14.4 2.6 19-3c3.7-4.6 7.1-9.4 10.1-14.5l2.4-4.2c2.8-5.1 5.3-10.3 7.4-15.8c2.6-6.8-.5-14.3-6.8-17.9l-3-1.7c-9.2-5.3-13.7-15.8-13.7-26.4s4.5-21.1 13.7-26.4l3-1.7zM472 384a40 40 0 1 1 80 0 40 40 0 1 1 -80 0z"]},gu=bN;var Mu={faHeart:ql,faChevronLeft:Gf,faAngleDown:Sf,faAngleRight:rl,faArrowLeft:rf,faBars:Ya,faList:k4,faWallet:Zl,faChartLine:Bl,faMoon:cu,faLanguage:Pl,faHeadset:Ll,faArrowRight:Ul,faSyncAlt:Qf,faKey:fl,faSignInAlt:Cl,faEye:Xl,faCheckDouble:pf,faHistory:Jf,faTrash:sf,faUsers:nl,faPlay:Pf,faFilter:Vl,faBan:ml,faTimesCircle:hu,faEllipsisVertical:yf,faCheckCircle:Dl,faCopy:jf,faImage:xl,faGripVertical:zf,faSortAmountDown:Al,faSortAmountUp:wf,faServer:Ml,faHashtag:nu,faAlignLeft:af,faLock:el,faHeartBroken:Qa,faBookOpen:lu,faChartArea:hl,faChartBar:bl,faChartPie:jl,faCheck:au,faChevronDown:Vf,faChevronRight:Wf,faChevronUp:il,faAngleUp:qf,faArrowUp:Of,faDownload:xf,faColumns:Nl,faDollarSign:pu,faCircle:Wl,faPaperPlane:du,faExclamation:mu,faFile:Tf,faExclamationTriangle:fu,faFlag:Ja,faLongArrowAltRight:bf,faMousePointer:gl,faPercentage:Zf,faPlusCircle:iu,faSearch:z8,faTable:Za,faTachometerAlt:Ff,faTag:uf,faTimes:Uf,faCreditCard:Af,faQuestionCircle:Ql,faHandPaper:sl,faCog:Mf,faUserCircle:Rf,faArrowDown:_f,faUser:ll,faUserFriends:zl,faInbox:kf,faEnvelope:hf,faRefresh:Tl,faComment:df,faThLarge:ff,faShoppingCart:Cf,faTrashCan:Ua,faEdit:tl,faCartShopping:jc,faShield:eu,faCirclePlus:$c,faPlus:Bf,faUserPen:vl,faPenNib:tu,faThumbTack:Yf,faPen:Jl,faBell:If,faWindowMaximize:ef,faXmark:Uc,faClock:Lf,faHourglassHalf:cl,faCircleExclamation:Hc,faCode:Hl,faPhone:cf,faFileImport:Sl,faInfoCircle:mf,faArrowsAltV:su,faRocket:$f,faShieldAlt:_l,faEllipsis:Bc,faArrowsAlt:Ol,faSort:Fl,faHeadphones:of,faSpinner:Kf,faFolder:al,faPlayCircle:wl,faAdjust:Hf,faMinusCircle:qa,faCoins:vf,faExchangeAlt:uu,faMoneyBillWave:dl,faAd:kl,faCube:Gl,faEllipsisH:Ef,faExternalLinkAlt:lf,faPalette:El,faPlug:ol,faUserCog:nf,faPuzzlePiece:Rl,faUndo:gf,faLightbulb:Ka,faGlobe:ul,faUserPlus:ru,faFileAlt:$a,faSearchAlt:z8,faTags:Kl,faUserCheck:Wa,faCalendar:ou,faComments:Ga,faExclamationCircle:Xa,faUpload:Df,faListAlt:k4,faThList:k4,faStar:pl,faHome:Nf,faSave:tf,faEnvelopeOpen:Il,faRobot:Xf,faTools:$l,faWrench:Yl,faGift:yl,faUsersCog:gu};var Cu={prefix:"fab",iconName:"cc-visa",icon:[576,512,[],"f1f0","M470.1 231.3s7.6 37.2 9.3 45H446c3.3-8.9 16-43.5 16-43.5-.2.3 3.3-9.1 5.3-14.9l2.8 13.4zM576 80v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48zM152.5 331.2L215.7 176h-42.5l-39.3 106-4.3-21.5-14-71.4c-2.3-9.9-9.4-12.7-18.2-13.1H32.7l-.7 3.1c15.8 4 29.9 9.8 42.2 17.1l35.8 135h42.5zm94.4.2L272.1 176h-40.2l-25.1 155.4h40.1zm139.9-50.8c.2-17.7-10.6-31.2-33.7-42.3-14.1-7.1-22.7-11.9-22.7-19.2.2-6.6 7.3-13.4 23.1-13.4 13.1-.3 22.7 2.8 29.9 5.9l3.6 1.7 5.5-33.6c-7.9-3.1-20.5-6.6-36-6.6-39.7 0-67.6 21.2-67.8 51.4-.3 22.3 20 34.7 35.2 42.2 15.5 7.6 20.8 12.6 20.8 19.3-.2 10.4-12.6 15.2-24.1 15.2-16 0-24.6-2.5-37.7-8.3l-5.3-2.5-5.6 34.9c9.4 4.3 26.8 8.1 44.8 8.3 42.2.1 69.7-20.8 70-53zM528 331.4L495.6 176h-31.1c-9.6 0-16.9 2.8-21 12.9l-59.7 142.5H426s6.9-19.2 8.4-23.3H486c1.2 5.5 4.8 23.3 4.8 23.3H528z"]};var zu={prefix:"fab",iconName:"google",icon:[488,512,[],"f1a0","M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"]};var Lu={prefix:"fab",iconName:"creative-commons",icon:[496,512,[],"f25e","M245.83 214.87l-33.22 17.28c-9.43-19.58-25.24-19.93-27.46-19.93-22.13 0-33.22 14.61-33.22 43.84 0 23.57 9.21 43.84 33.22 43.84 14.47 0 24.65-7.09 30.57-21.26l30.55 15.5c-6.17 11.51-25.69 38.98-65.1 38.98-22.6 0-73.96-10.32-73.96-77.05 0-58.69 43-77.06 72.63-77.06 30.72-.01 52.7 11.95 65.99 35.86zm143.05 0l-32.78 17.28c-9.5-19.77-25.72-19.93-27.9-19.93-22.14 0-33.22 14.61-33.22 43.84 0 23.55 9.23 43.84 33.22 43.84 14.45 0 24.65-7.09 30.54-21.26l31 15.5c-2.1 3.75-21.39 38.98-65.09 38.98-22.69 0-73.96-9.87-73.96-77.05 0-58.67 42.97-77.06 72.63-77.06 30.71-.01 52.58 11.95 65.56 35.86zM247.56 8.05C104.74 8.05 0 123.11 0 256.05c0 138.49 113.6 248 247.56 248 129.93 0 248.44-100.87 248.44-248 0-137.87-106.62-248-248.44-248zm.87 450.81c-112.54 0-203.7-93.04-203.7-202.81 0-105.42 85.43-203.27 203.72-203.27 112.53 0 202.82 89.46 202.82 203.26-.01 121.69-99.68 202.82-202.84 202.82z"]};var vu={prefix:"fab",iconName:"weixin",icon:[576,512,[],"f1d7","M385.2 167.6c6.4 0 12.6.3 18.8 1.1C387.4 90.3 303.3 32 207.7 32 100.5 32 13 104.8 13 197.4c0 53.4 29.3 97.5 77.9 131.6l-19.3 58.6 68-34.1c24.4 4.8 43.8 9.7 68.2 9.7 6.2 0 12.1-.3 18.3-.8-4-12.9-6.2-26.6-6.2-40.8-.1-84.9 72.9-154 165.3-154zm-104.5-52.9c14.5 0 24.2 9.7 24.2 24.4 0 14.5-9.7 24.2-24.2 24.2-14.8 0-29.3-9.7-29.3-24.2.1-14.7 14.6-24.4 29.3-24.4zm-136.4 48.6c-14.5 0-29.3-9.7-29.3-24.2 0-14.8 14.8-24.4 29.3-24.4 14.8 0 24.4 9.7 24.4 24.4 0 14.6-9.6 24.2-24.4 24.2zM563 319.4c0-77.9-77.9-141.3-165.4-141.3-92.7 0-165.4 63.4-165.4 141.3S305 460.7 397.6 460.7c19.3 0 38.9-5.1 58.6-9.9l53.4 29.3-14.8-48.6C534 402.1 563 363.2 563 319.4zm-219.1-24.5c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.8 0 24.4 9.7 24.4 19.3 0 10-9.7 19.6-24.4 19.6zm107.1 0c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.5 0 24.4 9.7 24.4 19.3.1 10-9.9 19.6-24.4 19.6z"]};var yu={prefix:"fab",iconName:"app-store",icon:[512,512,[],"f36f","M255.9 120.9l9.1-15.7c5.6-9.8 18.1-13.1 27.9-7.5 9.8 5.6 13.1 18.1 7.5 27.9l-87.5 151.5h63.3c20.5 0 32 24.1 23.1 40.8H113.8c-11.3 0-20.4-9.1-20.4-20.4 0-11.3 9.1-20.4 20.4-20.4h52l66.6-115.4-20.8-36.1c-5.6-9.8-2.3-22.2 7.5-27.9 9.8-5.6 22.2-2.3 27.9 7.5l8.9 15.7zm-78.7 218l-19.6 34c-5.6 9.8-18.1 13.1-27.9 7.5-9.8-5.6-13.1-18.1-7.5-27.9l14.6-25.2c16.4-5.1 29.8-1.2 40.4 11.6zm168.9-61.7h53.1c11.3 0 20.4 9.1 20.4 20.4 0 11.3-9.1 20.4-20.4 20.4h-29.5l19.9 34.5c5.6 9.8 2.3 22.2-7.5 27.9-9.8 5.6-22.2 2.3-27.9-7.5-33.5-58.1-58.7-101.6-75.4-130.6-17.1-29.5-4.9-59.1 7.2-69.1 13.4 23 33.4 57.7 60.1 104zM256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm216 248c0 118.7-96.1 216-216 216-118.7 0-216-96.1-216-216 0-118.7 96.1-216 216-216 118.7 0 216 96.1 216 216z"]},bu={prefix:"fab",iconName:"cc-mastercard",icon:[576,512,[],"f1f1","M482.9 410.3c0 6.8-4.6 11.7-11.2 11.7-6.8 0-11.2-5.2-11.2-11.7 0-6.5 4.4-11.7 11.2-11.7 6.6 0 11.2 5.2 11.2 11.7zm-310.8-11.7c-7.1 0-11.2 5.2-11.2 11.7 0 6.5 4.1 11.7 11.2 11.7 6.5 0 10.9-4.9 10.9-11.7-.1-6.5-4.4-11.7-10.9-11.7zm117.5-.3c-5.4 0-8.7 3.5-9.5 8.7h19.1c-.9-5.7-4.4-8.7-9.6-8.7zm107.8.3c-6.8 0-10.9 5.2-10.9 11.7 0 6.5 4.1 11.7 10.9 11.7 6.8 0 11.2-4.9 11.2-11.7 0-6.5-4.4-11.7-11.2-11.7zm105.9 26.1c0 .3.3.5.3 1.1 0 .3-.3.5-.3 1.1-.3.3-.3.5-.5.8-.3.3-.5.5-1.1.5-.3.3-.5.3-1.1.3-.3 0-.5 0-1.1-.3-.3 0-.5-.3-.8-.5-.3-.3-.5-.5-.5-.8-.3-.5-.3-.8-.3-1.1 0-.5 0-.8.3-1.1 0-.5.3-.8.5-1.1.3-.3.5-.3.8-.5.5-.3.8-.3 1.1-.3.5 0 .8 0 1.1.3.5.3.8.3 1.1.5s.2.6.5 1.1zm-2.2 1.4c.5 0 .5-.3.8-.3.3-.3.3-.5.3-.8 0-.3 0-.5-.3-.8-.3 0-.5-.3-1.1-.3h-1.6v3.5h.8V426h.3l1.1 1.4h.8l-1.1-1.3zM576 81v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V81c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48zM64 220.6c0 76.5 62.1 138.5 138.5 138.5 27.2 0 53.9-8.2 76.5-23.1-72.9-59.3-72.4-171.2 0-230.5-22.6-15-49.3-23.1-76.5-23.1-76.4-.1-138.5 62-138.5 138.2zm224 108.8c70.5-55 70.2-162.2 0-217.5-70.2 55.3-70.5 162.6 0 217.5zm-142.3 76.3c0-8.7-5.7-14.4-14.7-14.7-4.6 0-9.5 1.4-12.8 6.5-2.4-4.1-6.5-6.5-12.2-6.5-3.8 0-7.6 1.4-10.6 5.4V392h-8.2v36.7h8.2c0-18.9-2.5-30.2 9-30.2 10.2 0 8.2 10.2 8.2 30.2h7.9c0-18.3-2.5-30.2 9-30.2 10.2 0 8.2 10 8.2 30.2h8.2v-23zm44.9-13.7h-7.9v4.4c-2.7-3.3-6.5-5.4-11.7-5.4-10.3 0-18.2 8.2-18.2 19.3 0 11.2 7.9 19.3 18.2 19.3 5.2 0 9-1.9 11.7-5.4v4.6h7.9V392zm40.5 25.6c0-15-22.9-8.2-22.9-15.2 0-5.7 11.9-4.8 18.5-1.1l3.3-6.5c-9.4-6.1-30.2-6-30.2 8.2 0 14.3 22.9 8.3 22.9 15 0 6.3-13.5 5.8-20.7.8l-3.5 6.3c11.2 7.6 32.6 6 32.6-7.5zm35.4 9.3l-2.2-6.8c-3.8 2.1-12.2 4.4-12.2-4.1v-16.6h13.1V392h-13.1v-11.2h-8.2V392h-7.6v7.3h7.6V416c0 17.6 17.3 14.4 22.6 10.9zm13.3-13.4h27.5c0-16.2-7.4-22.6-17.4-22.6-10.6 0-18.2 7.9-18.2 19.3 0 20.5 22.6 23.9 33.8 14.2l-3.8-6c-7.8 6.4-19.6 5.8-21.9-4.9zm59.1-21.5c-4.6-2-11.6-1.8-15.2 4.4V392h-8.2v36.7h8.2V408c0-11.6 9.5-10.1 12.8-8.4l2.4-7.6zm10.6 18.3c0-11.4 11.6-15.1 20.7-8.4l3.8-6.5c-11.6-9.1-32.7-4.1-32.7 15 0 19.8 22.4 23.8 32.7 15l-3.8-6.5c-9.2 6.5-20.7 2.6-20.7-8.6zm66.7-18.3H408v4.4c-8.3-11-29.9-4.8-29.9 13.9 0 19.2 22.4 24.7 29.9 13.9v4.6h8.2V392zm33.7 0c-2.4-1.2-11-2.9-15.2 4.4V392h-7.9v36.7h7.9V408c0-11 9-10.3 12.8-8.4l2.4-7.6zm40.3-14.9h-7.9v19.3c-8.2-10.9-29.9-5.1-29.9 13.9 0 19.4 22.5 24.6 29.9 13.9v4.6h7.9v-51.7zm7.6-75.1v4.6h.8V302h1.9v-.8h-4.6v.8h1.9zm6.6 123.8c0-.5 0-1.1-.3-1.6-.3-.3-.5-.8-.8-1.1-.3-.3-.8-.5-1.1-.8-.5 0-1.1-.3-1.6-.3-.3 0-.8.3-1.4.3-.5.3-.8.5-1.1.8-.5.3-.8.8-.8 1.1-.3.5-.3 1.1-.3 1.6 0 .3 0 .8.3 1.4 0 .3.3.8.8 1.1.3.3.5.5 1.1.8.5.3 1.1.3 1.4.3.5 0 1.1 0 1.6-.3.3-.3.8-.5 1.1-.8.3-.3.5-.8.8-1.1.3-.6.3-1.1.3-1.4zm3.2-124.7h-1.4l-1.6 3.5-1.6-3.5h-1.4v5.4h.8v-4.1l1.6 3.5h1.1l1.4-3.5v4.1h1.1v-5.4zm4.4-80.5c0-76.2-62.1-138.3-138.5-138.3-27.2 0-53.9 8.2-76.5 23.1 72.1 59.3 73.2 171.5 0 230.5 22.6 15 49.5 23.1 76.5 23.1 76.4.1 138.5-61.9 138.5-138.4z"]};var xu={prefix:"fab",iconName:"skype",icon:[448,512,[],"f17e","M424.7 299.8c2.9-14 4.7-28.9 4.7-43.8 0-113.5-91.9-205.3-205.3-205.3-14.9 0-29.7 1.7-43.8 4.7C161.3 40.7 137.7 32 112 32 50.2 32 0 82.2 0 144c0 25.7 8.7 49.3 23.3 68.2-2.9 14-4.7 28.9-4.7 43.8 0 113.5 91.9 205.3 205.3 205.3 14.9 0 29.7-1.7 43.8-4.7 19 14.6 42.6 23.3 68.2 23.3 61.8 0 112-50.2 112-112 .1-25.6-8.6-49.2-23.2-68.1zm-194.6 91.5c-65.6 0-120.5-29.2-120.5-65 0-16 9-30.6 29.5-30.6 31.2 0 34.1 44.9 88.1 44.9 25.7 0 42.3-11.4 42.3-26.3 0-18.7-16-21.6-42-28-62.5-15.4-117.8-22-117.8-87.2 0-59.2 58.6-81.1 109.1-81.1 55.1 0 110.8 21.9 110.8 55.4 0 16.9-11.4 31.8-30.3 31.8-28.3 0-29.2-33.5-75-33.5-25.7 0-42 7-42 22.5 0 19.8 20.8 21.8 69.1 33 41.4 9.3 90.7 26.8 90.7 77.6 0 59.1-57.1 86.5-112 86.5z"]};var Nu={prefix:"fab",iconName:"behance",icon:[576,512,[],"f1b4","M232 237.2c31.8-15.2 48.4-38.2 48.4-74 0-70.6-52.6-87.8-113.3-87.8H0v354.4h171.8c64.4 0 124.9-30.9 124.9-102.9 0-44.5-21.1-77.4-64.7-89.7zM77.9 135.9H151c28.1 0 53.4 7.9 53.4 40.5 0 30.1-19.7 42.2-47.5 42.2h-79v-82.7zm83.3 233.7H77.9V272h84.9c34.3 0 56 14.3 56 50.6 0 35.8-25.9 47-57.6 47zm358.5-240.7H376V94h143.7v34.9zM576 305.2c0-75.9-44.4-139.2-124.9-139.2-78.2 0-131.3 58.8-131.3 135.8 0 79.9 50.3 134.7 131.3 134.7 61.3 0 101-27.6 120.1-86.3H509c-6.7 21.9-34.3 33.5-55.7 33.5-41.3 0-63-24.2-63-65.3h185.1c.3-4.2.6-8.7.6-13.2zM390.4 274c2.3-33.7 24.7-54.8 58.5-54.8 35.4 0 53.2 20.8 56.2 54.8H390.4z"]},wu={prefix:"fab",iconName:"reddit",icon:[512,512,[],"f1a1","M0 256C0 114.6 114.6 0 256 0S512 114.6 512 256s-114.6 256-256 256L37.1 512c-13.7 0-20.5-16.5-10.9-26.2L75 437C28.7 390.7 0 326.7 0 256zM349.6 153.6c23.6 0 42.7-19.1 42.7-42.7s-19.1-42.7-42.7-42.7c-20.6 0-37.8 14.6-41.8 34c-34.5 3.7-61.4 33-61.4 68.4l0 .2c-37.5 1.6-71.8 12.3-99 29.1c-10.1-7.8-22.8-12.5-36.5-12.5c-33 0-59.8 26.8-59.8 59.8c0 24 14.1 44.6 34.4 54.1c2 69.4 77.6 125.2 170.6 125.2s168.7-55.9 170.6-125.3c20.2-9.6 34.1-30.2 34.1-54c0-33-26.8-59.8-59.8-59.8c-13.7 0-26.3 4.6-36.4 12.4c-27.4-17-62.1-27.7-100-29.1l0-.2c0-25.4 18.9-46.5 43.4-49.9l0 0c4.4 18.8 21.3 32.8 41.5 32.8zM177.1 246.9c16.7 0 29.5 17.6 28.5 39.3s-13.5 29.6-30.3 29.6s-31.4-8.8-30.4-30.5s15.4-38.3 32.1-38.3zm190.1 38.3c1 21.7-13.7 30.5-30.4 30.5s-29.3-7.9-30.3-29.6c-1-21.7 11.8-39.3 28.5-39.3s31.2 16.6 32.1 38.3zm-48.1 56.7c-10.3 24.6-34.6 41.9-63 41.9s-52.7-17.3-63-41.9c-1.2-2.9 .8-6.2 3.9-6.5c18.4-1.9 38.3-2.9 59.1-2.9s40.7 1 59.1 2.9c3.1 .3 5.1 3.6 3.9 6.5z"]},Du={prefix:"fab",iconName:"discord",icon:[640,512,[],"f392","M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z"]};var Su={prefix:"fab",iconName:"ebay",icon:[640,512,[],"f4f4","M606 189.5l-54.8 109.9-54.9-109.9h-37.5l10.9 20.6c-11.5-19-35.9-26-63.3-26-31.8 0-67.9 8.7-71.5 43.1h33.7c1.4-13.8 15.7-21.8 35-21.8 26 0 41 9.6 41 33v3.4c-12.7 0-28 .1-41.7.4-42.4.9-69.6 10-76.7 34.4 1-5.2 1.5-10.6 1.5-16.2 0-52.1-39.7-76.2-75.4-76.2-21.3 0-43 5.5-58.7 24.2v-80.6h-32.1v169.5c0 10.3-.6 22.9-1.1 33.1h31.5c.7-6.3 1.1-12.9 1.1-19.5 13.6 16.6 35.4 24.9 58.7 24.9 36.9 0 64.9-21.9 73.3-54.2-.5 2.8-.7 5.8-.7 9 0 24.1 21.1 45 60.6 45 26.6 0 45.8-5.7 61.9-25.5 0 6.6.3 13.3 1.1 20.2h29.8c-.7-8.2-1-17.5-1-26.8v-65.6c0-9.3-1.7-17.2-4.8-23.8l61.5 116.1-28.5 54.1h35.9L640 189.5zM243.7 313.8c-29.6 0-50.2-21.5-50.2-53.8 0-32.4 20.6-53.8 50.2-53.8 29.8 0 50.2 21.4 50.2 53.8 0 32.3-20.4 53.8-50.2 53.8zm200.9-47.3c0 30-17.9 48.4-51.6 48.4-25.1 0-35-13.4-35-25.8 0-19.1 18.1-24.4 47.2-25.3 13.1-.5 27.6-.6 39.4-.6zm-411.9 1.6h128.8v-8.5c0-51.7-33.1-75.4-78.4-75.4-56.8 0-83 30.8-83 77.6 0 42.5 25.3 74 82.5 74 31.4 0 68-11.7 74.4-46.1h-33.1c-12 35.8-87.7 36.7-91.2-21.6zm95-21.4H33.3c6.9-56.6 92.1-54.7 94.4 0z"]},Eu={prefix:"fab",iconName:"amazon",icon:[448,512,[],"f270","M257.2 162.7c-48.7 1.8-169.5 15.5-169.5 117.5 0 109.5 138.3 114 183.5 43.2 6.5 10.2 35.4 37.5 45.3 46.8l56.8-56S341 288.9 341 261.4V114.3C341 89 316.5 32 228.7 32 140.7 32 94 87 94 136.3l73.5 6.8c16.3-49.5 54.2-49.5 54.2-49.5 40.7-.1 35.5 29.8 35.5 69.1zm0 86.8c0 80-84.2 68-84.2 17.2 0-47.2 50.5-56.7 84.2-57.8v40.6zm136 163.5c-7.7 10-70 67-174.5 67S34.2 408.5 9.7 379c-6.8-7.7 1-11.3 5.5-8.3C88.5 415.2 203 488.5 387.7 401c7.5-3.7 13.3 2 5.5 12zm39.8 2.2c-6.5 15.8-16 26.8-21.2 31-5.5 4.5-9.5 2.7-6.5-3.8s19.3-46.5 12.7-55c-6.5-8.3-37-4.3-48-3.2-10.8 1-13 2-14-.3-2.3-5.7 21.7-15.5 37.5-17.5 15.7-1.8 41-.8 46 5.7 3.7 5.1 0 27.1-6.5 43.1z"]};var Au={prefix:"fab",iconName:"apple",icon:[384,512,[],"f179","M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"]};var Iu={prefix:"fab",iconName:"shopify",icon:[448,512,[],"e057","M388.32,104.1a4.66,4.66,0,0,0-4.4-4c-2,0-37.23-.8-37.23-.8s-21.61-20.82-29.62-28.83V503.2L442.76,472S388.72,106.5,388.32,104.1ZM288.65,70.47a116.67,116.67,0,0,0-7.21-17.61C271,32.85,255.42,22,237,22a15,15,0,0,0-4,.4c-.4-.8-1.2-1.2-1.6-2C223.4,11.63,213,7.63,200.58,8c-24,.8-48,18-67.25,48.83-13.61,21.62-24,48.84-26.82,70.06-27.62,8.4-46.83,14.41-47.23,14.81-14,4.4-14.41,4.8-16,18-1.2,10-38,291.82-38,291.82L307.86,504V65.67a41.66,41.66,0,0,0-4.4.4S297.86,67.67,288.65,70.47ZM233.41,87.69c-16,4.8-33.63,10.4-50.84,15.61,4.8-18.82,14.41-37.63,25.62-50,4.4-4.4,10.41-9.61,17.21-12.81C232.21,54.86,233.81,74.48,233.41,87.69ZM200.58,24.44A27.49,27.49,0,0,1,215,28c-6.4,3.2-12.81,8.41-18.81,14.41-15.21,16.42-26.82,42-31.62,66.45-14.42,4.41-28.83,8.81-42,12.81C131.33,83.28,163.75,25.24,200.58,24.44ZM154.15,244.61c1.6,25.61,69.25,31.22,73.25,91.66,2.8,47.64-25.22,80.06-65.65,82.47-48.83,3.2-75.65-25.62-75.65-25.62l10.4-44s26.82,20.42,48.44,18.82c14-.8,19.22-12.41,18.81-20.42-2-33.62-57.24-31.62-60.84-86.86-3.2-46.44,27.22-93.27,94.47-97.68,26-1.6,39.23,4.81,39.23,4.81L221.4,225.39s-17.21-8-37.63-6.4C154.15,221,153.75,239.8,154.15,244.61ZM249.42,82.88c0-12-1.6-29.22-7.21-43.63,18.42,3.6,27.22,24,31.23,36.43Q262.63,78.68,249.42,82.88Z"]};var Tu={prefix:"fab",iconName:"bitcoin",icon:[512,512,[],"f379","M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zm-141.651-35.33c4.937-32.999-20.191-50.739-54.55-62.573l11.146-44.702-27.213-6.781-10.851 43.524c-7.154-1.783-14.502-3.464-21.803-5.13l10.929-43.81-27.198-6.781-11.153 44.686c-5.922-1.349-11.735-2.682-17.377-4.084l.031-.14-37.53-9.37-7.239 29.062s20.191 4.627 19.765 4.913c11.022 2.751 13.014 10.044 12.68 15.825l-12.696 50.925c.76.194 1.744.473 2.829.907-.907-.225-1.876-.473-2.876-.713l-17.796 71.338c-1.349 3.348-4.767 8.37-12.471 6.464.271.395-19.78-4.937-19.78-4.937l-13.51 31.147 35.414 8.827c6.588 1.651 13.045 3.379 19.4 5.006l-11.262 45.213 27.182 6.781 11.153-44.733a1038.209 1038.209 0 0 0 21.687 5.627l-11.115 44.523 27.213 6.781 11.262-45.128c46.404 8.781 81.299 5.239 95.986-36.727 11.836-33.79-.589-53.281-25.004-65.991 17.78-4.098 31.174-15.792 34.747-39.949zm-62.177 87.179c-8.41 33.79-65.308 15.523-83.755 10.943l14.944-59.899c18.446 4.603 77.6 13.717 68.811 48.956zm8.417-87.667c-7.673 30.736-55.031 15.12-70.393 11.292l13.548-54.327c15.363 3.828 64.836 10.973 56.845 43.035z"]};var _u={prefix:"fab",iconName:"paypal",icon:[384,512,[],"f1ed","M111.4 295.9c-3.5 19.2-17.4 108.7-21.5 134-.3 1.8-1 2.5-3 2.5H12.3c-7.6 0-13.1-6.6-12.1-13.9L58.8 46.6c1.5-9.6 10.1-16.9 20-16.9 152.3 0 165.1-3.7 204 11.4 60.1 23.3 65.6 79.5 44 140.3-21.5 62.6-72.5 89.5-140.1 90.3-43.4.7-69.5-7-75.3 24.2zM357.1 152c-1.8-1.3-2.5-1.8-3 1.3-2 11.4-5.1 22.5-8.8 33.6-39.9 113.8-150.5 103.9-204.5 103.9-6.1 0-10.1 3.3-10.9 9.4-22.6 140.4-27.1 169.7-27.1 169.7-1 7.1 3.5 12.9 10.6 12.9h63.5c8.6 0 15.7-6.3 17.4-14.9.7-5.4-1.1 6.1 14.4-91.3 4.6-22 14.3-19.7 29.3-19.7 71 0 126.4-28.8 142.9-112.3 6.5-34.8 4.6-71.4-23.8-92.6z"]};var ku={prefix:"fab",iconName:"bootstrap",icon:[576,512,[],"f836","M333.5,201.4c0-22.1-15.6-34.3-43-34.3h-50.4v71.2h42.5C315.4,238.2,333.5,225,333.5,201.4z M517,188.6 c-9.5-30.9-10.9-68.8-9.8-98.1c1.1-30.5-22.7-58.5-54.7-58.5H123.7c-32.1,0-55.8,28.1-54.7,58.5c1,29.3-0.3,67.2-9.8,98.1 c-9.6,31-25.7,50.6-52.2,53.1v28.5c26.4,2.5,42.6,22.1,52.2,53.1c9.5,30.9,10.9,68.8,9.8,98.1c-1.1,30.5,22.7,58.5,54.7,58.5h328.7 c32.1,0,55.8-28.1,54.7-58.5c-1-29.3,0.3-67.2,9.8-98.1c9.6-31,25.7-50.6,52.1-53.1v-28.5C542.7,239.2,526.5,219.6,517,188.6z M300.2,375.1h-97.9V136.8h97.4c43.3,0,71.7,23.4,71.7,59.4c0,25.3-19.1,47.9-43.5,51.8v1.3c33.2,3.6,55.5,26.6,55.5,58.3 C383.4,349.7,352.1,375.1,300.2,375.1z M290.2,266.4h-50.1v78.4h52.3c34.2,0,52.3-13.7,52.3-39.5 C344.7,279.6,326.1,266.4,290.2,266.4z"]};var Fu={prefix:"fab",iconName:"ethereum",icon:[320,512,[],"f42e","M311.9 260.8L160 353.6 8 260.8 160 0l151.9 260.8zM160 383.4L8 290.6 160 512l152-221.4-152 92.8z"]};var Pu={prefix:"fab",iconName:"facebook-f",icon:[320,512,[],"f39e","M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"]};var Vu={prefix:"fab",iconName:"spotify",icon:[496,512,[],"f1bc","M248 8C111.1 8 0 119.1 0 256s111.1 248 248 248 248-111.1 248-248S384.9 8 248 8zm100.7 364.9c-4.2 0-6.8-1.3-10.7-3.6-62.4-37.6-135-39.2-206.7-24.5-3.9 1-9 2.6-11.9 2.6-9.7 0-15.8-7.7-15.8-15.8 0-10.3 6.1-15.2 13.6-16.8 81.9-18.1 165.6-16.5 237 26.2 6.1 3.9 9.7 7.4 9.7 16.5s-7.1 15.4-15.2 15.4zm26.9-65.6c-5.2 0-8.7-2.3-12.3-4.2-62.5-37-155.7-51.9-238.6-29.4-4.8 1.3-7.4 2.6-11.9 2.6-10.7 0-19.4-8.7-19.4-19.4s5.2-17.8 15.5-20.7c27.8-7.8 56.2-13.6 97.8-13.6 64.9 0 127.6 16.1 177 45.5 8.1 4.8 11.3 11 11.3 19.7-.1 10.8-8.5 19.5-19.4 19.5zm31-76.2c-5.2 0-8.4-1.3-12.9-3.9-71.2-42.5-198.5-52.7-280.9-29.7-3.6 1-8.1 2.6-12.9 2.6-13.2 0-23.3-10.3-23.3-23.6 0-13.6 8.4-21.3 17.4-23.9 35.2-10.3 74.6-15.2 117.5-15.2 73 0 149.5 15.2 205.4 47.8 7.8 4.5 12.9 10.7 12.9 22.6 0 13.6-11 23.3-23.2 23.3z"]};var Ou={prefix:"fab",iconName:"viber",icon:[512,512,[],"f409","M444 49.9C431.3 38.2 379.9.9 265.3.4c0 0-135.1-8.1-200.9 52.3C27.8 89.3 14.9 143 13.5 209.5c-1.4 66.5-3.1 191.1 117 224.9h.1l-.1 51.6s-.8 20.9 13 25.1c16.6 5.2 26.4-10.7 42.3-27.8 8.7-9.4 20.7-23.2 29.8-33.7 82.2 6.9 145.3-8.9 152.5-11.2 16.6-5.4 110.5-17.4 125.7-142 15.8-128.6-7.6-209.8-49.8-246.5zM457.9 287c-12.9 104-89 110.6-103 115.1-6 1.9-61.5 15.7-131.2 11.2 0 0-52 62.7-68.2 79-5.3 5.3-11.1 4.8-11-5.7 0-6.9.4-85.7.4-85.7-.1 0-.1 0 0 0-101.8-28.2-95.8-134.3-94.7-189.8 1.1-55.5 11.6-101 42.6-131.6 55.7-50.5 170.4-43 170.4-43 96.9.4 143.3 29.6 154.1 39.4 35.7 30.6 53.9 103.8 40.6 211.1zm-139-80.8c.4 8.6-12.5 9.2-12.9.6-1.1-22-11.4-32.7-32.6-33.9-8.6-.5-7.8-13.4.7-12.9 27.9 1.5 43.4 17.5 44.8 46.2zm20.3 11.3c1-42.4-25.5-75.6-75.8-79.3-8.5-.6-7.6-13.5.9-12.9 58 4.2 88.9 44.1 87.8 92.5-.1 8.6-13.1 8.2-12.9-.3zm47 13.4c.1 8.6-12.9 8.7-12.9.1-.6-81.5-54.9-125.9-120.8-126.4-8.5-.1-8.5-12.9 0-12.9 73.7.5 133 51.4 133.7 139.2zM374.9 329v.2c-10.8 19-31 40-51.8 33.3l-.2-.3c-21.1-5.9-70.8-31.5-102.2-56.5-16.2-12.8-31-27.9-42.4-42.4-10.3-12.9-20.7-28.2-30.8-46.6-21.3-38.5-26-55.7-26-55.7-6.7-20.8 14.2-41 33.3-51.8h.2c9.2-4.8 18-3.2 23.9 3.9 0 0 12.4 14.8 17.7 22.1 5 6.8 11.7 17.7 15.2 23.8 6.1 10.9 2.3 22-3.7 26.6l-12 9.6c-6.1 4.9-5.3 14-5.3 14s17.8 67.3 84.3 84.3c0 0 9.1.8 14-5.3l9.6-12c4.6-6 15.7-9.8 26.6-3.7 14.7 8.3 33.4 21.2 45.8 32.9 7 5.7 8.6 14.4 3.8 23.6z"]};var Ru={prefix:"fab",iconName:"tiktok",icon:[448,512,[],"e07b","M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"]};var Hu={prefix:"fab",iconName:"linkedin",icon:[448,512,[],"f08c","M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"]};var ju={prefix:"fab",iconName:"twitch",icon:[512,512,[],"f1e8","M391.17,103.47H352.54v109.7h38.63ZM285,103H246.37V212.75H285ZM120.83,0,24.31,91.42V420.58H140.14V512l96.53-91.42h77.25L487.69,256V0ZM449.07,237.75l-77.22,73.12H294.61l-67.6,64v-64H140.14V36.58H449.07Z"]};var Bu={prefix:"fab",iconName:"vimeo",icon:[448,512,[],"f40a","M403.2 32H44.8C20.1 32 0 52.1 0 76.8v358.4C0 459.9 20.1 480 44.8 480h358.4c24.7 0 44.8-20.1 44.8-44.8V76.8c0-24.7-20.1-44.8-44.8-44.8zM377 180.8c-1.4 31.5-23.4 74.7-66 129.4-44 57.2-81.3 85.8-111.7 85.8-18.9 0-34.8-17.4-47.9-52.3-25.5-93.3-36.4-148-57.4-148-2.4 0-10.9 5.1-25.4 15.2l-15.2-19.6c37.3-32.8 72.9-69.2 95.2-71.2 25.2-2.4 40.7 14.8 46.5 51.7 20.7 131.2 29.9 151 67.6 91.6 13.5-21.4 20.8-37.7 21.8-48.9 3.5-33.2-25.9-30.9-45.8-22.4 15.9-52.1 46.3-77.4 91.2-76 33.3.9 49 22.5 47.1 64.7z"]};var Uu={prefix:"fab",iconName:"dropbox",icon:[528,512,[],"f16b","M264.4 116.3l-132 84.3 132 84.3-132 84.3L0 284.1l132.3-84.3L0 116.3 132.3 32l132.1 84.3zM131.6 395.7l132-84.3 132 84.3-132 84.3-132-84.3zm132.8-111.6l132-84.3-132-83.6L395.7 32 528 116.3l-132.3 84.3L528 284.8l-132.3 84.3-131.3-85z"]},$u={prefix:"fab",iconName:"instagram",icon:[448,512,[],"f16d","M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"]};var qu={prefix:"fab",iconName:"facebook",icon:[512,512,[62e3],"f09a","M512 256C512 114.6 397.4 0 256 0S0 114.6 0 256C0 376 82.7 476.8 194.2 504.5V334.2H141.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H287V510.1C413.8 494.8 512 386.9 512 256h0z"]};var Gu={prefix:"fab",iconName:"whatsapp",icon:[448,512,[],"f232","M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"]};var Wu={prefix:"fab",iconName:"gitter",icon:[384,512,[],"f426","M66.4 322.5H16V0h50.4v322.5zM166.9 76.1h-50.4V512h50.4V76.1zm100.6 0h-50.4V512h50.4V76.1zM368 76h-50.4v247H368V76z"]};var Zu={prefix:"fab",iconName:"html5",icon:[384,512,[],"f13b","M0 32l34.9 395.8L191.5 480l157.6-52.2L384 32H0zm308.2 127.9H124.4l4.1 49.4h175.6l-13.6 148.4-97.9 27v.3h-1.1l-98.7-27.3-6-75.8h47.7L138 320l53.5 14.5 53.7-14.5 6-62.2H84.3L71.5 112.2h241.1l-4.4 47.7z"]};var Yu={prefix:"fab",iconName:"snapchat",icon:[512,512,[62124,"snapchat-ghost"],"f2ab","M496.926,366.6c-3.373-9.176-9.8-14.086-17.112-18.153-1.376-.806-2.641-1.451-3.72-1.947-2.182-1.128-4.414-2.22-6.634-3.373-22.8-12.09-40.609-27.341-52.959-45.42a102.889,102.889,0,0,1-9.089-16.12c-1.054-3.013-1-4.724-.248-6.287a10.221,10.221,0,0,1,2.914-3.038c3.918-2.591,7.96-5.22,10.7-6.993,4.885-3.162,8.754-5.667,11.246-7.44,9.362-6.547,15.909-13.5,20-21.278a42.371,42.371,0,0,0,2.1-35.191c-6.2-16.318-21.613-26.449-40.287-26.449a55.543,55.543,0,0,0-11.718,1.24c-1.029.224-2.059.459-************-11.16-.074-22.94-1.066-34.534-3.522-40.758-17.794-62.123-32.674-79.16A130.167,130.167,0,0,0,332.1,36.443C309.515,23.547,283.91,17,256,17S202.6,23.547,180,36.443a129.735,129.735,0,0,0-33.281,26.783c-14.88,17.038-29.152,38.44-32.673,79.161-.992,11.594-1.24,23.435-1.079,34.533-1-.26-2.021-.5-3.051-.719a55.461,55.461,0,0,0-11.717-1.24c-18.687,0-34.125,10.131-40.3,26.449a42.423,42.423,0,0,0,2.046,35.228c4.105,7.774,10.652,14.731,20.014,21.278,2.48,1.736,6.361,4.24,11.246,7.44,2.641,1.711,6.5,4.216,10.28,6.72a11.054,11.054,0,0,1,3.3,3.311c.794,1.624.818,3.373-.36,6.6a102.02,102.02,0,0,1-8.94,15.785c-12.077,17.669-29.363,32.648-51.434,44.639C32.355,348.608,20.2,352.75,15.069,366.7c-3.868,10.528-1.339,22.506,8.494,32.6a49.137,49.137,0,0,0,12.4,9.387,134.337,134.337,0,0,0,30.342,12.139,20.024,20.024,0,0,1,6.126,2.741c3.583,3.137,3.075,7.861,7.849,14.78a34.468,34.468,0,0,0,8.977,9.127c10.019,6.919,21.278,7.353,33.207,7.811,10.776.41,22.989.881,36.939,5.481,5.778,1.91,11.78,5.605,18.736,9.92C194.842,480.951,217.707,495,255.973,495s61.292-14.123,78.118-24.428c6.907-4.24,12.872-7.9,18.489-9.758,13.949-4.613,26.163-5.072,36.939-5.481,11.928-.459,23.187-.893,33.206-7.812a34.584,34.584,0,0,0,10.218-11.16c3.434-5.84,3.348-9.919,6.572-12.771a18.971,18.971,0,0,1,5.753-2.629A134.893,134.893,0,0,0,476.02,408.71a48.344,48.344,0,0,0,13.019-10.193l.124-.149C498.389,388.5,500.708,376.867,496.926,366.6Zm-34.013,18.277c-20.745,11.458-34.533,10.23-45.259,17.137-9.114,5.865-3.72,18.513-10.342,23.076-8.134,5.617-32.177-.4-63.239,9.858-25.618,8.469-41.961,32.822-88.038,32.822s-62.036-24.3-88.076-32.884c-31-10.255-55.092-4.241-63.239-9.858-6.609-4.563-1.24-17.211-10.341-23.076-10.739-6.907-24.527-5.679-45.26-17.075-13.206-7.291-5.716-11.8-1.314-13.937,75.143-36.381,87.133-92.552,87.666-96.719.645-5.046,1.364-9.014-4.191-14.148-5.369-4.96-29.189-19.7-35.8-24.316-10.937-7.638-15.748-15.264-12.2-24.638,2.48-6.485,8.531-8.928,14.879-8.928a27.643,27.643,0,0,1,5.965.67c12,2.6,23.659,8.617,30.392,10.242a10.749,10.749,0,0,0,2.48.335c3.6,0,4.86-1.811,4.612-5.927-.768-13.132-2.628-38.725-.558-62.644,2.84-32.909,13.442-49.215,26.04-63.636,6.051-6.932,34.484-36.976,88.857-36.976s82.88,29.92,88.931,36.827c12.611,14.421,23.225,30.727,26.04,63.636,2.071,23.919.285,49.525-.558,62.644-.285,4.327,1.017,5.927,4.613,5.927a10.648,10.648,0,0,0,2.48-.335c6.745-1.624,18.4-7.638,30.4-10.242a27.641,27.641,0,0,1,5.964-.67c6.386,0,12.4,2.48,14.88,8.928,3.546,9.374-1.24,17-12.189,24.639-6.609,4.612-30.429,19.343-35.8,24.315-5.568,5.134-4.836,9.1-4.191,14.149.533,4.228,12.511,60.4,87.666,96.718C468.629,373.011,476.119,377.524,462.913,384.877Z"]};var Qu={prefix:"fab",iconName:"android",icon:[576,512,[],"f17b","M420.55,301.93a24,24,0,1,1,24-24,24,24,0,0,1-24,24m-265.1,0a24,24,0,1,1,24-24,24,24,0,0,1-24,24m273.7-144.48,47.94-83a10,10,0,1,0-17.27-10h0l-48.54,84.07a301.25,301.25,0,0,0-246.56,0L116.18,64.45a10,10,0,1,0-17.27,10h0l47.94,83C64.53,202.22,8.24,285.55,0,384H576c-8.24-98.45-64.54-181.78-146.85-226.55"]};var Ku={prefix:"fab",iconName:"cc-amex",icon:[576,512,[],"f1f3","M0 432c0 26.5 21.5 48 48 48H528c26.5 0 48-21.5 48-48v-1.1H514.3l-31.9-35.1-31.9 35.1H246.8V267.1H181L262.7 82.4h78.6l28.1 63.2V82.4h97.2L483.5 130l17-47.6H576V80c0-26.5-21.5-48-48-48H48C21.5 32 0 53.5 0 80V432zm440.4-21.7L482.6 364l42 46.3H576l-68-72.1 68-72.1H525.4l-42 46.7-41.5-46.7H390.5L458 338.6l-67.4 71.6V377.1h-83V354.9h80.9V322.6H307.6V300.2h83V267.1h-122V410.3H440.4zm96.3-72L576 380.2V296.9l-39.3 41.4zm-36.3-92l36.9-100.6V246.3H576V103H515.8l-32.2 89.3L451.7 103H390.5V246.1L327.3 103H276.1L213.7 246.3h43l11.9-28.7h65.9l12 28.7h82.7V146L466 246.3h34.4zM282 185.4l19.5-46.9 19.4 46.9H282z"]};var Xu={prefix:"fab",iconName:"github",icon:[496,512,[],"f09b","M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"]};var Ju={prefix:"fab",iconName:"youtube",icon:[576,512,[61802],"f167","M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"]};var ed={prefix:"fab",iconName:"twitter",icon:[512,512,[],"f099","M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"]};var td={prefix:"fab",iconName:"pinterest",icon:[496,512,[],"f0d2","M496 256c0 137-111 248-248 248-25.6 0-50.2-3.9-73.4-11.1 10.1-16.5 25.2-43.5 30.8-65 3-11.6 15.4-59 15.4-59 8.1 15.4 31.7 28.5 56.8 28.5 74.8 0 128.7-68.8 128.7-154.3 0-81.9-66.9-143.2-152.9-143.2-107 0-163.9 71.8-163.9 150.1 0 36.4 19.4 81.7 50.3 96.1 4.7 2.2 7.2 1.2 8.3-3.3.8-3.4 5-20.3 6.9-28.1.6-2.5.3-4.7-1.7-7.1-10.1-12.5-18.3-35.3-18.3-56.6 0-54.7 41.4-107.6 112-107.6 60.9 0 103.6 41.5 103.6 100.9 0 67.1-33.9 113.6-78 113.6-24.3 0-42.6-20.1-36.7-44.8 7-29.5 20.5-61.3 20.5-82.6 0-19-10.2-34.9-31.4-34.9-24.9 0-44.9 25.7-44.9 60.2 0 22 7.4 36.8 7.4 36.8s-24.5 103.8-29 123.2c-5 21.4-3 51.6-.9 71.2C65.4 450.9 0 361.1 0 256 0 119 111 8 248 8s248 111 248 248z"]};var cd={prefix:"fab",iconName:"telegram",icon:[496,512,[62462,"telegram-plane"],"f2c6","M248,8C111.033,8,0,119.033,0,256S111.033,504,248,504,496,392.967,496,256,384.967,8,248,8ZM362.952,176.66c-3.732,39.215-19.881,134.378-28.1,178.3-3.476,18.584-10.322,24.816-16.948,25.425-14.4,1.326-25.338-9.517-39.287-18.661-21.827-14.308-34.158-23.215-55.346-37.177-24.485-16.135-8.612-25,5.342-39.5,3.652-3.793,67.107-61.51,68.335-66.746.153-.655.3-3.1-1.154-4.384s-3.59-.849-5.135-.5q-3.283.746-104.608,69.142-14.845,10.194-26.894,9.934c-8.855-.191-25.888-5.006-38.551-9.123-15.531-5.048-27.875-7.717-26.8-16.291q.84-6.7,18.45-13.7,108.446-47.248,144.628-62.3c68.872-28.647,83.183-33.623,92.511-33.789,2.052-.034,6.639.474,9.61,2.885a10.452,10.452,0,0,1,3.53,6.716A43.765,43.765,0,0,1,362.952,176.66Z"]};var nd={prefix:"fab",iconName:"slack",icon:[448,512,[62447,"slack-hash"],"f198","M94.12 315.1c0 25.9-21.16 47.06-47.06 47.06S0 341 0 315.1c0-25.9 21.16-47.06 47.06-47.06h47.06v47.06zm23.72 0c0-25.9 21.16-47.06 47.06-47.06s47.06 21.16 47.06 47.06v117.84c0 25.9-21.16 47.06-47.06 47.06s-47.06-21.16-47.06-47.06V315.1zm47.06-188.98c-25.9 0-47.06-21.16-47.06-47.06S139 32 164.9 32s47.06 21.16 47.06 47.06v47.06H164.9zm0 23.72c25.9 0 47.06 21.16 47.06 47.06s-21.16 47.06-47.06 47.06H47.06C21.16 243.96 0 222.8 0 196.9s21.16-47.06 47.06-47.06H164.9zm188.98 47.06c0-25.9 21.16-47.06 47.06-47.06 25.9 0 47.06 21.16 47.06 47.06s-21.16 47.06-47.06 47.06h-47.06V196.9zm-23.72 0c0 25.9-21.16 47.06-47.06 47.06-25.9 0-47.06-21.16-47.06-47.06V79.06c0-25.9 21.16-47.06 47.06-47.06 25.9 0 47.06 21.16 47.06 47.06V196.9zM283.1 385.88c25.9 0 47.06 21.16 47.06 47.06 0 25.9-21.16 47.06-47.06 47.06-25.9 0-47.06-21.16-47.06-47.06v-47.06h47.06zm0-23.72c-25.9 0-47.06-21.16-47.06-47.06 0-25.9 21.16-47.06 47.06-47.06h117.84c25.9 0 47.06 21.16 47.06 47.06 0 25.9-21.16 47.06-47.06 47.06H283.1z"]};var sd={prefix:"fab",iconName:"medium",icon:[640,512,[62407,"medium-m"],"f23a","M180.5,74.262C80.813,74.262,0,155.633,0,256S80.819,437.738,180.5,437.738,361,356.373,361,256,280.191,74.262,180.5,74.262Zm288.25,10.646c-49.845,0-90.245,76.619-90.245,171.095s40.406,171.1,90.251,171.1,90.251-76.619,90.251-171.1H559C559,161.5,518.6,84.908,468.752,84.908Zm139.506,17.821c-17.526,0-31.735,68.628-31.735,153.274s14.2,153.274,31.735,153.274S640,340.631,640,256C640,171.351,625.785,102.729,608.258,102.729Z"]};var od={faAmazon:Eu,faAndroid:Qu,faApple:Au,faAppStore:yu,faBehance:Nu,faBitcoin:Tu,faBootstrap:ku,faCcAmex:Ku,faCcMastercard:bu,faCcVisa:Cu,faDiscord:Du,faDropbox:Uu,faEbay:Su,faEthereum:Fu,faFacebook:qu,faFacebookF:Pu,faGithub:Xu,faGoogle:zu,faHtml5:Zu,faInstagram:$u,faLinkedin:Hu,faMedium:sd,faPaypal:_u,faPinterest:td,faReddit:wu,faShopify:Iu,faSkype:xu,faSlack:nd,faSnapchat:Yu,faSpotify:Vu,faTelegram:cd,faTiktok:Ru,faTwitch:ju,faTwitter:ed,faViber:Ou,faVimeo:Bu,faWeixin:vu,faWhatsapp:Gu,faYoutube:Ju,faCreativeCommons:Lu,faGitter:Wu},oP={accessibleIcon:"#1a75ff",accusoft:"#186f93",acquisitionsIncorporated:"#006666",adn:"#ec1b23",adobe:"#ff0000",adversal:"#1e1e1e",affiliatetheme:"#000000",airbnb:"#ff5a5f",algolia:"#5468ff",alipay:"#00a0e9",amazon:"#ff9900",amazonPay:"#ff9900",amilia:"#0088cc",android:"#3ddc84",angellist:"#000000",angrycreative:"#f96400",angular:"#dd0031",appStore:"#007aff",apple:"#000000",applePay:"#000000",artstation:"#13aff0",asymmetrik:"#0071bc",atlassian:"#0052cc",audible:"#f8991c",autoprefixer:"#ff2b2b",avianex:"#7cc576",aviato:"#f06595",aws:"#ff9900",bandcamp:"#408294",battleNet:"#00aeff",behance:"#1769ff",bilibili:"#00a1d6",bimobject:"#0066b1",bitbucket:"#0052cc",bitcoin:"#f7931a",bity:"#00c2f2",blackberry:"#000000",blogger:"#ff5722",bluetooth:"#0082fc",bootstrap:"#7952b3",bots:"#e10098",brave:"#fb542b",buffer:"#168eea",buyNLarge:"#0077c0",buysellads:"#cd2027",canadianMapleLeaf:"#ff0000",ccAmex:"#2E77BC",ccMastercard:"#EB001B",ccVisa:"#142787",centercode:"#003c9f",chrome:"#4285f4",cloudscale:"#212121",cloudsmith:"#3e82f7",cloudversify:"#2f80ed",codepen:"#000000",codiepie:"#e14329",confluence:"#172b4d",connectdevelop:"#003e54",contao:"#f47f20",cottonBureau:"#f8df00",cpanel:"#ff6c2c",creativeCommons:"#ef9421",css3:"#264de4",css3Alt:"#1572b6",cuttlefish:"#7b8b8e",dailymotion:"#0066dc",dashcube:"#2e2e2e",deezer:"#ef5466",delicious:"#39f",deploydog:"#23a2f2",deskpro:"#1e1e1e",dev:"#0a0a0a",deviantart:"#05cc47",dhl:"#ba0c2f",diaspora:"#000000",digg:"#005be2",digitalOcean:"#0080ff",discord:"#5865f2",discourse:"#000000",dochub:"#008489",docker:"#0db7ed",draft2digital:"#d3302f",dribbble:"#ea4c89",dropbox:"#0061ff",drupal:"#0678be",dyalog:"#5a3a98",earlybirds:"#ff5f00",ebay:"#e53238",edge:"#0078d7",elementor:"#92003b",ello:"#000000",empire:"#000000",envira:"#7ac143",erlang:"#a90533",ethereum:"#3c3c3d",etsy:"#f16521",evernote:"#00a82d",expeditedssl:"#343538",facebook:"#1877f2",facebookF:"#1877f2",firefox:"#ff7139",firstOrder:"#000000",flickr:"#ff0084",flipboard:"#e12828",fly:"#ed1c24",fontAwesome:"#228ae6",fonticons:"#0b0b0b",fortAwesome:"#0e2a47",foursquare:"#f94877",freeCodeCamp:"#006400",freebsd:"#ab2b28",fulcrum:"#ff2a2a",galacticRepublic:"#000000",getPocket:"#ef3f56",gg:"#171717",git:"#f34f29",github:"#181717",gitkraken:"#179287",gitlab:"#fc6d26",gitter:"#ed1965",glide:"#00b4f1",gofore:"#ff6c00",goodreads:"#372213",google:"#4285f4",googleDrive:"#34a853",googlePay:"#000000",googlePlay:"#3bccff",googlePlus:"#db4437",grunt:"#fba919",gulp:"#cf4647",hackerNews:"#ff6600",hackerrank:"#2ec866",heroku:"#430098",html5:"#e34f26",hubspot:"#ff7a59",imdb:"#f5c518",instagram:"#e1306c",intercom:"#1f8ded",internetExplorer:"#0076d6",invision:"#ff3366",itunes:"#ea4cc0",java:"#007396",jira:"#0052cc",joomla:"#f44321",js:"#f7df1e",jsfiddle:"#4679bd",kaggle:"#20beff",keybase:"#33a0ff",kickstarter:"#2bde73",korvue:"#003366",laravel:"#ff2d20",lastfm:"#d51007",less:"#1d365d",line:"#00c300",linkedin:"#0077b5",linux:"#fcc624",lyft:"#ff00bf",magento:"#ee672f",mailchimp:"#ffe01b",markdown:"#000000",mastodon:"#6364ff",medium:"#00ab6c",mendeley:"#9d1620",microblog:"#ffd300",microsoft:"#666666",mix:"#ff8126",mixcloud:"#52aad8",monero:"#ff6600",napster:"#0d1a2b",neos:"#0088cc",npm:"#cb3837",node:"#339933",nodeJs:"#339933",npmJs:"#cb3837",nintendoSwitch:"#e60012",odnoklassniki:"#ed812b",opencart:"#2ed0e4",opera:"#ff1b2d",page4:"#e90000",patreon:"#f96854",paypal:"#003087",periscope:"#40a4c4",phabricator:"#4a5f88",phoenixFramework:"#f0653c",php:"#777bb4",piedPiper:"#1f8dd6",pinterest:"#e60023",playstation:"#003791",productHunt:"#da552f",python:"#306998",quora:"#a82400",react:"#61dafb",reddit:"#ff4500",redhat:"#e00",rocketchat:"#f80061",safari:"#1b94e0",sass:"#cc6699",scala:"#dc322f",shopify:"#7AB55C",sketch:"#f7b500",skype:"#00aff0",slack:"#4a154b",snapchat:"#fffc00",soundcloud:"#ff3300",spotify:"#1db954",stackExchange:"#285e8e",stackOverflow:"#f48024",steam:"#000000",stripe:"#635bff",stumbleupon:"#eb4924",telegram:"#0088cc",tencentWeibo:"#74af2c",tiktok:"#000000",tumblr:"#35465c",twitch:"#9146ff",twitter:"#1da1f2",uber:"#09091a",ubuntu:"#e95420",uikit:"#2396f3",uniregistry:"#007fff",unity:"#222c37",unsplash:"#000000",untappd:"#ffc000",ups:"#351c15",usps:"#333399",vaadin:"#00b4f0",viber:"#665cac",vimeo:"#1ab7ea",vine:"#11b48a",vk:"#4680c2",vuejs:"#42b883",weixin:"#07C160",whatsapp:"#25d366",wikipediaW:"#000000",windows:"#00a4ef",wordpress:"#21759b",xbox:"#107c10",xing:"#026466",yahoo:"#430297",yelp:"#af0606",youtube:"#ff0000"};var fP=(()=>{let e=class e{constructor(c){c.addIconPacks(Mu,od)}};e.\u0275fac=function(s){return new(s||e)(L(Rc))},e.\u0275mod=I1({type:e}),e.\u0275inj=A1({imports:[Ba]});let t=e;return t})();var F4=class{},id=(()=>{let e=class e extends F4{getTranslation(c){return l2({})}};I(e,"\u0275fac",(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})()),I(e,"\u0275prov",y({token:e,factory:e.\u0275fac}));let t=e;return t})(),B0=class{},rd=(()=>{let e=class e{handle(c){return c.key}};I(e,"\u0275fac",function(s){return new(s||e)}),I(e,"\u0275prov",y({token:e,factory:e.\u0275fac}));let t=e;return t})();function v8(t,e){if(t===e)return!0;if(t===null||e===null)return!1;if(t!==t&&e!==e)return!0;let n=typeof t,c=typeof e,s,o,i;if(n==c&&n=="object")if(Array.isArray(t)){if(!Array.isArray(e))return!1;if((s=t.length)==e.length){for(o=0;o<s;o++)if(!v8(t[o],e[o]))return!1;return!0}}else{if(Array.isArray(e))return!1;i=Object.create(null);for(o in t){if(!v8(t[o],e[o]))return!1;i[o]=!0}for(o in e)if(!(o in i)&&typeof e[o]<"u")return!1;return!0}return!1}function a3(t){return typeof t<"u"&&t!==null}function $0(t){return L8(t)&&!Xc(t)&&t!==null}function L8(t){return typeof t=="object"}function Xc(t){return Array.isArray(t)}function Jc(t){return typeof t=="string"}function xN(t){return typeof t=="function"}function qc(t,e){let n=Object.assign({},t);return L8(t)?(L8(t)&&L8(e)&&Object.keys(e).forEach(c=>{$0(e[c])?c in t?n[c]=qc(t[c],e[c]):Object.assign(n,{[c]:e[c]}):Object.assign(n,{[c]:e[c]})}),n):qc({},e)}function Gc(t,e){let n=e.split(".");e="";do e+=n.shift(),a3(t)&&a3(t[e])&&($0(t[e])||Xc(t[e])||!n.length)?(t=t[e],e=""):n.length?e+=".":t=void 0;while(n.length);return t}function NN(t,e,n){let c=e.split("."),s=t;for(let o=0;o<c.length;o++){let i=c[o];o===c.length-1?s[i]=n:((!s[i]||!$0(s[i]))&&(s[i]={}),s=s[i])}}var P4=class{},ad=(()=>{let e=class e extends P4{constructor(){super(...arguments);I(this,"templateMatcher",/{{\s?([^{}\s]*)\s?}}/g)}interpolate(s,o){if(Jc(s))return this.interpolateString(s,o);if(xN(s))return this.interpolateFunction(s,o)}interpolateFunction(s,o){return s(o)}interpolateString(s,o){return o?s.replace(this.templateMatcher,(i,r)=>{let a=Gc(o,r);return a3(a)?a:i}):s}};I(e,"\u0275fac",(()=>{let s;return function(i){return(s||(s=g1(e)))(i||e)}})()),I(e,"\u0275prov",y({token:e,factory:e.\u0275fac}));let t=e;return t})(),V4=class{},ld=(()=>{let e=class e extends V4{compile(c,s){return c}compileTranslations(c,s){return c}};I(e,"\u0275fac",(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})()),I(e,"\u0275prov",y({token:e,factory:e.\u0275fac}));let t=e;return t})(),U0=class{constructor(){I(this,"defaultLang");I(this,"currentLang",this.defaultLang);I(this,"translations",{});I(this,"langs",[]);I(this,"onTranslationChange",new l1);I(this,"onLangChange",new l1);I(this,"onDefaultLangChange",new l1)}},Wc=new w("ISOLATE_TRANSLATE_SERVICE"),Zc=new w("USE_DEFAULT_LANG"),Yc=new w("DEFAULT_LANGUAGE"),Qc=new w("USE_EXTEND"),j0=t=>b3(t)?t:l2(t),Kc=(()=>{let e=class e{constructor(c,s,o,i,r,a=!0,l=!1,f=!1,u){I(this,"store");I(this,"currentLoader");I(this,"compiler");I(this,"parser");I(this,"missingTranslationHandler");I(this,"useDefaultLang");I(this,"extend");I(this,"loadingTranslations");I(this,"pending",!1);I(this,"_translationRequests",{});I(this,"lastUseLanguage",null);this.store=c,this.currentLoader=s,this.compiler=o,this.parser=i,this.missingTranslationHandler=r,this.useDefaultLang=a,this.extend=f,l&&(this.store=new U0),u&&this.setDefaultLang(u)}get onTranslationChange(){return this.store.onTranslationChange}get onLangChange(){return this.store.onLangChange}get onDefaultLangChange(){return this.store.onDefaultLangChange}get defaultLang(){return this.store.defaultLang}set defaultLang(c){this.store.defaultLang=c}get currentLang(){return this.store.currentLang}set currentLang(c){this.store.currentLang=c}get langs(){return this.store.langs}set langs(c){this.store.langs=c}get translations(){return this.store.translations}set translations(c){this.store.translations=c}setDefaultLang(c){if(c===this.defaultLang)return;let s=this.retrieveTranslations(c);typeof s<"u"?(this.defaultLang==null&&(this.defaultLang=c),s.pipe($2(1)).subscribe(()=>{this.changeDefaultLang(c)})):this.changeDefaultLang(c)}getDefaultLang(){return this.defaultLang}use(c){if(this.lastUseLanguage=c,c===this.currentLang)return l2(this.translations[c]);this.currentLang||(this.currentLang=c);let s=this.retrieveTranslations(c);return b3(s)?(s.pipe($2(1)).subscribe(()=>{this.changeLang(c)}),s):(this.changeLang(c),l2(this.translations[c]))}changeLang(c){c===this.lastUseLanguage&&(this.currentLang=c,this.onLangChange.emit({lang:c,translations:this.translations[c]}),this.defaultLang==null&&this.changeDefaultLang(c))}retrieveTranslations(c){if(typeof this.translations[c]>"u"||this.extend)return this._translationRequests[c]=this._translationRequests[c]||this.loadAndCompileTranslations(c),this._translationRequests[c]}getTranslation(c){return this.loadAndCompileTranslations(c)}loadAndCompileTranslations(c){this.pending=!0;let s=this.currentLoader.getTranslation(c).pipe(X4(1),$2(1));return this.loadingTranslations=s.pipe(o1(o=>this.compiler.compileTranslations(o,c)),X4(1),$2(1)),this.loadingTranslations.subscribe({next:o=>{this.translations[c]=this.extend&&this.translations[c]?Z(Z({},o),this.translations[c]):o,this.updateLangs(),this.pending=!1},error:o=>{this.pending=!1}}),s}setTranslation(c,s,o=!1){let i=this.compiler.compileTranslations(s,c);(o||this.extend)&&this.translations[c]?this.translations[c]=qc(this.translations[c],i):this.translations[c]=i,this.updateLangs(),this.onTranslationChange.emit({lang:c,translations:this.translations[c]})}getLangs(){return this.langs}addLangs(c){let s=c.filter(o=>!this.langs.includes(o));s.length>0&&(this.langs=[...this.langs,...s])}updateLangs(){this.addLangs(Object.keys(this.translations))}getParsedResultForKey(c,s,o){let i;if(c&&(i=this.runInterpolation(Gc(c,s),o)),i===void 0&&this.defaultLang!=null&&this.defaultLang!==this.currentLang&&this.useDefaultLang&&(i=this.runInterpolation(Gc(this.translations[this.defaultLang],s),o)),i===void 0){let r={key:s,translateService:this};typeof o<"u"&&(r.interpolateParams=o),i=this.missingTranslationHandler.handle(r)}return i!==void 0?i:s}runInterpolation(c,s){if(Xc(c))return c.map(o=>this.runInterpolation(o,s));if($0(c)){let o={};for(let i in c){let r=this.runInterpolation(c[i],s);r!==void 0&&(o[i]=r)}return o}else return this.parser.interpolate(c,s)}getParsedResult(c,s,o){if(s instanceof Array){let i={},r=!1;for(let l of s)i[l]=this.getParsedResultForKey(c,l,o),r=r||b3(i[l]);if(!r)return i;let a=s.map(l=>j0(i[l]));return e0(a).pipe(o1(l=>{let f={};return l.forEach((u,d)=>{f[s[d]]=u}),f}))}return this.getParsedResultForKey(c,s,o)}get(c,s){if(!a3(c)||!c.length)throw new Error('Parameter "key" is required and cannot be empty');return this.pending?this.loadingTranslations.pipe(Y4(o=>j0(this.getParsedResult(o,c,s)))):j0(this.getParsedResult(this.translations[this.currentLang],c,s))}getStreamOnTranslationChange(c,s){if(!a3(c)||!c.length)throw new Error('Parameter "key" is required and cannot be empty');return z3(A6(()=>this.get(c,s)),this.onTranslationChange.pipe(y3(o=>{let i=this.getParsedResult(o.translations,c,s);return j0(i)})))}stream(c,s){if(!a3(c)||!c.length)throw new Error('Parameter "key" required');return z3(A6(()=>this.get(c,s)),this.onLangChange.pipe(y3(o=>{let i=this.getParsedResult(o.translations,c,s);return j0(i)})))}instant(c,s){if(!a3(c)||c.length===0)throw new Error('Parameter "key" is required and cannot be empty');let o=this.getParsedResult(this.translations[this.currentLang],c,s);return b3(o)?Array.isArray(c)?c.reduce((i,r)=>(i[r]=r,i),{}):c:o}set(c,s,o=this.currentLang){NN(this.translations[o],c,Jc(s)?this.compiler.compile(s,o):this.compiler.compileTranslations(s,o)),this.updateLangs(),this.onTranslationChange.emit({lang:o,translations:this.translations[o]})}changeDefaultLang(c){this.defaultLang=c,this.onDefaultLangChange.emit({lang:c,translations:this.translations[c]})}reloadLang(c){return this.resetLang(c),this.loadAndCompileTranslations(c)}resetLang(c){delete this._translationRequests[c],delete this.translations[c]}getBrowserLang(){if(typeof window>"u"||!window.navigator)return;let c=this.getBrowserCultureLang();return c?c.split(/[-_]/)[0]:void 0}getBrowserCultureLang(){if(!(typeof window>"u"||typeof window.navigator>"u"))return window.navigator.languages?window.navigator.languages[0]:window.navigator.language||window.navigator.browserLanguage||window.navigator.userLanguage}};I(e,"\u0275fac",function(s){return new(s||e)(L(U0),L(F4),L(V4),L(P4),L(B0),L(Zc),L(Wc),L(Qc),L(Yc))}),I(e,"\u0275prov",y({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var vP=(()=>{let e=class e{constructor(c,s){I(this,"translate");I(this,"_ref");I(this,"value","");I(this,"lastKey",null);I(this,"lastParams",[]);I(this,"onTranslationChange");I(this,"onLangChange");I(this,"onDefaultLangChange");this.translate=c,this._ref=s}updateValue(c,s,o){let i=r=>{this.value=r!==void 0?r:c,this.lastKey=c,this._ref.markForCheck()};if(o){let r=this.translate.getParsedResult(o,c,s);b3(r)?r.subscribe(i):i(r)}this.translate.get(c,s).subscribe(i)}transform(c,...s){if(!c||!c.length)return c;if(v8(c,this.lastKey)&&v8(s,this.lastParams))return this.value;let o;if(a3(s[0])&&s.length)if(Jc(s[0])&&s[0].length){let i=s[0].replace(/(')?([a-zA-Z0-9_]+)(')?(\s)?:/g,'"$2":').replace(/:(\s)?(')(.*?)(')/g,':"$3"');try{o=JSON.parse(i)}catch(r){throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${s[0]}`)}}else $0(s[0])&&(o=s[0]);return this.lastKey=c,this.lastParams=s,this.updateValue(c,o),this._dispose(),this.onTranslationChange||(this.onTranslationChange=this.translate.onTranslationChange.subscribe(i=>{this.lastKey&&i.lang===this.translate.currentLang&&(this.lastKey=null,this.updateValue(c,o,i.translations))})),this.onLangChange||(this.onLangChange=this.translate.onLangChange.subscribe(i=>{this.lastKey&&(this.lastKey=null,this.updateValue(c,o,i.translations))})),this.onDefaultLangChange||(this.onDefaultLangChange=this.translate.onDefaultLangChange.subscribe(()=>{this.lastKey&&(this.lastKey=null,this.updateValue(c,o))})),this.value}_dispose(){typeof this.onTranslationChange<"u"&&(this.onTranslationChange.unsubscribe(),this.onTranslationChange=void 0),typeof this.onLangChange<"u"&&(this.onLangChange.unsubscribe(),this.onLangChange=void 0),typeof this.onDefaultLangChange<"u"&&(this.onDefaultLangChange.unsubscribe(),this.onDefaultLangChange=void 0)}ngOnDestroy(){this._dispose()}};I(e,"\u0275fac",function(s){return new(s||e)(z(Kc,16),z(R3,16))}),I(e,"\u0275pipe",S2({name:"translate",type:e,pure:!1,standalone:!0})),I(e,"\u0275prov",y({token:e,factory:e.\u0275fac}));let t=e;return t})();var yP=(()=>{let e=class e{static forRoot(c={}){return{ngModule:e,providers:[c.loader||{provide:F4,useClass:id},c.compiler||{provide:V4,useClass:ld},c.parser||{provide:P4,useClass:ad},c.missingTranslationHandler||{provide:B0,useClass:rd},U0,{provide:Wc,useValue:c.isolate},{provide:Zc,useValue:c.useDefaultLang},{provide:Qc,useValue:c.extend},{provide:Yc,useValue:c.defaultLanguage},Kc]}}static forChild(c={}){return{ngModule:e,providers:[c.loader||{provide:F4,useClass:id},c.compiler||{provide:V4,useClass:ld},c.parser||{provide:P4,useClass:ad},c.missingTranslationHandler||{provide:B0,useClass:rd},{provide:Wc,useValue:c.isolate},{provide:Zc,useValue:c.useDefaultLang},{provide:Qc,useValue:c.extend},{provide:Yc,useValue:c.defaultLanguage},Kc]}}};I(e,"\u0275fac",function(s){return new(s||e)}),I(e,"\u0275mod",I1({type:e})),I(e,"\u0275inj",A1({}));let t=e;return t})();var Ld=(()=>{let e=class e{constructor(c,s){this._renderer=c,this._elementRef=s,this.onChange=o=>{},this.onTouched=()=>{}}setProperty(c,s){this._renderer.setProperty(this._elementRef.nativeElement,c,s)}registerOnTouched(c){this.onTouched=c}registerOnChange(c){this.onChange=c}setDisabledState(c){this.setProperty("disabled",c)}};e.\u0275fac=function(s){return new(s||e)(z(Z1),z(h1))},e.\u0275dir=H({type:e});let t=e;return t})(),j4=(()=>{let e=class e extends Ld{};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,features:[n1]});let t=e;return t})(),u3=new w(""),wN={provide:u3,useExisting:u1(()=>DN),multi:!0},DN=(()=>{let e=class e extends j4{writeValue(c){this.setProperty("checked",c)}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(s,o){s&1&&C2("change",function(r){return o.onChange(r.target.checked)})("blur",function(){return o.onTouched()})},features:[M1([wN]),n1]});let t=e;return t})(),SN={provide:u3,useExisting:u1(()=>vd),multi:!0};function EN(){let t=O2()?O2().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}var AN=new w(""),vd=(()=>{let e=class e extends Ld{constructor(c,s,o){super(c,s),this._compositionMode=o,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!EN())}writeValue(c){let s=c==null?"":c;this.setProperty("value",s)}_handleInput(c){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(c)}_compositionStart(){this._composing=!0}_compositionEnd(c){this._composing=!1,this._compositionMode&&this.onChange(c)}};e.\u0275fac=function(s){return new(s||e)(z(Z1),z(h1),z(AN,8))},e.\u0275dir=H({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(s,o){s&1&&C2("input",function(r){return o._handleInput(r.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(r){return o._compositionEnd(r.target.value)})},features:[M1([SN]),n1]});let t=e;return t})();function l3(t){return t==null||(typeof t=="string"||Array.isArray(t))&&t.length===0}function yd(t){return t!=null&&typeof t.length=="number"}var U2=new w(""),A8=new w(""),IN=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,fd=class{static min(e){return bd(e)}static max(e){return xd(e)}static required(e){return Nd(e)}static requiredTrue(e){return TN(e)}static email(e){return _N(e)}static minLength(e){return kN(e)}static maxLength(e){return wd(e)}static pattern(e){return Dd(e)}static nullValidator(e){return b8(e)}static compose(e){return _d(e)}static composeAsync(e){return kd(e)}};function bd(t){return e=>{if(l3(e.value)||l3(t))return null;let n=parseFloat(e.value);return!isNaN(n)&&n<t?{min:{min:t,actual:e.value}}:null}}function xd(t){return e=>{if(l3(e.value)||l3(t))return null;let n=parseFloat(e.value);return!isNaN(n)&&n>t?{max:{max:t,actual:e.value}}:null}}function Nd(t){return l3(t.value)?{required:!0}:null}function TN(t){return t.value===!0?null:{required:!0}}function _N(t){return l3(t.value)||IN.test(t.value)?null:{email:!0}}function kN(t){return e=>l3(e.value)||!yd(e.value)?null:e.value.length<t?{minlength:{requiredLength:t,actualLength:e.value.length}}:null}function wd(t){return e=>yd(e.value)&&e.value.length>t?{maxlength:{requiredLength:t,actualLength:e.value.length}}:null}function Dd(t){if(!t)return b8;let e,n;return typeof t=="string"?(n="",t.charAt(0)!=="^"&&(n+="^"),n+=t,t.charAt(t.length-1)!=="$"&&(n+="$"),e=new RegExp(n)):(n=t.toString(),e=t),c=>{if(l3(c.value))return null;let s=c.value;return e.test(s)?null:{pattern:{requiredPattern:n,actualValue:s}}}}function b8(t){return null}function Sd(t){return t!=null}function Ed(t){return x4(t)?D1(t):t}function Ad(t){let e={};return t.forEach(n=>{e=n!=null?Z(Z({},e),n):e}),Object.keys(e).length===0?null:e}function Id(t,e){return e.map(n=>n(t))}function FN(t){return!t.validate}function Td(t){return t.map(e=>FN(e)?e:n=>e.validate(n))}function _d(t){if(!t)return null;let e=t.filter(Sd);return e.length==0?null:function(n){return Ad(Id(n,e))}}function nn(t){return t!=null?_d(Td(t)):null}function kd(t){if(!t)return null;let e=t.filter(Sd);return e.length==0?null:function(n){let c=Id(n,e).map(Ed);return e0(c).pipe(o1(Ad))}}function sn(t){return t!=null?kd(Td(t)):null}function ud(t,e){return t===null?[e]:Array.isArray(t)?[...t,e]:[t,e]}function Fd(t){return t._rawValidators}function Pd(t){return t._rawAsyncValidators}function en(t){return t?Array.isArray(t)?t:[t]:[]}function x8(t,e){return Array.isArray(t)?t.includes(e):t===e}function dd(t,e){let n=en(e);return en(t).forEach(s=>{x8(n,s)||n.push(s)}),n}function hd(t,e){return en(e).filter(n=>!x8(t,n))}var N8=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=nn(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=sn(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,n){return this.control?this.control.hasError(e,n):!1}getError(e,n){return this.control?this.control.getError(e,n):null}},B2=class extends N8{get formDirective(){return null}get path(){return null}},f3=class extends N8{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},w8=class{constructor(e){this._cd=e}get isTouched(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.touched)}get isUntouched(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.untouched)}get isPristine(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.pristine)}get isDirty(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.dirty)}get isValid(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.valid)}get isInvalid(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.invalid)}get isPending(){var e,n;return!!((n=(e=this._cd)==null?void 0:e.control)!=null&&n.pending)}get isSubmitted(){var e;return!!((e=this._cd)!=null&&e.submitted)}},PN={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},HP=m1(Z({},PN),{"[class.ng-submitted]":"isSubmitted"}),jP=(()=>{let e=class e extends w8{constructor(c){super(c)}};e.\u0275fac=function(s){return new(s||e)(z(f3,2))},e.\u0275dir=H({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(s,o){s&2&&b0("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},features:[n1]});let t=e;return t})(),BP=(()=>{let e=class e extends w8{constructor(c){super(c)}};e.\u0275fac=function(s){return new(s||e)(z(B2,10))},e.\u0275dir=H({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(s,o){s&2&&b0("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},features:[n1]});let t=e;return t})();var q0="VALID",y8="INVALID",O4="PENDING",G0="DISABLED";function on(t){return(I8(t)?t.validators:t)||null}function VN(t){return Array.isArray(t)?nn(t):t||null}function rn(t,e){return(I8(e)?e.asyncValidators:t)||null}function ON(t){return Array.isArray(t)?sn(t):t||null}function I8(t){return t!=null&&!Array.isArray(t)&&typeof t=="object"}function Vd(t,e,n){let c=t.controls;if(!(e?Object.keys(c):c).length)throw new S(1e3,"");if(!c[n])throw new S(1001,"")}function Od(t,e,n){t._forEachChild((c,s)=>{if(n[s]===void 0)throw new S(1002,"")})}var R4=class{constructor(e,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(e),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get valid(){return this.status===q0}get invalid(){return this.status===y8}get pending(){return this.status==O4}get disabled(){return this.status===G0}get enabled(){return this.status!==G0}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(dd(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(dd(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(hd(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(hd(e,this._rawAsyncValidators))}hasValidator(e){return x8(this._rawValidators,e)}hasAsyncValidator(e){return x8(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){this.touched=!0,this._parent&&!e.onlySelf&&this._parent.markAsTouched(e)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(e=>e.markAllAsTouched())}markAsUntouched(e={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(n=>{n.markAsUntouched({onlySelf:!0})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e)}markAsDirty(e={}){this.pristine=!1,this._parent&&!e.onlySelf&&this._parent.markAsDirty(e)}markAsPristine(e={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(n=>{n.markAsPristine({onlySelf:!0})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e)}markAsPending(e={}){this.status=O4,e.emitEvent!==!1&&this.statusChanges.emit(this.status),this._parent&&!e.onlySelf&&this._parent.markAsPending(e)}disable(e={}){let n=this._parentMarkedDirty(e.onlySelf);this.status=G0,this.errors=null,this._forEachChild(c=>{c.disable(m1(Z({},e),{onlySelf:!0}))}),this._updateValue(),e.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(m1(Z({},e),{skipPristineCheck:n})),this._onDisabledChange.forEach(c=>c(!0))}enable(e={}){let n=this._parentMarkedDirty(e.onlySelf);this.status=q0,this._forEachChild(c=>{c.enable(m1(Z({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(m1(Z({},e),{skipPristineCheck:n})),this._onDisabledChange.forEach(c=>c(!1))}_updateAncestors(e){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===q0||this.status===O4)&&this._runAsyncValidator(e.emitEvent)),e.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(e)}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?G0:q0}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e){if(this.asyncValidator){this.status=O4,this._hasOwnPendingAsyncValidator=!0;let n=Ed(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(c=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(c,{emitEvent:e})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(e,n={}){this.errors=e,this._updateControlsErrors(n.emitEvent!==!1)}get(e){let n=e;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((c,s)=>c&&c._find(s),this)}getError(e,n){let c=n?this.get(n):this;return c&&c.errors?c.errors[e]:null}hasError(e,n){return!!this.getError(e,n)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(e)}_initObservables(){this.valueChanges=new l1,this.statusChanges=new l1}_calculateStatus(){return this._allControlsDisabled()?G0:this.errors?y8:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(O4)?O4:this._anyControlsHaveStatus(y8)?y8:q0}_anyControlsHaveStatus(e){return this._anyControls(n=>n.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e={}){this.pristine=!this._anyControlsDirty(),this._parent&&!e.onlySelf&&this._parent._updatePristine(e)}_updateTouched(e={}){this.touched=this._anyControlsTouched(),this._parent&&!e.onlySelf&&this._parent._updateTouched(e)}_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){I8(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let n=this._parent&&this._parent.dirty;return!e&&!!n&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=VN(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=ON(this._rawAsyncValidators)}},H4=class extends R4{constructor(e,n,c){super(on(n),rn(c,n)),this.controls=e,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(e,n){return this.controls[e]?this.controls[e]:(this.controls[e]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(e,n,c={}){this.registerControl(e,n),this.updateValueAndValidity({emitEvent:c.emitEvent}),this._onCollectionChange()}removeControl(e,n={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(e,n,c={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],n&&this.registerControl(e,n),this.updateValueAndValidity({emitEvent:c.emitEvent}),this._onCollectionChange()}contains(e){return this.controls.hasOwnProperty(e)&&this.controls[e].enabled}setValue(e,n={}){Od(this,!0,e),Object.keys(e).forEach(c=>{Vd(this,!0,c),this.controls[c].setValue(e[c],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(e,n={}){e!=null&&(Object.keys(e).forEach(c=>{let s=this.controls[c];s&&s.patchValue(e[c],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(e={},n={}){this._forEachChild((c,s)=>{c.reset(e?e[s]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n),this._updateTouched(n),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(e,n,c)=>(e[c]=n.getRawValue(),e))}_syncPendingControls(){let e=this._reduceChildren(!1,(n,c)=>c._syncPendingControls()?!0:n);return e&&this.updateValueAndValidity({onlySelf:!0}),e}_forEachChild(e){Object.keys(this.controls).forEach(n=>{let c=this.controls[n];c&&e(c,n)})}_setUpControls(){this._forEachChild(e=>{e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(e){for(let[n,c]of Object.entries(this.controls))if(this.contains(n)&&e(c))return!0;return!1}_reduceValue(){let e={};return this._reduceChildren(e,(n,c,s)=>((c.enabled||this.disabled)&&(n[s]=c.value),n))}_reduceChildren(e,n){let c=e;return this._forEachChild((s,o)=>{c=n(c,s,o)}),c}_allControlsDisabled(){for(let e of Object.keys(this.controls))if(this.controls[e].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(e){return this.controls.hasOwnProperty(e)?this.controls[e]:null}};var tn=class extends H4{};var B4=new w("CallSetDisabledState",{providedIn:"root",factory:()=>Y0}),Y0="always";function Rd(t,e){return[...e.path,t]}function D8(t,e,n=Y0){var c,s;an(t,e),e.valueAccessor.writeValue(t.value),(t.disabled||n==="always")&&((s=(c=e.valueAccessor).setDisabledState)==null||s.call(c,t.disabled)),HN(t,e),BN(t,e),jN(t,e),RN(t,e)}function md(t,e,n=!0){let c=()=>{};e.valueAccessor&&(e.valueAccessor.registerOnChange(c),e.valueAccessor.registerOnTouched(c)),E8(t,e),t&&(e._invokeOnDestroyCallbacks(),t._registerOnCollectionChange(()=>{}))}function S8(t,e){t.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(e)})}function RN(t,e){if(e.valueAccessor.setDisabledState){let n=c=>{e.valueAccessor.setDisabledState(c)};t.registerOnDisabledChange(n),e._registerOnDestroy(()=>{t._unregisterOnDisabledChange(n)})}}function an(t,e){let n=Fd(t);e.validator!==null?t.setValidators(ud(n,e.validator)):typeof n=="function"&&t.setValidators([n]);let c=Pd(t);e.asyncValidator!==null?t.setAsyncValidators(ud(c,e.asyncValidator)):typeof c=="function"&&t.setAsyncValidators([c]);let s=()=>t.updateValueAndValidity();S8(e._rawValidators,s),S8(e._rawAsyncValidators,s)}function E8(t,e){let n=!1;if(t!==null){if(e.validator!==null){let s=Fd(t);if(Array.isArray(s)&&s.length>0){let o=s.filter(i=>i!==e.validator);o.length!==s.length&&(n=!0,t.setValidators(o))}}if(e.asyncValidator!==null){let s=Pd(t);if(Array.isArray(s)&&s.length>0){let o=s.filter(i=>i!==e.asyncValidator);o.length!==s.length&&(n=!0,t.setAsyncValidators(o))}}}let c=()=>{};return S8(e._rawValidators,c),S8(e._rawAsyncValidators,c),n}function HN(t,e){e.valueAccessor.registerOnChange(n=>{t._pendingValue=n,t._pendingChange=!0,t._pendingDirty=!0,t.updateOn==="change"&&Hd(t,e)})}function jN(t,e){e.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,t.updateOn==="blur"&&t._pendingChange&&Hd(t,e),t.updateOn!=="submit"&&t.markAsTouched()})}function Hd(t,e){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function BN(t,e){let n=(c,s)=>{e.valueAccessor.writeValue(c),s&&e.viewToModelUpdate(c)};t.registerOnChange(n),e._registerOnDestroy(()=>{t._unregisterOnChange(n)})}function jd(t,e){t==null,an(t,e)}function UN(t,e){return E8(t,e)}function Bd(t,e){if(!t.hasOwnProperty("model"))return!1;let n=t.model;return n.isFirstChange()?!0:!Object.is(e,n.currentValue)}function $N(t){return Object.getPrototypeOf(t.constructor)===j4}function Ud(t,e){t._syncPendingControls(),e.forEach(n=>{let c=n.control;c.updateOn==="submit"&&c._pendingChange&&(n.viewToModelUpdate(c._pendingValue),c._pendingChange=!1)})}function $d(t,e){if(!e)return null;Array.isArray(e);let n,c,s;return e.forEach(o=>{o.constructor===vd?n=o:$N(o)?c=o:s=o}),s||c||n||null}function qN(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}var GN={provide:B2,useExisting:u1(()=>WN)},W0=Promise.resolve(),WN=(()=>{let e=class e extends B2{constructor(c,s,o){super(),this.callSetDisabledState=o,this.submitted=!1,this._directives=new Set,this.ngSubmit=new l1,this.form=new H4({},nn(c),sn(s))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(c){W0.then(()=>{let s=this._findContainer(c.path);c.control=s.registerControl(c.name,c.control),D8(c.control,c,this.callSetDisabledState),c.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(c)})}getControl(c){return this.form.get(c.path)}removeControl(c){W0.then(()=>{let s=this._findContainer(c.path);s&&s.removeControl(c.name),this._directives.delete(c)})}addFormGroup(c){W0.then(()=>{let s=this._findContainer(c.path),o=new H4({});jd(o,c),s.registerControl(c.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(c){W0.then(()=>{let s=this._findContainer(c.path);s&&s.removeControl(c.name)})}getFormGroup(c){return this.form.get(c.path)}updateModel(c,s){W0.then(()=>{this.form.get(c.path).setValue(s)})}setValue(c){this.control.setValue(c)}onSubmit(c){var s;return this.submitted=!0,Ud(this.form,this._directives),this.ngSubmit.emit(c),((s=c==null?void 0:c.target)==null?void 0:s.method)==="dialog"}onReset(){this.resetForm()}resetForm(c=void 0){this.form.reset(c),this.submitted=!1}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(c){return c.pop(),c.length?this.form.get(c):this.form}};e.\u0275fac=function(s){return new(s||e)(z(U2,10),z(A8,10),z(B4,8))},e.\u0275dir=H({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(s,o){s&1&&C2("submit",function(r){return o.onSubmit(r)})("reset",function(){return o.onReset()})},inputs:{options:[d1.None,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[M1([GN]),n1]});let t=e;return t})();function pd(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}function gd(t){return typeof t=="object"&&t!==null&&Object.keys(t).length===2&&"value"in t&&"disabled"in t}var Z0=class extends R4{constructor(e=null,n,c){super(on(n),rn(c,n)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(e),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),I8(n)&&(n.nonNullable||n.initialValueIsDefault)&&(gd(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,n={}){this.value=this._pendingValue=e,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(c=>c(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(e,n={}){this.setValue(e,n)}reset(e=this.defaultValue,n={}){this._applyFormState(e),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){pd(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){pd(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){gd(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var ZN=t=>t instanceof Z0;var YN={provide:f3,useExisting:u1(()=>QN)},Md=Promise.resolve(),QN=(()=>{let e=class e extends f3{constructor(c,s,o,i,r,a){super(),this._changeDetectorRef=r,this.callSetDisabledState=a,this.control=new Z0,this._registered=!1,this.name="",this.update=new l1,this._parent=c,this._setValidators(s),this._setAsyncValidators(o),this.valueAccessor=$d(this,i)}ngOnChanges(c){if(this._checkForErrors(),!this._registered||"name"in c){if(this._registered&&(this._checkName(),this.formDirective)){let s=c.name.previousValue;this.formDirective.removeControl({name:s,path:this._getPath(s)})}this._setUpControl()}"isDisabled"in c&&this._updateDisabled(c),Bd(c,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(c){this.viewModel=c,this.update.emit(c)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){D8(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(c){Md.then(()=>{var s;this.control.setValue(c,{emitViewToModelChange:!1}),(s=this._changeDetectorRef)==null||s.markForCheck()})}_updateDisabled(c){let s=c.isDisabled.currentValue,o=s!==0&&Be(s);Md.then(()=>{var i;o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),(i=this._changeDetectorRef)==null||i.markForCheck()})}_getPath(c){return this._parent?Rd(c,this._parent):[c]}};e.\u0275fac=function(s){return new(s||e)(z(B2,9),z(U2,10),z(A8,10),z(u3,10),z(R3,8),z(B4,8))},e.\u0275dir=H({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[d1.None,"disabled","isDisabled"],model:[d1.None,"ngModel","model"],options:[d1.None,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[M1([YN]),n1,o2]});let t=e;return t})(),$P=(()=>{let e=class e{};e.\u0275fac=function(s){return new(s||e)},e.\u0275dir=H({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]});let t=e;return t})(),KN={provide:u3,useExisting:u1(()=>XN),multi:!0},XN=(()=>{let e=class e extends j4{writeValue(c){let s=c==null?"":c;this.setProperty("value",s)}registerOnChange(c){this.onChange=s=>{c(s==""?null:parseFloat(s))}}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(s,o){s&1&&C2("input",function(r){return o.onChange(r.target.value)})("blur",function(){return o.onTouched()})},features:[M1([KN]),n1]});let t=e;return t})(),JN={provide:u3,useExisting:u1(()=>tw),multi:!0};var ew=(()=>{let e=class e{constructor(){this._accessors=[]}add(c,s){this._accessors.push([c,s])}remove(c){for(let s=this._accessors.length-1;s>=0;--s)if(this._accessors[s][1]===c){this._accessors.splice(s,1);return}}select(c){this._accessors.forEach(s=>{this._isSameGroup(s,c)&&s[1]!==c&&s[1].fireUncheck(c.value)})}_isSameGroup(c,s){return c[0].control?c[0]._parent===s._control._parent&&c[1].name===s.name:!1}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),tw=(()=>{let e=class e extends j4{constructor(c,s,o,i){var r;super(c,s),this._registry=o,this._injector=i,this.setDisabledStateFired=!1,this.onChange=()=>{},this.callSetDisabledState=(r=x(B4,{optional:!0}))!=null?r:Y0}ngOnInit(){this._control=this._injector.get(f3),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(c){this._state=c===this.value,this.setProperty("checked",this._state)}registerOnChange(c){this._fn=c,this.onChange=()=>{c(this.value),this._registry.select(this)}}setDisabledState(c){(this.setDisabledStateFired||c||this.callSetDisabledState==="whenDisabledForLegacyCode")&&this.setProperty("disabled",c),this.setDisabledStateFired=!0}fireUncheck(c){this.writeValue(c)}_checkName(){this.name&&this.formControlName&&(this.name,this.formControlName),!this.name&&this.formControlName&&(this.name=this.formControlName)}};e.\u0275fac=function(s){return new(s||e)(z(Z1),z(h1),z(ew),z(J2))},e.\u0275dir=H({type:e,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(s,o){s&1&&C2("change",function(){return o.onChange()})("blur",function(){return o.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},features:[M1([JN]),n1]});let t=e;return t})();var qd=new w("");var cw={provide:B2,useExisting:u1(()=>nw)},nw=(()=>{let e=class e extends B2{constructor(c,s,o){super(),this.callSetDisabledState=o,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new l1,this._setValidators(c),this._setAsyncValidators(s)}ngOnChanges(c){this._checkFormPresent(),c.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(E8(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(c){let s=this.form.get(c.path);return D8(s,c,this.callSetDisabledState),s.updateValueAndValidity({emitEvent:!1}),this.directives.push(c),s}getControl(c){return this.form.get(c.path)}removeControl(c){md(c.control||null,c,!1),qN(this.directives,c)}addFormGroup(c){this._setUpFormContainer(c)}removeFormGroup(c){this._cleanUpFormContainer(c)}getFormGroup(c){return this.form.get(c.path)}addFormArray(c){this._setUpFormContainer(c)}removeFormArray(c){this._cleanUpFormContainer(c)}getFormArray(c){return this.form.get(c.path)}updateModel(c,s){this.form.get(c.path).setValue(s)}onSubmit(c){var s;return this.submitted=!0,Ud(this.form,this.directives),this.ngSubmit.emit(c),((s=c==null?void 0:c.target)==null?void 0:s.method)==="dialog"}onReset(){this.resetForm()}resetForm(c=void 0){this.form.reset(c),this.submitted=!1}_updateDomValue(){this.directives.forEach(c=>{let s=c.control,o=this.form.get(c.path);s!==o&&(md(s||null,c),ZN(o)&&(D8(o,c,this.callSetDisabledState),c.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(c){let s=this.form.get(c.path);jd(s,c),s.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(c){if(this.form){let s=this.form.get(c.path);s&&UN(s,c)&&s.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){an(this.form,this),this._oldForm&&E8(this._oldForm,this)}_checkFormPresent(){this.form}};e.\u0275fac=function(s){return new(s||e)(z(U2,10),z(A8,10),z(B4,8))},e.\u0275dir=H({type:e,selectors:[["","formGroup",""]],hostBindings:function(s,o){s&1&&C2("submit",function(r){return o.onSubmit(r)})("reset",function(){return o.onReset()})},inputs:{form:[d1.None,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[M1([cw]),n1,o2]});let t=e;return t})();var sw={provide:f3,useExisting:u1(()=>ow)},ow=(()=>{let e=class e extends f3{set isDisabled(c){}constructor(c,s,o,i,r){super(),this._ngModelWarningConfig=r,this._added=!1,this.name=null,this.update=new l1,this._ngModelWarningSent=!1,this._parent=c,this._setValidators(s),this._setAsyncValidators(o),this.valueAccessor=$d(this,i)}ngOnChanges(c){this._added||this._setUpControl(),Bd(c,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(c){this.viewModel=c,this.update.emit(c)}get path(){return Rd(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}};e._ngModelWarningSentOnce=!1,e.\u0275fac=function(s){return new(s||e)(z(B2,13),z(U2,10),z(A8,10),z(u3,10),z(qd,8))},e.\u0275dir=H({type:e,selectors:[["","formControlName",""]],inputs:{name:[d1.None,"formControlName","name"],isDisabled:[d1.None,"disabled","isDisabled"],model:[d1.None,"ngModel","model"]},outputs:{update:"ngModelChange"},features:[M1([sw]),n1,o2]});let t=e;return t})(),iw={provide:u3,useExisting:u1(()=>Wd),multi:!0};function Gd(t,e){return t==null?`${e}`:(e&&typeof e=="object"&&(e="Object"),`${t}: ${e}`.slice(0,50))}function rw(t){return t.split(":")[0]}var Wd=(()=>{let e=class e extends j4{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(c){this._compareWith=c}writeValue(c){this.value=c;let s=this._getOptionId(c),o=Gd(s,c);this.setProperty("value",o)}registerOnChange(c){this.onChange=s=>{this.value=this._getOptionValue(s),c(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(c){for(let s of this._optionMap.keys())if(this._compareWith(this._optionMap.get(s),c))return s;return null}_getOptionValue(c){let s=rw(c);return this._optionMap.has(s)?this._optionMap.get(s):c}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(s,o){s&1&&C2("change",function(r){return o.onChange(r.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},features:[M1([iw]),n1]});let t=e;return t})(),qP=(()=>{let e=class e{constructor(c,s,o){this._element=c,this._renderer=s,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(c){this._select!=null&&(this._select._optionMap.set(this.id,c),this._setElementValue(Gd(this.id,c)),this._select.writeValue(this._select.value))}set value(c){this._setElementValue(c),this._select&&this._select.writeValue(this._select.value)}_setElementValue(c){this._renderer.setProperty(this._element.nativeElement,"value",c)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}};e.\u0275fac=function(s){return new(s||e)(z(h1),z(Z1),z(Wd,9))},e.\u0275dir=H({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}});let t=e;return t})(),aw={provide:u3,useExisting:u1(()=>Zd),multi:!0};function Cd(t,e){return t==null?`${e}`:(typeof e=="string"&&(e=`'${e}'`),e&&typeof e=="object"&&(e="Object"),`${t}: ${e}`.slice(0,50))}function lw(t){return t.split(":")[0]}var Zd=(()=>{let e=class e extends j4{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(c){this._compareWith=c}writeValue(c){this.value=c;let s;if(Array.isArray(c)){let o=c.map(i=>this._getOptionId(i));s=(i,r)=>{i._setSelected(o.indexOf(r.toString())>-1)}}else s=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(s)}registerOnChange(c){this.onChange=s=>{let o=[],i=s.selectedOptions;if(i!==void 0){let r=i;for(let a=0;a<r.length;a++){let l=r[a],f=this._getOptionValue(l.value);o.push(f)}}else{let r=s.options;for(let a=0;a<r.length;a++){let l=r[a];if(l.selected){let f=this._getOptionValue(l.value);o.push(f)}}}this.value=o,c(o)}}_registerOption(c){let s=(this._idCounter++).toString();return this._optionMap.set(s,c),s}_getOptionId(c){for(let s of this._optionMap.keys())if(this._compareWith(this._optionMap.get(s)._value,c))return s;return null}_getOptionValue(c){let s=lw(c);return this._optionMap.has(s)?this._optionMap.get(s)._value:c}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(s,o){s&1&&C2("change",function(r){return o.onChange(r.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},features:[M1([aw]),n1]});let t=e;return t})(),GP=(()=>{let e=class e{constructor(c,s,o){this._element=c,this._renderer=s,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(c){this._select!=null&&(this._value=c,this._setElementValue(Cd(this.id,c)),this._select.writeValue(this._select.value))}set value(c){this._select?(this._value=c,this._setElementValue(Cd(this.id,c)),this._select.writeValue(this._select.value)):this._setElementValue(c)}_setElementValue(c){this._renderer.setProperty(this._element.nativeElement,"value",c)}_setSelected(c){this._renderer.setProperty(this._element.nativeElement,"selected",c)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}};e.\u0275fac=function(s){return new(s||e)(z(h1),z(Z1),z(Zd,9))},e.\u0275dir=H({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}});let t=e;return t})();function fw(t){return typeof t=="number"?t:parseInt(t,10)}function Yd(t){return typeof t=="number"?t:parseFloat(t)}var Q0=(()=>{let e=class e{constructor(){this._validator=b8}ngOnChanges(c){if(this.inputName in c){let s=this.normalizeInput(c[this.inputName].currentValue);this._enabled=this.enabled(s),this._validator=this._enabled?this.createValidator(s):b8,this._onChange&&this._onChange()}}validate(c){return this._validator(c)}registerOnValidatorChange(c){this._onChange=c}enabled(c){return c!=null}};e.\u0275fac=function(s){return new(s||e)},e.\u0275dir=H({type:e,features:[o2]});let t=e;return t})(),uw={provide:U2,useExisting:u1(()=>dw),multi:!0},dw=(()=>{let e=class e extends Q0{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=c=>Yd(c),this.createValidator=c=>xd(c)}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(s,o){s&2&&T2("max",o._enabled?o.max:null)},inputs:{max:"max"},features:[M1([uw]),n1]});let t=e;return t})(),hw={provide:U2,useExisting:u1(()=>mw),multi:!0},mw=(()=>{let e=class e extends Q0{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=c=>Yd(c),this.createValidator=c=>bd(c)}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(s,o){s&2&&T2("min",o._enabled?o.min:null)},inputs:{min:"min"},features:[M1([hw]),n1]});let t=e;return t})(),pw={provide:U2,useExisting:u1(()=>gw),multi:!0};var gw=(()=>{let e=class e extends Q0{constructor(){super(...arguments),this.inputName="required",this.normalizeInput=Be,this.createValidator=c=>Nd}enabled(c){return c}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(s,o){s&2&&T2("required",o._enabled?"":null)},inputs:{required:"required"},features:[M1([pw]),n1]});let t=e;return t})();var Mw={provide:U2,useExisting:u1(()=>Cw),multi:!0},Cw=(()=>{let e=class e extends Q0{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=c=>fw(c),this.createValidator=c=>wd(c)}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(s,o){s&2&&T2("maxlength",o._enabled?o.maxlength:null)},inputs:{maxlength:"maxlength"},features:[M1([Mw]),n1]});let t=e;return t})(),zw={provide:U2,useExisting:u1(()=>Lw),multi:!0},Lw=(()=>{let e=class e extends Q0{constructor(){super(...arguments),this.inputName="pattern",this.normalizeInput=c=>c,this.createValidator=c=>Dd(c)}};e.\u0275fac=(()=>{let c;return function(o){return(c||(c=g1(e)))(o||e)}})(),e.\u0275dir=H({type:e,selectors:[["","pattern","","formControlName",""],["","pattern","","formControl",""],["","pattern","","ngModel",""]],hostVars:1,hostBindings:function(s,o){s&2&&T2("pattern",o._enabled?o.pattern:null)},inputs:{pattern:"pattern"},features:[M1([zw]),n1]});let t=e;return t})();var Qd=(()=>{let e=class e{};e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=I1({type:e}),e.\u0275inj=A1({});let t=e;return t})(),cn=class extends R4{constructor(e,n,c){super(on(n),rn(c,n)),this.controls=e,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(e){return this.controls[this._adjustIndex(e)]}push(e,n={}){this.controls.push(e),this._registerControl(e),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}insert(e,n,c={}){this.controls.splice(e,0,n),this._registerControl(n),this.updateValueAndValidity({emitEvent:c.emitEvent})}removeAt(e,n={}){let c=this._adjustIndex(e);c<0&&(c=0),this.controls[c]&&this.controls[c]._registerOnCollectionChange(()=>{}),this.controls.splice(c,1),this.updateValueAndValidity({emitEvent:n.emitEvent})}setControl(e,n,c={}){let s=this._adjustIndex(e);s<0&&(s=0),this.controls[s]&&this.controls[s]._registerOnCollectionChange(()=>{}),this.controls.splice(s,1),n&&(this.controls.splice(s,0,n),this._registerControl(n)),this.updateValueAndValidity({emitEvent:c.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(e,n={}){Od(this,!1,e),e.forEach((c,s)=>{Vd(this,!1,s),this.at(s).setValue(c,{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(e,n={}){e!=null&&(e.forEach((c,s)=>{this.at(s)&&this.at(s).patchValue(c,{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(e=[],n={}){this._forEachChild((c,s)=>{c.reset(e[s],{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n),this._updateTouched(n),this.updateValueAndValidity(n)}getRawValue(){return this.controls.map(e=>e.getRawValue())}clear(e={}){this.controls.length<1||(this._forEachChild(n=>n._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:e.emitEvent}))}_adjustIndex(e){return e<0?e+this.length:e}_syncPendingControls(){let e=this.controls.reduce((n,c)=>c._syncPendingControls()?!0:n,!1);return e&&this.updateValueAndValidity({onlySelf:!0}),e}_forEachChild(e){this.controls.forEach((n,c)=>{e(n,c)})}_updateValue(){this.value=this.controls.filter(e=>e.enabled||this.disabled).map(e=>e.value)}_anyControls(e){return this.controls.some(n=>n.enabled&&e(n))}_setUpControls(){this._forEachChild(e=>this._registerControl(e))}_allControlsDisabled(){for(let e of this.controls)if(e.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(e){e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange)}_find(e){var n;return(n=this.at(e))!=null?n:null}};function zd(t){return!!t&&(t.asyncValidators!==void 0||t.validators!==void 0||t.updateOn!==void 0)}var WP=(()=>{let e=class e{constructor(){this.useNonNullable=!1}get nonNullable(){let c=new e;return c.useNonNullable=!0,c}group(c,s=null){let o=this._reduceControls(c),i={};return zd(s)?i=s:s!==null&&(i.validators=s.validator,i.asyncValidators=s.asyncValidator),new H4(o,i)}record(c,s=null){let o=this._reduceControls(c);return new tn(o,s)}control(c,s,o){let i={};return this.useNonNullable?(zd(s)?i=s:(i.validators=s,i.asyncValidators=o),new Z0(c,m1(Z({},i),{nonNullable:!0}))):new Z0(c,s,o)}array(c,s,o){let i=c.map(r=>this._createControl(r));return new cn(i,s,o)}_reduceControls(c){let s={};return Object.keys(c).forEach(o=>{s[o]=this._createControl(c[o])}),s}_createControl(c){if(c instanceof Z0)return c;if(c instanceof R4)return c;if(Array.isArray(c)){let s=c[0],o=c.length>1?c[1]:null,i=c.length>2?c[2]:null;return this.control(s,o,i)}else return this.control(c)}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();var ZP=(()=>{let e=class e{static withConfig(c){var s;return{ngModule:e,providers:[{provide:B4,useValue:(s=c.callSetDisabledState)!=null?s:Y0}]}}};e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=I1({type:e}),e.\u0275inj=A1({imports:[Qd]});let t=e;return t})(),YP=(()=>{let e=class e{static withConfig(c){var s,o;return{ngModule:e,providers:[{provide:qd,useValue:(s=c.warnOnNgModelWithFormControl)!=null?s:"always"},{provide:B4,useValue:(o=c.callSetDisabledState)!=null?o:Y0}]}}};e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=I1({type:e}),e.\u0275inj=A1({imports:[Qd]});let t=e;return t})();var d3=(()=>{let e=class e{};e.SUCCESS="SUCCESS",e.WARNING="WARNING",e.ERROR="DANGER";let t=e;return t})();var eV=(()=>{let e=class e{constructor(){this.toasts=[]}showToast(c,s=d3.SUCCESS,o=3e3){let i=document.createElement("div");switch(i.className="toast-notification",s){case d3.SUCCESS:i.classList.add("toast-success");break;case d3.WARNING:i.classList.add("toast-warning");break;case d3.ERROR:i.classList.add("toast-error");break;default:i.classList.add("toast-success")}i.textContent=c,document.body.appendChild(i),this.toasts.push(i),setTimeout(()=>{i.classList.add("show")},10),setTimeout(()=>{i.classList.remove("show"),setTimeout(()=>{document.body.removeChild(i),this.toasts=this.toasts.filter(r=>r!==i)},300)},o)}showSuccess(c,s=3e3){this.showToast(c,d3.SUCCESS,s)}showWarning(c,s=3e3){this.showToast(c,d3.WARNING,s)}showError(c,s=3e3){this.showToast(c,d3.ERROR,s)}};e.\u0275fac=function(s){return new(s||e)},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();var T8={production:!1,apiUrl:"/api",cdnUrl:"http://localhost/cdn",tenantId:"0e22c37d-bfb5-4276-bd30-355fcdb39c9e",tenantDomain:"autovnfb.com",defaultLanguage:"vi",appName:"SMM Admin",appVersion:"1.0.0",defaultCurrency:"USD",defaultCurrencySymbol:"$",defaultDateFormat:"dd/MM/yyyy",defaultTimeFormat:"HH:mm",defaultDateTimeFormat:"dd/MM/yyyy HH:mm",defaultPageSize:10,maxPageSize:100,defaultTheme:"light",defaultPrimaryColor:"#1976d2",defaultSecondaryColor:"#f44336",defaultSuccessColor:"#4caf50",defaultWarningColor:"#ff9800",defaultErrorColor:"#f44336",defaultInfoColor:"#2196f3",defaultTextColor:"#212121",defaultBackgroundColor:"#ffffff",defaultFontFamily:'Roboto, "Helvetica Neue", sans-serif',defaultFontSize:"14px",defaultLineHeight:"1.5",defaultBorderRadius:"4px",defaultBoxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)",defaultTransition:"all 0.3s ease",defaultZIndex:1e3,defaultOpacity:.8,defaultSpacing:"8px",defaultPadding:"16px",defaultMargin:"16px",defaultGap:"8px",defaultBorderWidth:"1px",defaultBorderColor:"#e0e0e0",defaultBorderStyle:"solid",defaultOutlineWidth:"2px",defaultOutlineColor:"#1976d2",defaultOutlineStyle:"solid",defaultOutlineOffset:"2px",defaultFocusRingWidth:"2px",defaultFocusRingColor:"#1976d2",defaultFocusRingStyle:"solid",defaultFocusRingOffset:"2px",defaultDisabledOpacity:.5,defaultDisabledCursor:"not-allowed",defaultDisabledBackgroundColor:"#f5f5f5",defaultDisabledTextColor:"#9e9e9e",defaultDisabledBorderColor:"#e0e0e0",defaultHoverOpacity:.9,defaultHoverBackgroundColor:"#f5f5f5",defaultHoverTextColor:"#212121",defaultHoverBorderColor:"#e0e0e0",defaultActiveOpacity:1,defaultActiveBackgroundColor:"#e0e0e0",defaultActiveTextColor:"#212121",defaultActiveBorderColor:"#e0e0e0",defaultFocusOpacity:1,defaultFocusBackgroundColor:"#f5f5f5",defaultFocusTextColor:"#212121",defaultFocusBorderColor:"#1976d2"};var aV=(()=>{let e=class e{constructor(c){this.platformId=c,this._tenantConfig=null,this._apiUrl=this.getApiUrl(),this._tenantConfig=this.getTenantConfig(),console.log("ConfigService initialized with API URL:",this._apiUrl),this._tenantConfig&&console.log("Tenant configuration loaded:",this._tenantConfig)}getTenantConfig(){if(E4(this.platformId)){let c=window.TENANT_CONFIG;if(c)return c}return null}getApiUrl(){if(E4(this.platformId)){let c=window.TENANT_CONFIG;if(c&&c.apiUrl)return console.log("Using API URL from tenant configuration:",c.apiUrl),c.apiUrl;let s=window.API_URL;if(s)return console.log("Using API URL from window object:",s),s;let o=this.getTenantApiUrlFromHeaders();if(o)return console.log("Using API URL from headers:",o),o}if(H3(this.platformId)){let c=this.getServerApiUrl();if(c)return console.log("Using API URL from server environment:",c),c}return console.log("Using API URL from environment.ts:",T8.apiUrl),T8.apiUrl}getTenantApiUrlFromHeaders(){if(E4(this.platformId)&&document){let c=document.querySelector('meta[name="x-tenant-api-url"]');if(c){let s=c.getAttribute("content");if(s)return s}}return null}getServerApiUrl(){return null}get currentDomain(){return E4(this.platformId)?window.location.hostname:""}get apiUrl(){return this._apiUrl}get tenantConfig(){return this._tenantConfig}get tenantId(){var c;return((c=this._tenantConfig)==null?void 0:c.tenantId)||null}get theme(){var c;return((c=this._tenantConfig)==null?void 0:c.theme)||"default"}get logoUrl(){var c;return((c=this._tenantConfig)==null?void 0:c.logoUrl)||null}get companyName(){var c;return((c=this._tenantConfig)==null?void 0:c.companyName)||"SMM System"}get isProduction(){return T8.production}};e.\u0275fac=function(s){return new(s||e)(L(T1))},e.\u0275prov=y({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();export{t1 as a,eh as b,P as c,q8 as d,G8 as e,U1 as f,K4 as g,Fh as h,L3 as i,D1 as j,l2 as k,yh as l,b3 as m,b2 as n,Ph as o,o1 as p,Ch as q,y2 as r,Z4 as s,z3 as t,A6 as u,e0 as v,J8 as w,Nh as x,Uh as y,v3 as z,fh as A,B8 as B,Y4 as C,Lh as D,Q4 as E,$2 as F,vh as G,X0 as H,U8 as I,$8 as J,xh as K,wh as L,X4 as M,Dh as N,y3 as O,Sh as P,b6 as Q,S as R,u1 as S,y as T,A1 as U,q_ as V,w as W,R as X,L as Y,x as Z,S7 as _,Q9 as $,d1 as aa,T7 as ba,I1 as ca,H as da,S2 as ea,F3 as fa,_m as ga,h2 as ha,k7 as ia,$m as ja,o2 as ka,G_ as la,W_ as ma,Z_ as na,Y_ as oa,g1 as pa,_p as qa,J2 as ra,G7 as sa,Q_ as ta,h1 as ua,l1 as va,T1 as wa,i2 as xa,Lo as ya,Sg as za,K_ as Aa,X_ as Ba,J_ as Ca,ek as Da,z as Ea,tk as Fa,T3 as Ga,u0 as Ha,Z1 as Ia,a1 as Ja,dC as Ka,y4 as La,sk as Ma,n1 as Na,GC as Oa,f7 as Pa,WC as Qa,Re as Ra,QC as Sa,T2 as Ta,mz as Ua,Ci as Va,b0 as Wa,ok as Xa,ik as Ya,xi as Za,Ni as _a,Sz as $a,Tz as ab,_z as bb,rk as cb,C2 as db,ak as eb,Ai as fb,Ii as gb,Wz as hb,Ti as ib,lk as jb,fk as kb,uk as lb,dk as mb,hk as nb,mk as ob,Kz as pb,ki as qb,Xz as rb,Jz as sb,eL as tb,tL as ub,cL as vb,nL as wb,pk as xb,sL as yb,M1 as zb,vt as Ab,gk as Bb,Mk as Cb,Ck as Db,zk as Eb,Lk as Fb,vk as Gb,yk as Hb,bk as Ib,xk as Jb,Nk as Kb,wk as Lb,Ri as Mb,x4 as Nb,dL as Ob,je as Pb,N4 as Qb,Dk as Rb,R3 as Sb,Be as Tb,Sk as Ub,Ek as Vb,N1 as Wb,Zk as Xb,e8 as Yb,PL as Zb,Yk as _b,VL as $b,Qk as ac,Kk as bc,Xk as cc,Jk as dc,eF as ec,tF as fc,cF as gc,nF as hc,sF as ic,oF as jc,Ev as kc,E4 as lc,H3 as mc,iF as nc,z2 as oc,c3 as pc,n3 as qc,j3 as rc,t3 as sc,Rv as tc,MF as uc,CF as vc,zF as wc,VF as xc,OF as yc,oc as zc,RF as Ac,JF as Bc,Ba as Cc,od as Dc,oP as Ec,fP as Fc,F4 as Gc,Kc as Hc,vP as Ic,yP as Jc,u3 as Kc,DN as Lc,vd as Mc,U2 as Nc,fd as Oc,jP as Pc,BP as Qc,H4 as Rc,WN as Sc,Z0 as Tc,QN as Uc,$P as Vc,XN as Wc,tw as Xc,nw as Yc,ow as Zc,Wd as _c,qP as $c,GP as ad,dw as bd,mw as cd,gw as dd,Cw as ed,Lw as fd,WP as gd,ZP as hd,YP as id,d3 as jd,T8 as kd,aV as ld,eV as md};
