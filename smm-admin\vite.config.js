/**
 * Vite configuration file
 * This file is used to configure Vite for the Angular project
 */

module.exports = {
  // Exclude Quill CSS files from optimization
  optimizeDeps: {
    exclude: ['quill/dist/quill.core.css', 'quill/dist/quill.snow.css']
  },
  // Configure CSS handling
  css: {
    // Prevent CSS optimization for certain files
    preprocessorOptions: {
      // Add any CSS preprocessor options here if needed
    }
  },
  // Configure build options
  build: {
    // Customize rollup options
    rollupOptions: {
      // External dependencies that shouldn't be bundled
      external: []
    }
  },
  // Define global variables for browser compatibility
  define: {
    global: 'globalThis',
    'process.env': {},
    'process.nextTick': '((fn) => setTimeout(fn, 0))'
  },
  // Server configuration for development
  server: {
    // Handle Node.js polyfills
    fs: {
      allow: ['..']
    }
  }
};
