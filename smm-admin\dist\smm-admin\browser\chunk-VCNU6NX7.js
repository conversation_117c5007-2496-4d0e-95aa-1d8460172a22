import{$a as u,Ab as h,Bb as v,Bc as k,Da as l,Db as U,Fc as x,Jc as M,Mc as G,Pc as L,Qb as A,Sa as F,T as P,Ua as p,Ub as H,Uc as $,Va as I,Wa as O,Y as y,Za as n,_a as i,ba as g,bc as W,cb as j,db as d,dc as q,eb as z,ha as E,hd as C,kc as b,la as N,ld as J,ma as T,ob as a,pb as _,qb as D,tc as Y,va as m,wb as B,xb as R,yb as V}from"./chunk-MCI2ITGN.js";var Q=()=>["fas","ellipsis"],ue=(()=>{let e=class e{constructor(){this.domain="",this.close=new m,this.next=new m}onClose(){this.close.emit()}onNext(){this.domain&&this.domain.trim()&&this.next.emit(this.domain)}onOverlayClick(r){r.target===r.currentTarget&&this.onClose()}};e.\u0275fac=function(o){return new(o||e)},e.\u0275cmp=g({type:e,selectors:[["app-new-panel-step1"]],inputs:{domain:"domain"},outputs:{close:"close",next:"next"},standalone:!0,features:[h],decls:17,vars:4,consts:[[1,"overlay-black",3,"click"],[1,"modal-container"],[1,"modal-content"],[1,"icon-container"],[1,"icon-circle"],[1,"dots-icon",3,"icon"],[1,"header-section"],[1,"title"],[1,"description"],["href","https://name.com","target","_blank",1,"domain-link"],[1,"input-section"],["type","text","placeholder","Type your domain",1,"domain-input",3,"ngModelChange","keyup.enter","ngModel"],["type","button",1,"next-button",3,"click","disabled"]],template:function(o,t){o&1&&(n(0,"div",0),d("click",function(f){return t.onOverlayClick(f)}),n(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),u(5,"fa-icon",5),i()(),n(6,"div",6)(7,"h1",7),a(8,"Attach your domain"),i(),n(9,"p",8),a(10," If you dont have a domain, you can register it in any domain service, for example "),n(11,"a",9),a(12,"name.com"),i()()(),n(13,"div",10)(14,"input",11),V("ngModelChange",function(f){return R(t.domain,f)||(t.domain=f),f}),d("keyup.enter",function(){return t.onNext()}),i()(),n(15,"button",12),d("click",function(){return t.onNext()}),a(16," Next "),i()()()()),o&2&&(l(5),p("icon",v(3,Q)),l(9),B("ngModel",t.domain),l(),p("disabled",!t.domain||!t.domain.trim()))},dependencies:[b,C,G,L,$,M,x,k],styles:[".overlay-black[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:#00000080;z-index:9999;display:flex;justify-content:center;align-items:center}.modal-container[_ngcontent-%COMP%]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);width:100%;max-width:450px}.modal-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:.75rem;box-shadow:0 10px 25px #0000001a;padding:1.5rem;display:flex;flex-direction:column;align-items:center;width:100%}.icon-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:1rem}.icon-circle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:3rem;height:3rem;border-radius:9999px;background-color:var(--primary);color:#fff}.dots-icon[_ngcontent-%COMP%]{font-size:1.25rem}.header-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:1.5rem}.title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#1f2937;margin-bottom:.5rem}.description[_ngcontent-%COMP%]{color:#4b5563;font-size:.875rem}.domain-link[_ngcontent-%COMP%]{color:var(--primary)}.domain-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.input-section[_ngcontent-%COMP%]{width:100%;margin-bottom:1.5rem}.domain-input[_ngcontent-%COMP%]{width:100%;padding:.75rem 1rem;background-color:#f5f7fc;border:1px solid transparent;border-radius:.5rem;outline:none;color:#1f2937}.domain-input[_ngcontent-%COMP%]:focus{border-color:var(--primary);box-shadow:0 0 0 1px var(--primary)}.next-button[_ngcontent-%COMP%]{width:100%;background-color:var(--primary);color:#fff;font-weight:500;padding:.75rem 1rem;border-radius:.5rem;transition:background-color .3s}.next-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-hover)}.next-button[_ngcontent-%COMP%]:active{background-color:var(--primary-active)}.next-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}"]});let c=e;return c})();var Z=()=>["fas","puzzle-piece"],ee=()=>["fas","copy"];function te(c,e){if(c&1){let S=j();n(0,"div",17)(1,"span"),a(2),i(),n(3,"button",18),d("click",function(){let o=N(S).$implicit,t=z();return T(t.copyToClipboard(o))}),u(4,"fa-icon",19),i()()}if(c&2){let S=e.$implicit;l(2),_(S),l(2),p("icon",v(2,ee))}}var xe=(()=>{let e=class e{constructor(){this.domain="",this.close=new m,this.back=new m,this.next=new m,this.dnsServers=["ns1.autovnfb.com","ns2.autovnfb.com"]}onClose(){this.close.emit()}onBack(){this.back.emit()}onNext(){this.next.emit()}onOverlayClick(r){r.target===r.currentTarget&&this.onClose()}copyToClipboard(r){navigator.clipboard.writeText(r).then(()=>{console.log("Text copied to clipboard")}).catch(o=>{console.error("Could not copy text: ",o)})}};e.\u0275fac=function(o){return new(o||e)},e.\u0275cmp=g({type:e,selectors:[["app-new-panel-step2"]],inputs:{domain:"domain"},outputs:{close:"close",back:"back",next:"next"},standalone:!0,features:[h],decls:40,vars:3,consts:[[1,"overlay-black",3,"click"],[1,"modal-container"],[1,"modal-content"],["type","button",1,"back-button",3,"click"],[1,"icon-container"],[1,"icon-circle"],[1,"puzzle-icon",3,"icon"],[1,"header-section"],[1,"title"],[1,"description"],[1,"steps-container"],[1,"step"],[1,"step-number"],[1,"step-text"],[1,"dns-servers"],["class","dns-server",4,"ngFor","ngForOf"],["type","button",1,"next-button",3,"click"],[1,"dns-server"],[1,"copy-button",3,"click"],[3,"icon"]],template:function(o,t){o&1&&(n(0,"div",0),d("click",function(f){return t.onOverlayClick(f)}),n(1,"div",1)(2,"div",2)(3,"button",3),d("click",function(){return t.onBack()}),a(4," Back "),i(),n(5,"div",4)(6,"div",5),u(7,"fa-icon",6),i()(),n(8,"div",7)(9,"h1",8),a(10,"Tie domain"),i(),n(11,"p",9),a(12," For the domain to work properly"),u(13,"br"),a(14," you need to add Socpanel DNS servers. "),i()(),n(15,"div",10)(16,"div",11)(17,"div",12),a(18,"1"),i(),n(19,"div",13),a(20,"Go to the domain settings in domain service cabinet"),i()(),n(21,"div",11)(22,"div",12),a(23,"2"),i(),n(24,"div",13),a(25,"Find your DNS servers settings"),i()(),n(26,"div",11)(27,"div",12),a(28,"3"),i(),n(29,"div",13),a(30,"Enter both DNS servers listed below"),i()(),n(31,"div",14),F(32,te,5,3,"div",15),i(),n(33,"div",11)(34,"div",12),a(35,"4"),i(),n(36,"div",13),a(37,"You entered DNS into the domain?"),i()()(),n(38,"button",16),d("click",function(){return t.onNext()}),a(39," Yes "),i()()()()),o&2&&(l(7),p("icon",v(2,Z)),l(25),p("ngForOf",t.dnsServers))},dependencies:[b,W,C,M,x,k],styles:[".overlay-black[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:#00000080;z-index:9999;display:flex;justify-content:center;align-items:center}.modal-container[_ngcontent-%COMP%]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);width:100%;max-width:450px}.modal-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:.75rem;box-shadow:0 10px 25px #0000001a;padding:1.5rem;display:flex;flex-direction:column;align-items:center;width:100%;position:relative}.back-button[_ngcontent-%COMP%]{position:absolute;top:1.5rem;left:1.5rem;color:var(--primary-color);font-weight:500}.back-button[_ngcontent-%COMP%]:hover{color:var(--primary-color-hover)}.icon-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:1rem}.icon-circle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:3rem;height:3rem;border-radius:9999px;background-color:var(--primary-color);color:#fff}.puzzle-icon[_ngcontent-%COMP%]{font-size:1.25rem}.header-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:1.5rem}.title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#1f2937;margin-bottom:.5rem}.description[_ngcontent-%COMP%]{color:#4b5563;font-size:.875rem;text-align:center}.steps-container[_ngcontent-%COMP%]{width:100%;margin-bottom:1.5rem}.steps-container[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-top:1rem}.step[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.75rem}.step-number[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-width:24px;height:1.5rem;border-radius:9999px;background-color:#f3f4f6;color:#1f2937;font-size:.875rem;font-weight:500}.step-text[_ngcontent-%COMP%]{color:#4b5563;font-size:.875rem}.dns-servers[_ngcontent-%COMP%]{width:100%;margin:.5rem 0;padding-left:2.25rem}.dns-servers[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-top:.5rem}.dns-server[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%;padding:.5rem 1rem;background-color:#f5f7fc;border-radius:.5rem;color:#1f2937;font-size:.875rem}.copy-button[_ngcontent-%COMP%]{color:#6b7280;padding:.25rem}.copy-button[_ngcontent-%COMP%]:hover{color:var(--primary-color)}.next-button[_ngcontent-%COMP%]{width:100%;background-color:var(--primary-color);color:#fff;font-weight:500;padding:.75rem 1rem;border-radius:.5rem;transition:background-color .3s}.next-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-color-hover)}.next-button[_ngcontent-%COMP%]:active{background-color:var(--primary-color-active)}"]});let c=e;return c})();var ne=()=>["fas","flag"],Ie=(()=>{let e=class e{constructor(){this.domain="",this.close=new m,this.back=new m,this.createPanel=new m,this.selectedCurrency="USD",this.selectedCurrencyName="US Dollar",this.currencies=[{code:"USD",name:"US Dollar"}]}getCurrencyName(r){let o=this.currencies.find(t=>t.code===r);return o?o.name:this.selectedCurrencyName}onClose(){this.close.emit()}onBack(){this.back.emit()}onCreatePanel(){let r={domain:this.domain,currency:this.selectedCurrencyName,currencyCode:this.selectedCurrency};this.createPanel.emit(r)}onOverlayClick(r){r.target===r.currentTarget&&this.onClose()}};e.\u0275fac=function(o){return new(o||e)},e.\u0275cmp=g({type:e,selectors:[["app-new-panel-step3"]],inputs:{domain:"domain"},outputs:{close:"close",back:"back",createPanel:"createPanel"},standalone:!0,features:[h],decls:28,vars:5,consts:[[1,"overlay-black",3,"click"],[1,"modal-container"],[1,"modal-content"],["type","button",1,"back-button",3,"click"],[1,"icon-container"],[1,"icon-circle"],[1,"flag-icon",3,"icon"],[1,"header-section"],[1,"title"],[1,"description"],[1,"panel-info"],[1,"info-row"],[1,"domain-value"],[1,"info-label"],[1,"currency-code"],[1,"currency-name"],["type","button",1,"create-button",3,"click"]],template:function(o,t){o&1&&(n(0,"div",0),d("click",function(f){return t.onOverlayClick(f)}),n(1,"div",1)(2,"div",2)(3,"button",3),d("click",function(){return t.onBack()}),a(4," Back "),i(),n(5,"div",4)(6,"div",5),u(7,"fa-icon",6),i()(),n(8,"div",7)(9,"h1",8),a(10,"Final step"),i(),n(11,"p",9),a(12," Check the info and create the panel if all is well "),i()(),n(13,"div",10)(14,"div",11)(15,"span",12),a(16),i(),n(17,"span",13),a(18,"Domain"),i()(),n(19,"div",11)(20,"span",14),a(21),i(),n(22,"span",15),a(23),i(),n(24,"span",13),a(25,"Currency"),i()()(),n(26,"button",16),d("click",function(){return t.onCreatePanel()}),a(27," Create panel "),i()()()()),o&2&&(l(7),p("icon",v(4,ne)),l(9),_(t.domain),l(5),_(t.selectedCurrency),l(2),_(t.getCurrencyName(t.selectedCurrency)))},dependencies:[b,C,M,x,k],styles:[".overlay-black[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:#00000080;z-index:9999;display:flex;justify-content:center;align-items:center}.modal-container[_ngcontent-%COMP%]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);width:100%;max-width:450px}.modal-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:.75rem;box-shadow:0 10px 25px #0000001a;padding:1.5rem;display:flex;flex-direction:column;align-items:center;width:100%;position:relative}.back-button[_ngcontent-%COMP%]{position:absolute;top:1.5rem;left:1.5rem;color:var(--primary-color);font-weight:500}.back-button[_ngcontent-%COMP%]:hover{color:var(--primary-color-hover)}.icon-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:1rem}.icon-circle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:3rem;height:3rem;border-radius:9999px;background-color:var(--primary-color);color:#fff}.flag-icon[_ngcontent-%COMP%]{font-size:1.25rem}.header-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:1.5rem}.title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#1f2937;margin-bottom:.5rem}.description[_ngcontent-%COMP%]{color:#4b5563;font-size:.875rem;text-align:center}.panel-info[_ngcontent-%COMP%]{width:100%;margin-bottom:1.5rem}.panel-info[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-top:1rem}.info-row[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;padding:.75rem 0;border-bottom:1px solid #e5e7eb}.info-label[_ngcontent-%COMP%]{color:#6b7280;font-size:.75rem;margin-top:.25rem}.domain-value[_ngcontent-%COMP%], .currency-code[_ngcontent-%COMP%]{color:#1f2937;font-weight:500}.currency-name[_ngcontent-%COMP%]{color:#4b5563;font-size:.875rem}.create-button[_ngcontent-%COMP%]{width:100%;background-color:var(--primary-color);color:#fff;font-weight:500;padding:.75rem 1rem;border-radius:.5rem;transition:background-color .3s}.create-button[_ngcontent-%COMP%]:hover{background-color:var(--primary-color-hover)}.create-button[_ngcontent-%COMP%]:active{background-color:var(--primary-color-active)}"]});let c=e;return c})();var Be=(()=>{let e=class e{constructor(r,o){this.http=r,this.configService=o}createPanel(r){let o={domain:r};return this.http.post(`${this.configService.apiUrl}/panels`,o)}};e.\u0275fac=function(o){return new(o||e)(y(Y),y(J))},e.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"});let c=e;return c})();var Ae=(()=>{let e=class e{constructor(r,o){this.appRef=r,this.injector=o,this.modalComponentRef=null}open(r,o={}){this.close();let t=document.createElement("div");t.className="modal-host",document.body.appendChild(t);let s=H(r,{environmentInjector:this.injector,hostElement:t});return Object.assign(s.instance,o),this.appRef.attachView(s.hostView),this.modalComponentRef=s,s}close(){if(this.modalComponentRef){this.appRef.detachView(this.modalComponentRef.hostView),this.modalComponentRef.destroy();let r=document.querySelector(".modal-host");r&&document.body.removeChild(r),this.modalComponentRef=null}}};e.\u0275fac=function(o){return new(o||e)(y(A),y(E))},e.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"});let c=e;return c})();var oe=(c,e)=>({"background-color":c,"--tw-after-bg":e}),Je=(()=>{let e=class e{constructor(){this.isChecked=!1,this.label="",this.toggled=new m,this.circleColor="#B6BCCC",this.toggledBgColor="var(--primary)",this.disabled=!1}onToggle(){this.disabled||(this.isChecked=!this.isChecked,this.toggled.emit(this.isChecked))}};e.\u0275fac=function(o){return new(o||e)},e.\u0275cmp=g({type:e,selectors:[["app-toggle-switch"]],inputs:{isChecked:"isChecked",label:"label",circleColor:"circleColor",toggledBgColor:"toggledBgColor",disabled:"disabled"},outputs:{toggled:"toggled"},standalone:!0,features:[h],decls:5,vars:23,consts:[[1,"relative","inline-flex","items-center"],["type","checkbox",1,"sr-only","peer",3,"change","checked","disabled"],[1,"w-11","h-6","rounded-full","peer","peer-focus:ring-4","peer-focus:ring-blue-200","peer-checked:after:translate-x-full","peer-checked:after:border-white","after:content-['']","after:absolute","after:top-0.5","after:left-[2px]","after:border-gray-200","after:border","after:rounded-full","after:h-5","after:w-5","after:transition-all","after:bg-[var(--tw-after-bg)]",3,"ngStyle"],[1,"ml-3","text-sm","font-medium"]],template:function(o,t){o&1&&(n(0,"label",0)(1,"input",1),d("change",function(){return t.onToggle()}),i(),u(2,"div",2),n(3,"span",3),a(4),i()()),o&2&&(O("cursor-pointer",!t.disabled)("cursor-not-allowed",t.disabled),l(),p("checked",t.isChecked)("disabled",t.disabled),l(),I("--tw-after-bg-opacity",1),O("bg-gray-100",!t.disabled&&!t.isChecked)("bg-gray-200",t.disabled)("opacity-70",t.disabled),p("ngStyle",U(20,oe,t.isChecked?t.toggledBgColor:(t.disabled,""),t.circleColor)),l(),O("text-gray-800",!t.disabled)("text-gray-500",t.disabled),l(),D(" ",t.label," "))},dependencies:[C,b,q]});let c=e;return c})();export{ue as a,xe as b,Ie as c,Be as d,Ae as e,Je as f};
