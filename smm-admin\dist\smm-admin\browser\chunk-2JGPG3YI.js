import{a as fe}from"./chunk-HXICNFI7.js";import{$a as x,$b as R,$c as ce,Ab as $,Bb as v,Bc as G,Ca as j,Da as i,Ea as k,Fc as Q,Hb as g,Ib as d,Ic as Z,Jb as K,Jc as ee,Lc as te,Mc as ne,Pc as ie,Qc as ae,Sa as M,Sc as oe,T as D,Ua as l,Uc as re,Vc as se,Wa as I,Xa as T,Y as B,Za as o,_a as s,_c as le,ad as ge,ba as z,bc as q,cb as P,cc as Y,db as b,dd as de,eb as u,fd as pe,hb as E,hc as H,hd as me,kc as J,la as _,ld as ue,ma as f,md as _e,nb as W,ob as c,pb as h,qb as C,rb as N,tc as X,ub as U,wb as O,xb as S,yb as L}from"./chunk-MCI2ITGN.js";import"./chunk-WVXK5ZBG.js";var xe=(()=>{let m=class m{constructor(e,t){this.http=e,this.configService=t}getI18nContent(e){return this.http.get(`${this.configService.apiUrl}/tenant-settings/i18n/${e}`)}updateI18nContent(e,t){return this.http.put(`${this.configService.apiUrl}/tenant-settings/i18n/${e}`,t)}deleteI18nContent(e){return this.http.delete(`${this.configService.apiUrl}/tenant-settings/i18n/${e}`)}getAvailableLanguageCodes(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/i18n/languages`)}getDefaultTemplate(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/i18n/template`)}getTemplateForLanguage(e){return this.http.get(`${this.configService.apiUrl}/tenant-settings/i18n/template/${e}`)}importTranslations(e,t){return this.http.post(`${this.configService.apiUrl}/tenant-settings/i18n/${e}/import`,t)}getLanguageSettings(){return this.http.get(`${this.configService.apiUrl}/tenant-settings/language`)}updateLanguageSettings(e){return this.http.put(`${this.configService.apiUrl}/tenant-settings/language`,e)}};m.\u0275fac=function(t){return new(t||m)(B(X),B(ue))},m.\u0275prov=D({token:m,factory:m.\u0275fac,providedIn:"root"});let a=m;return a})();var Se=()=>["fas","arrow-left"],Le=()=>["fas","globe"],be=()=>["fas","language"],Ie=()=>["fas","chevron-down"],ke=()=>["fas","file-download"],Te=()=>["fas","download"],Ee=()=>["fas","upload"],he=()=>["fas","search"],Fe=()=>["fas","undo"],ve=()=>["fas","check"],A=()=>["fas","save"],F=()=>["fas","spinner"],Ce=()=>["fas","edit"],Me=()=>["fas","plus"],ye=()=>["fas","check-circle"],we=()=>["fas","circle"],Ve=()=>["fas","trash"],Be=()=>["fas","plus-circle"],Ae=()=>["fas","times"];function De(a,m){if(a&1&&(o(0,"div",21)(1,"div",26),c(2),g(3,"translate"),s(),o(4,"div",27),c(5),s()()),a&2){let n=u(2);i(2),h(d(3,2,"Unsaved Changes")),i(3),h(n.getModifiedCount())}}function ze(a,m){if(a&1&&(o(0,"div",20)(1,"div",21)(2,"div",22),c(3),g(4,"translate"),s(),o(5,"div",23),c(6),s()(),o(7,"div",21)(8,"div",22),c(9),g(10,"translate"),s(),o(11,"div",24),c(12),s()(),M(13,De,6,4,"div",25),s()),a&2){let n=u();i(3),h(d(4,5,"Total Keys")),i(3),h(n.totalKeys),i(3),h(d(10,7,"Customized")),i(3),h(n.customizedKeys),i(),l("ngIf",n.hasUnsavedChanges())}}function je(a,m){a&1&&x(0,"fa-icon",61),a&2&&l("icon",v(1,ve))}function We(a,m){if(a&1){let n=P();o(0,"div",59),b("click",function(){let t=_(n).$implicit,r=u(3);return f(r.selectLanguage(t))}),x(1,"span",36),o(2,"span"),c(3),s(),M(4,je,1,2,"fa-icon",60),s()}if(a&2){let n=m.$implicit,e=u(3);I("selected",e.selectedLanguage===n),i(),T(e.getLanguageFlag(n)),i(2),h(e.getLanguageName(n)),i(),l("ngIf",e.selectedLanguage===n)}}function Ne(a,m){a&1&&(o(0,"div",62),c(1),g(2,"translate"),o(3,"div",63),c(4),g(5,"translate"),s()()),a&2&&(i(),C(" ",d(2,2,"No languages available for editing")," "),i(3),C(" ",d(5,4,"Configure languages in Language Management & Settings tab first")," "))}function Ue(a,m){if(a&1&&(o(0,"div",56),M(1,We,5,6,"div",57)(2,Ne,6,6,"div",58),s()),a&2){let n=u(2);i(),l("ngForOf",n.customizedLanguages),i(),l("ngIf",n.customizedLanguages.length===0)}}function $e(a,m){a&1&&x(0,"fa-icon",7),a&2&&l("icon",v(1,A))}function Ke(a,m){a&1&&x(0,"fa-icon",64),a&2&&l("icon",v(2,F))("spin",!0)}function Re(a,m){if(a&1&&(o(0,"option",65),c(1),s()),a&2){let n=m.$implicit;l("value",n),i(),C(" ",n," ")}}function qe(a,m){a&1&&(o(0,"div",66)(1,"div",67),x(2,"fa-icon",68),o(3,"span",69),c(4),g(5,"translate"),s()()()),a&2&&(i(2),l("icon",v(5,F))("spin",!0),i(2),h(d(5,3,"Loading translations...")))}function Ye(a,m){a&1&&(o(0,"span",87),x(1,"fa-icon",88),c(2),g(3,"translate"),s()),a&2&&(i(),l("icon",v(4,Ce)),i(),C(" ",d(3,2,"Modified")," "))}function He(a,m){a&1&&(o(0,"span",89),x(1,"fa-icon",88),c(2),g(3,"translate"),s()),a&2&&(i(),l("icon",v(4,ve)),i(),C(" ",d(3,2,"Saved")," "))}function Je(a,m){if(a&1){let n=P();o(0,"tr",79)(1,"td",80)(2,"div",81),c(3),s(),o(4,"div",82),c(5),s()(),o(6,"td",80)(7,"textarea",83),g(8,"translate"),L("ngModelChange",function(t){let r=_(n).$implicit;return S(r.value,t)||(r.value=t),f(t)}),b("ngModelChange",function(){let t=_(n).$implicit,r=u(3);return f(r.onTranslationChange(t))}),c(9,"                  "),s()(),o(10,"td",84),M(11,Ye,4,5,"span",85)(12,He,4,5,"span",86),s()()}if(a&2){let n=m.$implicit;I("bg-yellow-50",n.isModified),i(3),h(n.key),i(2),C(" ",n.key.split(".")[0]," category "),i(2),I("border-orange-300",n.isModified)("bg-orange-50",n.isModified),E("placeholder",d(8,12,"Enter translation...")),O("ngModel",n.value),i(4),l("ngIf",n.isModified),i(),l("ngIf",!n.isModified)}}function Xe(a,m){a&1&&(o(0,"div",90)(1,"div",91),x(2,"fa-icon",92),o(3,"h3",93),c(4),g(5,"translate"),s(),o(6,"p",94),c(7),g(8,"translate"),s()()()),a&2&&(i(2),l("icon",v(7,be)),i(2),h(d(5,3,"No translations found")),i(3),h(d(8,5,"Try adjusting your search or filter criteria")))}function Ge(a,m){if(a&1&&(o(0,"div",70)(1,"div",71)(2,"table",72)(3,"thead")(4,"tr",73)(5,"th",74),c(6),g(7,"translate"),s(),o(8,"th",75),c(9),g(10,"translate"),s(),o(11,"th",76),c(12),g(13,"translate"),s()()(),o(14,"tbody"),M(15,Je,13,14,"tr",77),s()(),M(16,Xe,9,8,"div",78),s()()),a&2){let n=u(2);i(6),C(" ",d(7,6,"Translation Key")," "),i(3),C(" ",d(10,8,"Translation Value")," "),i(3),C(" ",d(13,10,"Status")," "),i(3),l("ngForOf",n.filteredTranslations)("ngForTrackBy",n.trackByKey),i(),l("ngIf",n.filteredTranslations.length===0)}}function Qe(a,m){if(a&1&&(o(0,"span",98),c(1),g(2,"translate"),g(3,"date"),s()),a&2){let n=u(3);i(),N(" ",d(2,2,"Last modified:")," ",K(3,4,n.lastModified,"medium")," ")}}function Ze(a,m){a&1&&x(0,"fa-icon",7),a&2&&l("icon",v(1,A))}function et(a,m){a&1&&x(0,"fa-icon",64),a&2&&l("icon",v(2,F))("spin",!0)}function tt(a,m){if(a&1){let n=P();o(0,"div",95)(1,"div",96)(2,"div",22),c(3),g(4,"translate"),g(5,"translate"),g(6,"translate"),M(7,Qe,4,7,"span",97),s(),o(8,"div",20)(9,"button",40),b("click",function(){_(n);let t=u(2);return f(t.resetTranslations())}),c(10),g(11,"translate"),s(),o(12,"button",42),b("click",function(){_(n);let t=u(2);return f(t.saveTranslations())}),M(13,Ze,1,2,"fa-icon",43)(14,et,1,3,"fa-icon",44),c(15),g(16,"translate"),g(17,"translate"),s()()()()}if(a&2){let n=u(2);i(3),U(" ",d(4,12,"Showing")," ",n.filteredTranslations.length," ",d(5,14,"of")," ",n.translations.length," ",d(6,16,"translations")," "),i(4),l("ngIf",n.lastModified),i(2),l("disabled",!n.hasUnsavedChanges()),i(),C(" ",d(11,18,"Reset All Changes")," "),i(2),l("disabled",n.isSaving||!n.canSaveTranslations()),i(),l("ngIf",!n.isSaving),i(),l("ngIf",n.isSaving),i(),C(" ",n.isSaving?d(16,20,"Saving..."):d(17,22,"Save All Changes")," ")}}function nt(a,m){if(a&1){let n=P();o(0,"div",28)(1,"div",29)(2,"div",30)(3,"div",31)(4,"label",32),c(5),g(6,"translate"),s(),o(7,"div",33)(8,"button",34),b("click",function(){_(n);let t=u();return f(t.toggleLanguageDropdown())}),o(9,"div",35),x(10,"span",36),o(11,"span"),c(12),s()(),x(13,"fa-icon",37),s(),M(14,Ue,3,2,"div",38),s()(),o(15,"div",39)(16,"button",40),b("click",function(){_(n);let t=u();return f(t.loadDefaultTemplate())}),x(17,"fa-icon",7),c(18),g(19,"translate"),s(),o(20,"button",40),b("click",function(){_(n);let t=u();return f(t.downloadTranslations())}),x(21,"fa-icon",7),c(22),g(23,"translate"),s(),o(24,"div",33)(25,"input",41,0),b("change",function(t){_(n);let r=u();return f(r.onFileSelected(t))}),s(),o(27,"button",40),b("click",function(){_(n);let t=W(26);return f(t.click())}),x(28,"fa-icon",7),c(29),g(30,"translate"),s()(),o(31,"button",42),b("click",function(){_(n);let t=u();return f(t.saveTranslations())}),M(32,$e,1,2,"fa-icon",43)(33,Ke,1,3,"fa-icon",44),c(34),g(35,"translate"),g(36,"translate"),s()()()(),o(37,"div",45)(38,"div",46)(39,"div",31)(40,"div",33),x(41,"fa-icon",47),o(42,"input",48),g(43,"translate"),L("ngModelChange",function(t){_(n);let r=u();return S(r.searchTerm,t)||(r.searchTerm=t),f(t)}),b("ngModelChange",function(){_(n);let t=u();return f(t.onSearchChange())}),s()()(),o(44,"div",49)(45,"select",50),L("ngModelChange",function(t){_(n);let r=u();return S(r.selectedCategory,t)||(r.selectedCategory=t),f(t)}),b("ngModelChange",function(){_(n);let t=u();return f(t.onCategoryChange())}),o(46,"option",51),c(47),g(48,"translate"),s(),M(49,Re,2,2,"option",52),s()(),o(50,"button",40),b("click",function(){_(n);let t=u();return f(t.resetTranslations())}),x(51,"fa-icon",7),c(52),g(53,"translate"),s()()(),M(54,qe,6,6,"div",53)(55,Ge,17,12,"div",54)(56,tt,18,24,"div",55),s()}if(a&2){let n=u();i(5),C(" ",d(6,34,"i18n_management.select_language")," "),i(3),l("disabled",n.isLoading),i(2),T(n.getLanguageFlag(n.selectedLanguage)),i(2),h(n.getLanguageName(n.selectedLanguage)),i(),I("rotate-180",n.showLanguageDropdown),l("icon",v(52,Ie)),i(),l("ngIf",n.showLanguageDropdown),i(2),l("disabled",n.isLoading),i(),l("icon",v(53,ke)),i(),C(" ",d(19,36,"i18n_management.load_translations")," "),i(2),l("disabled",n.isLoading||!n.selectedLanguage),i(),l("icon",v(54,Te)),i(),C(" ",d(23,38,"i18n_management.download_translations")," "),i(5),l("disabled",n.isLoading||!n.selectedLanguage),i(),l("icon",v(55,Ee)),i(),C(" ",d(30,40,"i18n_management.upload_translations")," "),i(2),l("disabled",n.isSaving||!n.canSaveTranslations()),i(),l("ngIf",!n.isSaving),i(),l("ngIf",n.isSaving),i(),C(" ",n.isSaving?d(35,42,"i18n_management.saving"):d(36,44,"i18n_management.save_translations")," "),i(7),l("icon",v(56,he)),i(),E("placeholder",d(43,46,"Search translations...")),O("ngModel",n.searchTerm),i(3),O("ngModel",n.selectedCategory),i(2),h(d(48,48,"All Categories")),i(2),l("ngForOf",n.categories),i(),l("disabled",!n.hasUnsavedChanges()),i(),l("icon",v(57,Fe)),i(),C(" ",d(53,50,"Reset")," "),i(2),l("ngIf",n.isLoading),i(),l("ngIf",!n.isLoading),i(),l("ngIf",!n.isLoading)}}function it(a,m){a&1&&(o(0,"div",126)(1,"span",127),c(2),g(3,"translate"),s()()),a&2&&(i(2),h(d(3,1,"Default")))}function at(a,m){if(a&1){let n=P();o(0,"button",128),b("click",function(t){_(n);let r=u().$implicit;return u(2).setDefaultLanguage(r.code),f(t.stopPropagation())}),c(1),g(2,"translate"),s()}a&2&&(i(),C(" ",d(2,1,"Set Default")," "))}function ot(a,m){a&1&&x(0,"fa-icon",129),a&2&&l("icon",v(1,ye))}function rt(a,m){a&1&&x(0,"fa-icon",130),a&2&&l("icon",v(1,we))}function st(a,m){if(a&1){let n=P();o(0,"div",116)(1,"div",117),b("click",function(){let t=_(n).$implicit,r=u(2);return f(r.toggleLanguageSelection(t.code))}),x(2,"span",36),o(3,"div",118)(4,"div",119),c(5),s(),o(6,"div",120),c(7),s(),M(8,it,4,3,"div",121),s()(),o(9,"div",122),M(10,at,3,3,"button",123)(11,ot,1,2,"fa-icon",124)(12,rt,1,2,"fa-icon",125),s()()}if(a&2){let n=m.$implicit,e=u(2);I("selected",e.selectedAvailableLanguages.includes(n.code))("is-default",e.defaultLanguage===n.code),i(2),T(n.flag),i(3),h(n.name),i(2),h(n.code),i(),l("ngIf",e.defaultLanguage===n.code),i(2),l("ngIf",e.selectedAvailableLanguages.includes(n.code)&&e.defaultLanguage!==n.code),i(),l("ngIf",e.selectedAvailableLanguages.includes(n.code)),i(),l("ngIf",!e.selectedAvailableLanguages.includes(n.code))}}function lt(a,m){if(a&1&&(o(0,"div",135),c(1),s()),a&2){let n=u().$implicit;i(),h(n.description)}}function ct(a,m){a&1&&(o(0,"div",126)(1,"span",127),c(2),g(3,"translate"),s()()),a&2&&(i(2),h(d(3,1,"Default")))}function gt(a,m){if(a&1){let n=P();o(0,"button",128),b("click",function(t){_(n);let r=u().$implicit;return u(2).setDefaultLanguage(r.language_code),f(t.stopPropagation())}),c(1),g(2,"translate"),s()}a&2&&(i(),C(" ",d(2,1,"Set Default")," "))}function dt(a,m){a&1&&x(0,"fa-icon",129),a&2&&l("icon",v(1,ye))}function pt(a,m){a&1&&x(0,"fa-icon",130),a&2&&l("icon",v(1,we))}function mt(a,m){if(a&1){let n=P();o(0,"div",131)(1,"div",117),b("click",function(){let t=_(n).$implicit,r=u(2);return f(r.toggleLanguageSelection(t.language_code))}),x(2,"span",36),o(3,"div",118)(4,"div",119),c(5),s(),o(6,"div",120),c(7),s(),M(8,lt,2,1,"div",132)(9,ct,4,3,"div",121),s()(),o(10,"div",122),M(11,gt,3,3,"button",123),o(12,"button",133),b("click",function(t){let r=_(n).$implicit;return u(2).editCustomLanguage(r),f(t.stopPropagation())}),x(13,"fa-icon",7),s(),o(14,"button",134),b("click",function(t){let r=_(n).$implicit;return u(2).deleteCustomLanguage(r),f(t.stopPropagation())}),x(15,"fa-icon",7),s(),M(16,dt,1,2,"fa-icon",124)(17,pt,1,2,"fa-icon",125),s()()}if(a&2){let n=m.$implicit,e=u(2);I("selected",e.selectedAvailableLanguages.includes(n.language_code))("inactive",!n.active)("is-default",e.defaultLanguage===n.language_code),i(2),T(n.flag_class||"fi fi-xx"),i(3),h(n.language_name),i(2),h(n.language_code),i(),l("ngIf",n.description),i(),l("ngIf",e.defaultLanguage===n.language_code),i(2),l("ngIf",e.selectedAvailableLanguages.includes(n.language_code)&&e.defaultLanguage!==n.language_code),i(2),l("icon",v(17,Ce)),i(2),l("icon",v(18,Ve)),i(),l("ngIf",e.selectedAvailableLanguages.includes(n.language_code)),i(),l("ngIf",!e.selectedAvailableLanguages.includes(n.language_code))}}function ut(a,m){if(a&1){let n=P();o(0,"div",90)(1,"div",136),x(2,"fa-icon",92),o(3,"h3",93),c(4),g(5,"translate"),s(),o(6,"p",137),c(7),g(8,"translate"),s(),o(9,"button",112),b("click",function(){_(n);let t=u(2);return f(t.openCustomLanguageForm())}),x(10,"fa-icon",7),c(11),g(12,"translate"),s()()()}a&2&&(i(2),l("icon",v(11,Be)),i(2),h(d(5,5,"No custom languages")),i(3),h(d(8,7,"Create your first custom language to get started")),i(3),l("icon",v(12,Me)),i(),C(" ",d(12,9,"Add Custom Language")," "))}function _t(a,m){a&1&&x(0,"fa-icon",7),a&2&&l("icon",v(1,A))}function ft(a,m){a&1&&x(0,"fa-icon",64),a&2&&l("icon",v(2,F))("spin",!0)}function xt(a,m){if(a&1){let n=P();o(0,"div",99)(1,"div",100)(2,"div",101)(3,"div",102)(4,"h3",103),c(5),g(6,"translate"),s(),o(7,"p",104),c(8),g(9,"translate"),s(),o(10,"div",105),x(11,"fa-icon",47),o(12,"input",106),g(13,"translate"),L("ngModelChange",function(t){_(n);let r=u();return S(r.predefinedLanguagesSearchTerm,t)||(r.predefinedLanguagesSearchTerm=t),f(t)}),b("ngModelChange",function(){_(n);let t=u();return f(t.onPredefinedLanguagesSearchChange())}),s()()(),o(14,"div",107),M(15,st,13,12,"div",108),s()(),o(16,"div",109)(17,"div",102)(18,"div",110)(19,"div")(20,"h3",103),c(21),g(22,"translate"),s(),o(23,"p",111),c(24),g(25,"translate"),s()(),o(26,"button",112),b("click",function(){_(n);let t=u();return f(t.openCustomLanguageForm())}),x(27,"fa-icon",7),c(28),g(29,"translate"),s()()(),o(30,"div",113),M(31,mt,18,19,"div",114)(32,ut,13,13,"div",78),s()()(),o(33,"div",115)(34,"button",42),b("click",function(){_(n);let t=u();return f(t.saveLanguageSettings())}),M(35,_t,1,2,"fa-icon",43)(36,ft,1,3,"fa-icon",44),c(37),g(38,"translate"),g(39,"translate"),s()()()}if(a&2){let n=u();i(5),h(d(6,16,"Predefined Languages")),i(3),h(d(9,18,"Choose from our extensive list of predefined languages")),i(3),l("icon",v(32,he)),i(),E("placeholder",d(13,20,"Search predefined languages...")),O("ngModel",n.predefinedLanguagesSearchTerm),i(3),l("ngForOf",n.filteredPredefinedLanguages),i(6),h(d(22,22,"Custom Languages")),i(3),h(d(25,24,'Create your own custom languages (e.g., "Vietnam 2", "English US")')),i(3),l("icon",v(33,Me)),i(),C(" ",d(29,26,"Add Custom Language")," "),i(3),l("ngForOf",n.customLanguages),i(),l("ngIf",n.customLanguages.length===0),i(2),l("disabled",n.isSaving||n.selectedAvailableLanguages.length===0),i(),l("ngIf",!n.isSaving),i(),l("ngIf",n.isSaving),i(),C(" ",n.isSaving?d(38,28,"Saving..."):d(39,30,"Save Language Configuration")," ")}}function bt(a,m){a&1&&x(0,"fa-icon",64),a&2&&l("icon",v(2,F))("spin",!0)}function ht(a,m){if(a&1){let n=P();o(0,"div",138),b("click",function(){_(n);let t=u();return f(t.closeCustomLanguageForm())}),o(1,"div",139),b("click",function(t){return _(n),f(t.stopPropagation())}),o(2,"div",140)(3,"h2",141),c(4),g(5,"translate"),g(6,"translate"),s(),o(7,"button",142),b("click",function(){_(n);let t=u();return f(t.closeCustomLanguageForm())}),x(8,"fa-icon",7),s()(),o(9,"div",143)(10,"form",144,1),b("ngSubmit",function(){_(n);let t=u();return f(t.saveCustomLanguage())}),o(12,"div",145)(13,"label",146),c(14),g(15,"translate"),s(),o(16,"input",147),L("ngModelChange",function(t){_(n);let r=u();return S(r.customLanguageForm.language_code,t)||(r.customLanguageForm.language_code=t),f(t)}),s(),o(17,"div",148),c(18),g(19,"translate"),s()(),o(20,"div",145)(21,"label",146),c(22),g(23,"translate"),s(),o(24,"input",149),L("ngModelChange",function(t){_(n);let r=u();return S(r.customLanguageForm.language_name,t)||(r.customLanguageForm.language_name=t),f(t)}),s()(),o(25,"div",145)(26,"label",146),c(27),g(28,"translate"),s(),o(29,"input",150),L("ngModelChange",function(t){_(n);let r=u();return S(r.customLanguageForm.flag_class,t)||(r.customLanguageForm.flag_class=t),f(t)}),s(),o(30,"div",148),c(31),g(32,"translate"),s()(),o(33,"div",145)(34,"label",146),c(35),g(36,"translate"),s(),o(37,"textarea",151),g(38,"translate"),L("ngModelChange",function(t){_(n);let r=u();return S(r.customLanguageForm.description,t)||(r.customLanguageForm.description=t),f(t)}),c(39,"            "),s()(),o(40,"div",145)(41,"label",152)(42,"input",153),L("ngModelChange",function(t){_(n);let r=u();return S(r.customLanguageForm.active,t)||(r.customLanguageForm.active=t),f(t)}),s(),o(43,"span",154),c(44),g(45,"translate"),s()()()()(),o(46,"div",155)(47,"button",156),b("click",function(){_(n);let t=u();return f(t.closeCustomLanguageForm())}),c(48),g(49,"translate"),s(),o(50,"button",42),b("click",function(){_(n);let t=u();return f(t.saveCustomLanguage())}),M(51,bt,1,3,"fa-icon",44),c(52),g(53,"translate"),g(54,"translate"),g(55,"translate"),s()()()()}if(a&2){let n=u();i(4),C(" ",n.editingCustomLanguage?d(5,21,"Edit Custom Language"):d(6,23,"Add Custom Language")," "),i(4),l("icon",v(49,Ae)),i(6),C("",d(15,25,"Language Code")," *"),i(2),O("ngModel",n.customLanguageForm.language_code),l("disabled",!!n.editingCustomLanguage),i(2),h(d(19,27,"Use lowercase letters, numbers, underscores, and hyphens only")),i(4),C("",d(23,29,"Language Name")," *"),i(2),O("ngModel",n.customLanguageForm.language_name),i(3),h(d(28,31,"Flag Class")),i(2),O("ngModel",n.customLanguageForm.flag_class),i(2),h(d(32,33,"CSS class for flag icon (optional)")),i(4),h(d(36,35,"Description")),i(2),E("placeholder",d(38,37,"Optional description for this custom language")),O("ngModel",n.customLanguageForm.description),i(5),O("ngModel",n.customLanguageForm.active),i(2),h(d(45,39,"Active")),i(3),l("disabled",n.isSaving),i(),C(" ",d(49,41,"Cancel")," "),i(2),l("disabled",n.isSaving||!n.customLanguageForm.language_code||!n.customLanguageForm.language_name),i(),l("ngIf",n.isSaving),i(),C(" ",n.isSaving?d(53,43,"Saving..."):n.editingCustomLanguage?d(54,45,"Update"):d(55,47,"Create")," ")}}var Et=(()=>{let m=class m{constructor(e,t,r,p){this.tenantI18nService=e,this.toastService=t,this.tenantSettingsService=r,this.location=p,this.activeTab="languages",this.predefinedLanguages=[],this.filteredPredefinedLanguages=[],this.predefinedLanguagesSearchTerm="",this.customLanguages=[],this.allAvailableLanguages=[],this.selectedLanguage="vi",this.customizedLanguages=[],this.translations=[],this.filteredTranslations=[],this.searchTerm="",this.selectedCategory="",this.categories=[],this.isLoading=!1,this.isSaving=!1,this.showLanguageDropdown=!1,this.showCustomLanguageForm=!1,this.editingCustomLanguage=null,this.customLanguageForm={language_code:"",language_name:"",flag_class:"",description:"",active:!0},this.selectedAvailableLanguages=[],this.defaultLanguage="vi",this.totalKeys=0,this.customizedKeys=0,this.subscriptions=[]}ngOnInit(){this.loadAllLanguageData(),this.loadTranslations()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}loadAllLanguageData(){this.isLoading=!0;let e=this.tenantSettingsService.getPredefinedLanguages().subscribe({next:p=>{this.predefinedLanguages=p,this.filteredPredefinedLanguages=p,this.updateAllAvailableLanguages()},error:p=>{console.error("Error loading predefined languages:",p),this.toastService.showError("Failed to load predefined languages")}});this.subscriptions.push(e);let t=this.tenantSettingsService.getCustomLanguages().subscribe({next:p=>{this.customLanguages=p,this.updateAllAvailableLanguages()},error:p=>{console.error("Error loading custom languages:",p),this.toastService.showError("Failed to load custom languages")}});this.subscriptions.push(t);let r=this.tenantSettingsService.getLanguageSettings().subscribe({next:p=>{this.defaultLanguage=p.default_language,this.selectedAvailableLanguages=p.available_languages,this.loadAvailableLanguages(),this.isLoading=!1},error:p=>{console.error("Error loading language settings:",p),this.isLoading=!1}});this.subscriptions.push(r)}updateAllAvailableLanguages(){this.allAvailableLanguages=[...this.predefinedLanguages,...this.customLanguages.map(e=>({code:e.language_code,name:e.language_name,flag:e.flag_class||"fi fi-xx",type:"custom",active:e.active,description:e.description}))]}loadAvailableLanguages(){if(this.selectedAvailableLanguages&&this.selectedAvailableLanguages.length>0)this.customizedLanguages=[...this.selectedAvailableLanguages],this.customizedLanguages.includes(this.selectedLanguage)||(this.selectedLanguage=this.customizedLanguages[0]);else{let e=this.tenantSettingsService.getTenantAvailableLanguages().subscribe({next:t=>{this.customizedLanguages=t,t.length>0&&!t.includes(this.selectedLanguage)&&(this.selectedLanguage=t[0])},error:t=>{console.error("Error loading available languages:",t),this.customizedLanguages=["vi","en","cn"],this.customizedLanguages.includes(this.selectedLanguage)||(this.selectedLanguage=this.customizedLanguages[0])}});this.subscriptions.push(e)}}loadTranslations(){if(!this.selectedLanguage)return;this.isLoading=!0;let e=this.tenantI18nService.getI18nContent(this.selectedLanguage).subscribe({next:t=>{this.processTranslationsResponse(t),this.isLoading=!1},error:t=>{console.error("Error loading translations:",t),this.loadDefaultTemplate()}});this.subscriptions.push(e)}loadDefaultTemplate(){if(!this.selectedLanguage){this.toastService.showWarning("Please select a language first");return}this.isLoading=!0;let e=this.tenantI18nService.getTemplateForLanguage(this.selectedLanguage).subscribe({next:t=>{this.processDefaultTemplate(t),this.isLoading=!1,this.toastService.showSuccess(`Template loaded for ${this.selectedLanguage}`)},error:t=>{console.error("Error loading template:",t),this.getDefaultTemplate()}});this.subscriptions.push(e)}getDefaultTemplate(){let e=this.tenantI18nService.getDefaultTemplate().subscribe({next:t=>{this.processDefaultTemplate(t),this.isLoading=!1,this.toastService.showSuccess("Default template loaded")},error:t=>{console.error("Error loading default template:",t),this.toastService.showError("Failed to load default translation template"),this.isLoading=!1}});this.subscriptions.push(e)}processTranslationsResponse(e){this.totalKeys=e.total_keys,this.customizedKeys=e.customized_keys,this.lastModified=e.last_modified,this.translations=Object.entries(e.translations).map(([t,r])=>({key:t,value:r,originalValue:r,isModified:!1})),this.extractCategories(),this.applyFilters()}processDefaultTemplate(e){let t=this.flattenObject(e);this.translations=Object.entries(t).map(([r,p])=>({key:r,value:String(p),originalValue:String(p),isModified:!1})),this.totalKeys=this.translations.length,this.customizedKeys=0,this.extractCategories(),this.applyFilters()}flattenObject(e,t=""){let r={};for(let p in e)if(e.hasOwnProperty(p)){let y=t?`${t}.${p}`:p;typeof e[p]=="object"&&e[p]!==null&&!Array.isArray(e[p])?Object.assign(r,this.flattenObject(e[p],y)):r[y]=String(e[p])}return r}extractCategories(){let e=new Set;this.translations.forEach(t=>{let r=t.key.split(".");r.length>1&&e.add(r[0])}),this.categories=Array.from(e).sort()}onLanguageChange(){this.loadTranslations()}onTranslationChange(e){e.isModified=e.value!==e.originalValue}applyFilters(){this.filteredTranslations=this.translations.filter(e=>{let t=!this.searchTerm||e.key.toLowerCase().includes(this.searchTerm.toLowerCase())||e.value.toLowerCase().includes(this.searchTerm.toLowerCase()),r=!this.selectedCategory||e.key.startsWith(this.selectedCategory+".");return t&&r})}onSearchChange(){this.applyFilters()}onCategoryChange(){this.applyFilters()}onPredefinedLanguagesSearchChange(){this.filterPredefinedLanguages()}filterPredefinedLanguages(){if(!this.predefinedLanguagesSearchTerm.trim())this.filteredPredefinedLanguages=[...this.predefinedLanguages];else{let e=this.predefinedLanguagesSearchTerm.toLowerCase();this.filteredPredefinedLanguages=this.predefinedLanguages.filter(t=>t.name.toLowerCase().includes(e)||t.code.toLowerCase().includes(e))}}saveTranslations(){if(this.translations.length===0){this.toastService.showWarning("No translations to save");return}this.isSaving=!0;let e=this.unflattenObject(this.translations.reduce((p,y)=>(p[y.key]=y.value,p),{})),t={language_code:this.selectedLanguage,translations:e,description:`Updated translations for ${this.selectedLanguage}`},r=this.tenantI18nService.updateI18nContent(this.selectedLanguage,t).subscribe({next:p=>{this.toastService.showSuccess("Translations saved successfully"),this.processTranslationsResponse(p),this.loadAvailableLanguages(),this.isSaving=!1},error:p=>{console.error("Error saving translations:",p),this.toastService.showError("Failed to save translations"),this.isSaving=!1}});this.subscriptions.push(r)}unflattenObject(e){let t={};for(let r in e){let p=r.split("."),y=t;for(let w=0;w<p.length-1;w++)y[p[w]]||(y[p[w]]={}),y=y[p[w]];y[p[p.length-1]]=e[r]}return t}resetTranslations(){this.translations.forEach(e=>{e.value=e.originalValue||"",e.isModified=!1}),this.applyFilters()}getLanguageName(e){let t=this.allAvailableLanguages.find(r=>r.code===e);return t?t.name:e.toUpperCase()}getLanguageFlag(e){let t=this.allAvailableLanguages.find(r=>r.code===e);return t?t.flag:"fi fi-xx"}hasUnsavedChanges(){return this.translations.some(e=>e.isModified)}canSaveTranslations(){return this.translations.length>0}getModifiedCount(){return this.translations.filter(e=>e.isModified).length}trackByKey(e,t){return t.key}downloadTranslations(){if(!this.selectedLanguage||this.translations.length===0){this.toastService.showWarning("No translations to download");return}try{let e=this.unflattenObject(this.translations.reduce((w,V)=>(w[V.key]=V.value,w),{})),t=JSON.stringify(e,null,2),r=new Blob([t],{type:"application/json"}),p=window.URL.createObjectURL(r),y=document.createElement("a");y.href=p,y.download=`${this.selectedLanguage}.json`,document.body.appendChild(y),y.click(),document.body.removeChild(y),window.URL.revokeObjectURL(p),this.toastService.showSuccess("Translations downloaded successfully")}catch(e){console.error("Error downloading translations:",e),this.toastService.showError("Failed to download translations")}}onFileSelected(e){let t=e.target.files[0];if(!t)return;if(!t.name.endsWith(".json")){this.toastService.showError("Please select a JSON file");return}let r=new FileReader;r.onload=p=>{var y;try{let w=(y=p.target)==null?void 0:y.result,V=JSON.parse(w);this.uploadTranslations(V)}catch(w){console.error("Error parsing JSON file:",w),this.toastService.showError("Invalid JSON file format")}},r.readAsText(t),e.target.value=""}uploadTranslations(e){try{if(!this.selectedLanguage){this.toastService.showError("Please select a language first");return}let t=this.flattenObject(e);Object.keys(t).forEach(r=>{let p=this.translations.find(y=>y.key===r);p?(p.value=t[r],p.isModified=p.value!==p.originalValue):this.translations.push({key:r,value:t[r],originalValue:"",isModified:!0})}),this.extractCategories(),this.applyFilters(),this.toastService.showSuccess(`Uploaded ${Object.keys(t).length} translation keys`)}catch(t){console.error("Error uploading translations:",t),this.toastService.showError("Failed to upload translations")}}setActiveTab(e){this.activeTab=e}toggleLanguageDropdown(){this.showLanguageDropdown=!this.showLanguageDropdown}selectLanguage(e){e!==this.selectedLanguage?(this.selectedLanguage=e,this.showLanguageDropdown=!1,this.onLanguageChange()):this.showLanguageDropdown=!1}openCustomLanguageForm(){this.showCustomLanguageForm=!0,this.editingCustomLanguage=null,this.resetCustomLanguageForm()}editCustomLanguage(e){this.showCustomLanguageForm=!0,this.editingCustomLanguage=e,this.customLanguageForm={language_code:e.language_code,language_name:e.language_name,flag_class:e.flag_class||"",description:e.description||"",active:e.active}}closeCustomLanguageForm(){this.showCustomLanguageForm=!1,this.editingCustomLanguage=null,this.resetCustomLanguageForm()}resetCustomLanguageForm(){this.customLanguageForm={language_code:"",language_name:"",flag_class:"",description:"",active:!0}}saveCustomLanguage(){if(!this.customLanguageForm.language_code||!this.customLanguageForm.language_name){this.toastService.showError("Language code and name are required");return}if(this.isSaving=!0,this.editingCustomLanguage){let e=this.tenantSettingsService.updateCustomLanguage(this.editingCustomLanguage.language_code,this.customLanguageForm).subscribe({next:()=>{this.toastService.showSuccess("Custom language updated successfully"),this.loadAllLanguageData(),this.closeCustomLanguageForm(),this.isSaving=!1},error:t=>{console.error("Error updating custom language:",t),this.toastService.showError("Failed to update custom language"),this.isSaving=!1}});this.subscriptions.push(e)}else{let e=this.tenantSettingsService.createCustomLanguage(this.customLanguageForm).subscribe({next:()=>{this.toastService.showSuccess("Custom language created successfully"),this.loadAllLanguageData(),this.closeCustomLanguageForm(),this.isSaving=!1},error:t=>{console.error("Error creating custom language:",t),this.toastService.showError("Failed to create custom language"),this.isSaving=!1}});this.subscriptions.push(e)}}deleteCustomLanguage(e){if(!confirm(`Are you sure you want to delete the custom language "${e.language_name}"?`))return;let t=this.tenantSettingsService.deleteCustomLanguage(e.language_code).subscribe({next:()=>{this.toastService.showSuccess("Custom language deleted successfully"),this.loadAllLanguageData()},error:r=>{console.error("Error deleting custom language:",r),this.toastService.showError("Failed to delete custom language")}});this.subscriptions.push(t)}toggleLanguageSelection(e){let t=this.selectedAvailableLanguages.indexOf(e);t>-1?this.selectedAvailableLanguages.length>1&&(this.selectedAvailableLanguages.splice(t,1),this.defaultLanguage===e&&(this.defaultLanguage=this.selectedAvailableLanguages[0])):this.selectedAvailableLanguages.push(e),this.loadAvailableLanguages()}setDefaultLanguage(e){this.selectedAvailableLanguages.includes(e)&&(this.defaultLanguage=e)}saveLanguageSettings(){if(this.selectedAvailableLanguages.length===0){this.toastService.showError("At least one language must be selected");return}if(!this.selectedAvailableLanguages.includes(this.defaultLanguage)){this.toastService.showError("Default language must be in the selected languages list");return}this.isSaving=!0;let e={default_language:this.defaultLanguage,available_languages:this.selectedAvailableLanguages},t=this.tenantSettingsService.updateLanguageSettings(e).subscribe({next:()=>{this.toastService.showSuccess("Language settings saved successfully"),this.loadAvailableLanguages(),this.isSaving=!1},error:r=>{console.error("Error saving language settings:",r),this.toastService.showError("Failed to save language settings"),this.isSaving=!1}});this.subscriptions.push(t)}onDocumentClick(e){!e.target.closest(".language-dropdown-button, .language-dropdown-menu")&&this.showLanguageDropdown&&(this.showLanguageDropdown=!1)}goBack(){this.location.back()}};m.\u0275fac=function(t){return new(t||m)(k(xe),k(_e),k(fe),k(R))},m.\u0275cmp=z({type:m,selectors:[["app-i18n-management"]],hostBindings:function(t,r){t&1&&b("click",function(y){return r.onDocumentClick(y)},!1,j)},standalone:!0,features:[$],decls:30,vars:26,consts:[["fileInput",""],["customLangForm","ngForm"],[1,"page-container"],[1,"content-container"],[1,"page-header"],[1,"flex","items-center","gap-3"],[1,"back-button",3,"click"],[3,"icon"],[1,"page-title"],[1,"header-section"],[1,"flex","items-center","justify-between","mb-6"],[1,"text-gray-600","mt-1"],["class","flex items-center space-x-3",4,"ngIf"],[1,"tab-navigation"],[1,"flex","space-x-8","border-b","border-gray-200"],["type","button",1,"tab-button",3,"click"],[1,"tab-content"],["class","translations-tab",4,"ngIf"],["class","languages-tab",4,"ngIf"],["class","modal-overlay",3,"click",4,"ngIf"],[1,"flex","items-center","space-x-3"],[1,"stats-card"],[1,"text-sm","text-gray-500"],[1,"text-lg","font-semibold"],[1,"text-lg","font-semibold","text-blue-600"],["class","stats-card",4,"ngIf"],[1,"text-sm","text-orange-500"],[1,"text-lg","font-semibold","text-orange-600"],[1,"translations-tab"],[1,"language-selection","mb-6"],[1,"flex","items-center","space-x-4"],[1,"flex-1"],[1,"block","text-sm","font-medium","text-gray-700","mb-2"],[1,"relative"],["type","button",1,"language-dropdown-button",3,"click","disabled"],[1,"flex","items-center","space-x-2"],[1,"flag-icon"],[1,"transition-transform","duration-200",3,"icon"],["class","language-dropdown-menu",4,"ngIf"],[1,"flex","items-end","space-x-2"],["type","button",1,"btn","btn-outline",3,"click","disabled"],["type","file","accept",".json",1,"hidden",3,"change"],["type","button",1,"btn","btn-primary",3,"click","disabled"],[3,"icon",4,"ngIf"],[3,"icon","spin",4,"ngIf"],[1,"filters-section"],[1,"flex","items-center","space-x-4","mb-4"],[1,"absolute","left-3","top-1/2","transform","-translate-y-1/2","text-gray-400",3,"icon"],["type","text",1,"form-input","pl-10",3,"ngModelChange","ngModel","placeholder"],[1,"w-48"],[1,"form-select",3,"ngModelChange","ngModel"],["value",""],[3,"value",4,"ngFor","ngForOf"],["class","loading-state",4,"ngIf"],["class","translations-table",4,"ngIf"],["class","footer-actions mt-4",4,"ngIf"],[1,"language-dropdown-menu"],["class","language-dropdown-item",3,"selected","click",4,"ngFor","ngForOf"],["class","language-dropdown-empty",4,"ngIf"],[1,"language-dropdown-item",3,"click"],["class","text-blue-500",3,"icon",4,"ngIf"],[1,"text-blue-500",3,"icon"],[1,"language-dropdown-empty"],[1,"text-xs","text-gray-400","mt-1"],[3,"icon","spin"],[3,"value"],[1,"loading-state"],[1,"flex","items-center","justify-center","py-12"],[1,"text-2xl","text-blue-500","mr-3",3,"icon","spin"],[1,"text-gray-600"],[1,"translations-table"],[1,"table-container"],[1,"w-full"],[1,"border-b","border-gray-200"],[1,"text-left","py-3","px-4","font-medium","text-gray-700","w-1/3"],[1,"text-left","py-3","px-4","font-medium","text-gray-700"],[1,"text-center","py-3","px-4","font-medium","text-gray-700","w-20"],["class","border-b border-gray-100 hover:bg-gray-50",3,"bg-yellow-50",4,"ngFor","ngForOf","ngForTrackBy"],["class","empty-state",4,"ngIf"],[1,"border-b","border-gray-100","hover:bg-gray-50"],[1,"py-3","px-4"],[1,"font-mono","text-sm","text-gray-800"],[1,"text-xs","text-gray-500","mt-1"],[1,"form-textarea","w-full","min-h-[60px]","resize-y",3,"ngModelChange","ngModel","placeholder"],[1,"py-3","px-4","text-center"],["class","inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",4,"ngIf"],["class","inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",4,"ngIf"],[1,"inline-flex","items-center","px-2","py-1","rounded-full","text-xs","font-medium","bg-orange-100","text-orange-800"],[1,"mr-1",3,"icon"],[1,"inline-flex","items-center","px-2","py-1","rounded-full","text-xs","font-medium","bg-gray-100","text-gray-800"],[1,"empty-state"],[1,"text-center","py-12"],[1,"text-4xl","text-gray-300","mb-4",3,"icon"],[1,"text-lg","font-medium","text-gray-900","mb-2"],[1,"text-gray-500"],[1,"footer-actions","mt-4"],[1,"flex","items-center","justify-between","py-4","border-t","border-gray-200"],["class","ml-4",4,"ngIf"],[1,"ml-4"],[1,"languages-tab"],[1,"grid","grid-cols-1","lg:grid-cols-2","gap-6"],[1,"predefined-languages-section"],[1,"section-header"],[1,"text-lg","font-semibold","text-gray-900","mb-2"],[1,"text-sm","text-gray-600","mb-4"],[1,"relative","mb-4"],["type","text",1,"form-input","pl-10","w-full",3,"ngModelChange","ngModel","placeholder"],[1,"language-grid"],["class","language-card predefined",3,"selected","is-default",4,"ngFor","ngForOf"],[1,"custom-languages-section"],[1,"flex","items-center","justify-between","mb-4"],[1,"text-sm","text-gray-600"],["type","button",1,"btn","btn-primary",3,"click"],[1,"custom-language-list"],["class","language-card custom",3,"selected","inactive","is-default",4,"ngFor","ngForOf"],[1,"settings-actions","mt-8"],[1,"language-card","predefined"],[1,"language-info",3,"click"],[1,"language-details"],[1,"language-name"],[1,"language-code"],["class","default-indicator",4,"ngIf"],[1,"language-actions"],["type","button","class","btn-sm btn-outline text-xs","title","Set as Default",3,"click",4,"ngIf"],["class","text-green-500 ml-2",3,"icon",4,"ngIf"],["class","text-gray-400 ml-2",3,"icon",4,"ngIf"],[1,"default-indicator"],[1,"text-xs","bg-blue-100","text-blue-800","px-2","py-0.5","rounded-full"],["type","button","title","Set as Default",1,"btn-sm","btn-outline","text-xs",3,"click"],[1,"text-green-500","ml-2",3,"icon"],[1,"text-gray-400","ml-2",3,"icon"],[1,"language-card","custom"],["class","language-description",4,"ngIf"],["type","button","title","Edit",1,"btn-icon",3,"click"],["type","button","title","Delete",1,"btn-icon","text-red-600",3,"click"],[1,"language-description"],[1,"text-center","py-8"],[1,"text-gray-500","mb-4"],[1,"modal-overlay",3,"click"],[1,"modal-container",3,"click"],[1,"modal-header"],[1,"modal-title"],[1,"close-button",3,"click"],[1,"modal-content"],[3,"ngSubmit"],[1,"form-group"],[1,"form-label"],["type","text","name","language_code","placeholder","e.g., vi2, en-us, custom1","required","","pattern","^[a-z0-9_-]+$",1,"form-input",3,"ngModelChange","ngModel","disabled"],[1,"form-help"],["type","text","name","language_name","placeholder","e.g., Vi\u1EC7t Nam 2, English (US), Custom Language","required","",1,"form-input",3,"ngModelChange","ngModel"],["type","text","name","flag_class","placeholder","e.g., fi fi-vn, fi fi-us",1,"form-input",3,"ngModelChange","ngModel"],["name","description","rows","3",1,"form-textarea",3,"ngModelChange","ngModel","placeholder"],[1,"flex","items-center"],["type","checkbox","name","active",1,"form-checkbox",3,"ngModelChange","ngModel"],[1,"ml-2"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click","disabled"]],template:function(t,r){t&1&&(o(0,"div",2)(1,"div",3)(2,"div",4)(3,"div",5)(4,"button",6),b("click",function(){return r.goBack()}),x(5,"fa-icon",7),s(),o(6,"h1",8),c(7),g(8,"translate"),s()()(),o(9,"div",9)(10,"div",10)(11,"div")(12,"p",11),c(13),g(14,"translate"),s()(),M(15,ze,14,9,"div",12),s(),o(16,"div",13)(17,"nav",14)(18,"button",15),b("click",function(){return r.setActiveTab("languages")}),x(19,"fa-icon",7),c(20),g(21,"translate"),s(),o(22,"button",15),b("click",function(){return r.setActiveTab("translations")}),x(23,"fa-icon",7),c(24),g(25,"translate"),s()()()(),o(26,"div",16),M(27,nt,57,58,"div",17)(28,xt,40,34,"div",18),s(),M(29,ht,56,50,"div",19),s()()),t&2&&(i(5),l("icon",v(23,Se)),i(2),h(d(8,15,"i18n_management.title")),i(6),h(d(14,17,"i18n_management.description")),i(2),l("ngIf",r.activeTab==="translations"),i(3),I("active",r.activeTab==="languages"),i(),l("icon",v(24,Le)),i(),C(" ",d(21,19,"i18n_management.languages_tab")," "),i(2),I("active",r.activeTab==="translations"),i(),l("icon",v(25,be)),i(),C(" ",d(25,21,"i18n_management.translations_tab")," "),i(3),l("ngIf",r.activeTab==="translations"),i(),l("ngIf",r.activeTab==="languages"),i(),l("ngIf",r.showCustomLanguageForm))},dependencies:[J,q,Y,H,me,se,ce,ge,ne,te,le,ie,ae,de,pe,re,oe,ee,Z,Q,G],styles:[".page-container[_ngcontent-%COMP%]{margin-left:auto;margin-right:auto;max-width:80rem;padding-top:.5rem;padding-bottom:.5rem;width:100%}.content-container[_ngcontent-%COMP%]{border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:0!important;--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.page-header[_ngcontent-%COMP%]{margin-bottom:1rem;display:flex;align-items:center;justify-content:space-between;border-bottom-width:1px;--tw-border-opacity: 1;border-color:rgb(238 238 238 / var(--tw-border-opacity, 1));padding-bottom:.75rem}.language-selection[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{align-items:flex-end}.language-selection[_ngcontent-%COMP%]   .page-header.items-end[_ngcontent-%COMP%]{gap:.5rem}@media (max-width: 768px){.header-section[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.language-selection[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:1rem}.filters-section[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%], .footer-actions[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{flex-direction:column;gap:.75rem}}.page-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:1rem;border-color:#e5e7eb;border-bottom-width:1px}.page-title[_ngcontent-%COMP%]{color:var(--gray-800);font-size:1.5rem;line-height:2rem;font-weight:600}.back-button[_ngcontent-%COMP%]{padding:.5rem;--tw-text-opacity: 1;color:rgb(158 158 158 / var(--tw-text-opacity, 1));transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s;color:#6b7280;border-radius:9999px}.back-button[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(245 245 245 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(97 97 97 / var(--tw-text-opacity, 1))}.i18n-management-container[_ngcontent-%COMP%]{max-width:1280px;margin:0 auto;padding:1.5rem;min-height:100vh}.header-section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.stats-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;border:1px solid #e5e7eb;padding:1rem;text-align:center;min-width:100px;box-shadow:0 1px 3px #0000001a}.language-selection[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;border:1px solid #e5e7eb;padding:1rem;margin-bottom:1.5rem;box-shadow:0 1px 3px #0000001a}.language-selection[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%]{align-items:flex-end}.language-selection[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{height:42px;display:inline-flex;align-items:center;white-space:nowrap;min-width:auto}.language-selection[_ngcontent-%COMP%]   .flex.items-end[_ngcontent-%COMP%]{gap:.5rem}.filters-section[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;border:1px solid #e5e7eb;padding:1rem;margin-bottom:1.5rem;box-shadow:0 1px 3px #0000001a}.form-select[_ngcontent-%COMP%]{display:block;width:100%;padding:.5rem .75rem;border:1px solid #d1d5db;border-radius:.375rem;box-shadow:0 1px 2px #0000000d;background-color:#fff;color:#374151;height:42px}.form-select[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.form-input[_ngcontent-%COMP%]{display:block;width:100%;padding:.5rem .75rem;border:1px solid #d1d5db;border-radius:.375rem;box-shadow:0 1px 2px #0000000d;background-color:#fff;color:#374151}.form-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.form-textarea[_ngcontent-%COMP%]{display:block;width:100%;padding:.5rem .75rem;border:1px solid #d1d5db;border-radius:.375rem;box-shadow:0 1px 2px #0000000d;background-color:#fff;color:#374151;resize:none}.form-textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.5rem 1rem;border:1px solid transparent;font-size:.875rem;font-weight:500;border-radius:.375rem;box-shadow:0 1px 2px #0000000d;transition:all .2s;cursor:pointer}.btn[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #3b82f61a}.btn-primary[_ngcontent-%COMP%]{color:#fff;background-color:#3b82f6}.btn-primary[_ngcontent-%COMP%]:hover{background-color:#2563eb}.btn-primary[_ngcontent-%COMP%]:disabled{background-color:#9ca3af;cursor:not-allowed}.btn-outline[_ngcontent-%COMP%]{color:#374151;background-color:#fff;border-color:#d1d5db}.btn-outline[_ngcontent-%COMP%]:hover{background-color:#f9fafb}.btn-outline[_ngcontent-%COMP%]:disabled{color:#9ca3af;cursor:not-allowed}.btn-outline[_ngcontent-%COMP%]:disabled:hover{background-color:#fff}.loading-state[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;border:1px solid #e5e7eb;padding:2rem}.translations-table[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;border:1px solid #e5e7eb;overflow:hidden}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.table-container[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{min-width:100%;border-collapse:collapse}.table-container[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#f9fafb;text-align:left;font-size:.75rem;font-weight:500;color:#6b7280;text-transform:uppercase;letter-spacing:.05em;padding:.75rem;border-bottom:1px solid #e5e7eb}.table-container[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{white-space:nowrap;font-size:.875rem;color:#111827;padding:.75rem;border-bottom:1px solid #e5e7eb}.empty-state[_ngcontent-%COMP%]{background-color:#f9fafb;padding:3rem;text-align:center}.footer-actions[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;border:1px solid #e5e7eb;padding:1.5rem}.form-textarea[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.form-textarea[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.form-textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.form-textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.bg-yellow-50[_ngcontent-%COMP%]{transition:background-color .2s ease-in-out}.fi[_ngcontent-%COMP%]{margin-right:8px}@media (max-width: 768px){.i18n-management-container[_ngcontent-%COMP%]{padding:1rem}.header-section[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.language-selection[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch!important;gap:1rem}.language-selection[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{height:auto;padding:.75rem 1rem}.filters-section[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%], .footer-actions[_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%]{flex-direction:column;gap:.75rem}.table-container[_ngcontent-%COMP%]{font-size:.875rem}.stats-card[_ngcontent-%COMP%]{min-width:80px;padding:.5rem .75rem}}.flex[_ngcontent-%COMP%]{display:flex}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.items-center[_ngcontent-%COMP%]{align-items:center}.items-end[_ngcontent-%COMP%]{align-items:flex-end}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.space-x-4[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-left:1rem}.space-y-4[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-top:1rem}.space-x-3[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-left:.75rem}.space-x-2[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-left:.5rem}.space-y-3[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] + *[_ngcontent-%COMP%]{margin-top:.75rem}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-medium[_ngcontent-%COMP%]{font-weight:500}.text-gray-600[_ngcontent-%COMP%]{color:#4b5563}.text-gray-500[_ngcontent-%COMP%]{color:#6b7280}.text-blue-600[_ngcontent-%COMP%]{color:#2563eb}.bg-yellow-50[_ngcontent-%COMP%]{background-color:#fffbeb;transition:background-color .2s ease-in-out}.w-full[_ngcontent-%COMP%]{width:100%}.h-20[_ngcontent-%COMP%]{height:5rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-1[_ngcontent-%COMP%]{margin-bottom:.25rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.ml-4[_ngcontent-%COMP%]{margin-left:1rem}.py-12[_ngcontent-%COMP%]{padding-top:3rem;padding-bottom:3rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-2[_ngcontent-%COMP%]{padding-left:.5rem;padding-right:.5rem}.pl-10[_ngcontent-%COMP%]{padding-left:2.5rem}.w-48[_ngcontent-%COMP%]{width:12rem}.w-20[_ngcontent-%COMP%]{width:5rem}.w-1\\/3[_ngcontent-%COMP%]{width:33.333333%}.flex-1[_ngcontent-%COMP%]{flex:1 1 0%}.min-h-\\__ph-0__[_ngcontent-%COMP%]{min-height:60px}.text-4xl[_ngcontent-%COMP%]{font-size:2.25rem;line-height:2.5rem}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.font-mono[_ngcontent-%COMP%]{font-family:ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace}.text-gray-900[_ngcontent-%COMP%]{color:#111827}.text-gray-800[_ngcontent-%COMP%]{color:#1f2937}.text-gray-700[_ngcontent-%COMP%]{color:#374151}.text-gray-400[_ngcontent-%COMP%]{color:#9ca3af}.text-gray-300[_ngcontent-%COMP%]{color:#d1d5db}.text-blue-500[_ngcontent-%COMP%]{color:#3b82f6}.text-orange-500[_ngcontent-%COMP%]{color:#f97316}.text-orange-600[_ngcontent-%COMP%]{color:#ea580c}.text-orange-800[_ngcontent-%COMP%]{color:#9a3412}.bg-gray-50[_ngcontent-%COMP%]{background-color:#f9fafb}.bg-gray-100[_ngcontent-%COMP%]{background-color:#f3f4f6}.bg-orange-50[_ngcontent-%COMP%]{background-color:#fff7ed}.bg-orange-100[_ngcontent-%COMP%]{background-color:#ffedd5}.border-gray-200[_ngcontent-%COMP%]{border-color:#e5e7eb}.border-gray-100[_ngcontent-%COMP%]{border-color:#f3f4f6}.border-orange-300[_ngcontent-%COMP%]{border-color:#fdba74}.border-b[_ngcontent-%COMP%]{border-bottom-width:1px}.border-t[_ngcontent-%COMP%]{border-top-width:1px}.hover\\:bg-gray-50[_ngcontent-%COMP%]:hover{background-color:#f9fafb}.text-left[_ngcontent-%COMP%]{text-align:left}.text-center[_ngcontent-%COMP%]{text-align:center}.inline-flex[_ngcontent-%COMP%]{display:inline-flex}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.resize-y[_ngcontent-%COMP%]{resize:vertical}.relative[_ngcontent-%COMP%]{position:relative}.absolute[_ngcontent-%COMP%]{position:absolute}.left-3[_ngcontent-%COMP%]{left:.75rem}.top-1\\/2[_ngcontent-%COMP%]{top:50%}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\/2[_ngcontent-%COMP%]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.language-dropdown-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%;padding:.5rem .75rem;border:1px solid #d1d5db;border-radius:.375rem;background-color:#fff;color:#374151;font-size:.875rem;cursor:pointer;transition:all .2s;height:42px}.language-dropdown-button[_ngcontent-%COMP%]:hover{border-color:#9ca3af}.language-dropdown-button[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.language-dropdown-button[_ngcontent-%COMP%]:disabled{background-color:#f9fafb;color:#9ca3af;cursor:not-allowed}.language-dropdown-menu[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;z-index:50;margin-top:.25rem;background-color:#fff;border:1px solid #d1d5db;border-radius:.375rem;box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;max-height:200px;overflow-y:auto}.language-dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:.5rem .75rem;cursor:pointer;transition:background-color .2s;font-size:.875rem;color:#374151}.language-dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f9fafb}.language-dropdown-item.selected[_ngcontent-%COMP%]{background-color:#eff6ff;color:#1d4ed8}.language-dropdown-empty[_ngcontent-%COMP%]{padding:.75rem;text-align:center;color:#6b7280;font-size:.875rem;font-style:italic}.flag-icon[_ngcontent-%COMP%]{width:20px;height:15px;margin-right:.5rem;border-radius:2px;flex-shrink:0}.rotate-180[_ngcontent-%COMP%]{transform:rotate(180deg)}.transition-transform[_ngcontent-%COMP%]{transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1)}.duration-200[_ngcontent-%COMP%]{transition-duration:.2s}.tab-navigation[_ngcontent-%COMP%]{margin-bottom:2rem}.tab-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;font-size:.875rem;font-weight:500;color:#6b7280;border-bottom:2px solid transparent;background:none;border-top:none;border-left:none;border-right:none;cursor:pointer;transition:all .2s}.tab-button[_ngcontent-%COMP%]:hover{color:#374151;border-bottom-color:#d1d5db}.tab-button.active[_ngcontent-%COMP%]{color:#2563eb;border-bottom-color:#2563eb}.tab-content[_ngcontent-%COMP%]{min-height:400px}.languages-tab[_ngcontent-%COMP%]{padding:1rem 0}.section-header[_ngcontent-%COMP%]{margin-bottom:1rem}.language-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:.75rem;max-height:400px;overflow-y:auto;padding:.5rem;border:1px solid #e5e7eb;border-radius:.5rem;background-color:#f9fafb}.language-card[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:.75rem;background-color:#fff;border:1px solid #e5e7eb;border-radius:.5rem;cursor:pointer;transition:all .2s}.language-card[_ngcontent-%COMP%]:hover{border-color:#d1d5db;box-shadow:0 1px 3px #0000001a}.language-card.selected[_ngcontent-%COMP%]{border-color:#10b981;background-color:#ecfdf5}.language-card.custom[_ngcontent-%COMP%]{border-color:#3b82f6;background-color:#eff6ff}.language-card.custom.selected[_ngcontent-%COMP%]{border-color:#10b981;background-color:#ecfdf5}.language-card.is-default[_ngcontent-%COMP%]{border-color:#3b82f6;background-color:#eff6ff;box-shadow:0 2px 4px #3b82f633}.language-card.inactive[_ngcontent-%COMP%]{opacity:.5}.language-info[_ngcontent-%COMP%]{display:flex;align-items:center;flex:1}.language-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.language-name[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#111827}.language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6b7280}.language-description[_ngcontent-%COMP%]{font-size:.75rem;color:#9ca3af;margin-top:.25rem}.default-indicator[_ngcontent-%COMP%]{margin-top:.25rem}.language-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.btn-icon[_ngcontent-%COMP%]{padding:.25rem;color:#6b7280;background:none;border:none;border-radius:.25rem;cursor:pointer;transition:color .2s}.btn-icon[_ngcontent-%COMP%]:hover{color:#374151}.btn-icon.text-red-600[_ngcontent-%COMP%]{color:#dc2626}.btn-icon.text-red-600[_ngcontent-%COMP%]:hover{color:#b91c1c}.custom-language-list[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto;padding:.5rem;border:1px solid #e5e7eb;border-radius:.5rem;background-color:#f9fafb}.custom-language-list[_ngcontent-%COMP%]   .language-card[_ngcontent-%COMP%]{margin-bottom:.75rem}.custom-language-list[_ngcontent-%COMP%]   .language-card[_ngcontent-%COMP%]:last-child{margin-bottom:0}.language-settings-section[_ngcontent-%COMP%]{margin-top:2rem}.selected-languages-summary[_ngcontent-%COMP%]{margin-bottom:1rem}.selected-languages-summary[_ngcontent-%COMP%]   .flag-icon[_ngcontent-%COMP%]{width:1rem;height:1rem;border-radius:.125rem}.default-language-selection[_ngcontent-%COMP%]{margin-bottom:1rem}.default-language-selection[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{transition:all .2s ease-in-out}.default-language-selection[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.settings-actions[_ngcontent-%COMP%]{margin-top:1rem}.settings-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{box-shadow:0 2px 4px #3b82f633}.settings-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px #3b82f64d;transform:translateY(-1px)}.modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:#00000080;display:flex;align-items:center;justify-content:center;z-index:1000;padding:1rem}.modal-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;box-shadow:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;max-width:500px;width:100%;max-height:90vh;overflow-y:auto}.modal-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1.5rem;border-bottom:1px solid #e5e7eb}.modal-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:#111827}.close-button[_ngcontent-%COMP%]{padding:.25rem;color:#6b7280;background:none;border:none;border-radius:.25rem;cursor:pointer;transition:color .2s}.close-button[_ngcontent-%COMP%]:hover{color:#374151}.modal-content[_ngcontent-%COMP%]{padding:1.5rem}.modal-footer[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:.75rem;padding:1.5rem;border-top:1px solid #e5e7eb}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.form-label[_ngcontent-%COMP%]{display:block;font-size:.875rem;font-weight:500;color:#374151;margin-bottom:.5rem}.form-help[_ngcontent-%COMP%]{margin-top:.25rem;font-size:.75rem;color:#6b7280}.form-checkbox[_ngcontent-%COMP%]{width:1rem;height:1rem;color:#3b82f6;border:1px solid #d1d5db;border-radius:.25rem}.form-checkbox[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #3b82f61a}.btn-sm[_ngcontent-%COMP%]{padding:.375rem .75rem;font-size:.75rem}.btn-secondary[_ngcontent-%COMP%]{color:#374151;background-color:#fff;border:1px solid #d1d5db}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#f9fafb}.btn-secondary[_ngcontent-%COMP%]:disabled{color:#9ca3af;cursor:not-allowed}.grid[_ngcontent-%COMP%]{display:grid}.grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.gap-6[_ngcontent-%COMP%]{gap:1.5rem}.max-w-2xl[_ngcontent-%COMP%]{max-width:42rem}@media (min-width: 1024px){.lg\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (max-width: 768px){.tab-navigation[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:0}.tab-button[_ngcontent-%COMP%]{border-bottom:none;border-left:4px solid transparent;padding-left:1rem;justify-content:flex-start}.tab-button.active[_ngcontent-%COMP%]{border-bottom:none;border-left-color:#2563eb}.language-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.modal-container[_ngcontent-%COMP%]{margin:1rem;max-width:calc(100% - 2rem)}.selected-language-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.75rem}.selected-language-item[_ngcontent-%COMP%]   .language-actions[_ngcontent-%COMP%]{align-self:stretch;justify-content:flex-end}}"]});let a=m;return a})();export{Et as I18nManagementComponent};
