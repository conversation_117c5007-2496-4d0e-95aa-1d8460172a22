import{d as J}from"./chunk-HJYZAHTZ.js";import{$a as w,Ab as O,Da as c,Db as L,Eb as b,H as x,I as S,Q as p,Sa as _,T as v,Ua as l,W as U,Y as s,Za as h,_a as f,ac as R,ba as N,cc as z,eb as m,g,kc as H,lc as I,ld as j,ob as k,oc as F,pb as y,tc as E,wa as M}from"./chunk-MCI2ITGN.js";var d=class d{};d.PANEL="PANEL",d.USER="USER",d.ADMIN_PANEL="ADMIN_PANEL",d.ROLES=[d.PANEL,d.USER,d.ADMIN_PANEL];var $=d;var W=new U("JWT_OPTIONS"),C=(()=>{class n{constructor(t=null){this.tokenGetter=t&&t.tokenGetter||function(){}}urlBase64Decode(t){let e=t.replace(/-/g,"+").replace(/_/g,"/");switch(e.length%4){case 0:break;case 2:{e+="==";break}case 3:{e+="=";break}default:throw new Error("Illegal base64url string!")}return this.b64DecodeUnicode(e)}b64decode(t){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i="";if(t=String(t).replace(/=+$/,""),t.length%4===1)throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");for(let o=0,u,a,T=0;a=t.charAt(T++);~a&&(u=o%4?u*64+a:a,o++%4)?i+=String.fromCharCode(255&u>>(-2*o&6)):0)a=e.indexOf(a);return i}b64DecodeUnicode(t){return decodeURIComponent(Array.prototype.map.call(this.b64decode(t),e=>"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)).join(""))}decodeToken(t=this.tokenGetter()){return t instanceof Promise?t.then(e=>this._decodeToken(e)):this._decodeToken(t)}_decodeToken(t){if(!t||t==="")return null;let e=t.split(".");if(e.length!==3)throw new Error("The inspected token doesn't appear to be a JWT. Check to make sure it has three parts and see https://jwt.io for more.");let i=this.urlBase64Decode(e[1]);if(!i)throw new Error("Cannot decode the token.");return JSON.parse(i)}getTokenExpirationDate(t=this.tokenGetter()){return t instanceof Promise?t.then(e=>this._getTokenExpirationDate(e)):this._getTokenExpirationDate(t)}_getTokenExpirationDate(t){let e;if(e=this.decodeToken(t),!e||!e.hasOwnProperty("exp"))return null;let i=new Date(0);return i.setUTCSeconds(e.exp),i}isTokenExpired(t=this.tokenGetter(),e){return t instanceof Promise?t.then(i=>this._isTokenExpired(i,e)):this._isTokenExpired(t,e)}_isTokenExpired(t,e){if(!t||t==="")return!0;let i=this.getTokenExpirationDate(t);return e=e||0,i===null?!1:!(i.valueOf()>new Date().valueOf()+e*1e3)}getAuthScheme(t,e){return typeof t=="function"?t(e):t}}return n.\u0275fac=function(t){return new(t||n)(s(W))},n.\u0275prov=v({token:n,factory:n.\u0275fac}),n})();var G=(()=>{let r=class r{constructor(e,i,o,u,a){this.router=e,this.http=i,this.jwtHelper=o,this.platformId=u,this.configService=a,this._loading$=new g(!1);let T;if(I(this.platformId)){let P=localStorage.getItem("user");if(P)try{T=JSON.parse(P)}catch(B){console.error("Error parsing user from localStorage",B)}}this._authSubject$=new g(T),this._auth$=this._authSubject$.asObservable()}get authValue(){return this._authSubject$.getValue()}isAdmin(){let e=this.getRoles();return e?e.every(i=>[$.PANEL].includes(i)):!1}getRoles(){let e=this.authValue;if(e)return this.jwtHelper.decodeToken(e.tokens.access_token).roles}isAuthenticated(){let e=this.authValue;return!!(e&&e.tokens.access_token)}isTokenExpired(){return this.isAuthenticated()&&this.jwtHelper.isTokenExpired(this.authValue.tokens.access_token)}register(e){return this.loading$.next(!0),this.http.post(`${this.configService.apiUrl}/users/register`,e).pipe(S(),p(i=>this.updateUser(i)),x(()=>this.loading$.next(!1)))}updateUser(e){if(this._authSubject$.next(e),console.log("AuthService - updateUser called, user:",e==null?void 0:e.id),I(this.platformId))try{localStorage.setItem("user",JSON.stringify(e)),console.log("AuthService - User saved to localStorage")}catch(i){console.error("AuthService - Error saving user to localStorage:",i)}}refreshToken(e){this.loading$.next(!0);let i=new F({"x-refresh-token":e});return this.http.post(`${this.configService.apiUrl}/access/refresh`,null,{headers:i}).pipe(S(),p(o=>this.updateUser(o)),x(()=>this.loading$.next(!1)))}login(e,i){return console.log("AuthService - login method called"),this.loading$.next(!0),this.http.post(`${this.configService.apiUrl}/access/login`,{user_name:e,password:i}).pipe(S(),p(o=>{console.log("AuthService - Login successful, updating user"),this.updateUser(o)}),x(()=>{console.log("AuthService - Login finalized"),this.loading$.next(!1)}))}logout(){this.loading$.next(!0),this.http.post(`${this.configService.apiUrl}/access/logout`,null).pipe(S(),x(()=>{this.loading$.next(!1),this._authSubject$.next(void 0),I(this.platformId)&&localStorage.removeItem("user"),this.redirectTo("/auth/login")})).subscribe()}redirectTo(e){this.router.navigate([e])}get loading$(){return this._loading$}get auth$(){return this._auth$}};r.\u0275fac=function(i){return new(i||r)(s(J),s(E),s(C),s(M),s(j))},r.\u0275prov=v({token:r,factory:r.\u0275fac,providedIn:"root"});let n=r;return n})();var ye=(()=>{let r=class r{get currentTenantValue(){return this._currentTenant$.value}get accessibleTenantsValue(){return this._accessibleTenants$.value}constructor(e,i,o,u){this.http=e,this.configService=i,this.authService=o,this.jwtHelper=u,this._currentTenant$=new g(null),this._accessibleTenants$=new g([]),this.currentTenant$=this._currentTenant$.asObservable(),this.accessibleTenants$=this._accessibleTenants$.asObservable(),this.updateTenantInfoFromToken(),this.authService.auth$.subscribe(a=>{a&&a.tokens&&a.tokens.access_token?this.updateTenantInfoFromToken():(this._currentTenant$.next(null),this._accessibleTenants$.next([]))})}updateTenantInfoFromToken(){let e=this.authService.authValue;if(e&&e.tokens&&e.tokens.access_token)try{let i=this.jwtHelper.decodeToken(e.tokens.access_token);console.log("TenantService - Token payload:",i);let o=i.current_tenant_id;o&&(this._currentTenant$.next(o),typeof localStorage<"u"&&localStorage.setItem("tenant_id",o))}catch(i){console.error("Error extracting tenant info from token:",i)}}switchTenant(e){return this.http.post(`${this.configService.apiUrl}/access/switch/${e}`,{}).pipe(p(i=>{let o=this.authService.authValue;o&&o.tokens&&(o.tokens.access_token=i.access_token,o.tokens.refresh_token=i.refresh_token,o.tokens.client_id=i.client_id,this.authService.updateUser(o)),typeof localStorage<"u"&&(localStorage.removeItem("app_design_settings"),localStorage.removeItem("general_settings")),this.updateTenantInfoFromToken()}))}reloadDataAfterTenantSwitch(){this.updateTenantInfoFromToken(),this.getAccessibleTenants().subscribe()}getAccessibleTenants(){return this.http.get(`${this.configService.apiUrl}/access/tenants/accessible`).pipe(p(e=>{this._accessibleTenants$.next(e)}))}getAllTenantSubscriptions(){return this.http.get(`${this.configService.apiUrl}/tenant-subscriptions`)}toggleAutoRenewal(e,i){return this.http.put(`${this.configService.apiUrl}/tenant-subscriptions/${e}/auto-renewal`,{auto_renewal:i})}};r.\u0275fac=function(i){return new(i||r)(s(E),s(j),s(G),s(C))},r.\u0275prov=v({token:r,factory:r.\u0275fac,providedIn:"root"});let n=r;return n})();var D=(n,r,t)=>({sm:n,md:r,lg:t}),Y=(n,r)=>({"bg-white bg-opacity-70":n,"bg-transparent":r});function Z(n,r){if(n&1&&(h(0,"div",7),k(1),f()),n&2){let t=m(2);c(),y(t.message)}}function q(n,r){if(n&1&&(h(0,"div",3)(1,"div",4),w(2,"div",5),_(3,Z,2,1,"div",6),f()()),n&2){let t=m();c(2),l("ngClass",b(2,D,t.size==="sm",t.size==="md",t.size==="lg")),c(),l("ngIf",t.message)}}function ee(n,r){if(n&1&&(h(0,"div",10),k(1),f()),n&2){let t=m(2);c(),y(t.message)}}function te(n,r){if(n&1&&(h(0,"div",8)(1,"div",4),w(2,"div",5),_(3,ee,2,1,"div",9),f()()),n&2){let t=m();l("ngClass",L(3,Y,!t.transparent,t.transparent)),c(2),l("ngClass",b(6,D,t.size==="sm",t.size==="md",t.size==="lg")),c(),l("ngIf",t.message)}}function ie(n,r){if(n&1&&(h(0,"div",10),k(1),f()),n&2){let t=m(2);c(),y(t.message)}}function ne(n,r){if(n&1&&(h(0,"div",11)(1,"div",4),w(2,"div",5),_(3,ie,2,1,"div",9),f()()),n&2){let t=m();c(2),l("ngClass",b(2,D,t.size==="sm",t.size==="md",t.size==="lg")),c(),l("ngIf",t.message)}}var je=(()=>{let r=class r{constructor(){this.size="md",this.overlay=!1,this.fullScreen=!1,this.message="",this.transparent=!1}};r.\u0275fac=function(i){return new(i||r)},r.\u0275cmp=N({type:r,selectors:[["app-loading"]],inputs:{size:"size",overlay:"overlay",fullScreen:"fullScreen",message:"message",transparent:"transparent"},standalone:!0,features:[O],decls:3,vars:3,consts:[["class","fixed inset-0 bg-white bg-opacity-90 flex justify-center items-center z-50",4,"ngIf"],["class","absolute inset-0 flex justify-center items-center z-10",3,"ngClass",4,"ngIf"],["class","flex justify-center items-center",4,"ngIf"],[1,"fixed","inset-0","bg-white","bg-opacity-90","flex","justify-center","items-center","z-50"],[1,"flex","flex-col","items-center"],[1,"loading-spinner",3,"ngClass"],["class","mt-4 text-gray-800 font-medium",4,"ngIf"],[1,"mt-4","text-gray-800","font-medium"],[1,"absolute","inset-0","flex","justify-center","items-center","z-10",3,"ngClass"],["class","mt-2 text-gray-800 font-medium",4,"ngIf"],[1,"mt-2","text-gray-800","font-medium"],[1,"flex","justify-center","items-center"]],template:function(i,o){i&1&&_(0,q,4,6,"div",0)(1,te,4,10,"div",1)(2,ne,4,6,"div",2),i&2&&(l("ngIf",o.fullScreen),c(),l("ngIf",o.overlay&&!o.fullScreen),c(),l("ngIf",!o.overlay&&!o.fullScreen))},dependencies:[H,R,z],styles:[".loading-spinner[_ngcontent-%COMP%]{border-radius:50%;border:3px solid rgba(var(--primary-rgb, 48, 176, 199),.2);border-top-color:var(--primary, #30B0C7);animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}.loading-spinner.sm[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}.loading-spinner.md[_ngcontent-%COMP%]{width:2.5rem;height:2.5rem}.loading-spinner.lg[_ngcontent-%COMP%]{width:4rem;height:4rem}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}"]});let n=r;return n})();export{$ as a,W as b,C as c,G as d,ye as e,je as f};
