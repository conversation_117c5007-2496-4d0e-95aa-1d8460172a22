package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.dto.request.UserReq;
import tndung.vnfb.smm.dto.response.ApiKeyUserRes;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.GUserSuperRes;
import tndung.vnfb.smm.entity.GUser;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class GUserMapperImpl implements GUserMapper {

    @Autowired
    private CurrencyMapper currencyMapper;

    @Override
    public ApiKeyUserRes toApiKey(GUser user) {
        if ( user == null ) {
            return null;
        }

        ApiKeyUserRes apiKeyUserRes = new ApiKeyUserRes();

        apiKeyUserRes.setId( user.getId() );
        apiKeyUserRes.setUserName( user.getUserName() );
        apiKeyUserRes.setEmail( user.getEmail() );
        apiKeyUserRes.setPhone( user.getPhone() );
        apiKeyUserRes.setAvatar( user.getAvatar() );
        apiKeyUserRes.setBalance( user.getBalance() );
        apiKeyUserRes.setStatus( user.getStatus() );
        apiKeyUserRes.setLastLoginAt( user.getLastLoginAt() );
        apiKeyUserRes.setCustomDiscount( user.getCustomDiscount() );
        apiKeyUserRes.setCustomReferralRate( user.getCustomReferralRate() );
        apiKeyUserRes.setCreatedAt( user.getCreatedAt() );
        apiKeyUserRes.setPreferredCurrency( currencyMapper.toRes( user.getPreferredCurrency() ) );
        apiKeyUserRes.setMfaEnabled( user.getMfaEnabled() );
        apiKeyUserRes.setTotalOrder( user.getTotalOrder() );
        apiKeyUserRes.setApiKey( user.getApiKey() );

        return apiKeyUserRes;
    }

    @Override
    public GUserRes toRes(GUser user) {
        if ( user == null ) {
            return null;
        }

        GUserRes gUserRes = new GUserRes();

        gUserRes.setId( user.getId() );
        gUserRes.setUserName( user.getUserName() );
        gUserRes.setEmail( user.getEmail() );
        gUserRes.setPhone( user.getPhone() );
        gUserRes.setAvatar( user.getAvatar() );
        gUserRes.setBalance( user.getBalance() );
        gUserRes.setStatus( user.getStatus() );
        gUserRes.setLastLoginAt( user.getLastLoginAt() );
        gUserRes.setCustomDiscount( user.getCustomDiscount() );
        gUserRes.setCustomReferralRate( user.getCustomReferralRate() );
        gUserRes.setCreatedAt( user.getCreatedAt() );
        gUserRes.setPreferredCurrency( currencyMapper.toRes( user.getPreferredCurrency() ) );
        gUserRes.setMfaEnabled( user.getMfaEnabled() );
        gUserRes.setTotalOrder( user.getTotalOrder() );

        return gUserRes;
    }

    @Override
    public List<GUserRes> toRes(List<GUser> users) {
        if ( users == null ) {
            return null;
        }

        List<GUserRes> list = new ArrayList<GUserRes>( users.size() );
        for ( GUser gUser : users ) {
            list.add( toRes( gUser ) );
        }

        return list;
    }

    @Override
    public GUserSuperRes toSuperRes(GUser user) {
        if ( user == null ) {
            return null;
        }

        GUserSuperRes gUserSuperRes = new GUserSuperRes();

        gUserSuperRes.setId( user.getId() );
        gUserSuperRes.setUserName( user.getUserName() );
        gUserSuperRes.setEmail( user.getEmail() );
        gUserSuperRes.setPhone( user.getPhone() );
        gUserSuperRes.setAvatar( user.getAvatar() );
        gUserSuperRes.setBalance( user.getBalance() );
        gUserSuperRes.setLastLoginAt( user.getLastLoginAt() );
        gUserSuperRes.setCustomDiscount( user.getCustomDiscount() );
        gUserSuperRes.setCustomReferralRate( user.getCustomReferralRate() );
        gUserSuperRes.setCreatedAt( user.getCreatedAt() );
        gUserSuperRes.setPreferredCurrency( currencyMapper.toRes( user.getPreferredCurrency() ) );
        gUserSuperRes.setMfaEnabled( user.getMfaEnabled() );
        gUserSuperRes.setTotalOrder( user.getTotalOrder() );
        gUserSuperRes.setApiKey( user.getApiKey() );
        gUserSuperRes.setLanguage( user.getLanguage() );
        gUserSuperRes.setSecretKey( user.getSecretKey() );
        List<Role> list = user.getRoles();
        if ( list != null ) {
            gUserSuperRes.setRoles( new ArrayList<Role>( list ) );
        }
        gUserSuperRes.setTimeZone( user.getTimeZone() );
        gUserSuperRes.setStatus( user.getStatus() );

        return gUserSuperRes;
    }

    @Override
    public List<GUserSuperRes> toSuperRes(List<GUser> users) {
        if ( users == null ) {
            return null;
        }

        List<GUserSuperRes> list = new ArrayList<GUserSuperRes>( users.size() );
        for ( GUser gUser : users ) {
            list.add( toSuperRes( gUser ) );
        }

        return list;
    }

    @Override
    public GUser toEntity(UserReq req) {
        if ( req == null ) {
            return null;
        }

        GUser gUser = new GUser();

        gUser.setUserName( req.getUserName() );
        gUser.setEmail( req.getEmail() );
        gUser.setPhone( req.getPhone() );
        gUser.setPassword( req.getPassword() );

        return gUser;
    }
}
