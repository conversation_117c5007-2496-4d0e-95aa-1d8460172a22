package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.AddFundReq;
import tndung.vnfb.smm.dto.response.MyTransactionRes;
import tndung.vnfb.smm.dto.response.TransactionRes;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GTransaction;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class TransactionLogMapperImpl implements TransactionLogMapper {

    @Autowired
    private OrderMapper orderMapper;

    @Override
    public GTransaction toEntity(AddFundReq req) {
        if ( req == null ) {
            return null;
        }

        GTransaction gTransaction = new GTransaction();

        gTransaction.setNote( req.getNote() );
        gTransaction.setSource( req.getSource() );

        return gTransaction;
    }

    @Override
    public TransactionRes toRes(GTransaction entity) {
        if ( entity == null ) {
            return null;
        }

        TransactionRes transactionRes = new TransactionRes();

        Long id = entityOrderId( entity );
        if ( id != null ) {
            transactionRes.setOrderId( id.intValue() );
        }
        if ( entity.getId() != null ) {
            transactionRes.setId( entity.getId().intValue() );
        }
        transactionRes.setNote( entity.getNote() );
        transactionRes.setChange( entity.getChange() );
        transactionRes.setBalance( entity.getBalance() );
        transactionRes.setType( entity.getType() );
        transactionRes.setSource( entity.getSource() );
        transactionRes.setCreatedAt( entity.getCreatedAt() );

        return transactionRes;
    }

    @Override
    public List<TransactionRes> toRes(List<GTransaction> entity) {
        if ( entity == null ) {
            return null;
        }

        List<TransactionRes> list = new ArrayList<TransactionRes>( entity.size() );
        for ( GTransaction gTransaction : entity ) {
            list.add( toRes( gTransaction ) );
        }

        return list;
    }

    @Override
    public MyTransactionRes toMyRes(GTransaction entity) {
        if ( entity == null ) {
            return null;
        }

        MyTransactionRes myTransactionRes = new MyTransactionRes();

        myTransactionRes.setOrder( orderMapper.toRes( entity.getOrder() ) );
        if ( entity.getId() != null ) {
            myTransactionRes.setId( entity.getId().intValue() );
        }
        myTransactionRes.setNote( entity.getNote() );
        myTransactionRes.setChange( entity.getChange() );
        myTransactionRes.setBalance( entity.getBalance() );
        myTransactionRes.setType( entity.getType() );
        myTransactionRes.setSource( entity.getSource() );
        myTransactionRes.setCreatedAt( entity.getCreatedAt() );

        return myTransactionRes;
    }

    @Override
    public List<MyTransactionRes> toMyRes(List<GTransaction> entity) {
        if ( entity == null ) {
            return null;
        }

        List<MyTransactionRes> list = new ArrayList<MyTransactionRes>( entity.size() );
        for ( GTransaction gTransaction : entity ) {
            list.add( toMyRes( gTransaction ) );
        }

        return list;
    }

    private Long entityOrderId(GTransaction gTransaction) {
        if ( gTransaction == null ) {
            return null;
        }
        GOrder order = gTransaction.getOrder();
        if ( order == null ) {
            return null;
        }
        Long id = order.getId();
        if ( id == null ) {
            return null;
        }
        return id;
    }
}
