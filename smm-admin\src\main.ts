import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';

// Global error handler for uncaught errors
window.addEventListener('error', (event) => {
  // Handle specific known errors gracefully
  if (event.message.includes('global is not defined')) {
    console.warn('Global variable issue detected - this is handled by polyfills');
    event.preventDefault();
    return;
  }

  if (event.message.includes('addEventListener') && event.message.includes('null')) {
    console.warn('Attempted to add event listener to null element - likely from external script');
    event.preventDefault();
    return;
  }

  if (event.filename && event.filename.includes('share-modal')) {
    console.warn('Share modal script error - likely from browser extension or cached script');
    event.preventDefault();
    return;
  }
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && event.reason.message.includes('global is not defined')) {
    console.warn('Global variable promise rejection handled');
    event.preventDefault();
    return;
  }
});

bootstrapApplication(AppComponent, appConfig)
  .catch((err) => console.error(err));
