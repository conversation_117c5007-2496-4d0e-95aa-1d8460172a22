var ae=globalThis;function te(e){return(ae.__Zone_symbol_prefix||"__zone_symbol__")+e}function dt(){let e=ae.performance;function n(j){e&&e.mark&&e.mark(j)}function a(j,i){e&&e.measure&&e.measure(j,i)}n("Zone");let Y=class Y{static assertZonePatched(){if(ae.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let i=Y.current;for(;i.parent;)i=i.parent;return i}static get current(){return b.zone}static get currentTask(){return D}static __load_patch(i,s,o=!1){if(O<PERSON>hasOwnProperty(i)){let p=ae[te("forceDuplicateZoneCheck")]===!0;if(!o&&p)throw Error("Already loaded patch: "+i)}else if(!ae["__Zone_disable_"+i]){let p="Zone:"+i;n(p),O[i]=s(ae,Y,R),a(p,p)}}get parent(){return this._parent}get name(){return this._name}constructor(i,s){this._parent=i,this._name=s?s.name||"unnamed":"<root>",this._properties=s&&s.properties||{},this._zoneDelegate=new f(this,this._parent&&this._parent._zoneDelegate,s)}get(i){let s=this.getZoneWith(i);if(s)return s._properties[i]}getZoneWith(i){let s=this;for(;s;){if(s._properties.hasOwnProperty(i))return s;s=s._parent}return null}fork(i){if(!i)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,i)}wrap(i,s){if(typeof i!="function")throw new Error("Expecting function got: "+i);let o=this._zoneDelegate.intercept(this,i,s),p=this;return function(){return p.runGuarded(o,this,arguments,s)}}run(i,s,o,p){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,i,s,o,p)}finally{b=b.parent}}runGuarded(i,s=null,o,p){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,i,s,o,p)}catch(H){if(this._zoneDelegate.handleError(this,H))throw H}}finally{b=b.parent}}runTask(i,s,o){if(i.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(i.zone||Q).name+"; Execution: "+this.name+")");let p=i,{type:H,data:{isPeriodic:M=!1,isRefreshable:ie=!1}={}}=i;if(i.state===V&&(H===W||H===y))return;let le=i.state!=x;le&&p._transitionTo(x,d);let ue=D;D=p,b={parent:b,zone:this};try{H==y&&i.data&&!M&&!ie&&(i.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,p,s,o)}catch(re){if(this._zoneDelegate.handleError(this,re))throw re}}finally{let re=i.state;if(re!==V&&re!==z)if(H==W||M||ie&&re===k)le&&p._transitionTo(d,x,k);else{let h=p._zoneDelegates;this._updateTaskCount(p,-1),le&&p._transitionTo(V,x,V),ie&&(p._zoneDelegates=h)}b=b.parent,D=ue}}scheduleTask(i){if(i.zone&&i.zone!==this){let o=this;for(;o;){if(o===i.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${i.zone.name}`);o=o.parent}}i._transitionTo(k,V);let s=[];i._zoneDelegates=s,i._zone=this;try{i=this._zoneDelegate.scheduleTask(this,i)}catch(o){throw i._transitionTo(z,k,V),this._zoneDelegate.handleError(this,o),o}return i._zoneDelegates===s&&this._updateTaskCount(i,1),i.state==k&&i._transitionTo(d,k),i}scheduleMicroTask(i,s,o,p){return this.scheduleTask(new T(F,i,s,o,p,void 0))}scheduleMacroTask(i,s,o,p,H){return this.scheduleTask(new T(y,i,s,o,p,H))}scheduleEventTask(i,s,o,p,H){return this.scheduleTask(new T(W,i,s,o,p,H))}cancelTask(i){if(i.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(i.zone||Q).name+"; Execution: "+this.name+")");if(!(i.state!==d&&i.state!==x)){i._transitionTo(G,d,x);try{this._zoneDelegate.cancelTask(this,i)}catch(s){throw i._transitionTo(z,G),this._zoneDelegate.handleError(this,s),s}return this._updateTaskCount(i,-1),i._transitionTo(V,G),i.runCount=-1,i}}_updateTaskCount(i,s){let o=i._zoneDelegates;s==-1&&(i._zoneDelegates=null);for(let p=0;p<o.length;p++)o[p]._updateTaskCount(i.type,s)}};Y.__symbol__=te;let t=Y,c={name:"",onHasTask:(j,i,s,o)=>j.hasTask(s,o),onScheduleTask:(j,i,s,o)=>j.scheduleTask(s,o),onInvokeTask:(j,i,s,o,p,H)=>j.invokeTask(s,o,p,H),onCancelTask:(j,i,s,o)=>j.cancelTask(s,o)};class f{get zone(){return this._zone}constructor(i,s,o){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=i,this._parentDelegate=s,this._forkZS=o&&(o&&o.onFork?o:s._forkZS),this._forkDlgt=o&&(o.onFork?s:s._forkDlgt),this._forkCurrZone=o&&(o.onFork?this._zone:s._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:s._interceptZS),this._interceptDlgt=o&&(o.onIntercept?s:s._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this._zone:s._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:s._invokeZS),this._invokeDlgt=o&&(o.onInvoke?s:s._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this._zone:s._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:s._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?s:s._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this._zone:s._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:s._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?s:s._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this._zone:s._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:s._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?s:s._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this._zone:s._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:s._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?s:s._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this._zone:s._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let p=o&&o.onHasTask,H=s&&s._hasTaskZS;(p||H)&&(this._hasTaskZS=p?o:c,this._hasTaskDlgt=s,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,o.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=s,this._scheduleTaskCurrZone=this._zone),o.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=s,this._invokeTaskCurrZone=this._zone),o.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=s,this._cancelTaskCurrZone=this._zone))}fork(i,s){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,i,s):new t(i,s)}intercept(i,s,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,i,s,o):s}invoke(i,s,o,p,H){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,i,s,o,p,H):s.apply(o,p)}handleError(i,s){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,i,s):!0}scheduleTask(i,s){let o=s;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,i,s),o||(o=s);else if(s.scheduleFn)s.scheduleFn(s);else if(s.type==F)J(s);else throw new Error("Task is missing scheduleFn.");return o}invokeTask(i,s,o,p){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,i,s,o,p):s.callback.apply(o,p)}cancelTask(i,s){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,i,s);else{if(!s.cancelFn)throw Error("Task is not cancelable");o=s.cancelFn(s)}return o}hasTask(i,s){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,i,s)}catch(o){this.handleError(i,o)}}_updateTaskCount(i,s){let o=this._taskCounts,p=o[i],H=o[i]=p+s;if(H<0)throw new Error("More tasks executed then were scheduled.");if(p==0||H==0){let M={microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:i};this.hasTask(this._zone,M)}}}class T{constructor(i,s,o,p,H,M){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=i,this.source=s,this.data=p,this.scheduleFn=H,this.cancelFn=M,!o)throw new Error("callback is not defined");this.callback=o;let ie=this;i===W&&p&&p.useG?this.invoke=T.invokeTask:this.invoke=function(){return T.invokeTask.call(ae,ie,this,arguments)}}static invokeTask(i,s,o){i||(i=this),K++;try{return i.runCount++,i.zone.runTask(i,s,o)}finally{K==1&&q(),K--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(V,k)}_transitionTo(i,s,o){if(this._state===s||this._state===o)this._state=i,i==V&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${i}', expecting state '${s}'${o?" or '"+o+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let g=te("setTimeout"),m=te("Promise"),C=te("then"),_=[],w=!1,S;function Z(j){if(S||ae[m]&&(S=ae[m].resolve(0)),S){let i=S[C];i||(i=S.then),i.call(S,j)}else ae[g](j,0)}function J(j){K===0&&_.length===0&&Z(q),j&&_.push(j)}function q(){if(!w){for(w=!0;_.length;){let j=_;_=[];for(let i=0;i<j.length;i++){let s=j[i];try{s.zone.runTask(s,null,null)}catch(o){R.onUnhandledError(o)}}}R.microtaskDrainDone(),w=!1}}let Q={name:"NO ZONE"},V="notScheduled",k="scheduling",d="scheduled",x="running",G="canceling",z="unknown",F="microTask",y="macroTask",W="eventTask",O={},R={symbol:te,currentZoneFrame:()=>b,onUnhandledError:X,microtaskDrainDone:X,scheduleMicroTask:J,showUncaughtError:()=>!t[te("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:X,patchMethod:()=>X,bindArguments:()=>[],patchThen:()=>X,patchMacroTask:()=>X,patchEventPrototype:()=>X,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>X,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>X,wrapWithCurrentZone:()=>X,filterProperties:()=>[],attachOriginToPatched:()=>X,_redefineProperty:()=>X,patchCallbacks:()=>X,nativeScheduleMicroTask:Z},b={parent:null,zone:new t(null,null)},D=null,K=0;function X(){}return a("Zone","Zone"),t}function _t(){var a;let e=globalThis,n=e[te("forceDuplicateZoneCheck")]===!0;if(e.Zone&&(n||typeof e.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return(a=e.Zone)!=null||(e.Zone=dt()),e.Zone}var be=Object.getOwnPropertyDescriptor,Ae=Object.defineProperty,je=Object.getPrototypeOf,Et=Object.create,Tt=Array.prototype.slice,He="addEventListener",xe="removeEventListener",Le=te(He),Ie=te(xe),fe="true",he="false",Pe=te("");function Ve(e,n){return Zone.current.wrap(e,n)}function Ge(e,n,a,t,c){return Zone.current.scheduleMacroTask(e,n,a,t,c)}var A=te,De=typeof window<"u",pe=De?window:void 0,$=De&&pe||globalThis,gt="removeAttribute";function Fe(e,n){for(let a=e.length-1;a>=0;a--)typeof e[a]=="function"&&(e[a]=Ve(e[a],n+"_"+a));return e}function yt(e,n){let a=e.constructor.name;for(let t=0;t<n.length;t++){let c=n[t],f=e[c];if(f){let T=be(e,c);if(!tt(T))continue;e[c]=(g=>{let m=function(){return g.apply(this,Fe(arguments,a+"."+c))};return _e(m,g),m})(f)}}}function tt(e){return e?e.writable===!1?!1:!(typeof e.get=="function"&&typeof e.set>"u"):!0}var nt=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Se=!("nw"in $)&&typeof $.process<"u"&&$.process.toString()==="[object process]",Be=!Se&&!nt&&!!(De&&pe.HTMLElement),rt=typeof $.process<"u"&&$.process.toString()==="[object process]"&&!nt&&!!(De&&pe.HTMLElement),Ce={},mt=A("enable_beforeunload"),Ye=function(e){if(e=e||$.event,!e)return;let n=Ce[e.type];n||(n=Ce[e.type]=A("ON_PROPERTY"+e.type));let a=this||e.target||$,t=a[n],c;if(Be&&a===pe&&e.type==="error"){let f=e;c=t&&t.call(this,f.message,f.filename,f.lineno,f.colno,f.error),c===!0&&e.preventDefault()}else c=t&&t.apply(this,arguments),e.type==="beforeunload"&&$[mt]&&typeof c=="string"?e.returnValue=c:c!=null&&!c&&e.preventDefault();return c};function $e(e,n,a){let t=be(e,n);if(!t&&a&&be(a,n)&&(t={enumerable:!0,configurable:!0}),!t||!t.configurable)return;let c=A("on"+n+"patched");if(e.hasOwnProperty(c)&&e[c])return;delete t.writable,delete t.value;let f=t.get,T=t.set,g=n.slice(2),m=Ce[g];m||(m=Ce[g]=A("ON_PROPERTY"+g)),t.set=function(C){let _=this;if(!_&&e===$&&(_=$),!_)return;typeof _[m]=="function"&&_.removeEventListener(g,Ye),T&&T.call(_,null),_[m]=C,typeof C=="function"&&_.addEventListener(g,Ye,!1)},t.get=function(){let C=this;if(!C&&e===$&&(C=$),!C)return null;let _=C[m];if(_)return _;if(f){let w=f.call(this);if(w)return t.set.call(this,w),typeof C[gt]=="function"&&C.removeAttribute(n),w}return null},Ae(e,n,t),e[c]=!0}function ot(e,n,a){if(n)for(let t=0;t<n.length;t++)$e(e,"on"+n[t],a);else{let t=[];for(let c in e)c.slice(0,2)=="on"&&t.push(c);for(let c=0;c<t.length;c++)$e(e,t[c],a)}}var se=A("originalInstance");function ve(e){let n=$[e];if(!n)return;$[A(e)]=n,$[e]=function(){let c=Fe(arguments,e);switch(c.length){case 0:this[se]=new n;break;case 1:this[se]=new n(c[0]);break;case 2:this[se]=new n(c[0],c[1]);break;case 3:this[se]=new n(c[0],c[1],c[2]);break;case 4:this[se]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},_e($[e],n);let a=new n(function(){}),t;for(t in a)e==="XMLHttpRequest"&&t==="responseBlob"||function(c){typeof a[c]=="function"?$[e].prototype[c]=function(){return this[se][c].apply(this[se],arguments)}:Ae($[e].prototype,c,{set:function(f){typeof f=="function"?(this[se][c]=Ve(f,e+"."+c),_e(this[se][c],f)):this[se][c]=f},get:function(){return this[se][c]}})}(t);for(t in n)t!=="prototype"&&n.hasOwnProperty(t)&&($[e][t]=n[t])}function de(e,n,a){let t=e;for(;t&&!t.hasOwnProperty(n);)t=je(t);!t&&e[n]&&(t=e);let c=A(n),f=null;if(t&&(!(f=t[c])||!t.hasOwnProperty(c))){f=t[c]=t[n];let T=t&&be(t,n);if(tt(T)){let g=a(f,c,n);t[n]=function(){return g(this,arguments)},_e(t[n],f)}}return f}function pt(e,n,a){let t=null;function c(f){let T=f.data;return T.args[T.cbIdx]=function(){f.invoke.apply(this,arguments)},t.apply(T.target,T.args),f}t=de(e,n,f=>function(T,g){let m=a(T,g);return m.cbIdx>=0&&typeof g[m.cbIdx]=="function"?Ge(m.name,g[m.cbIdx],m,c):f.apply(T,g)})}function _e(e,n){e[A("OriginalDelegate")]=n}var Je=!1,Me=!1;function kt(){try{let e=pe.navigator.userAgent;if(e.indexOf("MSIE ")!==-1||e.indexOf("Trident/")!==-1)return!0}catch{}return!1}function vt(){if(Je)return Me;Je=!0;try{let e=pe.navigator.userAgent;(e.indexOf("MSIE ")!==-1||e.indexOf("Trident/")!==-1||e.indexOf("Edge/")!==-1)&&(Me=!0)}catch{}return Me}function Ke(e){return typeof e=="function"}function Qe(e){return typeof e=="number"}var me=!1;if(typeof window<"u")try{let e=Object.defineProperty({},"passive",{get:function(){me=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch{me=!1}var bt={useG:!0},ne={},st={},it=new RegExp("^"+Pe+"(\\w+)(true|false)$"),ct=A("propagationStopped");function at(e,n){let a=(n?n(e):e)+he,t=(n?n(e):e)+fe,c=Pe+a,f=Pe+t;ne[e]={},ne[e][he]=c,ne[e][fe]=f}function Pt(e,n,a,t){let c=t&&t.add||He,f=t&&t.rm||xe,T=t&&t.listeners||"eventListeners",g=t&&t.rmAll||"removeAllListeners",m=A(c),C="."+c+":",_="prependListener",w="."+_+":",S=function(k,d,x){if(k.isRemoved)return;let G=k.callback;typeof G=="object"&&G.handleEvent&&(k.callback=y=>G.handleEvent(y),k.originalDelegate=G);let z;try{k.invoke(k,d,[x])}catch(y){z=y}let F=k.options;if(F&&typeof F=="object"&&F.once){let y=k.originalDelegate?k.originalDelegate:k.callback;d[f].call(d,x.type,y,F)}return z};function Z(k,d,x){if(d=d||e.event,!d)return;let G=k||d.target||e,z=G[ne[d.type][x?fe:he]];if(z){let F=[];if(z.length===1){let y=S(z[0],G,d);y&&F.push(y)}else{let y=z.slice();for(let W=0;W<y.length&&!(d&&d[ct]===!0);W++){let O=S(y[W],G,d);O&&F.push(O)}}if(F.length===1)throw F[0];for(let y=0;y<F.length;y++){let W=F[y];n.nativeScheduleMicroTask(()=>{throw W})}}}let J=function(k){return Z(this,k,!1)},q=function(k){return Z(this,k,!0)};function Q(k,d){if(!k)return!1;let x=!0;d&&d.useG!==void 0&&(x=d.useG);let G=d&&d.vh,z=!0;d&&d.chkDup!==void 0&&(z=d.chkDup);let F=!1;d&&d.rt!==void 0&&(F=d.rt);let y=k;for(;y&&!y.hasOwnProperty(c);)y=je(y);if(!y&&k[c]&&(y=k),!y||y[m])return!1;let W=d&&d.eventNameToString,O={},R=y[m]=y[c],b=y[A(f)]=y[f],D=y[A(T)]=y[T],K=y[A(g)]=y[g],X;d&&d.prepend&&(X=y[A(d.prepend)]=y[d.prepend]);function Y(r,u){return!me&&typeof r=="object"&&r?!!r.capture:!me||!u?r:typeof r=="boolean"?{capture:r,passive:!0}:r?typeof r=="object"&&r.passive!==!1?{...r,passive:!0}:r:{passive:!0}}let j=function(r){if(!O.isExisting)return R.call(O.target,O.eventName,O.capture?q:J,O.options)},i=function(r){if(!r.isRemoved){let u=ne[r.eventName],v;u&&(v=u[r.capture?fe:he]);let P=v&&r.target[v];if(P){for(let E=0;E<P.length;E++)if(P[E]===r){P.splice(E,1),r.isRemoved=!0,r.removeAbortListener&&(r.removeAbortListener(),r.removeAbortListener=null),P.length===0&&(r.allRemoved=!0,r.target[v]=null);break}}}if(r.allRemoved)return b.call(r.target,r.eventName,r.capture?q:J,r.options)},s=function(r){return R.call(O.target,O.eventName,r.invoke,O.options)},o=function(r){return X.call(O.target,O.eventName,r.invoke,O.options)},p=function(r){return b.call(r.target,r.eventName,r.invoke,r.options)},H=x?j:s,M=x?i:p,ie=function(r,u){let v=typeof u;return v==="function"&&r.callback===u||v==="object"&&r.originalDelegate===u},le=d&&d.diff?d.diff:ie,ue=Zone[A("UNPATCHED_EVENTS")],re=e[A("PASSIVE_EVENTS")];function h(r){if(typeof r=="object"&&r!==null){let u={...r};return r.signal&&(u.signal=r.signal),u}return r}let l=function(r,u,v,P,E=!1,N=!1){return function(){let L=this||e,I=arguments[0];d&&d.transferEventName&&(I=d.transferEventName(I));let B=arguments[1];if(!B)return r.apply(this,arguments);if(Se&&I==="uncaughtException")return r.apply(this,arguments);let U=!1;if(typeof B!="function"){if(!B.handleEvent)return r.apply(this,arguments);U=!0}if(G&&!G(r,B,L,arguments))return;let Ee=me&&!!re&&re.indexOf(I)!==-1,ee=h(Y(arguments[2],Ee)),Te=ee==null?void 0:ee.signal;if(Te!=null&&Te.aborted)return;if(ue){for(let ce=0;ce<ue.length;ce++)if(I===ue[ce])return Ee?r.call(L,I,B,ee):r.apply(this,arguments)}let Oe=ee?typeof ee=="boolean"?!0:ee.capture:!1,Ue=ee&&typeof ee=="object"?ee.once:!1,ht=Zone.current,Ne=ne[I];Ne||(at(I,W),Ne=ne[I]);let ze=Ne[Oe?fe:he],ge=L[ze],We=!1;if(ge){if(We=!0,z){for(let ce=0;ce<ge.length;ce++)if(le(ge[ce],B))return}}else ge=L[ze]=[];let we,qe=L.constructor.name,Xe=st[qe];Xe&&(we=Xe[I]),we||(we=qe+u+(W?W(I):I)),O.options=ee,Ue&&(O.options.once=!1),O.target=L,O.capture=Oe,O.eventName=I,O.isExisting=We;let ke=x?bt:void 0;ke&&(ke.taskData=O),Te&&(O.options.signal=void 0);let oe=ht.scheduleEventTask(we,B,ke,v,P);if(Te){O.options.signal=Te;let ce=()=>oe.zone.cancelTask(oe);r.call(Te,"abort",ce,{once:!0}),oe.removeAbortListener=()=>Te.removeEventListener("abort",ce)}if(O.target=null,ke&&(ke.taskData=null),Ue&&(O.options.once=!0),!me&&typeof oe.options=="boolean"||(oe.options=ee),oe.target=L,oe.capture=Oe,oe.eventName=I,U&&(oe.originalDelegate=B),N?ge.unshift(oe):ge.push(oe),E)return L}};return y[c]=l(R,C,H,M,F),X&&(y[_]=l(X,w,o,M,F,!0)),y[f]=function(){let r=this||e,u=arguments[0];d&&d.transferEventName&&(u=d.transferEventName(u));let v=arguments[2],P=v?typeof v=="boolean"?!0:v.capture:!1,E=arguments[1];if(!E)return b.apply(this,arguments);if(G&&!G(b,E,r,arguments))return;let N=ne[u],L;N&&(L=N[P?fe:he]);let I=L&&r[L];if(I)for(let B=0;B<I.length;B++){let U=I[B];if(le(U,E)){if(I.splice(B,1),U.isRemoved=!0,I.length===0&&(U.allRemoved=!0,r[L]=null,!P&&typeof u=="string")){let Ee=Pe+"ON_PROPERTY"+u;r[Ee]=null}return U.zone.cancelTask(U),F?r:void 0}}return b.apply(this,arguments)},y[T]=function(){let r=this||e,u=arguments[0];d&&d.transferEventName&&(u=d.transferEventName(u));let v=[],P=lt(r,W?W(u):u);for(let E=0;E<P.length;E++){let N=P[E],L=N.originalDelegate?N.originalDelegate:N.callback;v.push(L)}return v},y[g]=function(){let r=this||e,u=arguments[0];if(u){d&&d.transferEventName&&(u=d.transferEventName(u));let v=ne[u];if(v){let P=v[he],E=v[fe],N=r[P],L=r[E];if(N){let I=N.slice();for(let B=0;B<I.length;B++){let U=I[B],Ee=U.originalDelegate?U.originalDelegate:U.callback;this[f].call(this,u,Ee,U.options)}}if(L){let I=L.slice();for(let B=0;B<I.length;B++){let U=I[B],Ee=U.originalDelegate?U.originalDelegate:U.callback;this[f].call(this,u,Ee,U.options)}}}}else{let v=Object.keys(r);for(let P=0;P<v.length;P++){let E=v[P],N=it.exec(E),L=N&&N[1];L&&L!=="removeListener"&&this[g].call(this,L)}this[g].call(this,"removeListener")}if(F)return this},_e(y[c],R),_e(y[f],b),K&&_e(y[g],K),D&&_e(y[T],D),!0}let V=[];for(let k=0;k<a.length;k++)V[k]=Q(a[k],t);return V}function lt(e,n){if(!n){let f=[];for(let T in e){let g=it.exec(T),m=g&&g[1];if(m&&(!n||m===n)){let C=e[T];if(C)for(let _=0;_<C.length;_++)f.push(C[_])}}return f}let a=ne[n];a||(at(n),a=ne[n]);let t=e[a[he]],c=e[a[fe]];return t?c?t.concat(c):t.slice():c?c.slice():[]}function wt(e,n){let a=e.Event;a&&a.prototype&&n.patchMethod(a.prototype,"stopImmediatePropagation",t=>function(c,f){c[ct]=!0,t&&t.apply(c,f)})}function Rt(e,n){n.patchMethod(e,"queueMicrotask",a=>function(t,c){Zone.current.scheduleMicroTask("queueMicrotask",c[0])})}var Re=A("zoneTask");function ye(e,n,a,t){let c=null,f=null;n+=t,a+=t;let T={};function g(C){let _=C.data;_.args[0]=function(){return C.invoke.apply(this,arguments)};let w=c.apply(e,_.args);return Qe(w)?_.handleId=w:(_.handle=w,_.isRefreshable=Ke(w.refresh)),C}function m(C){let{handle:_,handleId:w}=C.data;return f.call(e,_!=null?_:w)}c=de(e,n,C=>function(_,w){var S;if(Ke(w[0])){let Z={isRefreshable:!1,isPeriodic:t==="Interval",delay:t==="Timeout"||t==="Interval"?w[1]||0:void 0,args:w},J=w[0];w[0]=function(){try{return J.apply(this,arguments)}finally{let{handle:G,handleId:z,isPeriodic:F,isRefreshable:y}=Z;!F&&!y&&(z?delete T[z]:G&&(G[Re]=null))}};let q=Ge(n,w[0],Z,g,m);if(!q)return q;let{handleId:Q,handle:V,isRefreshable:k,isPeriodic:d}=q.data;if(Q)T[Q]=q;else if(V&&(V[Re]=q,k&&!d)){let x=V.refresh;V.refresh=function(){let{zone:G,state:z}=q;return z==="notScheduled"?(q._state="scheduled",G._updateTaskCount(q,1)):z==="running"&&(q._state="scheduling"),x.call(this)}}return(S=V!=null?V:Q)!=null?S:q}else return C.apply(e,w)}),f=de(e,a,C=>function(_,w){let S=w[0],Z;Qe(S)?(Z=T[S],delete T[S]):(Z=S==null?void 0:S[Re],Z?S[Re]=null:Z=S),Z!=null&&Z.type?Z.cancelFn&&Z.zone.cancelTask(Z):C.apply(e,w)})}function Ct(e,n){let{isBrowser:a,isMix:t}=n.getGlobalObjects();if(!a&&!t||!e.customElements||!("customElements"in e))return;let c=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"];n.patchCallbacks(n,e.customElements,"customElements","define",c)}function Dt(e,n){if(Zone[n.symbol("patchEventTarget")])return;let{eventNames:a,zoneSymbolEventNames:t,TRUE_STR:c,FALSE_STR:f,ZONE_SYMBOL_PREFIX:T}=n.getGlobalObjects();for(let m=0;m<a.length;m++){let C=a[m],_=C+f,w=C+c,S=T+_,Z=T+w;t[C]={},t[C][f]=S,t[C][c]=Z}let g=e.EventTarget;if(!(!g||!g.prototype))return n.patchEventTarget(e,n,[g&&g.prototype]),!0}function St(e,n){n.patchEventPrototype(e,n)}function ut(e,n,a){if(!a||a.length===0)return n;let t=a.filter(f=>f.target===e);if(!t||t.length===0)return n;let c=t[0].ignoreProperties;return n.filter(f=>c.indexOf(f)===-1)}function et(e,n,a,t){if(!e)return;let c=ut(e,n,a);ot(e,c,t)}function Ze(e){return Object.getOwnPropertyNames(e).filter(n=>n.startsWith("on")&&n.length>2).map(n=>n.substring(2))}function Ot(e,n){if(Se&&!rt||Zone[e.symbol("patchEvents")])return;let a=n.__Zone_ignore_on_properties,t=[];if(Be){let c=window;t=t.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);let f=kt()?[{target:c,ignoreProperties:["error"]}]:[];et(c,Ze(c),a&&a.concat(f),je(c))}t=t.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let c=0;c<t.length;c++){let f=n[t[c]];f&&f.prototype&&et(f.prototype,Ze(f.prototype),a)}}function Nt(e){e.__load_patch("legacy",n=>{let a=n[e.__symbol__("legacyPatch")];a&&a()}),e.__load_patch("timers",n=>{let a="set",t="clear";ye(n,a,t,"Timeout"),ye(n,a,t,"Interval"),ye(n,a,t,"Immediate")}),e.__load_patch("requestAnimationFrame",n=>{ye(n,"request","cancel","AnimationFrame"),ye(n,"mozRequest","mozCancel","AnimationFrame"),ye(n,"webkitRequest","webkitCancel","AnimationFrame")}),e.__load_patch("blocking",(n,a)=>{let t=["alert","prompt","confirm"];for(let c=0;c<t.length;c++){let f=t[c];de(n,f,(T,g,m)=>function(C,_){return a.current.run(T,n,_,m)})}}),e.__load_patch("EventTarget",(n,a,t)=>{St(n,t),Dt(n,t);let c=n.XMLHttpRequestEventTarget;c&&c.prototype&&t.patchEventTarget(n,t,[c.prototype])}),e.__load_patch("MutationObserver",(n,a,t)=>{ve("MutationObserver"),ve("WebKitMutationObserver")}),e.__load_patch("IntersectionObserver",(n,a,t)=>{ve("IntersectionObserver")}),e.__load_patch("FileReader",(n,a,t)=>{ve("FileReader")}),e.__load_patch("on_property",(n,a,t)=>{Ot(t,n)}),e.__load_patch("customElements",(n,a,t)=>{Ct(n,t)}),e.__load_patch("XHR",(n,a)=>{C(n);let t=A("xhrTask"),c=A("xhrSync"),f=A("xhrListener"),T=A("xhrScheduled"),g=A("xhrURL"),m=A("xhrErrorBeforeScheduled");function C(_){let w=_.XMLHttpRequest;if(!w)return;let S=w.prototype;function Z(R){return R[t]}let J=S[Le],q=S[Ie];if(!J){let R=_.XMLHttpRequestEventTarget;if(R){let b=R.prototype;J=b[Le],q=b[Ie]}}let Q="readystatechange",V="scheduled";function k(R){let b=R.data,D=b.target;D[T]=!1,D[m]=!1;let K=D[f];J||(J=D[Le],q=D[Ie]),K&&q.call(D,Q,K);let X=D[f]=()=>{if(D.readyState===D.DONE)if(!b.aborted&&D[T]&&R.state===V){let j=D[a.__symbol__("loadfalse")];if(D.status!==0&&j&&j.length>0){let i=R.invoke;R.invoke=function(){let s=D[a.__symbol__("loadfalse")];for(let o=0;o<s.length;o++)s[o]===R&&s.splice(o,1);!b.aborted&&R.state===V&&i.call(R)},j.push(R)}else R.invoke()}else!b.aborted&&D[T]===!1&&(D[m]=!0)};return J.call(D,Q,X),D[t]||(D[t]=R),W.apply(D,b.args),D[T]=!0,R}function d(){}function x(R){let b=R.data;return b.aborted=!0,O.apply(b.target,b.args)}let G=de(S,"open",()=>function(R,b){return R[c]=b[2]==!1,R[g]=b[1],G.apply(R,b)}),z="XMLHttpRequest.send",F=A("fetchTaskAborting"),y=A("fetchTaskScheduling"),W=de(S,"send",()=>function(R,b){if(a.current[y]===!0||R[c])return W.apply(R,b);{let D={target:R,url:R[g],isPeriodic:!1,args:b,aborted:!1},K=Ge(z,d,D,k,x);R&&R[m]===!0&&!D.aborted&&K.state===V&&K.invoke()}}),O=de(S,"abort",()=>function(R,b){let D=Z(R);if(D&&typeof D.type=="string"){if(D.cancelFn==null||D.data&&D.data.aborted)return;D.zone.cancelTask(D)}else if(a.current[F]===!0)return O.apply(R,b)})}}),e.__load_patch("geolocation",n=>{n.navigator&&n.navigator.geolocation&&yt(n.navigator.geolocation,["getCurrentPosition","watchPosition"])}),e.__load_patch("PromiseRejectionEvent",(n,a)=>{function t(c){return function(f){lt(n,c).forEach(g=>{let m=n.PromiseRejectionEvent;if(m){let C=new m(c,{promise:f.promise,reason:f.rejection});g.invoke(C)}})}}n.PromiseRejectionEvent&&(a[A("unhandledPromiseRejectionHandler")]=t("unhandledrejection"),a[A("rejectionHandledHandler")]=t("rejectionhandled"))}),e.__load_patch("queueMicrotask",(n,a,t)=>{Rt(n,t)})}function Lt(e){e.__load_patch("ZoneAwarePromise",(n,a,t)=>{let c=Object.getOwnPropertyDescriptor,f=Object.defineProperty;function T(h){if(h&&h.toString===Object.prototype.toString){let l=h.constructor&&h.constructor.name;return(l||"")+": "+JSON.stringify(h)}return h?h.toString():Object.prototype.toString.call(h)}let g=t.symbol,m=[],C=n[g("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,_=g("Promise"),w=g("then"),S="__creationTrace__";t.onUnhandledError=h=>{if(t.showUncaughtError()){let l=h&&h.rejection;l?console.error("Unhandled Promise rejection:",l instanceof Error?l.message:l,"; Zone:",h.zone.name,"; Task:",h.task&&h.task.source,"; Value:",l,l instanceof Error?l.stack:void 0):console.error(h)}},t.microtaskDrainDone=()=>{for(;m.length;){let h=m.shift();try{h.zone.runGuarded(()=>{throw h.throwOriginal?h.rejection:h})}catch(l){J(l)}}};let Z=g("unhandledPromiseRejectionHandler");function J(h){t.onUnhandledError(h);try{let l=a[Z];typeof l=="function"&&l.call(this,h)}catch{}}function q(h){return h&&h.then}function Q(h){return h}function V(h){return M.reject(h)}let k=g("state"),d=g("value"),x=g("finally"),G=g("parentPromiseValue"),z=g("parentPromiseState"),F="Promise.then",y=null,W=!0,O=!1,R=0;function b(h,l){return r=>{try{Y(h,l,r)}catch(u){Y(h,!1,u)}}}let D=function(){let h=!1;return function(r){return function(){h||(h=!0,r.apply(null,arguments))}}},K="Promise resolved with itself",X=g("currentTaskTrace");function Y(h,l,r){let u=D();if(h===r)throw new TypeError(K);if(h[k]===y){let v=null;try{(typeof r=="object"||typeof r=="function")&&(v=r&&r.then)}catch(P){return u(()=>{Y(h,!1,P)})(),h}if(l!==O&&r instanceof M&&r.hasOwnProperty(k)&&r.hasOwnProperty(d)&&r[k]!==y)i(r),Y(h,r[k],r[d]);else if(l!==O&&typeof v=="function")try{v.call(r,u(b(h,l)),u(b(h,!1)))}catch(P){u(()=>{Y(h,!1,P)})()}else{h[k]=l;let P=h[d];if(h[d]=r,h[x]===x&&l===W&&(h[k]=h[z],h[d]=h[G]),l===O&&r instanceof Error){let E=a.currentTask&&a.currentTask.data&&a.currentTask.data[S];E&&f(r,X,{configurable:!0,enumerable:!1,writable:!0,value:E})}for(let E=0;E<P.length;)s(h,P[E++],P[E++],P[E++],P[E++]);if(P.length==0&&l==O){h[k]=R;let E=r;try{throw new Error("Uncaught (in promise): "+T(r)+(r&&r.stack?`
`+r.stack:""))}catch(N){E=N}C&&(E.throwOriginal=!0),E.rejection=r,E.promise=h,E.zone=a.current,E.task=a.currentTask,m.push(E),t.scheduleMicroTask()}}}return h}let j=g("rejectionHandledHandler");function i(h){if(h[k]===R){try{let l=a[j];l&&typeof l=="function"&&l.call(this,{rejection:h[d],promise:h})}catch{}h[k]=O;for(let l=0;l<m.length;l++)h===m[l].promise&&m.splice(l,1)}}function s(h,l,r,u,v){i(h);let P=h[k],E=P?typeof u=="function"?u:Q:typeof v=="function"?v:V;l.scheduleMicroTask(F,()=>{try{let N=h[d],L=!!r&&x===r[x];L&&(r[G]=N,r[z]=P);let I=l.run(E,void 0,L&&E!==V&&E!==Q?[]:[N]);Y(r,!0,I)}catch(N){Y(r,!1,N)}},r)}let o="function ZoneAwarePromise() { [native code] }",p=function(){},H=n.AggregateError;class M{static toString(){return o}static resolve(l){return l instanceof M?l:Y(new this(null),W,l)}static reject(l){return Y(new this(null),O,l)}static withResolvers(){let l={};return l.promise=new M((r,u)=>{l.resolve=r,l.reject=u}),l}static any(l){if(!l||typeof l[Symbol.iterator]!="function")return Promise.reject(new H([],"All promises were rejected"));let r=[],u=0;try{for(let E of l)u++,r.push(M.resolve(E))}catch{return Promise.reject(new H([],"All promises were rejected"))}if(u===0)return Promise.reject(new H([],"All promises were rejected"));let v=!1,P=[];return new M((E,N)=>{for(let L=0;L<r.length;L++)r[L].then(I=>{v||(v=!0,E(I))},I=>{P.push(I),u--,u===0&&(v=!0,N(new H(P,"All promises were rejected")))})})}static race(l){let r,u,v=new this((N,L)=>{r=N,u=L});function P(N){r(N)}function E(N){u(N)}for(let N of l)q(N)||(N=this.resolve(N)),N.then(P,E);return v}static all(l){return M.allWithCallback(l)}static allSettled(l){return(this&&this.prototype instanceof M?this:M).allWithCallback(l,{thenCallback:u=>({status:"fulfilled",value:u}),errorCallback:u=>({status:"rejected",reason:u})})}static allWithCallback(l,r){let u,v,P=new this((I,B)=>{u=I,v=B}),E=2,N=0,L=[];for(let I of l){q(I)||(I=this.resolve(I));let B=N;try{I.then(U=>{L[B]=r?r.thenCallback(U):U,E--,E===0&&u(L)},U=>{r?(L[B]=r.errorCallback(U),E--,E===0&&u(L)):v(U)})}catch(U){v(U)}E++,N++}return E-=2,E===0&&u(L),P}constructor(l){let r=this;if(!(r instanceof M))throw new Error("Must be an instanceof Promise.");r[k]=y,r[d]=[];try{let u=D();l&&l(u(b(r,W)),u(b(r,O)))}catch(u){Y(r,!1,u)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return M}then(l,r){var E;let u=(E=this.constructor)==null?void 0:E[Symbol.species];(!u||typeof u!="function")&&(u=this.constructor||M);let v=new u(p),P=a.current;return this[k]==y?this[d].push(P,v,l,r):s(this,P,v,l,r),v}catch(l){return this.then(null,l)}finally(l){var P;let r=(P=this.constructor)==null?void 0:P[Symbol.species];(!r||typeof r!="function")&&(r=M);let u=new r(p);u[x]=x;let v=a.current;return this[k]==y?this[d].push(v,u,l,l):s(this,v,u,l,l),u}}M.resolve=M.resolve,M.reject=M.reject,M.race=M.race,M.all=M.all;let ie=n[_]=n.Promise;n.Promise=M;let le=g("thenPatched");function ue(h){let l=h.prototype,r=c(l,"then");if(r&&(r.writable===!1||!r.configurable))return;let u=l.then;l[w]=u,h.prototype.then=function(v,P){return new M((N,L)=>{u.call(this,N,L)}).then(v,P)},h[le]=!0}t.patchThen=ue;function re(h){return function(l,r){let u=h.apply(l,r);if(u instanceof M)return u;let v=u.constructor;return v[le]||ue(v),u}}return ie&&(ue(ie),de(n,"fetch",h=>re(h))),Promise[a.__symbol__("uncaughtPromiseErrors")]=m,M})}function It(e){e.__load_patch("toString",n=>{let a=Function.prototype.toString,t=A("OriginalDelegate"),c=A("Promise"),f=A("Error"),T=function(){if(typeof this=="function"){let _=this[t];if(_)return typeof _=="function"?a.call(_):Object.prototype.toString.call(_);if(this===Promise){let w=n[c];if(w)return a.call(w)}if(this===Error){let w=n[f];if(w)return a.call(w)}}return a.call(this)};T[t]=a,Function.prototype.toString=T;let g=Object.prototype.toString,m="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?m:g.call(this)}})}function Mt(e,n,a,t,c){let f=Zone.__symbol__(t);if(n[f])return;let T=n[f]=n[t];n[t]=function(g,m,C){return m&&m.prototype&&c.forEach(function(_){let w=`${a}.${t}::`+_,S=m.prototype;try{if(S.hasOwnProperty(_)){let Z=e.ObjectGetOwnPropertyDescriptor(S,_);Z&&Z.value?(Z.value=e.wrapWithCurrentZone(Z.value,w),e._redefineProperty(m.prototype,_,Z)):S[_]&&(S[_]=e.wrapWithCurrentZone(S[_],w))}else S[_]&&(S[_]=e.wrapWithCurrentZone(S[_],w))}catch{}}),T.call(n,g,m,C)},e.attachOriginToPatched(n[t],T)}function Zt(e){e.__load_patch("util",(n,a,t)=>{let c=Ze(n);t.patchOnProperties=ot,t.patchMethod=de,t.bindArguments=Fe,t.patchMacroTask=pt;let f=a.__symbol__("BLACK_LISTED_EVENTS"),T=a.__symbol__("UNPATCHED_EVENTS");n[T]&&(n[f]=n[T]),n[f]&&(a[f]=a[T]=n[f]),t.patchEventPrototype=wt,t.patchEventTarget=Pt,t.isIEOrEdge=vt,t.ObjectDefineProperty=Ae,t.ObjectGetOwnPropertyDescriptor=be,t.ObjectCreate=Et,t.ArraySlice=Tt,t.patchClass=ve,t.wrapWithCurrentZone=Ve,t.filterProperties=ut,t.attachOriginToPatched=_e,t._redefineProperty=Object.defineProperty,t.patchCallbacks=Mt,t.getGlobalObjects=()=>({globalSources:st,zoneSymbolEventNames:ne,eventNames:c,isBrowser:Be,isMix:rt,isNode:Se,TRUE_STR:fe,FALSE_STR:he,ZONE_SYMBOL_PREFIX:Pe,ADD_EVENT_LISTENER_STR:He,REMOVE_EVENT_LISTENER_STR:xe})})}function At(e){Lt(e),It(e),Zt(e)}var ft=_t();At(ft);Nt(ft);
