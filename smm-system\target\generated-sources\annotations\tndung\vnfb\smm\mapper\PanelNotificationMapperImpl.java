package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.PanelNotificationReq;
import tndung.vnfb.smm.dto.PanelNotificationRes;
import tndung.vnfb.smm.entity.PanelNotification;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class PanelNotificationMapperImpl implements PanelNotificationMapper {

    @Override
    public PanelNotification toEntity(PanelNotificationReq req) {
        if ( req == null ) {
            return null;
        }

        PanelNotification panelNotification = new PanelNotification();

        panelNotification.setUserId( req.getUserId() );
        panelNotification.setTitle( req.getTitle() );
        panelNotification.setContent( req.getContent() );
        panelNotification.setType( req.getType() );
        panelNotification.setCategory( req.getCategory() );

        return panelNotification;
    }

    @Override
    public PanelNotificationRes toDto(PanelNotification entity) {
        if ( entity == null ) {
            return null;
        }

        PanelNotificationRes panelNotificationRes = new PanelNotificationRes();

        panelNotificationRes.setId( entity.getId() );
        panelNotificationRes.setTitle( entity.getTitle() );
        panelNotificationRes.setContent( entity.getContent() );
        panelNotificationRes.setType( entity.getType() );
        panelNotificationRes.setCategory( entity.getCategory() );
        panelNotificationRes.setIsRead( entity.getIsRead() );
        panelNotificationRes.setCreatedAt( entity.getCreatedAt() );
        panelNotificationRes.setUpdatedAt( entity.getUpdatedAt() );

        return panelNotificationRes;
    }

    @Override
    public List<PanelNotificationRes> toDto(List<PanelNotification> entities) {
        if ( entities == null ) {
            return null;
        }

        List<PanelNotificationRes> list = new ArrayList<PanelNotificationRes>( entities.size() );
        for ( PanelNotification panelNotification : entities ) {
            list.add( toDto( panelNotification ) );
        }

        return list;
    }
}
