import './polyfills.server.mjs';
import{a as w,b as P,d as A,e as I,f as V}from"./chunk-N5I4D346.mjs";import{d as S,e as T}from"./chunk-YCBU6FSH.mjs";import{$b as p,Vb as a,ac as y,bc as h,dc as C,fc as E}from"./chunk-3IIE333G.mjs";import{h as g,j as c,k as d,l as f}from"./chunk-2FGBTQRU.mjs";function R(e,s,n,o=""){return d(this,null,function*(){for(let t of e){let{path:r,redirectTo:i,loadChildren:_,children:u}=t;if(r===void 0)continue;let l=F(o,r);if(i!==void 0){yield{route:l,success:!1,redirect:!0};continue}if(/[:*]/.test(r)){yield{route:l,success:!1,redirect:!1};continue}if(yield{route:l,success:!0,redirect:!1},u!=null&&u.length&&(yield*f(R(u,s,n,l))),_){let m=yield new c(S(t,s,n).toPromise());if(m){let{routes:L,injector:N=n}=m;yield*f(R(L,s,N,l))}}}})}function b(e,s){return d(this,null,function*(){let n=C(E,"server",[{provide:w,useValue:{document:s,url:""}},{provide:a,useFactory:()=>{class o extends a{constructor(){super(...arguments);g(this,"ignoredLogs",new Set(["Angular is running in development mode."]))}log(i){this.ignoredLogs.has(i)||super.log(i)}}return new o}},...P])();try{let o;x(e)?o=yield new c(e()):o=(yield new c(n.bootstrapModule(e))).injector.get(p),yield new c(y(o));let t=o.injector,r=t.get(T);if(r.config.length===0)yield{route:"",success:!0,redirect:!1};else{let i=t.get(h);yield*f(R(r.config,i,t))}}finally{n.destroy()}})}function x(e){return typeof e=="function"&&!("\u0275mod"in e)}function F(...e){return e.filter(Boolean).join("/")}export{b as extractRoutes,V as renderApplication,I as renderModule,a as \u0275Console,A as \u0275SERVER_CONTEXT};
