package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.ServiceLabel;
import tndung.vnfb.smm.dto.request.CurrencyRes;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.SpecialPriceLiteRes;
import tndung.vnfb.smm.dto.response.SpecialPriceRes;
import tndung.vnfb.smm.dto.response.SpecialPriceRes.SpecialPriceResBuilder;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.entity.Currency;
import tndung.vnfb.smm.entity.Currency.CurrencyBuilder;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.SpecialPrice;
import tndung.vnfb.smm.entity.SpecialPrice.DiscountType;
import tndung.vnfb.smm.entity.SpecialPrice.SpecialPriceBuilder;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class SpecialPriceMapperImpl implements SpecialPriceMapper {

    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public SpecialPriceRes toRes(SpecialPrice entity) {
        if ( entity == null ) {
            return null;
        }

        SpecialPriceResBuilder specialPriceRes = SpecialPriceRes.builder();

        specialPriceRes.user( gUserMapper.toApiKey( entity.getUser() ) );
        specialPriceRes.service( gServiceToServiceRes( entity.getService() ) );
        specialPriceRes.id( entity.getId() );
        if ( entity.getDiscountType() != null ) {
            specialPriceRes.discountType( entity.getDiscountType().name() );
        }
        specialPriceRes.discountValue( entity.getDiscountValue() );

        return specialPriceRes.build();
    }

    @Override
    public SpecialPriceLiteRes toLiteRes(SpecialPrice entity) {
        if ( entity == null ) {
            return null;
        }

        SpecialPriceLiteRes specialPriceLiteRes = new SpecialPriceLiteRes();

        specialPriceLiteRes.setId( entity.getId() );
        if ( entity.getDiscountType() != null ) {
            specialPriceLiteRes.setDiscountType( entity.getDiscountType().name() );
        }
        specialPriceLiteRes.setDiscountValue( entity.getDiscountValue() );

        return specialPriceLiteRes;
    }

    @Override
    public List<SpecialPriceLiteRes> toLiteRes(List<SpecialPrice> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SpecialPriceLiteRes> list = new ArrayList<SpecialPriceLiteRes>( entities.size() );
        for ( SpecialPrice specialPrice : entities ) {
            list.add( toLiteRes( specialPrice ) );
        }

        return list;
    }

    @Override
    public List<SpecialPriceRes> toRes(List<SpecialPrice> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SpecialPriceRes> list = new ArrayList<SpecialPriceRes>( entities.size() );
        for ( SpecialPrice specialPrice : entities ) {
            list.add( toRes( specialPrice ) );
        }

        return list;
    }

    @Override
    public SpecialPrice toEntity(SpecialPriceRes dto) {
        if ( dto == null ) {
            return null;
        }

        SpecialPriceBuilder specialPrice = SpecialPrice.builder();

        specialPrice.id( dto.getId() );
        specialPrice.user( gUserResToGUser( dto.getUser() ) );
        specialPrice.service( serviceResToGService( dto.getService() ) );
        if ( dto.getDiscountType() != null ) {
            specialPrice.discountType( Enum.valueOf( DiscountType.class, dto.getDiscountType() ) );
        }
        specialPrice.discountValue( dto.getDiscountValue() );

        return specialPrice.build();
    }

    protected ServiceRes gServiceToServiceRes(GService gService) {
        if ( gService == null ) {
            return null;
        }

        ServiceRes serviceRes = new ServiceRes();

        if ( gService.getId() != null ) {
            serviceRes.setId( gService.getId().intValue() );
        }
        serviceRes.setName( gService.getName() );
        serviceRes.setDescription( gService.getDescription() );
        serviceRes.setPrice( gService.getPrice() );
        serviceRes.setAddType( gService.getAddType() );
        List<ServiceLabel> list = gService.getLabels();
        if ( list != null ) {
            serviceRes.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        serviceRes.setType( gService.getType() );
        serviceRes.setMin( gService.getMin() );
        serviceRes.setMax( gService.getMax() );
        serviceRes.setAverageTime( gService.getAverageTime() );
        serviceRes.setRefill( gService.getRefill() );
        serviceRes.setRefillDays( gService.getRefillDays() );
        serviceRes.setSort( gService.getSort() );
        serviceRes.setSampleLink( gService.getSampleLink() );
        serviceRes.setIsOverflow( gService.getIsOverflow() );
        serviceRes.setOverflow( gService.getOverflow() );
        serviceRes.setSpeedPerDay( gService.getSpeedPerDay() );
        serviceRes.setIsFixedPrice( gService.getIsFixedPrice() );
        serviceRes.setCancelButton( gService.getCancelButton() );
        serviceRes.setSpecialPrices( toLiteRes( gService.getSpecialPrices() ) );

        return serviceRes;
    }

    protected Currency currencyResToCurrency(CurrencyRes currencyRes) {
        if ( currencyRes == null ) {
            return null;
        }

        CurrencyBuilder currency = Currency.builder();

        currency.code( currencyRes.getCode() );
        currency.symbol( currencyRes.getSymbol() );
        currency.exchangeRate( currencyRes.getExchangeRate() );

        return currency.build();
    }

    protected GUser gUserResToGUser(GUserRes gUserRes) {
        if ( gUserRes == null ) {
            return null;
        }

        GUser gUser = new GUser();

        gUser.setCreatedAt( gUserRes.getCreatedAt() );
        gUser.setId( gUserRes.getId() );
        gUser.setUserName( gUserRes.getUserName() );
        gUser.setEmail( gUserRes.getEmail() );
        gUser.setPhone( gUserRes.getPhone() );
        gUser.setAvatar( gUserRes.getAvatar() );
        gUser.setCustomDiscount( gUserRes.getCustomDiscount() );
        gUser.setCustomReferralRate( gUserRes.getCustomReferralRate() );
        gUser.setBalance( gUserRes.getBalance() );
        gUser.setStatus( gUserRes.getStatus() );
        gUser.setMfaEnabled( gUserRes.getMfaEnabled() );
        gUser.setLastLoginAt( gUserRes.getLastLoginAt() );
        gUser.setPreferredCurrency( currencyResToCurrency( gUserRes.getPreferredCurrency() ) );
        gUser.setTotalOrder( gUserRes.getTotalOrder() );

        return gUser;
    }

    protected SpecialPrice specialPriceLiteResToSpecialPrice(SpecialPriceLiteRes specialPriceLiteRes) {
        if ( specialPriceLiteRes == null ) {
            return null;
        }

        SpecialPriceBuilder specialPrice = SpecialPrice.builder();

        specialPrice.id( specialPriceLiteRes.getId() );
        if ( specialPriceLiteRes.getDiscountType() != null ) {
            specialPrice.discountType( Enum.valueOf( DiscountType.class, specialPriceLiteRes.getDiscountType() ) );
        }
        specialPrice.discountValue( specialPriceLiteRes.getDiscountValue() );

        return specialPrice.build();
    }

    protected List<SpecialPrice> specialPriceLiteResListToSpecialPriceList(List<SpecialPriceLiteRes> list) {
        if ( list == null ) {
            return null;
        }

        List<SpecialPrice> list1 = new ArrayList<SpecialPrice>( list.size() );
        for ( SpecialPriceLiteRes specialPriceLiteRes : list ) {
            list1.add( specialPriceLiteResToSpecialPrice( specialPriceLiteRes ) );
        }

        return list1;
    }

    protected GService serviceResToGService(ServiceRes serviceRes) {
        if ( serviceRes == null ) {
            return null;
        }

        GService gService = new GService();

        if ( serviceRes.getId() != null ) {
            gService.setId( serviceRes.getId().longValue() );
        }
        gService.setName( serviceRes.getName() );
        gService.setDescription( serviceRes.getDescription() );
        gService.setPrice( serviceRes.getPrice() );
        gService.setAverageTime( serviceRes.getAverageTime() );
        gService.setIsFixedPrice( serviceRes.getIsFixedPrice() );
        gService.setRefill( serviceRes.getRefill() );
        gService.setRefillDays( serviceRes.getRefillDays() );
        gService.setMin( serviceRes.getMin() );
        gService.setMax( serviceRes.getMax() );
        gService.setAddType( serviceRes.getAddType() );
        List<ServiceLabel> list = serviceRes.getLabels();
        if ( list != null ) {
            gService.setLabels( new ArrayList<ServiceLabel>( list ) );
        }
        gService.setType( serviceRes.getType() );
        gService.setSort( serviceRes.getSort() );
        gService.setSampleLink( serviceRes.getSampleLink() );
        gService.setIsOverflow( serviceRes.getIsOverflow() );
        gService.setOverflow( serviceRes.getOverflow() );
        gService.setSpeedPerDay( serviceRes.getSpeedPerDay() );
        gService.setCancelButton( serviceRes.getCancelButton() );
        gService.setSpecialPrices( specialPriceLiteResListToSpecialPriceList( serviceRes.getSpecialPrices() ) );

        return gService;
    }
}
