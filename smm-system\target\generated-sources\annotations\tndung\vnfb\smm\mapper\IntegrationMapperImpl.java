package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.IntegrationReq;
import tndung.vnfb.smm.dto.response.IntegrationRes;
import tndung.vnfb.smm.entity.Integration;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class IntegrationMapperImpl implements IntegrationMapper {

    @Override
    public IntegrationRes toRes(Integration entity) {
        if ( entity == null ) {
            return null;
        }

        IntegrationRes integrationRes = new IntegrationRes();

        integrationRes.setId( entity.getId() );
        integrationRes.setKey( entity.getKey() );
        integrationRes.setValue( entity.getValue() );
        integrationRes.setIcon( entity.getIcon() );
        integrationRes.setPosition( entity.getPosition() );
        integrationRes.setActive( entity.getActive() );

        return integrationRes;
    }

    @Override
    public List<IntegrationRes> toRes(List<Integration> entity) {
        if ( entity == null ) {
            return null;
        }

        List<IntegrationRes> list = new ArrayList<IntegrationRes>( entity.size() );
        for ( Integration integration : entity ) {
            list.add( toRes( integration ) );
        }

        return list;
    }

    @Override
    public Integration toEntity(IntegrationReq req) {
        if ( req == null ) {
            return null;
        }

        Integration integration = new Integration();

        integration.setKey( req.getKey() );
        integration.setValue( req.getValue() );
        integration.setIcon( req.getIcon() );
        integration.setPosition( req.getPosition() );

        return integration;
    }

    @Override
    public void updateEntityFromDto(IntegrationReq dto, Integration entity) {
        if ( dto == null ) {
            return;
        }

        entity.setKey( dto.getKey() );
        entity.setValue( dto.getValue() );
        entity.setIcon( dto.getIcon() );
        entity.setPosition( dto.getPosition() );
    }
}
