package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.request.VoucherReq;
import tndung.vnfb.smm.dto.response.VoucherRes;
import tndung.vnfb.smm.entity.Voucher;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class VoucherMapperImpl implements VoucherMapper {

    @Override
    public VoucherRes toDto(Voucher entity) {
        if ( entity == null ) {
            return null;
        }

        VoucherRes voucherRes = new VoucherRes();

        voucherRes.setId( entity.getId() );
        voucherRes.setCode( entity.getCode() );
        voucherRes.setDiscountValue( entity.getDiscountValue() );
        voucherRes.setUsageLimit( entity.getUsageLimit() );
        voucherRes.setType( entity.getType() );
        voucherRes.setCreatedAt( entity.getCreatedAt() );

        return voucherRes;
    }

    @Override
    public List<VoucherRes> toDto(List<Voucher> entities) {
        if ( entities == null ) {
            return null;
        }

        List<VoucherRes> list = new ArrayList<VoucherRes>( entities.size() );
        for ( Voucher voucher : entities ) {
            list.add( toDto( voucher ) );
        }

        return list;
    }

    @Override
    public Voucher toEntity(VoucherReq voucherReq) {
        if ( voucherReq == null ) {
            return null;
        }

        Voucher voucher = new Voucher();

        voucher.setCode( voucherReq.getCode() );
        voucher.setDiscountValue( voucherReq.getDiscountValue() );
        voucher.setUsageLimit( voucherReq.getUsageLimit() );
        voucher.setType( voucherReq.getType() );

        return voucher;
    }
}
