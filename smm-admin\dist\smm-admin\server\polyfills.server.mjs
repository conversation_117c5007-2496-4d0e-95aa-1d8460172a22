import { createRequire } from 'node:module';
globalThis['require'] ??= createRequire(import.meta.url);
var zr=(b=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(b,{get:(w,h)=>(typeof require<"u"?require:w)[h]}):b)(function(b){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+b+'" is not supported')});var lt=globalThis;function ut(b){return(lt.__Zone_symbol_prefix||"__zone_symbol__")+b}function Hs(){let b=lt.performance;function w(L){b&&b.mark&&b.mark(L)}function h(L,E){b&&b.measure&&b.measure(L,E)}w("Zone");let m=class m{static assertZonePatched(){if(lt.Promise!==D.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let E=m.current;for(;E.parent;)E=E.parent;return E}static get current(){return W.zone}static get currentTask(){return f}static __load_patch(E,C,M=!1){if(D.hasOwnProperty(E)){let z=lt[ut("forceDuplicateZoneCheck")]===!0;if(!M&&z)throw Error("Already loaded patch: "+E)}else if(!lt["__Zone_disable_"+E]){let z="Zone:"+E;w(z),D[E]=C(lt,m,B),h(z,z)}}get parent(){return this._parent}get name(){return this._name}constructor(E,C){this._parent=E,this._name=C?C.name||"unnamed":"<root>",this._properties=C&&C.properties||{},this._zoneDelegate=new a(this,this._parent&&this._parent._zoneDelegate,C)}get(E){let C=this.getZoneWith(E);if(C)return C._properties[E]}getZoneWith(E){let C=this;for(;C;){if(C._properties.hasOwnProperty(E))return C;C=C._parent}return null}fork(E){if(!E)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,E)}wrap(E,C){if(typeof E!="function")throw new Error("Expecting function got: "+E);let M=this._zoneDelegate.intercept(this,E,C),z=this;return function(){return z.runGuarded(M,this,arguments,C)}}run(E,C,M,z){W={parent:W,zone:this};try{return this._zoneDelegate.invoke(this,E,C,M,z)}finally{W=W.parent}}runGuarded(E,C=null,M,z){W={parent:W,zone:this};try{try{return this._zoneDelegate.invoke(this,E,C,M,z)}catch(T){if(this._zoneDelegate.handleError(this,T))throw T}}finally{W=W.parent}}runTask(E,C,M){if(E.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(E.zone||ee).name+"; Execution: "+this.name+")");let z=E,{type:T,data:{isPeriodic:A=!1,isRefreshable:X=!1}={}}=E;if(E.state===P&&(T===G||T===$))return;let me=E.state!=S;me&&z._transitionTo(S,p);let Ie=f;f=z,W={parent:W,zone:this};try{T==$&&E.data&&!A&&!X&&(E.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,z,C,M)}catch(Xe){if(this._zoneDelegate.handleError(this,Xe))throw Xe}}finally{let Xe=E.state;if(Xe!==P&&Xe!==re)if(T==G||A||X&&Xe===v)me&&z._transitionTo(p,S,v);else{let Q=z._zoneDelegates;this._updateTaskCount(z,-1),me&&z._transitionTo(P,S,P),X&&(z._zoneDelegates=Q)}W=W.parent,f=Ie}}scheduleTask(E){if(E.zone&&E.zone!==this){let M=this;for(;M;){if(M===E.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${E.zone.name}`);M=M.parent}}E._transitionTo(v,P);let C=[];E._zoneDelegates=C,E._zone=this;try{E=this._zoneDelegate.scheduleTask(this,E)}catch(M){throw E._transitionTo(re,v,P),this._zoneDelegate.handleError(this,M),M}return E._zoneDelegates===C&&this._updateTaskCount(E,1),E.state==v&&E._transitionTo(p,v),E}scheduleMicroTask(E,C,M,z){return this.scheduleTask(new o(ae,E,C,M,z,void 0))}scheduleMacroTask(E,C,M,z,T){return this.scheduleTask(new o($,E,C,M,z,T))}scheduleEventTask(E,C,M,z,T){return this.scheduleTask(new o(G,E,C,M,z,T))}cancelTask(E){if(E.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(E.zone||ee).name+"; Execution: "+this.name+")");if(!(E.state!==p&&E.state!==S)){E._transitionTo(_,p,S);try{this._zoneDelegate.cancelTask(this,E)}catch(C){throw E._transitionTo(re,_),this._zoneDelegate.handleError(this,C),C}return this._updateTaskCount(E,-1),E._transitionTo(P,_),E.runCount=-1,E}}_updateTaskCount(E,C){let M=E._zoneDelegates;C==-1&&(E._zoneDelegates=null);for(let z=0;z<M.length;z++)M[z]._updateTaskCount(E.type,C)}};m.__symbol__=ut;let c=m,t={name:"",onHasTask:(L,E,C,M)=>L.hasTask(C,M),onScheduleTask:(L,E,C,M)=>L.scheduleTask(C,M),onInvokeTask:(L,E,C,M,z,T)=>L.invokeTask(C,M,z,T),onCancelTask:(L,E,C,M)=>L.cancelTask(C,M)};class a{get zone(){return this._zone}constructor(E,C,M){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=E,this._parentDelegate=C,this._forkZS=M&&(M&&M.onFork?M:C._forkZS),this._forkDlgt=M&&(M.onFork?C:C._forkDlgt),this._forkCurrZone=M&&(M.onFork?this._zone:C._forkCurrZone),this._interceptZS=M&&(M.onIntercept?M:C._interceptZS),this._interceptDlgt=M&&(M.onIntercept?C:C._interceptDlgt),this._interceptCurrZone=M&&(M.onIntercept?this._zone:C._interceptCurrZone),this._invokeZS=M&&(M.onInvoke?M:C._invokeZS),this._invokeDlgt=M&&(M.onInvoke?C:C._invokeDlgt),this._invokeCurrZone=M&&(M.onInvoke?this._zone:C._invokeCurrZone),this._handleErrorZS=M&&(M.onHandleError?M:C._handleErrorZS),this._handleErrorDlgt=M&&(M.onHandleError?C:C._handleErrorDlgt),this._handleErrorCurrZone=M&&(M.onHandleError?this._zone:C._handleErrorCurrZone),this._scheduleTaskZS=M&&(M.onScheduleTask?M:C._scheduleTaskZS),this._scheduleTaskDlgt=M&&(M.onScheduleTask?C:C._scheduleTaskDlgt),this._scheduleTaskCurrZone=M&&(M.onScheduleTask?this._zone:C._scheduleTaskCurrZone),this._invokeTaskZS=M&&(M.onInvokeTask?M:C._invokeTaskZS),this._invokeTaskDlgt=M&&(M.onInvokeTask?C:C._invokeTaskDlgt),this._invokeTaskCurrZone=M&&(M.onInvokeTask?this._zone:C._invokeTaskCurrZone),this._cancelTaskZS=M&&(M.onCancelTask?M:C._cancelTaskZS),this._cancelTaskDlgt=M&&(M.onCancelTask?C:C._cancelTaskDlgt),this._cancelTaskCurrZone=M&&(M.onCancelTask?this._zone:C._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let z=M&&M.onHasTask,T=C&&C._hasTaskZS;(z||T)&&(this._hasTaskZS=z?M:t,this._hasTaskDlgt=C,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,M.onScheduleTask||(this._scheduleTaskZS=t,this._scheduleTaskDlgt=C,this._scheduleTaskCurrZone=this._zone),M.onInvokeTask||(this._invokeTaskZS=t,this._invokeTaskDlgt=C,this._invokeTaskCurrZone=this._zone),M.onCancelTask||(this._cancelTaskZS=t,this._cancelTaskDlgt=C,this._cancelTaskCurrZone=this._zone))}fork(E,C){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,E,C):new c(E,C)}intercept(E,C,M){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,E,C,M):C}invoke(E,C,M,z,T){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,E,C,M,z,T):C.apply(M,z)}handleError(E,C){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,E,C):!0}scheduleTask(E,C){let M=C;if(this._scheduleTaskZS)this._hasTaskZS&&M._zoneDelegates.push(this._hasTaskDlgtOwner),M=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,E,C),M||(M=C);else if(C.scheduleFn)C.scheduleFn(C);else if(C.type==ae)F(C);else throw new Error("Task is missing scheduleFn.");return M}invokeTask(E,C,M,z){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,E,C,M,z):C.callback.apply(M,z)}cancelTask(E,C){let M;if(this._cancelTaskZS)M=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,E,C);else{if(!C.cancelFn)throw Error("Task is not cancelable");M=C.cancelFn(C)}return M}hasTask(E,C){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,E,C)}catch(M){this.handleError(E,M)}}_updateTaskCount(E,C){let M=this._taskCounts,z=M[E],T=M[E]=z+C;if(T<0)throw new Error("More tasks executed then were scheduled.");if(z==0||T==0){let A={microTask:M.microTask>0,macroTask:M.macroTask>0,eventTask:M.eventTask>0,change:E};this.hasTask(this._zone,A)}}}class o{constructor(E,C,M,z,T,A){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=E,this.source=C,this.data=z,this.scheduleFn=T,this.cancelFn=A,!M)throw new Error("callback is not defined");this.callback=M;let X=this;E===G&&z&&z.useG?this.invoke=o.invokeTask:this.invoke=function(){return o.invokeTask.call(lt,X,this,arguments)}}static invokeTask(E,C,M){E||(E=this),i++;try{return E.runCount++,E.zone.runTask(E,C,M)}finally{i==1&&R(),i--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(P,v)}_transitionTo(E,C,M){if(this._state===C||this._state===M)this._state=E,E==P&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${E}', expecting state '${C}'${M?" or '"+M+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let l=ut("setTimeout"),u=ut("Promise"),s=ut("then"),g=[],d=!1,N;function O(L){if(N||lt[u]&&(N=lt[u].resolve(0)),N){let E=N[s];E||(E=N.then),E.call(N,L)}else lt[l](L,0)}function F(L){i===0&&g.length===0&&O(R),L&&g.push(L)}function R(){if(!d){for(d=!0;g.length;){let L=g;g=[];for(let E=0;E<L.length;E++){let C=L[E];try{C.zone.runTask(C,null,null)}catch(M){B.onUnhandledError(M)}}}B.microtaskDrainDone(),d=!1}}let ee={name:"NO ZONE"},P="notScheduled",v="scheduling",p="scheduled",S="running",_="canceling",re="unknown",ae="microTask",$="macroTask",G="eventTask",D={},B={symbol:ut,currentZoneFrame:()=>W,onUnhandledError:n,microtaskDrainDone:n,scheduleMicroTask:F,showUncaughtError:()=>!c[ut("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:n,patchMethod:()=>n,bindArguments:()=>[],patchThen:()=>n,patchMacroTask:()=>n,patchEventPrototype:()=>n,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>n,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>n,wrapWithCurrentZone:()=>n,filterProperties:()=>[],attachOriginToPatched:()=>n,_redefineProperty:()=>n,patchCallbacks:()=>n,nativeScheduleMicroTask:O},W={parent:null,zone:new c(null,null)},f=null,i=0;function n(){}return h("Zone","Zone"),c}var Tn=Object.getOwnPropertyDescriptor,qs=Object.defineProperty,aa=Object.getPrototypeOf,Ps=Array.prototype.slice,Bs="addEventListener",Fs="removeEventListener",Ht="true",qt="false",Wr=ut("");function Us(b,w){return Zone.current.wrap(b,w)}function sa(b,w,h,c,t){return Zone.current.scheduleMacroTask(b,w,h,c,t)}var Ue=ut,Yr=typeof window<"u",Qr=Yr?window:void 0,tt=Yr&&Qr||globalThis,js="removeAttribute";function Vs(b,w){for(let h=b.length-1;h>=0;h--)typeof b[h]=="function"&&(b[h]=Us(b[h],w+"_"+h));return b}function Gs(b){return b?b.writable===!1?!1:!(typeof b.get=="function"&&typeof b.set>"u"):!0}var ia=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,oa=!("nw"in tt)&&typeof tt.process<"u"&&tt.process.toString()==="[object process]",zs=!oa&&!ia&&!!(Yr&&Qr.HTMLElement),Jn=typeof tt.process<"u"&&tt.process.toString()==="[object process]"&&!ia&&!!(Yr&&Qr.HTMLElement),Kr={},Zs=Ue("enable_beforeunload"),ea=function(b){if(b=b||tt.event,!b)return;let w=Kr[b.type];w||(w=Kr[b.type]=Ue("ON_PROPERTY"+b.type));let h=this||b.target||tt,c=h[w],t;if(zs&&h===Qr&&b.type==="error"){let a=b;t=c&&c.call(this,a.message,a.filename,a.lineno,a.colno,a.error),t===!0&&b.preventDefault()}else t=c&&c.apply(this,arguments),b.type==="beforeunload"&&tt[Zs]&&typeof t=="string"?b.returnValue=t:t!=null&&!t&&b.preventDefault();return t};function ta(b,w,h){let c=Tn(b,w);if(!c&&h&&Tn(h,w)&&(c={enumerable:!0,configurable:!0}),!c||!c.configurable)return;let t=Ue("on"+w+"patched");if(b.hasOwnProperty(t)&&b[t])return;delete c.writable,delete c.value;let a=c.get,o=c.set,l=w.slice(2),u=Kr[l];u||(u=Kr[l]=Ue("ON_PROPERTY"+l)),c.set=function(s){let g=this;if(!g&&b===tt&&(g=tt),!g)return;typeof g[u]=="function"&&g.removeEventListener(l,ea),o&&o.call(g,null),g[u]=s,typeof s=="function"&&g.addEventListener(l,ea,!1)},c.get=function(){let s=this;if(!s&&b===tt&&(s=tt),!s)return null;let g=s[u];if(g)return g;if(a){let d=a.call(this);if(d)return c.set.call(this,d),typeof s[js]=="function"&&s.removeAttribute(w),d}return null},qs(b,w,c),b[t]=!0}function Ws(b,w,h){if(w)for(let c=0;c<w.length;c++)ta(b,"on"+w[c],h);else{let c=[];for(let t in b)t.slice(0,2)=="on"&&c.push(t);for(let t=0;t<c.length;t++)ta(b,c[t],h)}}function Ks(b,w){if(typeof Object.getOwnPropertySymbols!="function")return;Object.getOwnPropertySymbols(b).forEach(c=>{let t=Object.getOwnPropertyDescriptor(b,c);Object.defineProperty(w,c,{get:function(){return b[c]},set:function(a){t&&(!t.writable||typeof t.set!="function")||(b[c]=a)},enumerable:t?t.enumerable:!0,configurable:t?t.configurable:!0})})}var ca=!1;function Xs(b){ca=b}function nr(b,w,h){let c=b;for(;c&&!c.hasOwnProperty(w);)c=aa(c);!c&&b[w]&&(c=b);let t=Ue(w),a=null;if(c&&(!(a=c[t])||!c.hasOwnProperty(t))){a=c[t]=c[w];let o=c&&Tn(c,w);if(Gs(o)){let l=h(a,t,w);c[w]=function(){return l(this,arguments)},_r(c[w],a),ca&&Ks(a,c[w])}}return a}function Xr(b,w,h){let c=null;function t(a){let o=a.data;return o.args[o.cbIdx]=function(){a.invoke.apply(this,arguments)},c.apply(o.target,o.args),a}c=nr(b,w,a=>function(o,l){let u=h(o,l);return u.cbIdx>=0&&typeof l[u.cbIdx]=="function"?sa(u.name,l[u.cbIdx],u,t):a.apply(o,l)})}function Ys(b,w,h){let c=null;function t(a){let o=a.data;return o.args[o.cbIdx]=function(){a.invoke.apply(this,arguments)},c.apply(o.target,o.args),a}c=nr(b,w,a=>function(o,l){let u=h(o,l);return u.cbIdx>=0&&typeof l[u.cbIdx]=="function"?Zone.current.scheduleMicroTask(u.name,l[u.cbIdx],u,t):a.apply(o,l)})}function _r(b,w){b[Ue("OriginalDelegate")]=w}function ra(b){return typeof b=="function"}function na(b){return typeof b=="number"}function Qs(b){b.__load_patch("ZoneAwarePromise",(w,h,c)=>{let t=Object.getOwnPropertyDescriptor,a=Object.defineProperty;function o(Q){if(Q&&Q.toString===Object.prototype.toString){let Z=Q.constructor&&Q.constructor.name;return(Z||"")+": "+JSON.stringify(Q)}return Q?Q.toString():Object.prototype.toString.call(Q)}let l=c.symbol,u=[],s=w[l("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,g=l("Promise"),d=l("then"),N="__creationTrace__";c.onUnhandledError=Q=>{if(c.showUncaughtError()){let Z=Q&&Q.rejection;Z?console.error("Unhandled Promise rejection:",Z instanceof Error?Z.message:Z,"; Zone:",Q.zone.name,"; Task:",Q.task&&Q.task.source,"; Value:",Z,Z instanceof Error?Z.stack:void 0):console.error(Q)}},c.microtaskDrainDone=()=>{for(;u.length;){let Q=u.shift();try{Q.zone.runGuarded(()=>{throw Q.throwOriginal?Q.rejection:Q})}catch(Z){F(Z)}}};let O=l("unhandledPromiseRejectionHandler");function F(Q){c.onUnhandledError(Q);try{let Z=h[O];typeof Z=="function"&&Z.call(this,Q)}catch{}}function R(Q){return Q&&Q.then}function ee(Q){return Q}function P(Q){return A.reject(Q)}let v=l("state"),p=l("value"),S=l("finally"),_=l("parentPromiseValue"),re=l("parentPromiseState"),ae="Promise.then",$=null,G=!0,D=!1,B=0;function W(Q,Z){return H=>{try{m(Q,Z,H)}catch(K){m(Q,!1,K)}}}let f=function(){let Q=!1;return function(H){return function(){Q||(Q=!0,H.apply(null,arguments))}}},i="Promise resolved with itself",n=l("currentTaskTrace");function m(Q,Z,H){let K=f();if(Q===H)throw new TypeError(i);if(Q[v]===$){let ce=null;try{(typeof H=="object"||typeof H=="function")&&(ce=H&&H.then)}catch(he){return K(()=>{m(Q,!1,he)})(),Q}if(Z!==D&&H instanceof A&&H.hasOwnProperty(v)&&H.hasOwnProperty(p)&&H[v]!==$)E(H),m(Q,H[v],H[p]);else if(Z!==D&&typeof ce=="function")try{ce.call(H,K(W(Q,Z)),K(W(Q,!1)))}catch(he){K(()=>{m(Q,!1,he)})()}else{Q[v]=Z;let he=Q[p];if(Q[p]=H,Q[S]===S&&Z===G&&(Q[v]=Q[re],Q[p]=Q[_]),Z===D&&H instanceof Error){let se=h.currentTask&&h.currentTask.data&&h.currentTask.data[N];se&&a(H,n,{configurable:!0,enumerable:!1,writable:!0,value:se})}for(let se=0;se<he.length;)C(Q,he[se++],he[se++],he[se++],he[se++]);if(he.length==0&&Z==D){Q[v]=B;let se=H;try{throw new Error("Uncaught (in promise): "+o(H)+(H&&H.stack?`
`+H.stack:""))}catch(ge){se=ge}s&&(se.throwOriginal=!0),se.rejection=H,se.promise=Q,se.zone=h.current,se.task=h.currentTask,u.push(se),c.scheduleMicroTask()}}}return Q}let L=l("rejectionHandledHandler");function E(Q){if(Q[v]===B){try{let Z=h[L];Z&&typeof Z=="function"&&Z.call(this,{rejection:Q[p],promise:Q})}catch{}Q[v]=D;for(let Z=0;Z<u.length;Z++)Q===u[Z].promise&&u.splice(Z,1)}}function C(Q,Z,H,K,ce){E(Q);let he=Q[v],se=he?typeof K=="function"?K:ee:typeof ce=="function"?ce:P;Z.scheduleMicroTask(ae,()=>{try{let ge=Q[p],be=!!H&&S===H[S];be&&(H[_]=ge,H[re]=he);let _e=Z.run(se,void 0,be&&se!==P&&se!==ee?[]:[ge]);m(H,!0,_e)}catch(ge){m(H,!1,ge)}},H)}let M="function ZoneAwarePromise() { [native code] }",z=function(){},T=w.AggregateError;class A{static toString(){return M}static resolve(Z){return Z instanceof A?Z:m(new this(null),G,Z)}static reject(Z){return m(new this(null),D,Z)}static withResolvers(){let Z={};return Z.promise=new A((H,K)=>{Z.resolve=H,Z.reject=K}),Z}static any(Z){if(!Z||typeof Z[Symbol.iterator]!="function")return Promise.reject(new T([],"All promises were rejected"));let H=[],K=0;try{for(let se of Z)K++,H.push(A.resolve(se))}catch{return Promise.reject(new T([],"All promises were rejected"))}if(K===0)return Promise.reject(new T([],"All promises were rejected"));let ce=!1,he=[];return new A((se,ge)=>{for(let be=0;be<H.length;be++)H[be].then(_e=>{ce||(ce=!0,se(_e))},_e=>{he.push(_e),K--,K===0&&(ce=!0,ge(new T(he,"All promises were rejected")))})})}static race(Z){let H,K,ce=new this((ge,be)=>{H=ge,K=be});function he(ge){H(ge)}function se(ge){K(ge)}for(let ge of Z)R(ge)||(ge=this.resolve(ge)),ge.then(he,se);return ce}static all(Z){return A.allWithCallback(Z)}static allSettled(Z){return(this&&this.prototype instanceof A?this:A).allWithCallback(Z,{thenCallback:K=>({status:"fulfilled",value:K}),errorCallback:K=>({status:"rejected",reason:K})})}static allWithCallback(Z,H){let K,ce,he=new this((_e,Ae)=>{K=_e,ce=Ae}),se=2,ge=0,be=[];for(let _e of Z){R(_e)||(_e=this.resolve(_e));let Ae=ge;try{_e.then(Se=>{be[Ae]=H?H.thenCallback(Se):Se,se--,se===0&&K(be)},Se=>{H?(be[Ae]=H.errorCallback(Se),se--,se===0&&K(be)):ce(Se)})}catch(Se){ce(Se)}se++,ge++}return se-=2,se===0&&K(be),he}constructor(Z){let H=this;if(!(H instanceof A))throw new Error("Must be an instanceof Promise.");H[v]=$,H[p]=[];try{let K=f();Z&&Z(K(W(H,G)),K(W(H,D)))}catch(K){m(H,!1,K)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return A}then(Z,H){var se;let K=(se=this.constructor)==null?void 0:se[Symbol.species];(!K||typeof K!="function")&&(K=this.constructor||A);let ce=new K(z),he=h.current;return this[v]==$?this[p].push(he,ce,Z,H):C(this,he,ce,Z,H),ce}catch(Z){return this.then(null,Z)}finally(Z){var he;let H=(he=this.constructor)==null?void 0:he[Symbol.species];(!H||typeof H!="function")&&(H=A);let K=new H(z);K[S]=S;let ce=h.current;return this[v]==$?this[p].push(ce,K,Z,Z):C(this,ce,K,Z,Z),K}}A.resolve=A.resolve,A.reject=A.reject,A.race=A.race,A.all=A.all;let X=w[g]=w.Promise;w.Promise=A;let me=l("thenPatched");function Ie(Q){let Z=Q.prototype,H=t(Z,"then");if(H&&(H.writable===!1||!H.configurable))return;let K=Z.then;Z[d]=K,Q.prototype.then=function(ce,he){return new A((ge,be)=>{K.call(this,ge,be)}).then(ce,he)},Q[me]=!0}c.patchThen=Ie;function Xe(Q){return function(Z,H){let K=Q.apply(Z,H);if(K instanceof A)return K;let ce=K.constructor;return ce[me]||Ie(ce),K}}return X&&(Ie(X),nr(w,"fetch",Q=>Xe(Q))),Promise[h.__symbol__("uncaughtPromiseErrors")]=u,A})}function $s(b){b.__load_patch("toString",w=>{let h=Function.prototype.toString,c=Ue("OriginalDelegate"),t=Ue("Promise"),a=Ue("Error"),o=function(){if(typeof this=="function"){let g=this[c];if(g)return typeof g=="function"?h.call(g):Object.prototype.toString.call(g);if(this===Promise){let d=w[t];if(d)return h.call(d)}if(this===Error){let d=w[a];if(d)return h.call(d)}}return h.call(this)};o[c]=h,Function.prototype.toString=o;let l=Object.prototype.toString,u="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?u:l.call(this)}})}function Js(){var h;let b=globalThis,w=b[ut("forceDuplicateZoneCheck")]===!0;if(b.Zone&&(w||typeof b.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return(h=b.Zone)!=null||(b.Zone=Hs()),b.Zone}var rr=!1;if(typeof window<"u")try{let b=Object.defineProperty({},"passive",{get:function(){rr=!0}});window.addEventListener("test",b,b),window.removeEventListener("test",b,b)}catch{rr=!1}var ei={useG:!0},at={},ti={},la=new RegExp("^"+Wr+"(\\w+)(true|false)$"),ri=Ue("propagationStopped");function ua(b,w){let h=(w?w(b):b)+qt,c=(w?w(b):b)+Ht,t=Wr+h,a=Wr+c;at[b]={},at[b][qt]=t,at[b][Ht]=a}function ni(b,w,h,c){let t=c&&c.add||Bs,a=c&&c.rm||Fs,o=c&&c.listeners||"eventListeners",l=c&&c.rmAll||"removeAllListeners",u=Ue(t),s="."+t+":",g="prependListener",d="."+g+":",N=function(v,p,S){if(v.isRemoved)return;let _=v.callback;typeof _=="object"&&_.handleEvent&&(v.callback=$=>_.handleEvent($),v.originalDelegate=_);let re;try{v.invoke(v,p,[S])}catch($){re=$}let ae=v.options;if(ae&&typeof ae=="object"&&ae.once){let $=v.originalDelegate?v.originalDelegate:v.callback;p[a].call(p,S.type,$,ae)}return re};function O(v,p,S){if(p=p||b.event,!p)return;let _=v||p.target||b,re=_[at[p.type][S?Ht:qt]];if(re){let ae=[];if(re.length===1){let $=N(re[0],_,p);$&&ae.push($)}else{let $=re.slice();for(let G=0;G<$.length&&!(p&&p[ri]===!0);G++){let D=N($[G],_,p);D&&ae.push(D)}}if(ae.length===1)throw ae[0];for(let $=0;$<ae.length;$++){let G=ae[$];w.nativeScheduleMicroTask(()=>{throw G})}}}let F=function(v){return O(this,v,!1)},R=function(v){return O(this,v,!0)};function ee(v,p){if(!v)return!1;let S=!0;p&&p.useG!==void 0&&(S=p.useG);let _=p&&p.vh,re=!0;p&&p.chkDup!==void 0&&(re=p.chkDup);let ae=!1;p&&p.rt!==void 0&&(ae=p.rt);let $=v;for(;$&&!$.hasOwnProperty(t);)$=aa($);if(!$&&v[t]&&($=v),!$||$[u])return!1;let G=p&&p.eventNameToString,D={},B=$[u]=$[t],W=$[Ue(a)]=$[a],f=$[Ue(o)]=$[o],i=$[Ue(l)]=$[l],n;p&&p.prepend&&(n=$[Ue(p.prepend)]=$[p.prepend]);function m(H,K){return!rr&&typeof H=="object"&&H?!!H.capture:!rr||!K?H:typeof H=="boolean"?{capture:H,passive:!0}:H?typeof H=="object"&&H.passive!==!1?{...H,passive:!0}:H:{passive:!0}}let L=function(H){if(!D.isExisting)return B.call(D.target,D.eventName,D.capture?R:F,D.options)},E=function(H){if(!H.isRemoved){let K=at[H.eventName],ce;K&&(ce=K[H.capture?Ht:qt]);let he=ce&&H.target[ce];if(he){for(let se=0;se<he.length;se++)if(he[se]===H){he.splice(se,1),H.isRemoved=!0,H.removeAbortListener&&(H.removeAbortListener(),H.removeAbortListener=null),he.length===0&&(H.allRemoved=!0,H.target[ce]=null);break}}}if(H.allRemoved)return W.call(H.target,H.eventName,H.capture?R:F,H.options)},C=function(H){return B.call(D.target,D.eventName,H.invoke,D.options)},M=function(H){return n.call(D.target,D.eventName,H.invoke,D.options)},z=function(H){return W.call(H.target,H.eventName,H.invoke,H.options)},T=S?L:C,A=S?E:z,X=function(H,K){let ce=typeof K;return ce==="function"&&H.callback===K||ce==="object"&&H.originalDelegate===K},me=p&&p.diff?p.diff:X,Ie=Zone[Ue("UNPATCHED_EVENTS")],Xe=b[Ue("PASSIVE_EVENTS")];function Q(H){if(typeof H=="object"&&H!==null){let K={...H};return H.signal&&(K.signal=H.signal),K}return H}let Z=function(H,K,ce,he,se=!1,ge=!1){return function(){let be=this||b,_e=arguments[0];p&&p.transferEventName&&(_e=p.transferEventName(_e));let Ae=arguments[1];if(!Ae)return H.apply(this,arguments);if(oa&&_e==="uncaughtException")return H.apply(this,arguments);let Se=!1;if(typeof Ae!="function"){if(!Ae.handleEvent)return H.apply(this,arguments);Se=!0}if(_&&!_(H,Ae,be,arguments))return;let Qe=rr&&!!Xe&&Xe.indexOf(_e)!==-1,Ze=Q(m(arguments[2],Qe)),Ve=Ze==null?void 0:Ze.signal;if(Ve!=null&&Ve.aborted)return;if(Ie){for(let U=0;U<Ie.length;U++)if(_e===Ie[U])return Qe?H.call(be,_e,Ae,Ze):H.apply(this,arguments)}let Et=Ze?typeof Ze=="boolean"?!0:Ze.capture:!1,Fe=Ze&&typeof Ze=="object"?Ze.once:!1,tn=Zone.current,Ne=at[_e];Ne||(ua(_e,G),Ne=at[_e]);let sr=Ne[Et?Ht:qt],ft=be[sr],vr=!1;if(ft){if(vr=!0,re){for(let U=0;U<ft.length;U++)if(me(ft[U],Ae))return}}else ft=be[sr]=[];let Pt,ir=be.constructor.name,Xt=ti[ir];Xt&&(Pt=Xt[_e]),Pt||(Pt=ir+K+(G?G(_e):_e)),D.options=Ze,Fe&&(D.options.once=!1),D.target=be,D.capture=Et,D.eventName=_e,D.isExisting=vr;let vt=S?ei:void 0;vt&&(vt.taskData=D),Ve&&(D.options.signal=void 0);let de=tn.scheduleEventTask(Pt,Ae,vt,ce,he);if(Ve){D.options.signal=Ve;let U=()=>de.zone.cancelTask(de);H.call(Ve,"abort",U,{once:!0}),de.removeAbortListener=()=>Ve.removeEventListener("abort",U)}if(D.target=null,vt&&(vt.taskData=null),Fe&&(D.options.once=!0),!rr&&typeof de.options=="boolean"||(de.options=Ze),de.target=be,de.capture=Et,de.eventName=_e,Se&&(de.originalDelegate=Ae),ge?ft.unshift(de):ft.push(de),se)return be}};return $[t]=Z(B,s,T,A,ae),n&&($[g]=Z(n,d,M,A,ae,!0)),$[a]=function(){let H=this||b,K=arguments[0];p&&p.transferEventName&&(K=p.transferEventName(K));let ce=arguments[2],he=ce?typeof ce=="boolean"?!0:ce.capture:!1,se=arguments[1];if(!se)return W.apply(this,arguments);if(_&&!_(W,se,H,arguments))return;let ge=at[K],be;ge&&(be=ge[he?Ht:qt]);let _e=be&&H[be];if(_e)for(let Ae=0;Ae<_e.length;Ae++){let Se=_e[Ae];if(me(Se,se)){if(_e.splice(Ae,1),Se.isRemoved=!0,_e.length===0&&(Se.allRemoved=!0,H[be]=null,!he&&typeof K=="string")){let Qe=Wr+"ON_PROPERTY"+K;H[Qe]=null}return Se.zone.cancelTask(Se),ae?H:void 0}}return W.apply(this,arguments)},$[o]=function(){let H=this||b,K=arguments[0];p&&p.transferEventName&&(K=p.transferEventName(K));let ce=[],he=fa(H,G?G(K):K);for(let se=0;se<he.length;se++){let ge=he[se],be=ge.originalDelegate?ge.originalDelegate:ge.callback;ce.push(be)}return ce},$[l]=function(){let H=this||b,K=arguments[0];if(K){p&&p.transferEventName&&(K=p.transferEventName(K));let ce=at[K];if(ce){let he=ce[qt],se=ce[Ht],ge=H[he],be=H[se];if(ge){let _e=ge.slice();for(let Ae=0;Ae<_e.length;Ae++){let Se=_e[Ae],Qe=Se.originalDelegate?Se.originalDelegate:Se.callback;this[a].call(this,K,Qe,Se.options)}}if(be){let _e=be.slice();for(let Ae=0;Ae<_e.length;Ae++){let Se=_e[Ae],Qe=Se.originalDelegate?Se.originalDelegate:Se.callback;this[a].call(this,K,Qe,Se.options)}}}}else{let ce=Object.keys(H);for(let he=0;he<ce.length;he++){let se=ce[he],ge=la.exec(se),be=ge&&ge[1];be&&be!=="removeListener"&&this[l].call(this,be)}this[l].call(this,"removeListener")}if(ae)return this},_r($[t],B),_r($[a],W),i&&_r($[l],i),f&&_r($[o],f),!0}let P=[];for(let v=0;v<h.length;v++)P[v]=ee(h[v],c);return P}function fa(b,w){if(!w){let a=[];for(let o in b){let l=la.exec(o),u=l&&l[1];if(u&&(!w||u===w)){let s=b[o];if(s)for(let g=0;g<s.length;g++)a.push(s[g])}}return a}let h=at[w];h||(ua(w),h=at[w]);let c=b[h[qt]],t=b[h[Ht]];return c?t?c.concat(t):c.slice():t?t.slice():[]}function ai(b,w){w.patchMethod(b,"queueMicrotask",h=>function(c,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])})}var Zr=Ue("zoneTask");function Jt(b,w,h,c){let t=null,a=null;w+=c,h+=c;let o={};function l(s){let g=s.data;g.args[0]=function(){return s.invoke.apply(this,arguments)};let d=t.apply(b,g.args);return na(d)?g.handleId=d:(g.handle=d,g.isRefreshable=ra(d.refresh)),s}function u(s){let{handle:g,handleId:d}=s.data;return a.call(b,g!=null?g:d)}t=nr(b,w,s=>function(g,d){var N;if(ra(d[0])){let O={isRefreshable:!1,isPeriodic:c==="Interval",delay:c==="Timeout"||c==="Interval"?d[1]||0:void 0,args:d},F=d[0];d[0]=function(){try{return F.apply(this,arguments)}finally{let{handle:_,handleId:re,isPeriodic:ae,isRefreshable:$}=O;!ae&&!$&&(re?delete o[re]:_&&(_[Zr]=null))}};let R=sa(w,d[0],O,l,u);if(!R)return R;let{handleId:ee,handle:P,isRefreshable:v,isPeriodic:p}=R.data;if(ee)o[ee]=R;else if(P&&(P[Zr]=R,v&&!p)){let S=P.refresh;P.refresh=function(){let{zone:_,state:re}=R;return re==="notScheduled"?(R._state="scheduled",_._updateTaskCount(R,1)):re==="running"&&(R._state="scheduling"),S.call(this)}}return(N=P!=null?P:ee)!=null?N:R}else return s.apply(b,d)}),a=nr(b,h,s=>function(g,d){let N=d[0],O;na(N)?(O=o[N],delete o[N]):(O=N==null?void 0:N[Zr],O?N[Zr]=null:O=N),O!=null&&O.type?O.cancelFn&&O.zone.cancelTask(O):s.apply(b,d)})}function si(b){b.__load_patch("EventEmitter",(w,h,c)=>{let t="addListener",a="prependListener",o="removeListener",l="removeAllListeners",u="listeners",s="on",g="off",d=function(R,ee){return R.callback===ee||R.callback.listener===ee},N=function(R){return typeof R=="string"?R:R?R.toString().replace("(","_").replace(")","_"):""};function O(R){let ee=ni(w,c,[R],{useG:!1,add:t,rm:o,prepend:a,rmAll:l,listeners:u,chkDup:!1,rt:!0,diff:d,eventNameToString:N});ee&&ee[0]&&(R[s]=R[t],R[g]=R[o])}let F;try{F=zr("events")}catch{}F&&F.EventEmitter&&O(F.EventEmitter.prototype)})}function ii(b){b.__load_patch("fs",(w,h,c)=>{var l;let t;try{t=zr("fs")}catch{}if(!t)return;["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter(u=>!!t[u]&&typeof t[u]=="function").forEach(u=>{Xr(t,u,(s,g)=>({name:"fs."+u,args:g,cbIdx:g.length>0?g.length-1:-1,target:s}))});let o=(l=t.realpath)==null?void 0:l[c.symbol("OriginalDelegate")];o!=null&&o.native&&(t.realpath.native=o.native,Xr(t.realpath,"native",(u,s)=>({args:s,target:u,cbIdx:s.length>0?s.length-1:-1,name:"fs.realpath.native"})))})}function oi(b){b.__load_patch("node_util",(w,h,c)=>{c.patchOnProperties=Ws,c.patchMethod=nr,c.bindArguments=Vs,c.patchMacroTask=Xr,Xs(!0)})}var er="set",tr="clear";function ci(b){oi(b),si(b),ii(b),b.__load_patch("node_timers",(w,h)=>{let c=!1;try{let t=zr("timers");if(!(w.setTimeout===t.setTimeout)&&!Jn){let o=t.setTimeout;t.setTimeout=function(){return c=!0,o.apply(this,arguments)};let l=w.setTimeout(()=>{},100);clearTimeout(l),t.setTimeout=o}Jt(t,er,tr,"Timeout"),Jt(t,er,tr,"Interval"),Jt(t,er,tr,"Immediate")}catch{}Jn||(c?(w[h.__symbol__("setTimeout")]=w.setTimeout,w[h.__symbol__("setInterval")]=w.setInterval,w[h.__symbol__("setImmediate")]=w.setImmediate):(Jt(w,er,tr,"Timeout"),Jt(w,er,tr,"Interval"),Jt(w,er,tr,"Immediate")))}),b.__load_patch("nextTick",()=>{Ys(process,"nextTick",(w,h)=>({name:"process.nextTick",args:h,cbIdx:h.length>0&&typeof h[0]=="function"?0:-1,target:process}))}),b.__load_patch("handleUnhandledPromiseRejection",(w,h,c)=>{h[c.symbol("unhandledPromiseRejectionHandler")]=t("unhandledRejection"),h[c.symbol("rejectionHandledHandler")]=t("rejectionHandled");function t(a){return function(o){fa(process,a).forEach(u=>{a==="unhandledRejection"?u.invoke(o.rejection,o.promise):a==="rejectionHandled"&&u.invoke(o.promise)})}}}),b.__load_patch("crypto",()=>{let w;try{w=zr("crypto")}catch{}w&&["randomBytes","pbkdf2"].forEach(c=>{Xr(w,c,(t,a)=>({name:"crypto."+c,args:a,cbIdx:a.length>0&&typeof a[a.length-1]=="function"?a.length-1:-1,target:w}))})}),b.__load_patch("console",(w,h)=>{["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach(t=>{let a=console[h.__symbol__(t)]=console[t];a&&(console[t]=function(){let o=Ps.call(arguments);return h.current===h.root?a.apply(this,o):h.root.run(a,this,o)})})}),b.__load_patch("queueMicrotask",(w,h,c)=>{ai(w,c)})}function li(){let b=Js();return ci(b),Qs(b),$s(b),b}li();var ui=Object.getOwnPropertyNames,le=(b,w)=>function(){return w||(0,b[ui(b)[0]])((w={exports:{}}).exports,w),w.exports},br=le({"external/npm/node_modules/domino/lib/Event.js"(b,w){"use strict";w.exports=h,h.CAPTURING_PHASE=1,h.AT_TARGET=2,h.BUBBLING_PHASE=3;function h(c,t){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=h.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,c&&(this.type=c),t)for(var a in t)this[a]=t[a]}h.prototype=Object.create(Object.prototype,{constructor:{value:h},stopPropagation:{value:function(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0)}},initEvent:{value:function(t,a,o){this._initialized=!0,!this._dispatching&&(this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=t,this.bubbles=a,this.cancelable=o)}}})}}),da=le({"external/npm/node_modules/domino/lib/UIEvent.js"(b,w){"use strict";var h=br();w.exports=c;function c(){h.call(this),this.view=null,this.detail=0}c.prototype=Object.create(h.prototype,{constructor:{value:c},initUIEvent:{value:function(t,a,o,l,u){this.initEvent(t,a,o),this.view=l,this.detail=u}}})}}),pa=le({"external/npm/node_modules/domino/lib/MouseEvent.js"(b,w){"use strict";var h=da();w.exports=c;function c(){h.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}c.prototype=Object.create(h.prototype,{constructor:{value:c},initMouseEvent:{value:function(t,a,o,l,u,s,g,d,N,O,F,R,ee,P,v){switch(this.initEvent(t,a,o,l,u),this.screenX=s,this.screenY=g,this.clientX=d,this.clientY=N,this.ctrlKey=O,this.altKey=F,this.shiftKey=R,this.metaKey=ee,this.button=P,P){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0;break}this.relatedTarget=v}},getModifierState:{value:function(t){switch(t){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})}}),yn=le({"external/npm/node_modules/domino/lib/DOMException.js"(b,w){"use strict";w.exports=D;var h=1,c=3,t=4,a=5,o=7,l=8,u=9,s=11,g=12,d=13,N=14,O=15,F=17,R=18,ee=19,P=20,v=21,p=22,S=23,_=24,re=25,ae=[null,"INDEX_SIZE_ERR",null,"HIERARCHY_REQUEST_ERR","WRONG_DOCUMENT_ERR","INVALID_CHARACTER_ERR",null,"NO_MODIFICATION_ALLOWED_ERR","NOT_FOUND_ERR","NOT_SUPPORTED_ERR","INUSE_ATTRIBUTE_ERR","INVALID_STATE_ERR","SYNTAX_ERR","INVALID_MODIFICATION_ERR","NAMESPACE_ERR","INVALID_ACCESS_ERR",null,"TYPE_MISMATCH_ERR","SECURITY_ERR","NETWORK_ERR","ABORT_ERR","URL_MISMATCH_ERR","QUOTA_EXCEEDED_ERR","TIMEOUT_ERR","INVALID_NODE_TYPE_ERR","DATA_CLONE_ERR"],$=[null,"INDEX_SIZE_ERR (1): the index is not in the allowed range",null,"HIERARCHY_REQUEST_ERR (3): the operation would yield an incorrect nodes model","WRONG_DOCUMENT_ERR (4): the object is in the wrong Document, a call to importNode is required","INVALID_CHARACTER_ERR (5): the string contains invalid characters",null,"NO_MODIFICATION_ALLOWED_ERR (7): the object can not be modified","NOT_FOUND_ERR (8): the object can not be found here","NOT_SUPPORTED_ERR (9): this operation is not supported","INUSE_ATTRIBUTE_ERR (10): setAttributeNode called on owned Attribute","INVALID_STATE_ERR (11): the object is in an invalid state","SYNTAX_ERR (12): the string did not match the expected pattern","INVALID_MODIFICATION_ERR (13): the object can not be modified in this way","NAMESPACE_ERR (14): the operation is not allowed by Namespaces in XML","INVALID_ACCESS_ERR (15): the object does not support the operation or argument",null,"TYPE_MISMATCH_ERR (17): the type of the object does not match the expected type","SECURITY_ERR (18): the operation is insecure","NETWORK_ERR (19): a network error occurred","ABORT_ERR (20): the user aborted an operation","URL_MISMATCH_ERR (21): the given URL does not match another URL","QUOTA_EXCEEDED_ERR (22): the quota has been exceeded","TIMEOUT_ERR (23): a timeout occurred","INVALID_NODE_TYPE_ERR (24): the supplied node is invalid or has an invalid ancestor for this operation","DATA_CLONE_ERR (25): the object can not be cloned."],G={INDEX_SIZE_ERR:h,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:c,WRONG_DOCUMENT_ERR:t,INVALID_CHARACTER_ERR:a,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:o,NOT_FOUND_ERR:l,NOT_SUPPORTED_ERR:u,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:s,SYNTAX_ERR:g,INVALID_MODIFICATION_ERR:d,NAMESPACE_ERR:N,INVALID_ACCESS_ERR:O,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:F,SECURITY_ERR:R,NETWORK_ERR:ee,ABORT_ERR:P,URL_MISMATCH_ERR:v,QUOTA_EXCEEDED_ERR:p,TIMEOUT_ERR:S,INVALID_NODE_TYPE_ERR:_,DATA_CLONE_ERR:re};function D(f){Error.call(this),Error.captureStackTrace(this,this.constructor),this.code=f,this.message=$[f],this.name=ae[f]}D.prototype.__proto__=Error.prototype;for(W in G)B={value:G[W]},Object.defineProperty(D,W,B),Object.defineProperty(D.prototype,W,B);var B,W}}),Nn=le({"external/npm/node_modules/domino/lib/config.js"(b){b.isApiWritable=!globalThis.__domino_frozen__}}),Be=le({"external/npm/node_modules/domino/lib/utils.js"(b){"use strict";var w=yn(),h=w,c=Nn().isApiWritable;b.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"},b.IndexSizeError=function(){throw new w(h.INDEX_SIZE_ERR)},b.HierarchyRequestError=function(){throw new w(h.HIERARCHY_REQUEST_ERR)},b.WrongDocumentError=function(){throw new w(h.WRONG_DOCUMENT_ERR)},b.InvalidCharacterError=function(){throw new w(h.INVALID_CHARACTER_ERR)},b.NoModificationAllowedError=function(){throw new w(h.NO_MODIFICATION_ALLOWED_ERR)},b.NotFoundError=function(){throw new w(h.NOT_FOUND_ERR)},b.NotSupportedError=function(){throw new w(h.NOT_SUPPORTED_ERR)},b.InvalidStateError=function(){throw new w(h.INVALID_STATE_ERR)},b.SyntaxError=function(){throw new w(h.SYNTAX_ERR)},b.InvalidModificationError=function(){throw new w(h.INVALID_MODIFICATION_ERR)},b.NamespaceError=function(){throw new w(h.NAMESPACE_ERR)},b.InvalidAccessError=function(){throw new w(h.INVALID_ACCESS_ERR)},b.TypeMismatchError=function(){throw new w(h.TYPE_MISMATCH_ERR)},b.SecurityError=function(){throw new w(h.SECURITY_ERR)},b.NetworkError=function(){throw new w(h.NETWORK_ERR)},b.AbortError=function(){throw new w(h.ABORT_ERR)},b.UrlMismatchError=function(){throw new w(h.URL_MISMATCH_ERR)},b.QuotaExceededError=function(){throw new w(h.QUOTA_EXCEEDED_ERR)},b.TimeoutError=function(){throw new w(h.TIMEOUT_ERR)},b.InvalidNodeTypeError=function(){throw new w(h.INVALID_NODE_TYPE_ERR)},b.DataCloneError=function(){throw new w(h.DATA_CLONE_ERR)},b.nyi=function(){throw new Error("NotYetImplemented")},b.shouldOverride=function(){throw new Error("Abstract function; should be overriding in subclass.")},b.assert=function(t,a){if(!t)throw new Error("Assertion failed: "+(a||"")+`
`+new Error().stack)},b.expose=function(t,a){for(var o in t)Object.defineProperty(a.prototype,o,{value:t[o],writable:c})},b.merge=function(t,a){for(var o in a)t[o]=a[o]},b.documentOrder=function(t,a){return 3-(t.compareDocumentPosition(a)&6)},b.toASCIILowerCase=function(t){return t.replace(/[A-Z]+/g,function(a){return a.toLowerCase()})},b.toASCIIUpperCase=function(t){return t.replace(/[a-z]+/g,function(a){return a.toUpperCase()})}}}),ma=le({"external/npm/node_modules/domino/lib/EventTarget.js"(b,w){"use strict";var h=br(),c=pa(),t=Be();w.exports=a;function a(){}a.prototype={addEventListener:function(l,u,s){if(u){s===void 0&&(s=!1),this._listeners||(this._listeners=Object.create(null)),this._listeners[l]||(this._listeners[l]=[]);for(var g=this._listeners[l],d=0,N=g.length;d<N;d++){var O=g[d];if(O.listener===u&&O.capture===s)return}var F={listener:u,capture:s};typeof u=="function"&&(F.f=u),g.push(F)}},removeEventListener:function(l,u,s){if(s===void 0&&(s=!1),this._listeners){var g=this._listeners[l];if(g)for(var d=0,N=g.length;d<N;d++){var O=g[d];if(O.listener===u&&O.capture===s){g.length===1?this._listeners[l]=void 0:g.splice(d,1);return}}}},dispatchEvent:function(l){return this._dispatchEvent(l,!1)},_dispatchEvent:function(l,u){typeof u!="boolean"&&(u=!1);function s(R,ee){var P=ee.type,v=ee.eventPhase;if(ee.currentTarget=R,v!==h.CAPTURING_PHASE&&R._handlers&&R._handlers[P]){var p=R._handlers[P],S;if(typeof p=="function")S=p.call(ee.currentTarget,ee);else{var _=p.handleEvent;if(typeof _!="function")throw new TypeError("handleEvent property of event handler object isnot a function.");S=_.call(p,ee)}switch(ee.type){case"mouseover":S===!0&&ee.preventDefault();break;case"beforeunload":default:S===!1&&ee.preventDefault();break}}var re=R._listeners&&R._listeners[P];if(re){re=re.slice();for(var ae=0,$=re.length;ae<$;ae++){if(ee._immediatePropagationStopped)return;var G=re[ae];if(!(v===h.CAPTURING_PHASE&&!G.capture||v===h.BUBBLING_PHASE&&G.capture))if(G.f)G.f.call(ee.currentTarget,ee);else{var D=G.listener.handleEvent;if(typeof D!="function")throw new TypeError("handleEvent property of event listener object is not a function.");D.call(G.listener,ee)}}}}(!l._initialized||l._dispatching)&&t.InvalidStateError(),l.isTrusted=u,l._dispatching=!0,l.target=this;for(var g=[],d=this.parentNode;d;d=d.parentNode)g.push(d);l.eventPhase=h.CAPTURING_PHASE;for(var N=g.length-1;N>=0&&(s(g[N],l),!l._propagationStopped);N--);if(l._propagationStopped||(l.eventPhase=h.AT_TARGET,s(this,l)),l.bubbles&&!l._propagationStopped){l.eventPhase=h.BUBBLING_PHASE;for(var O=0,F=g.length;O<F&&(s(g[O],l),!l._propagationStopped);O++);}if(l._dispatching=!1,l.eventPhase=h.AT_TARGET,l.currentTarget=null,u&&!l.defaultPrevented&&l instanceof c)switch(l.type){case"mousedown":this._armed={x:l.clientX,y:l.clientY,t:l.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":this._isClick(l)&&this._doClick(l),this._armed=null;break}return!l.defaultPrevented},_isClick:function(o){return this._armed!==null&&o.type==="mouseup"&&o.isTrusted&&o.button===0&&o.timeStamp-this._armed.t<1e3&&Math.abs(o.clientX-this._armed.x)<10&&Math.abs(o.clientY-this._armed.Y)<10},_doClick:function(o){if(!this._click_in_progress){this._click_in_progress=!0;for(var l=this;l&&!l._post_click_activation_steps;)l=l.parentNode;l&&l._pre_click_activation_steps&&l._pre_click_activation_steps();var u=this.ownerDocument.createEvent("MouseEvent");u.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,o.screenX,o.screenY,o.clientX,o.clientY,o.ctrlKey,o.altKey,o.shiftKey,o.metaKey,o.button,null);var s=this._dispatchEvent(u,!0);l&&(s?l._post_click_activation_steps&&l._post_click_activation_steps(u):l._cancelled_activation_steps&&l._cancelled_activation_steps())}},_setEventHandler:function(l,u){this._handlers||(this._handlers=Object.create(null)),this._handlers[l]=u},_getEventHandler:function(l){return this._handlers&&this._handlers[l]||null}}}}),ga=le({"external/npm/node_modules/domino/lib/LinkedList.js"(b,w){"use strict";var h=Be(),c=w.exports={valid:function(t){return h.assert(t,"list falsy"),h.assert(t._previousSibling,"previous falsy"),h.assert(t._nextSibling,"next falsy"),!0},insertBefore:function(t,a){h.assert(c.valid(t)&&c.valid(a));var o=t,l=t._previousSibling,u=a,s=a._previousSibling;o._previousSibling=s,l._nextSibling=u,s._nextSibling=o,u._previousSibling=l,h.assert(c.valid(t)&&c.valid(a))},replace:function(t,a){h.assert(c.valid(t)&&(a===null||c.valid(a))),a!==null&&c.insertBefore(a,t),c.remove(t),h.assert(c.valid(t)&&(a===null||c.valid(a)))},remove:function(t){h.assert(c.valid(t));var a=t._previousSibling;if(a!==t){var o=t._nextSibling;a._nextSibling=o,o._previousSibling=a,t._previousSibling=t._nextSibling=t,h.assert(c.valid(t))}}}}}),_a=le({"external/npm/node_modules/domino/lib/NodeUtils.js"(b,w){"use strict";w.exports={serializeOne:ee,\u0275escapeMatchingClosingTag:N,\u0275escapeClosingCommentTag:F,\u0275escapeProcessingInstructionContent:R};var h=Be(),c=h.NAMESPACE,t={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},a={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},o={},l=/[&<>\u00A0]/g,u=/[&"<>\u00A0]/g;function s(P){return l.test(P)?P.replace(l,v=>{switch(v){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case"\xA0":return"&nbsp;"}}):P}function g(P){return u.test(P)?P.replace(u,v=>{switch(v){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"\xA0":return"&nbsp;"}}):P}function d(P){var v=P.namespaceURI;return v?v===c.XML?"xml:"+P.localName:v===c.XLINK?"xlink:"+P.localName:v===c.XMLNS?P.localName==="xmlns"?"xmlns":"xmlns:"+P.localName:P.name:P.localName}function N(P,v){let p="</"+v;if(!P.toLowerCase().includes(p))return P;let S=[...P],_=P.matchAll(new RegExp(p,"ig"));for(let re of _)S[re.index]="&lt;";return S.join("")}var O=/--!?>/;function F(P){return O.test(P)?P.replace(/(--\!?)>/g,"$1&gt;"):P}function R(P){return P.includes(">")?P.replaceAll(">","&gt;"):P}function ee(P,v){var p="";switch(P.nodeType){case 1:var S=P.namespaceURI,_=S===c.HTML,re=_||S===c.SVG||S===c.MATHML?P.localName:P.tagName;p+="<"+re;for(var ae=0,$=P._numattrs;ae<$;ae++){var G=P._attr(ae);p+=" "+d(G),G.value!==void 0&&(p+='="'+g(G.value)+'"')}if(p+=">",!(_&&a[re])){var D=P.serialize();t[re.toUpperCase()]&&(D=N(D,re)),_&&o[re]&&D.charAt(0)===`
`&&(p+=`
`),p+=D,p+="</"+re+">"}break;case 3:case 4:var B;v.nodeType===1&&v.namespaceURI===c.HTML?B=v.tagName:B="",t[B]||B==="NOSCRIPT"&&v.ownerDocument._scripting_enabled?p+=P.data:p+=s(P.data);break;case 8:p+="<!--"+F(P.data)+"-->";break;case 7:let W=R(P.data);p+="<?"+P.target+" "+W+"?>";break;case 10:p+="<!DOCTYPE "+P.name,p+=">";break;default:h.InvalidStateError()}return p}}}),Ge=le({"external/npm/node_modules/domino/lib/Node.js"(b,w){"use strict";w.exports=o;var h=ma(),c=ga(),t=_a(),a=Be();function o(){h.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var l=o.ELEMENT_NODE=1,u=o.ATTRIBUTE_NODE=2,s=o.TEXT_NODE=3,g=o.CDATA_SECTION_NODE=4,d=o.ENTITY_REFERENCE_NODE=5,N=o.ENTITY_NODE=6,O=o.PROCESSING_INSTRUCTION_NODE=7,F=o.COMMENT_NODE=8,R=o.DOCUMENT_NODE=9,ee=o.DOCUMENT_TYPE_NODE=10,P=o.DOCUMENT_FRAGMENT_NODE=11,v=o.NOTATION_NODE=12,p=o.DOCUMENT_POSITION_DISCONNECTED=1,S=o.DOCUMENT_POSITION_PRECEDING=2,_=o.DOCUMENT_POSITION_FOLLOWING=4,re=o.DOCUMENT_POSITION_CONTAINS=8,ae=o.DOCUMENT_POSITION_CONTAINED_BY=16,$=o.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;o.prototype=Object.create(h.prototype,{baseURI:{get:a.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===l?this.parentNode:null}},hasChildNodes:{value:a.shouldOverride},firstChild:{get:a.shouldOverride},lastChild:{get:a.shouldOverride},isConnected:{get:function(){let G=this;for(;G!=null;){if(G.nodeType===o.DOCUMENT_NODE)return!0;G=G.parentNode,G!=null&&G.nodeType===o.DOCUMENT_FRAGMENT_NODE&&(G=G.host)}return!1}},previousSibling:{get:function(){var G=this.parentNode;return!G||this===G.firstChild?null:this._previousSibling}},nextSibling:{get:function(){var G=this.parentNode,D=this._nextSibling;return!G||D===G.firstChild?null:D}},textContent:{get:function(){return null},set:function(G){}},innerText:{get:function(){return null},set:function(G){}},_countChildrenOfType:{value:function(G){for(var D=0,B=this.firstChild;B!==null;B=B.nextSibling)B.nodeType===G&&D++;return D}},_ensureInsertValid:{value:function(D,B,W){var f=this,i,n;if(!D.nodeType)throw new TypeError("not a node");switch(f.nodeType){case R:case P:case l:break;default:a.HierarchyRequestError()}switch(D.isAncestor(f)&&a.HierarchyRequestError(),(B!==null||!W)&&B.parentNode!==f&&a.NotFoundError(),D.nodeType){case P:case ee:case l:case s:case O:case F:break;default:a.HierarchyRequestError()}if(f.nodeType===R)switch(D.nodeType){case s:a.HierarchyRequestError();break;case P:switch(D._countChildrenOfType(s)>0&&a.HierarchyRequestError(),D._countChildrenOfType(l)){case 0:break;case 1:if(B!==null)for(W&&B.nodeType===ee&&a.HierarchyRequestError(),n=B.nextSibling;n!==null;n=n.nextSibling)n.nodeType===ee&&a.HierarchyRequestError();i=f._countChildrenOfType(l),W?i>0&&a.HierarchyRequestError():(i>1||i===1&&B.nodeType!==l)&&a.HierarchyRequestError();break;default:a.HierarchyRequestError()}break;case l:if(B!==null)for(W&&B.nodeType===ee&&a.HierarchyRequestError(),n=B.nextSibling;n!==null;n=n.nextSibling)n.nodeType===ee&&a.HierarchyRequestError();i=f._countChildrenOfType(l),W?i>0&&a.HierarchyRequestError():(i>1||i===1&&B.nodeType!==l)&&a.HierarchyRequestError();break;case ee:if(B===null)f._countChildrenOfType(l)&&a.HierarchyRequestError();else for(n=f.firstChild;n!==null&&n!==B;n=n.nextSibling)n.nodeType===l&&a.HierarchyRequestError();i=f._countChildrenOfType(ee),W?i>0&&a.HierarchyRequestError():(i>1||i===1&&B.nodeType!==ee)&&a.HierarchyRequestError();break}else D.nodeType===ee&&a.HierarchyRequestError()}},insertBefore:{value:function(D,B){var W=this;W._ensureInsertValid(D,B,!0);var f=B;return f===D&&(f=D.nextSibling),W.doc.adoptNode(D),D._insertOrReplace(W,f,!1),D}},appendChild:{value:function(G){return this.insertBefore(G,null)}},_appendChild:{value:function(G){G._insertOrReplace(this,null,!1)}},removeChild:{value:function(D){var B=this;if(!D.nodeType)throw new TypeError("not a node");return D.parentNode!==B&&a.NotFoundError(),D.remove(),D}},replaceChild:{value:function(D,B){var W=this;return W._ensureInsertValid(D,B,!1),D.doc!==W.doc&&W.doc.adoptNode(D),D._insertOrReplace(W,B,!0),B}},contains:{value:function(D){return D===null?!1:this===D?!0:(this.compareDocumentPosition(D)&ae)!==0}},compareDocumentPosition:{value:function(D){if(this===D)return 0;if(this.doc!==D.doc||this.rooted!==D.rooted)return p+$;for(var B=[],W=[],f=this;f!==null;f=f.parentNode)B.push(f);for(f=D;f!==null;f=f.parentNode)W.push(f);if(B.reverse(),W.reverse(),B[0]!==W[0])return p+$;f=Math.min(B.length,W.length);for(var i=1;i<f;i++)if(B[i]!==W[i])return B[i].index<W[i].index?_:S;return B.length<W.length?_+ae:S+re}},isSameNode:{value:function(D){return this===D}},isEqualNode:{value:function(D){if(!D||D.nodeType!==this.nodeType||!this.isEqual(D))return!1;for(var B=this.firstChild,W=D.firstChild;B&&W;B=B.nextSibling,W=W.nextSibling)if(!B.isEqualNode(W))return!1;return B===null&&W===null}},cloneNode:{value:function(G){var D=this.clone();if(G)for(var B=this.firstChild;B!==null;B=B.nextSibling)D._appendChild(B.cloneNode(!0));return D}},lookupPrefix:{value:function(D){var B;if(D===""||D===null||D===void 0)return null;switch(this.nodeType){case l:return this._lookupNamespacePrefix(D,this);case R:return B=this.documentElement,B?B.lookupPrefix(D):null;case N:case v:case P:case ee:return null;case u:return B=this.ownerElement,B?B.lookupPrefix(D):null;default:return B=this.parentElement,B?B.lookupPrefix(D):null}}},lookupNamespaceURI:{value:function(D){(D===""||D===void 0)&&(D=null);var B;switch(this.nodeType){case l:return a.shouldOverride();case R:return B=this.documentElement,B?B.lookupNamespaceURI(D):null;case N:case v:case ee:case P:return null;case u:return B=this.ownerElement,B?B.lookupNamespaceURI(D):null;default:return B=this.parentElement,B?B.lookupNamespaceURI(D):null}}},isDefaultNamespace:{value:function(D){(D===""||D===void 0)&&(D=null);var B=this.lookupNamespaceURI(null);return B===D}},index:{get:function(){var G=this.parentNode;if(this===G.firstChild)return 0;var D=G.childNodes;if(this._index===void 0||D[this._index]!==this){for(var B=0;B<D.length;B++)D[B]._index=B;a.assert(D[this._index]===this)}return this._index}},isAncestor:{value:function(G){if(this.doc!==G.doc||this.rooted!==G.rooted)return!1;for(var D=G;D;D=D.parentNode)if(D===this)return!0;return!1}},ensureSameDoc:{value:function(G){G.ownerDocument===null?G.ownerDocument=this.doc:G.ownerDocument!==this.doc&&a.WrongDocumentError()}},removeChildren:{value:a.shouldOverride},_insertOrReplace:{value:function(D,B,W){var f=this,i,n;if(f.nodeType===P&&f.rooted&&a.HierarchyRequestError(),D._childNodes&&(i=B===null?D._childNodes.length:B.index,f.parentNode===D)){var m=f.index;m<i&&i--}W&&(B.rooted&&B.doc.mutateRemove(B),B.parentNode=null);var L=B;L===null&&(L=D.firstChild);var E=f.rooted&&D.rooted;if(f.nodeType===P){for(var C=[0,W?1:0],M,z=f.firstChild;z!==null;z=M)M=z.nextSibling,C.push(z),z.parentNode=D;var T=C.length;if(W?c.replace(L,T>2?C[2]:null):T>2&&L!==null&&c.insertBefore(C[2],L),D._childNodes)for(C[0]=B===null?D._childNodes.length:B._index,D._childNodes.splice.apply(D._childNodes,C),n=2;n<T;n++)C[n]._index=C[0]+(n-2);else D._firstChild===B&&(T>2?D._firstChild=C[2]:W&&(D._firstChild=null));if(f._childNodes?f._childNodes.length=0:f._firstChild=null,D.rooted)for(D.modify(),n=2;n<T;n++)D.doc.mutateInsert(C[n])}else{if(B===f)return;E?f._remove():f.parentNode&&f.remove(),f.parentNode=D,W?(c.replace(L,f),D._childNodes?(f._index=i,D._childNodes[i]=f):D._firstChild===B&&(D._firstChild=f)):(L!==null&&c.insertBefore(f,L),D._childNodes?(f._index=i,D._childNodes.splice(i,0,f)):D._firstChild===B&&(D._firstChild=f)),E?(D.modify(),D.doc.mutateMove(f)):D.rooted&&(D.modify(),D.doc.mutateInsert(f))}}},lastModTime:{get:function(){return this._lastModTime||(this._lastModTime=this.doc.modclock),this._lastModTime}},modify:{value:function(){if(this.doc.modclock)for(var G=++this.doc.modclock,D=this;D;D=D.parentElement)D._lastModTime&&(D._lastModTime=G)}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){for(var G,D=this.firstChild;D!==null;D=G)if(G=D.nextSibling,D.normalize&&D.normalize(),D.nodeType===o.TEXT_NODE){if(D.nodeValue===""){this.removeChild(D);continue}var B=D.previousSibling;B!==null&&B.nodeType===o.TEXT_NODE&&(B.appendData(D.nodeValue),this.removeChild(D))}}},serialize:{value:function(){if(this._innerHTML)return this._innerHTML;for(var G="",D=this.firstChild;D!==null;D=D.nextSibling)G+=t.serializeOne(D,this);return G}},outerHTML:{get:function(){return t.serializeOne(this,{nodeType:0})},set:a.nyi},ELEMENT_NODE:{value:l},ATTRIBUTE_NODE:{value:u},TEXT_NODE:{value:s},CDATA_SECTION_NODE:{value:g},ENTITY_REFERENCE_NODE:{value:d},ENTITY_NODE:{value:N},PROCESSING_INSTRUCTION_NODE:{value:O},COMMENT_NODE:{value:F},DOCUMENT_NODE:{value:R},DOCUMENT_TYPE_NODE:{value:ee},DOCUMENT_FRAGMENT_NODE:{value:P},NOTATION_NODE:{value:v},DOCUMENT_POSITION_DISCONNECTED:{value:p},DOCUMENT_POSITION_PRECEDING:{value:S},DOCUMENT_POSITION_FOLLOWING:{value:_},DOCUMENT_POSITION_CONTAINS:{value:re},DOCUMENT_POSITION_CONTAINED_BY:{value:ae},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:$}})}}),fi=le({"external/npm/node_modules/domino/lib/NodeList.es6.js"(b,w){"use strict";w.exports=class extends Array{constructor(c){if(super(c&&c.length||0),c)for(var t in c)this[t]=c[t]}item(c){return this[c]||null}}}}),hi=le({"external/npm/node_modules/domino/lib/NodeList.es5.js"(b,w){"use strict";function h(t){return this[t]||null}function c(t){return t||(t=[]),t.item=h,t}w.exports=c}}),ar=le({"external/npm/node_modules/domino/lib/NodeList.js"(b,w){"use strict";var h;try{h=fi()}catch{h=hi()}w.exports=h}}),wn=le({"external/npm/node_modules/domino/lib/ContainerNode.js"(b,w){"use strict";w.exports=t;var h=Ge(),c=ar();function t(){h.call(this),this._firstChild=this._childNodes=null}t.prototype=Object.create(h.prototype,{hasChildNodes:{value:function(){return this._childNodes?this._childNodes.length>0:this._firstChild!==null}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){return this._childNodes?this._childNodes.length===0?null:this._childNodes[0]:this._firstChild}},lastChild:{get:function(){var a=this._childNodes,o;return a?a.length===0?null:a[a.length-1]:(o=this._firstChild,o===null?null:o._previousSibling)}},_ensureChildNodes:{value:function(){if(!this._childNodes){var a=this._firstChild,o=a,l=this._childNodes=new c;if(a)do l.push(o),o=o._nextSibling;while(o!==a);this._firstChild=null}}},removeChildren:{value:function(){for(var o=this.rooted?this.ownerDocument:null,l=this.firstChild,u;l!==null;)u=l,l=u.nextSibling,o&&o.mutateRemove(u),u.parentNode=null;this._childNodes?this._childNodes.length=0:this._firstChild=null,this.modify()}}})}}),Sn=le({"external/npm/node_modules/domino/lib/xmlnames.js"(b){"use strict";b.isValidName=R,b.isValidQName=ee;var w=/^[_:A-Za-z][-.:\w]+$/,h=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,c="_A-Za-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",t="-._A-Za-z0-9\xB7\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0300-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",a="["+c+"]["+t+"]*",o=c+":",l=t+":",u=new RegExp("^["+o+"]["+l+"]*$"),s=new RegExp("^("+a+"|"+a+":"+a+")$"),g=/[\uD800-\uDB7F\uDC00-\uDFFF]/,d=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,N=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;c+="\uD800-\u{EFC00}-\uDFFF",t+="\uD800-\u{EFC00}-\uDFFF",a="["+c+"]["+t+"]*",o=c+":",l=t+":";var O=new RegExp("^["+o+"]["+l+"]*$"),F=new RegExp("^("+a+"|"+a+":"+a+")$");function R(P){if(w.test(P)||u.test(P))return!0;if(!g.test(P)||!O.test(P))return!1;var v=P.match(d),p=P.match(N);return p!==null&&2*p.length===v.length}function ee(P){if(h.test(P)||s.test(P))return!0;if(!g.test(P)||!F.test(P))return!1;var v=P.match(d),p=P.match(N);return p!==null&&2*p.length===v.length}}}),ba=le({"external/npm/node_modules/domino/lib/attributes.js"(b){"use strict";var w=Be();b.property=function(c){if(Array.isArray(c.type)){var t=Object.create(null);c.type.forEach(function(l){t[l.value||l]=l.alias||l});var a=c.missing;a===void 0&&(a=null);var o=c.invalid;return o===void 0&&(o=a),{get:function(){var l=this._getattr(c.name);return l===null?a:(l=t[l.toLowerCase()],l!==void 0?l:o!==null?o:l)},set:function(l){this._setattr(c.name,l)}}}else{if(c.type===Boolean)return{get:function(){return this.hasAttribute(c.name)},set:function(l){l?this._setattr(c.name,""):this.removeAttribute(c.name)}};if(c.type===Number||c.type==="long"||c.type==="unsigned long"||c.type==="limited unsigned long with fallback")return h(c);if(!c.type||c.type===String)return{get:function(){return this._getattr(c.name)||""},set:function(l){c.treatNullAsEmptyString&&l===null&&(l=""),this._setattr(c.name,l)}};if(typeof c.type=="function")return c.type(c.name,c)}throw new Error("Invalid attribute definition")};function h(c){var t;typeof c.default=="function"?t=c.default:typeof c.default=="number"?t=function(){return c.default}:t=function(){w.assert(!1,typeof c.default)};var a=c.type==="unsigned long",o=c.type==="long",l=c.type==="limited unsigned long with fallback",u=c.min,s=c.max,g=c.setmin;return u===void 0&&(a&&(u=0),o&&(u=-2147483648),l&&(u=1)),s===void 0&&(a||o||l)&&(s=2147483647),{get:function(){var d=this._getattr(c.name),N=c.float?parseFloat(d):parseInt(d,10);if(d===null||!isFinite(N)||u!==void 0&&N<u||s!==void 0&&N>s)return t.call(this);if(a||o||l){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(d))return t.call(this);N=N|0}return N},set:function(d){c.float||(d=Math.floor(d)),g!==void 0&&d<g&&w.IndexSizeError(c.name+" set to "+d),a?d=d<0||d>2147483647?t.call(this):d|0:l?d=d<1||d>2147483647?t.call(this):d|0:o&&(d=d<-2147483648||d>2147483647?t.call(this):d|0),this._setattr(c.name,String(d))}}}b.registerChangeHandler=function(c,t,a){var o=c.prototype;Object.prototype.hasOwnProperty.call(o,"_attributeChangeHandlers")||(o._attributeChangeHandlers=Object.create(o._attributeChangeHandlers||null)),o._attributeChangeHandlers[t]=a}}}),di=le({"external/npm/node_modules/domino/lib/FilteredElementList.js"(b,w){"use strict";w.exports=c;var h=Ge();function c(t,a){this.root=t,this.filter=a,this.lastModTime=t.lastModTime,this.done=!1,this.cache=[],this.traverse()}c.prototype=Object.create(Object.prototype,{length:{get:function(){return this.checkcache(),this.done||this.traverse(),this.cache.length}},item:{value:function(t){return this.checkcache(),!this.done&&t>=this.cache.length&&this.traverse(),this.cache[t]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var t=this.cache.length-1;t>=0;t--)this[t]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(t){t!==void 0&&t++;for(var a;(a=this.next())!==null;)if(this[this.cache.length]=a,this.cache.push(a),t&&this.cache.length===t)return;this.done=!0}},next:{value:function(){var t=this.cache.length===0?this.root:this.cache[this.cache.length-1],a;for(t.nodeType===h.DOCUMENT_NODE?a=t.documentElement:a=t.nextElement(this.root);a;){if(this.filter(a))return a;a=a.nextElement(this.root)}return null}}})}}),Ea=le({"external/npm/node_modules/domino/lib/DOMTokenList.js"(b,w){"use strict";var h=Be();w.exports=c;function c(u,s){this._getString=u,this._setString=s,this._length=0,this._lastStringValue="",this._update()}Object.defineProperties(c.prototype,{length:{get:function(){return this._length}},item:{value:function(u){var s=l(this);return u<0||u>=s.length?null:s[u]}},contains:{value:function(u){u=String(u);var s=l(this);return s.indexOf(u)>-1}},add:{value:function(){for(var u=l(this),s=0,g=arguments.length;s<g;s++){var d=a(arguments[s]);u.indexOf(d)<0&&u.push(d)}this._update(u)}},remove:{value:function(){for(var u=l(this),s=0,g=arguments.length;s<g;s++){var d=a(arguments[s]),N=u.indexOf(d);N>-1&&u.splice(N,1)}this._update(u)}},toggle:{value:function(s,g){return s=a(s),this.contains(s)?g===void 0||g===!1?(this.remove(s),!1):!0:g===void 0||g===!0?(this.add(s),!0):!1}},replace:{value:function(s,g){String(g)===""&&h.SyntaxError(),s=a(s),g=a(g);var d=l(this),N=d.indexOf(s);if(N<0)return!1;var O=d.indexOf(g);return O<0?d[N]=g:N<O?(d[N]=g,d.splice(O,1)):d.splice(N,1),this._update(d),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(u){this._setString(u),this._update()}},_update:{value:function(u){u?(t(this,u),this._setString(u.join(" ").trim())):t(this,l(this)),this._lastStringValue=this._getString()}}});function t(u,s){var g=u._length,d;for(u._length=s.length,d=0;d<s.length;d++)u[d]=s[d];for(;d<g;d++)u[d]=void 0}function a(u){return u=String(u),u===""&&h.SyntaxError(),/[ \t\r\n\f]/.test(u)&&h.InvalidCharacterError(),u}function o(u){for(var s=u._length,g=Array(s),d=0;d<s;d++)g[d]=u[d];return g}function l(u){var s=u._getString();if(s===u._lastStringValue)return o(u);var g=s.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(g==="")return[];var d=Object.create(null);return g.split(/[ \t\r\n\f]+/g).filter(function(N){var O="$"+N;return d[O]?!1:(d[O]=!0,!0)})}}}),kn=le({"external/npm/node_modules/domino/lib/select.js"(b,w){"use strict";var h=Object.create(null,{location:{get:function(){throw new Error("window.location is not supported.")}}}),c=function(f,i){return f.compareDocumentPosition(i)},t=function(f,i){return c(f,i)&2?1:-1},a=function(f){for(;(f=f.nextSibling)&&f.nodeType!==1;);return f},o=function(f){for(;(f=f.previousSibling)&&f.nodeType!==1;);return f},l=function(f){if(f=f.firstChild)for(;f.nodeType!==1&&(f=f.nextSibling););return f},u=function(f){if(f=f.lastChild)for(;f.nodeType!==1&&(f=f.previousSibling););return f},s=function(f){if(!f.parentNode)return!1;var i=f.parentNode.nodeType;return i===1||i===9},g=function(f){if(!f)return f;var i=f[0];return i==='"'||i==="'"?(f[f.length-1]===i?f=f.slice(1,-1):f=f.slice(1),f.replace(_.str_escape,function(n){var m=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(n);if(!m)return n.slice(1);if(m[2])return"";var L=parseInt(m[1],16);return String.fromCodePoint?String.fromCodePoint(L):String.fromCharCode(L)})):_.ident.test(f)?d(f):f},d=function(f){return f.replace(_.escape,function(i){var n=/^\\([0-9A-Fa-f]+)/.exec(i);if(!n)return i[1];var m=parseInt(n[1],16);return String.fromCodePoint?String.fromCodePoint(m):String.fromCharCode(m)})},N=function(){return Array.prototype.indexOf?Array.prototype.indexOf:function(f,i){for(var n=this.length;n--;)if(this[n]===i)return n;return-1}}(),O=function(f,i){var n=_.inside.source.replace(/</g,f).replace(/>/g,i);return new RegExp(n)},F=function(f,i,n){return f=f.source,f=f.replace(i,n.source||n),new RegExp(f)},R=function(f,i){return f.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",i).join("/")},ee=function(f,i){var n=f.replace(/\s+/g,""),m;return n==="even"?n="2n+0":n==="odd"?n="2n+1":n.indexOf("n")===-1&&(n="0n"+n),m=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(n),{group:m[1]==="-"?-(m[2]||1):+(m[2]||1),offset:m[4]?m[3]==="-"?-m[4]:+m[4]:0}},P=function(f,i,n){var m=ee(f),L=m.group,E=m.offset,C=n?u:l,M=n?o:a;return function(z){if(s(z))for(var T=C(z.parentNode),A=0;T;){if(i(T,z)&&A++,T===z)return A-=E,L&&A?A%L===0&&A<0==L<0:!A;T=M(T)}}},v={"*":function(){return function(){return!0}}(),type:function(f){return f=f.toLowerCase(),function(i){return i.nodeName.toLowerCase()===f}},attr:function(f,i,n,m){return i=p[i],function(L){var E;switch(f){case"for":E=L.htmlFor;break;case"class":E=L.className,E===""&&L.getAttribute("class")==null&&(E=null);break;case"href":case"src":E=L.getAttribute(f,2);break;case"title":E=L.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(L.getAttribute){E=L.getAttribute(f);break}default:if(L.hasAttribute&&!L.hasAttribute(f))break;E=L[f]!=null?L[f]:L.getAttribute&&L.getAttribute(f);break}if(E!=null)return E=E+"",m&&(E=E.toLowerCase(),n=n.toLowerCase()),i(E,n)}},":first-child":function(f){return!o(f)&&s(f)},":last-child":function(f){return!a(f)&&s(f)},":only-child":function(f){return!o(f)&&!a(f)&&s(f)},":nth-child":function(f,i){return P(f,function(){return!0},i)},":nth-last-child":function(f){return v[":nth-child"](f,!0)},":root":function(f){return f.ownerDocument.documentElement===f},":empty":function(f){return!f.firstChild},":not":function(f){var i=B(f);return function(n){return!i(n)}},":first-of-type":function(f){if(s(f)){for(var i=f.nodeName;f=o(f);)if(f.nodeName===i)return;return!0}},":last-of-type":function(f){if(s(f)){for(var i=f.nodeName;f=a(f);)if(f.nodeName===i)return;return!0}},":only-of-type":function(f){return v[":first-of-type"](f)&&v[":last-of-type"](f)},":nth-of-type":function(f,i){return P(f,function(n,m){return n.nodeName===m.nodeName},i)},":nth-last-of-type":function(f){return v[":nth-of-type"](f,!0)},":checked":function(f){return!!(f.checked||f.selected)},":indeterminate":function(f){return!v[":checked"](f)},":enabled":function(f){return!f.disabled&&f.type!=="hidden"},":disabled":function(f){return!!f.disabled},":target":function(f){return f.id===h.location.hash.substring(1)},":focus":function(f){return f===f.ownerDocument.activeElement},":is":function(f){return B(f)},":matches":function(f){return v[":is"](f)},":nth-match":function(f,i){var n=f.split(/\s*,\s*/),m=n.shift(),L=B(n.join(","));return P(m,L,i)},":nth-last-match":function(f){return v[":nth-match"](f,!0)},":links-here":function(f){return f+""==h.location+""},":lang":function(f){return function(i){for(;i;){if(i.lang)return i.lang.indexOf(f)===0;i=i.parentNode}}},":dir":function(f){return function(i){for(;i;){if(i.dir)return i.dir===f;i=i.parentNode}}},":scope":function(f,i){var n=i||f.ownerDocument;return n.nodeType===9?f===n.documentElement:f===n},":any-link":function(f){return typeof f.href=="string"},":local-link":function(f){if(f.nodeName)return f.href&&f.host===h.location.host;var i=+f+1;return function(n){if(n.href){var m=h.location+"",L=n+"";return R(m,i)===R(L,i)}}},":default":function(f){return!!f.defaultSelected},":valid":function(f){return f.willValidate||f.validity&&f.validity.valid},":invalid":function(f){return!v[":valid"](f)},":in-range":function(f){return f.value>f.min&&f.value<=f.max},":out-of-range":function(f){return!v[":in-range"](f)},":required":function(f){return!!f.required},":optional":function(f){return!f.required},":read-only":function(f){if(f.readOnly)return!0;var i=f.getAttribute("contenteditable"),n=f.contentEditable,m=f.nodeName.toLowerCase();return m=m!=="input"&&m!=="textarea",(m||f.disabled)&&i==null&&n!=="true"},":read-write":function(f){return!v[":read-only"](f)},":hover":function(){throw new Error(":hover is not supported.")},":active":function(){throw new Error(":active is not supported.")},":link":function(){throw new Error(":link is not supported.")},":visited":function(){throw new Error(":visited is not supported.")},":column":function(){throw new Error(":column is not supported.")},":nth-column":function(){throw new Error(":nth-column is not supported.")},":nth-last-column":function(){throw new Error(":nth-last-column is not supported.")},":current":function(){throw new Error(":current is not supported.")},":past":function(){throw new Error(":past is not supported.")},":future":function(){throw new Error(":future is not supported.")},":contains":function(f){return function(i){var n=i.innerText||i.textContent||i.value||"";return n.indexOf(f)!==-1}},":has":function(f){return function(i){return W(f,i).length>0}}},p={"-":function(){return!0},"=":function(f,i){return f===i},"*=":function(f,i){return f.indexOf(i)!==-1},"~=":function(f,i){var n,m,L,E;for(m=0;;m=n+1){if(n=f.indexOf(i,m),n===-1)return!1;if(L=f[n-1],E=f[n+i.length],(!L||L===" ")&&(!E||E===" "))return!0}},"|=":function(f,i){var n=f.indexOf(i),m;if(n===0)return m=f[n+i.length],m==="-"||!m},"^=":function(f,i){return f.indexOf(i)===0},"$=":function(f,i){var n=f.lastIndexOf(i);return n!==-1&&n+i.length===f.length},"!=":function(f,i){return f!==i}},S={" ":function(f){return function(i){for(;i=i.parentNode;)if(f(i))return i}},">":function(f){return function(i){if(i=i.parentNode)return f(i)&&i}},"+":function(f){return function(i){if(i=o(i))return f(i)&&i}},"~":function(f){return function(i){for(;i=o(i);)if(f(i))return i}},noop:function(f){return function(i){return f(i)&&i}},ref:function(f,i){var n;function m(L){for(var E=L.ownerDocument,C=E.getElementsByTagName("*"),M=C.length;M--;)if(n=C[M],m.test(L))return n=null,!0;n=null}return m.combinator=function(L){if(!(!n||!n.getAttribute)){var E=n.getAttribute(i)||"";if(E[0]==="#"&&(E=E.substring(1)),E===L.id&&f(n))return n}},m}},_={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};_.cssid=F(_.cssid,"nonascii",_.nonascii),_.cssid=F(_.cssid,"escape",_.escape),_.qname=F(_.qname,"cssid",_.cssid),_.simple=F(_.simple,"cssid",_.cssid),_.ref=F(_.ref,"cssid",_.cssid),_.attr=F(_.attr,"cssid",_.cssid),_.pseudo=F(_.pseudo,"cssid",_.cssid),_.inside=F(_.inside,`[^"'>]*`,_.inside),_.attr=F(_.attr,"inside",O("\\[","\\]")),_.pseudo=F(_.pseudo,"inside",O("\\(","\\)")),_.simple=F(_.simple,"pseudo",_.pseudo),_.simple=F(_.simple,"attr",_.attr),_.ident=F(_.ident,"cssid",_.cssid),_.str_escape=F(_.str_escape,"escape",_.escape);var re=function(f){for(var i=f.replace(/^\s+|\s+$/g,""),n,m=[],L=[],E,C,M,z,T;i;){if(M=_.qname.exec(i))i=i.substring(M[0].length),C=d(M[1]),L.push(ae(C,!0));else if(M=_.simple.exec(i))i=i.substring(M[0].length),C="*",L.push(ae(C,!0)),L.push(ae(M));else throw new SyntaxError("Invalid selector.");for(;M=_.simple.exec(i);)i=i.substring(M[0].length),L.push(ae(M));if(i[0]==="!"&&(i=i.substring(1),E=D(),E.qname=C,L.push(E.simple)),M=_.ref.exec(i)){i=i.substring(M[0].length),T=S.ref($(L),d(M[1])),m.push(T.combinator),L=[];continue}if(M=_.combinator.exec(i)){if(i=i.substring(M[0].length),z=M[1]||M[2]||M[3],z===","){m.push(S.noop($(L)));break}}else z="noop";if(!S[z])throw new SyntaxError("Bad combinator.");m.push(S[z]($(L))),L=[]}return n=G(m),n.qname=C,n.sel=i,E&&(E.lname=n.qname,E.test=n,E.qname=E.qname,E.sel=n.sel,n=E),T&&(T.test=n,T.qname=n.qname,T.sel=n.sel,n=T),n},ae=function(f,i){if(i)return f==="*"?v["*"]:v.type(f);if(f[1])return f[1][0]==="."?v.attr("class","~=",d(f[1].substring(1)),!1):v.attr("id","=",d(f[1].substring(1)),!1);if(f[2])return f[3]?v[d(f[2])](g(f[3])):v[d(f[2])];if(f[4]){var n=f[6],m=/["'\s]\s*I$/i.test(n);return m&&(n=n.replace(/\s*I$/i,"")),v.attr(d(f[4]),f[5]||"-",g(n),m)}throw new SyntaxError("Unknown Selector.")},$=function(f){var i=f.length,n;return i<2?f[0]:function(m){if(m){for(n=0;n<i;n++)if(!f[n](m))return;return!0}}},G=function(f){return f.length<2?function(i){return!!f[0](i)}:function(i){for(var n=f.length;n--;)if(!(i=f[n](i)))return;return!0}},D=function(){var f;function i(n){for(var m=n.ownerDocument,L=m.getElementsByTagName(i.lname),E=L.length;E--;)if(i.test(L[E])&&f===n)return f=null,!0;f=null}return i.simple=function(n){return f=n,!0},i},B=function(f){for(var i=re(f),n=[i];i.sel;)i=re(i.sel),n.push(i);return n.length<2?i:function(m){for(var L=n.length,E=0;E<L;E++)if(n[E](m))return!0}},W=function(f,i){for(var n=[],m=re(f),L=i.getElementsByTagName(m.qname),E=0,C;C=L[E++];)m(C)&&n.push(C);if(m.sel){for(;m.sel;)for(m=re(m.sel),L=i.getElementsByTagName(m.qname),E=0;C=L[E++];)m(C)&&N.call(n,C)===-1&&n.push(C);n.sort(t)}return n};w.exports=b=function(f,i){var n,m;if(i.nodeType!==11&&f.indexOf(" ")===-1){if(f[0]==="#"&&i.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(f)&&i.doc._hasMultipleElementsWithId&&(n=f.substring(1),!i.doc._hasMultipleElementsWithId(n)))return m=i.doc.getElementById(n),m?[m]:[];if(f[0]==="."&&/^\.\w+$/.test(f))return i.getElementsByClassName(f.substring(1));if(/^\w+$/.test(f))return i.getElementsByTagName(f)}return W(f,i)},b.selectors=v,b.operators=p,b.combinators=S,b.matches=function(f,i){var n={sel:i};do if(n=re(n.sel),n(f))return!0;while(n.sel);return!1}}}),Ln=le({"external/npm/node_modules/domino/lib/ChildNode.js"(b,w){"use strict";var h=Ge(),c=ga(),t=function(o,l){for(var u=o.createDocumentFragment(),s=0;s<l.length;s++){var g=l[s],d=g instanceof h;u.appendChild(d?g:o.createTextNode(String(g)))}return u},a={after:{value:function(){var l=Array.prototype.slice.call(arguments),u=this.parentNode,s=this.nextSibling;if(u!==null){for(;s&&l.some(function(d){return d===s});)s=s.nextSibling;var g=t(this.doc,l);u.insertBefore(g,s)}}},before:{value:function(){var l=Array.prototype.slice.call(arguments),u=this.parentNode,s=this.previousSibling;if(u!==null){for(;s&&l.some(function(N){return N===s});)s=s.previousSibling;var g=t(this.doc,l),d=s?s.nextSibling:u.firstChild;u.insertBefore(g,d)}}},remove:{value:function(){this.parentNode!==null&&(this.doc&&(this.doc._preremoveNodeIterators(this),this.rooted&&this.doc.mutateRemove(this)),this._remove(),this.parentNode=null)}},_remove:{value:function(){var l=this.parentNode;l!==null&&(l._childNodes?l._childNodes.splice(this.index,1):l._firstChild===this&&(this._nextSibling===this?l._firstChild=null:l._firstChild=this._nextSibling),c.remove(this),l.modify())}},replaceWith:{value:function(){var l=Array.prototype.slice.call(arguments),u=this.parentNode,s=this.nextSibling;if(u!==null){for(;s&&l.some(function(d){return d===s});)s=s.nextSibling;var g=t(this.doc,l);this.parentNode===u?u.replaceChild(g,this):u.insertBefore(g,s)}}}};w.exports=a}}),va=le({"external/npm/node_modules/domino/lib/NonDocumentTypeChildNode.js"(b,w){"use strict";var h=Ge(),c={nextElementSibling:{get:function(){if(this.parentNode){for(var t=this.nextSibling;t!==null;t=t.nextSibling)if(t.nodeType===h.ELEMENT_NODE)return t}return null}},previousElementSibling:{get:function(){if(this.parentNode){for(var t=this.previousSibling;t!==null;t=t.previousSibling)if(t.nodeType===h.ELEMENT_NODE)return t}return null}}};w.exports=c}}),Ta=le({"external/npm/node_modules/domino/lib/NamedNodeMap.js"(b,w){"use strict";w.exports=c;var h=Be();function c(t){this.element=t}Object.defineProperties(c.prototype,{length:{get:h.shouldOverride},item:{value:h.shouldOverride},getNamedItem:{value:function(a){return this.element.getAttributeNode(a)}},getNamedItemNS:{value:function(a,o){return this.element.getAttributeNodeNS(a,o)}},setNamedItem:{value:h.nyi},setNamedItemNS:{value:h.nyi},removeNamedItem:{value:function(a){var o=this.element.getAttributeNode(a);if(o)return this.element.removeAttribute(a),o;h.NotFoundError()}},removeNamedItemNS:{value:function(a,o){var l=this.element.getAttributeNodeNS(a,o);if(l)return this.element.removeAttributeNS(a,o),l;h.NotFoundError()}}})}}),Er=le({"external/npm/node_modules/domino/lib/Element.js"(b,w){"use strict";w.exports=v;var h=Sn(),c=Be(),t=c.NAMESPACE,a=ba(),o=Ge(),l=ar(),u=_a(),s=di(),g=yn(),d=Ea(),N=kn(),O=wn(),F=Ln(),R=va(),ee=Ta(),P=Object.create(null);function v(i,n,m,L){O.call(this),this.nodeType=o.ELEMENT_NODE,this.ownerDocument=i,this.localName=n,this.namespaceURI=m,this.prefix=L,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function p(i,n){if(i.nodeType===o.TEXT_NODE)n.push(i._data);else for(var m=0,L=i.childNodes.length;m<L;m++)p(i.childNodes[m],n)}v.prototype=Object.create(O.prototype,{isHTML:{get:function(){return this.namespaceURI===t.HTML&&this.ownerDocument.isHTML}},tagName:{get:function(){if(this._tagName===void 0){var n;if(this.prefix===null?n=this.localName:n=this.prefix+":"+this.localName,this.isHTML){var m=P[n];m||(P[n]=m=c.toASCIIUpperCase(n)),n=m}this._tagName=n}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var i=[];return p(this,i),i.join("")},set:function(i){this.removeChildren(),i!=null&&i!==""&&this._appendChild(this.ownerDocument.createTextNode(i))}},innerText:{get:function(){var i=[];return p(this,i),i.join("").replace(/[ \t\n\f\r]+/g," ").trim()},set:function(i){this.removeChildren(),i!=null&&i!==""&&this._appendChild(this.ownerDocument.createTextNode(i))}},innerHTML:{get:function(){return this.serialize()},set:c.nyi},outerHTML:{get:function(){return u.serializeOne(this,{nodeType:0})},set:function(i){var n=this.ownerDocument,m=this.parentNode;if(m!==null){m.nodeType===o.DOCUMENT_NODE&&c.NoModificationAllowedError(),m.nodeType===o.DOCUMENT_FRAGMENT_NODE&&(m=m.ownerDocument.createElement("body"));var L=n.implementation.mozHTMLParser(n._address,m);L.parse(i===null?"":String(i),!0),this.replaceWith(L._asDocumentFragment())}}},_insertAdjacent:{value:function(n,m){var L=!1;switch(n){case"beforebegin":L=!0;case"afterend":var E=this.parentNode;return E===null?null:E.insertBefore(m,L?this:this.nextSibling);case"afterbegin":L=!0;case"beforeend":return this.insertBefore(m,L?this.firstChild:null);default:return c.SyntaxError()}}},insertAdjacentElement:{value:function(n,m){if(m.nodeType!==o.ELEMENT_NODE)throw new TypeError("not an element");return n=c.toASCIILowerCase(String(n)),this._insertAdjacent(n,m)}},insertAdjacentText:{value:function(n,m){var L=this.ownerDocument.createTextNode(m);n=c.toASCIILowerCase(String(n)),this._insertAdjacent(n,L)}},insertAdjacentHTML:{value:function(n,m){n=c.toASCIILowerCase(String(n)),m=String(m);var L;switch(n){case"beforebegin":case"afterend":L=this.parentNode,(L===null||L.nodeType===o.DOCUMENT_NODE)&&c.NoModificationAllowedError();break;case"afterbegin":case"beforeend":L=this;break;default:c.SyntaxError()}(!(L instanceof v)||L.ownerDocument.isHTML&&L.localName==="html"&&L.namespaceURI===t.HTML)&&(L=L.ownerDocument.createElementNS(t.HTML,"body"));var E=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,L);E.parse(m,!0),this._insertAdjacent(n,E._asDocumentFragment())}},children:{get:function(){return this._children||(this._children=new ae(this)),this._children}},attributes:{get:function(){return this._attributes||(this._attributes=new _(this)),this._attributes}},firstElementChild:{get:function(){for(var i=this.firstChild;i!==null;i=i.nextSibling)if(i.nodeType===o.ELEMENT_NODE)return i;return null}},lastElementChild:{get:function(){for(var i=this.lastChild;i!==null;i=i.previousSibling)if(i.nodeType===o.ELEMENT_NODE)return i;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(i){i||(i=this.ownerDocument.documentElement);var n=this.firstElementChild;if(!n){if(this===i)return null;n=this.nextElementSibling}if(n)return n;for(var m=this.parentElement;m&&m!==i;m=m.parentElement)if(n=m.nextElementSibling,n)return n;return null}},getElementsByTagName:{value:function(n){var m;return n?(n==="*"?m=function(){return!0}:this.isHTML?m=G(n):m=$(n),new s(this,m)):new l}},getElementsByTagNameNS:{value:function(n,m){var L;return n==="*"&&m==="*"?L=function(){return!0}:n==="*"?L=$(m):m==="*"?L=D(n):L=B(n,m),new s(this,L)}},getElementsByClassName:{value:function(n){if(n=String(n).trim(),n===""){var m=new l;return m}return n=n.split(/[ \t\r\n\f]+/),new s(this,W(n))}},getElementsByName:{value:function(n){return new s(this,f(String(n)))}},clone:{value:function(){var n;this.namespaceURI!==t.HTML||this.prefix||!this.ownerDocument.isHTML?n=this.ownerDocument.createElementNS(this.namespaceURI,this.prefix!==null?this.prefix+":"+this.localName:this.localName):n=this.ownerDocument.createElement(this.localName);for(var m=0,L=this._attrKeys.length;m<L;m++){var E=this._attrKeys[m],C=this._attrsByLName[E],M=C.cloneNode();M._setOwnerElement(n),n._attrsByLName[E]=M,n._addQName(M)}return n._attrKeys=this._attrKeys.concat(),n}},isEqual:{value:function(n){if(this.localName!==n.localName||this.namespaceURI!==n.namespaceURI||this.prefix!==n.prefix||this._numattrs!==n._numattrs)return!1;for(var m=0,L=this._numattrs;m<L;m++){var E=this._attr(m);if(!n.hasAttributeNS(E.namespaceURI,E.localName)||n.getAttributeNS(E.namespaceURI,E.localName)!==E.value)return!1}return!0}},_lookupNamespacePrefix:{value:function(n,m){if(this.namespaceURI&&this.namespaceURI===n&&this.prefix!==null&&m.lookupNamespaceURI(this.prefix)===n)return this.prefix;for(var L=0,E=this._numattrs;L<E;L++){var C=this._attr(L);if(C.prefix==="xmlns"&&C.value===n&&m.lookupNamespaceURI(C.localName)===n)return C.localName}var M=this.parentElement;return M?M._lookupNamespacePrefix(n,m):null}},lookupNamespaceURI:{value:function(n){if((n===""||n===void 0)&&(n=null),this.namespaceURI!==null&&this.prefix===n)return this.namespaceURI;for(var m=0,L=this._numattrs;m<L;m++){var E=this._attr(m);if(E.namespaceURI===t.XMLNS&&(E.prefix==="xmlns"&&E.localName===n||n===null&&E.prefix===null&&E.localName==="xmlns"))return E.value||null}var C=this.parentElement;return C?C.lookupNamespaceURI(n):null}},getAttribute:{value:function(n){var m=this.getAttributeNode(n);return m?m.value:null}},getAttributeNS:{value:function(n,m){var L=this.getAttributeNodeNS(n,m);return L?L.value:null}},getAttributeNode:{value:function(n){n=String(n),/[A-Z]/.test(n)&&this.isHTML&&(n=c.toASCIILowerCase(n));var m=this._attrsByQName[n];return m?(Array.isArray(m)&&(m=m[0]),m):null}},getAttributeNodeNS:{value:function(n,m){n=n==null?"":String(n),m=String(m);var L=this._attrsByLName[n+"|"+m];return L||null}},hasAttribute:{value:function(n){return n=String(n),/[A-Z]/.test(n)&&this.isHTML&&(n=c.toASCIILowerCase(n)),this._attrsByQName[n]!==void 0}},hasAttributeNS:{value:function(n,m){n=n==null?"":String(n),m=String(m);var L=n+"|"+m;return this._attrsByLName[L]!==void 0}},hasAttributes:{value:function(){return this._numattrs>0}},toggleAttribute:{value:function(n,m){n=String(n),h.isValidName(n)||c.InvalidCharacterError(),/[A-Z]/.test(n)&&this.isHTML&&(n=c.toASCIILowerCase(n));var L=this._attrsByQName[n];return L===void 0?m===void 0||m===!0?(this._setAttribute(n,""),!0):!1:m===void 0||m===!1?(this.removeAttribute(n),!1):!0}},_setAttribute:{value:function(n,m){var L=this._attrsByQName[n],E;L?Array.isArray(L)&&(L=L[0]):(L=this._newattr(n),E=!0),L.value=m,this._attributes&&(this._attributes[n]=L),E&&this._newattrhook&&this._newattrhook(n,m)}},setAttribute:{value:function(n,m){n=String(n),h.isValidName(n)||c.InvalidCharacterError(),/[A-Z]/.test(n)&&this.isHTML&&(n=c.toASCIILowerCase(n)),this._setAttribute(n,String(m))}},_setAttributeNS:{value:function(n,m,L){var E=m.indexOf(":"),C,M;E<0?(C=null,M=m):(C=m.substring(0,E),M=m.substring(E+1)),(n===""||n===void 0)&&(n=null);var z=(n===null?"":n)+"|"+M,T=this._attrsByLName[z],A;T||(T=new S(this,M,C,n),A=!0,this._attrsByLName[z]=T,this._attributes&&(this._attributes[this._attrKeys.length]=T),this._attrKeys.push(z),this._addQName(T)),T.value=L,A&&this._newattrhook&&this._newattrhook(m,L)}},setAttributeNS:{value:function(n,m,L){n=n==null||n===""?null:String(n),m=String(m),h.isValidQName(m)||c.InvalidCharacterError();var E=m.indexOf(":"),C=E<0?null:m.substring(0,E);(C!==null&&n===null||C==="xml"&&n!==t.XML||(m==="xmlns"||C==="xmlns")&&n!==t.XMLNS||n===t.XMLNS&&!(m==="xmlns"||C==="xmlns"))&&c.NamespaceError(),this._setAttributeNS(n,m,String(L))}},setAttributeNode:{value:function(n){if(n.ownerElement!==null&&n.ownerElement!==this)throw new g(g.INUSE_ATTRIBUTE_ERR);var m=null,L=this._attrsByQName[n.name];if(L){if(Array.isArray(L)||(L=[L]),L.some(function(E){return E===n}))return n;if(n.ownerElement!==null)throw new g(g.INUSE_ATTRIBUTE_ERR);L.forEach(function(E){this.removeAttributeNode(E)},this),m=L[0]}return this.setAttributeNodeNS(n),m}},setAttributeNodeNS:{value:function(n){if(n.ownerElement!==null)throw new g(g.INUSE_ATTRIBUTE_ERR);var m=n.namespaceURI,L=(m===null?"":m)+"|"+n.localName,E=this._attrsByLName[L];return E&&this.removeAttributeNode(E),n._setOwnerElement(this),this._attrsByLName[L]=n,this._attributes&&(this._attributes[this._attrKeys.length]=n),this._attrKeys.push(L),this._addQName(n),this._newattrhook&&this._newattrhook(n.name,n.value),E||null}},removeAttribute:{value:function(n){n=String(n),/[A-Z]/.test(n)&&this.isHTML&&(n=c.toASCIILowerCase(n));var m=this._attrsByQName[n];if(m){Array.isArray(m)?m.length>2?m=m.shift():(this._attrsByQName[n]=m[1],m=m[0]):this._attrsByQName[n]=void 0;var L=m.namespaceURI,E=(L===null?"":L)+"|"+m.localName;this._attrsByLName[E]=void 0;var C=this._attrKeys.indexOf(E);this._attributes&&(Array.prototype.splice.call(this._attributes,C,1),this._attributes[n]=void 0),this._attrKeys.splice(C,1);var M=m.onchange;m._setOwnerElement(null),M&&M.call(m,this,m.localName,m.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(m)}}},removeAttributeNS:{value:function(n,m){n=n==null?"":String(n),m=String(m);var L=n+"|"+m,E=this._attrsByLName[L];if(E){this._attrsByLName[L]=void 0;var C=this._attrKeys.indexOf(L);this._attributes&&Array.prototype.splice.call(this._attributes,C,1),this._attrKeys.splice(C,1),this._removeQName(E);var M=E.onchange;E._setOwnerElement(null),M&&M.call(E,this,E.localName,E.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(E)}}},removeAttributeNode:{value:function(n){var m=n.namespaceURI,L=(m===null?"":m)+"|"+n.localName;return this._attrsByLName[L]!==n&&c.NotFoundError(),this.removeAttributeNS(m,n.localName),n}},getAttributeNames:{value:function(){var n=this;return this._attrKeys.map(function(m){return n._attrsByLName[m].name})}},_getattr:{value:function(n){var m=this._attrsByQName[n];return m?m.value:null}},_setattr:{value:function(n,m){var L=this._attrsByQName[n],E;L||(L=this._newattr(n),E=!0),L.value=String(m),this._attributes&&(this._attributes[n]=L),E&&this._newattrhook&&this._newattrhook(n,m)}},_newattr:{value:function(n){var m=new S(this,n,null,null),L="|"+n;return this._attrsByQName[n]=m,this._attrsByLName[L]=m,this._attributes&&(this._attributes[this._attrKeys.length]=m),this._attrKeys.push(L),m}},_addQName:{value:function(i){var n=i.name,m=this._attrsByQName[n];m?Array.isArray(m)?m.push(i):this._attrsByQName[n]=[m,i]:this._attrsByQName[n]=i,this._attributes&&(this._attributes[n]=i)}},_removeQName:{value:function(i){var n=i.name,m=this._attrsByQName[n];if(Array.isArray(m)){var L=m.indexOf(i);c.assert(L!==-1),m.length===2?(this._attrsByQName[n]=m[1-L],this._attributes&&(this._attributes[n]=this._attrsByQName[n])):(m.splice(L,1),this._attributes&&this._attributes[n]===i&&(this._attributes[n]=m[0]))}else c.assert(m===i),this._attrsByQName[n]=void 0,this._attributes&&(this._attributes[n]=void 0)}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(i){return this._attrsByLName[this._attrKeys[i]]}},id:a.property({name:"id"}),className:a.property({name:"class"}),classList:{get:function(){var i=this;if(this._classList)return this._classList;var n=new d(function(){return i.className||""},function(m){i.className=m});return this._classList=n,n},set:function(i){this.className=i}},matches:{value:function(i){return N.matches(this,i)}},closest:{value:function(i){var n=this;do{if(n.matches&&n.matches(i))return n;n=n.parentElement||n.parentNode}while(n!==null&&n.nodeType===o.ELEMENT_NODE);return null}},querySelector:{value:function(i){return N(i,this)[0]}},querySelectorAll:{value:function(i){var n=N(i,this);return n.item?n:new l(n)}}}),Object.defineProperties(v.prototype,F),Object.defineProperties(v.prototype,R),a.registerChangeHandler(v,"id",function(i,n,m,L){i.rooted&&(m&&i.ownerDocument.delId(m,i),L&&i.ownerDocument.addId(L,i))}),a.registerChangeHandler(v,"class",function(i,n,m,L){i._classList&&i._classList._update()});function S(i,n,m,L,E){this.localName=n,this.prefix=m===null||m===""?null:""+m,this.namespaceURI=L===null||L===""?null:""+L,this.data=E,this._setOwnerElement(i)}S.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function(n){this._ownerElement=n,this.prefix===null&&this.namespaceURI===null&&n?this.onchange=n._attributeChangeHandlers[this.localName]:this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(i){var n=this.data;i=i===void 0?"":i+"",i!==n&&(this.data=i,this.ownerElement&&(this.onchange&&this.onchange(this.ownerElement,this.localName,n,i),this.ownerElement.rooted&&this.ownerElement.ownerDocument.mutateAttr(this,n)))}},cloneNode:{value:function(n){return new S(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return o.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(i){this.value=i}},textContent:{get:function(){return this.value},set:function(i){i==null&&(i=""),this.value=i}},innerText:{get:function(){return this.value},set:function(i){i==null&&(i=""),this.value=i}}}),v._Attr=S;function _(i){ee.call(this,i);for(var n in i._attrsByQName)this[n]=i._attrsByQName[n];for(var m=0;m<i._attrKeys.length;m++)this[m]=i._attrsByLName[i._attrKeys[m]]}_.prototype=Object.create(ee.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(i){return i=i>>>0,i>=this.length?null:this.element._attrsByLName[this.element._attrKeys[i]]}}});var re;(re=globalThis.Symbol)!=null&&re.iterator&&(_.prototype[globalThis.Symbol.iterator]=function(){var i=0,n=this.length,m=this;return{next:function(){return i<n?{value:m.item(i++)}:{done:!0}}}});function ae(i){this.element=i,this.updateCache()}ae.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function(n){return this.updateCache(),this.childrenByNumber[n]||null}},namedItem:{value:function(n){return this.updateCache(),this.childrenByName[n]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function(){var n=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;for(var m=this.childrenByNumber&&this.childrenByNumber.length||0,L=0;L<m;L++)this[L]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var E=this.element.firstChild;E!==null;E=E.nextSibling)if(E.nodeType===o.ELEMENT_NODE){this[this.childrenByNumber.length]=E,this.childrenByNumber.push(E);var C=E.getAttribute("id");C&&!this.childrenByName[C]&&(this.childrenByName[C]=E);var M=E.getAttribute("name");M&&this.element.namespaceURI===t.HTML&&n.test(this.element.localName)&&!this.childrenByName[M]&&(this.childrenByName[C]=E)}}}}});function $(i){return function(n){return n.localName===i}}function G(i){var n=c.toASCIILowerCase(i);return n===i?$(i):function(m){return m.isHTML?m.localName===n:m.localName===i}}function D(i){return function(n){return n.namespaceURI===i}}function B(i,n){return function(m){return m.namespaceURI===i&&m.localName===n}}function W(i){return function(n){return i.every(function(m){return n.classList.contains(m)})}}function f(i){return function(n){return n.namespaceURI!==t.HTML?!1:n.getAttribute("name")===i}}}}),ya=le({"external/npm/node_modules/domino/lib/Leaf.js"(b,w){"use strict";w.exports=l;var h=Ge(),c=ar(),t=Be(),a=t.HierarchyRequestError,o=t.NotFoundError;function l(){h.call(this)}l.prototype=Object.create(h.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(u,s){if(!u.nodeType)throw new TypeError("not a node");a()}},replaceChild:{value:function(u,s){if(!u.nodeType)throw new TypeError("not a node");a()}},removeChild:{value:function(u){if(!u.nodeType)throw new TypeError("not a node");o()}},removeChildren:{value:function(){}},childNodes:{get:function(){return this._childNodes||(this._childNodes=new c),this._childNodes}}})}}),$r=le({"external/npm/node_modules/domino/lib/CharacterData.js"(b,w){"use strict";w.exports=o;var h=ya(),c=Be(),t=Ln(),a=va();function o(){h.call(this)}o.prototype=Object.create(h.prototype,{substringData:{value:function(u,s){if(arguments.length<2)throw new TypeError("Not enough arguments");return u=u>>>0,s=s>>>0,(u>this.data.length||u<0||s<0)&&c.IndexSizeError(),this.data.substring(u,u+s)}},appendData:{value:function(u){if(arguments.length<1)throw new TypeError("Not enough arguments");this.data+=String(u)}},insertData:{value:function(u,s){return this.replaceData(u,0,s)}},deleteData:{value:function(u,s){return this.replaceData(u,s,"")}},replaceData:{value:function(u,s,g){var d=this.data,N=d.length;u=u>>>0,s=s>>>0,g=String(g),(u>N||u<0)&&c.IndexSizeError(),u+s>N&&(s=N-u);var O=d.substring(0,u),F=d.substring(u+s);this.data=O+g+F}},isEqual:{value:function(u){return this._data===u._data}},length:{get:function(){return this.data.length}}}),Object.defineProperties(o.prototype,t),Object.defineProperties(o.prototype,a)}}),Na=le({"external/npm/node_modules/domino/lib/Text.js"(b,w){"use strict";w.exports=a;var h=Be(),c=Ge(),t=$r();function a(l,u){t.call(this),this.nodeType=c.TEXT_NODE,this.ownerDocument=l,this._data=u,this._index=void 0}var o={get:function(){return this._data},set:function(l){l==null?l="":l=String(l),l!==this._data&&(this._data=l,this.rooted&&this.ownerDocument.mutateValue(this),this.parentNode&&this.parentNode._textchangehook&&this.parentNode._textchangehook(this))}};a.prototype=Object.create(t.prototype,{nodeName:{value:"#text"},nodeValue:o,textContent:o,innerText:o,data:{get:o.get,set:function(l){o.set.call(this,l===null?"":String(l))}},splitText:{value:function(u){(u>this._data.length||u<0)&&h.IndexSizeError();var s=this._data.substring(u),g=this.ownerDocument.createTextNode(s);this.data=this.data.substring(0,u);var d=this.parentNode;return d!==null&&d.insertBefore(g,this.nextSibling),g}},wholeText:{get:function(){for(var u=this.textContent,s=this.nextSibling;s&&s.nodeType===c.TEXT_NODE;s=s.nextSibling)u+=s.textContent;return u}},replaceWholeText:{value:h.nyi},clone:{value:function(){return new a(this.ownerDocument,this._data)}}})}}),wa=le({"external/npm/node_modules/domino/lib/Comment.js"(b,w){"use strict";w.exports=t;var h=Ge(),c=$r();function t(o,l){c.call(this),this.nodeType=h.COMMENT_NODE,this.ownerDocument=o,this._data=l}var a={get:function(){return this._data},set:function(o){o==null?o="":o=String(o),this._data=o,this.rooted&&this.ownerDocument.mutateValue(this)}};t.prototype=Object.create(c.prototype,{nodeName:{value:"#comment"},nodeValue:a,textContent:a,innerText:a,data:{get:a.get,set:function(o){a.set.call(this,o===null?"":String(o))}},clone:{value:function(){return new t(this.ownerDocument,this._data)}}})}}),Sa=le({"external/npm/node_modules/domino/lib/DocumentFragment.js"(b,w){"use strict";w.exports=u;var h=Ge(),c=ar(),t=wn(),a=Er(),o=kn(),l=Be();function u(s){t.call(this),this.nodeType=h.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=s}u.prototype=Object.create(t.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(a.prototype,"textContent"),innerText:Object.getOwnPropertyDescriptor(a.prototype,"innerText"),querySelector:{value:function(s){var g=this.querySelectorAll(s);return g.length?g[0]:null}},querySelectorAll:{value:function(s){var g=Object.create(this);g.isHTML=!0,g.getElementsByTagName=a.prototype.getElementsByTagName,g.nextElement=Object.getOwnPropertyDescriptor(a.prototype,"firstElementChild").get;var d=o(s,g);return d.item?d:new c(d)}},clone:{value:function(){return new u(this.ownerDocument)}},isEqual:{value:function(g){return!0}},innerHTML:{get:function(){return this.serialize()},set:l.nyi},outerHTML:{get:function(){return this.serialize()},set:l.nyi}})}}),ka=le({"external/npm/node_modules/domino/lib/ProcessingInstruction.js"(b,w){"use strict";w.exports=t;var h=Ge(),c=$r();function t(o,l,u){c.call(this),this.nodeType=h.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=o,this.target=l,this._data=u}var a={get:function(){return this._data},set:function(o){o==null?o="":o=String(o),this._data=o,this.rooted&&this.ownerDocument.mutateValue(this)}};t.prototype=Object.create(c.prototype,{nodeName:{get:function(){return this.target}},nodeValue:a,textContent:a,innerText:a,data:{get:a.get,set:function(o){a.set.call(this,o===null?"":String(o))}},clone:{value:function(){return new t(this.ownerDocument,this.target,this._data)}},isEqual:{value:function(l){return this.target===l.target&&this._data===l._data}}})}}),Jr=le({"external/npm/node_modules/domino/lib/NodeFilter.js"(b,w){"use strict";var h={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:4294967295,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};w.exports=h.constructor=h.prototype=h}}),La=le({"external/npm/node_modules/domino/lib/NodeTraversal.js"(b,w){"use strict";var h=w.exports={nextSkippingChildren:c,nextAncestorSibling:t,next:a,previous:l,deepLastChild:o};function c(u,s){return u===s?null:u.nextSibling!==null?u.nextSibling:t(u,s)}function t(u,s){for(u=u.parentNode;u!==null;u=u.parentNode){if(u===s)return null;if(u.nextSibling!==null)return u.nextSibling}return null}function a(u,s){var g;return g=u.firstChild,g!==null?g:u===s?null:(g=u.nextSibling,g!==null?g:t(u,s))}function o(u){for(;u.lastChild;)u=u.lastChild;return u}function l(u,s){var g;return g=u.previousSibling,g!==null?o(g):(g=u.parentNode,g===s?null:g)}}}),pi=le({"external/npm/node_modules/domino/lib/TreeWalker.js"(b,w){"use strict";w.exports=g;var h=Ge(),c=Jr(),t=La(),a=Be(),o={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},l={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function u(d,N){var O,F,R,ee,P;for(F=d._currentNode[o[N]];F!==null;){if(ee=d._internalFilter(F),ee===c.FILTER_ACCEPT)return d._currentNode=F,F;if(ee===c.FILTER_SKIP&&(O=F[o[N]],O!==null)){F=O;continue}for(;F!==null;){if(P=F[l[N]],P!==null){F=P;break}if(R=F.parentNode,R===null||R===d.root||R===d._currentNode)return null;F=R}}return null}function s(d,N){var O,F,R;if(O=d._currentNode,O===d.root)return null;for(;;){for(R=O[l[N]];R!==null;){if(O=R,F=d._internalFilter(O),F===c.FILTER_ACCEPT)return d._currentNode=O,O;R=O[o[N]],(F===c.FILTER_REJECT||R===null)&&(R=O[l[N]])}if(O=O.parentNode,O===null||O===d.root||d._internalFilter(O)===c.FILTER_ACCEPT)return null}}function g(d,N,O){(!d||!d.nodeType)&&a.NotSupportedError(),this._root=d,this._whatToShow=Number(N)||0,this._filter=O||null,this._active=!1,this._currentNode=d}Object.defineProperties(g.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function(){return this._currentNode},set:function(N){if(!(N instanceof h))throw new TypeError("Not a Node");this._currentNode=N}},_internalFilter:{value:function(N){var O,F;if(this._active&&a.InvalidStateError(),!(1<<N.nodeType-1&this._whatToShow))return c.FILTER_SKIP;if(F=this._filter,F===null)O=c.FILTER_ACCEPT;else{this._active=!0;try{typeof F=="function"?O=F(N):O=F.acceptNode(N)}finally{this._active=!1}}return+O}},parentNode:{value:function(){for(var N=this._currentNode;N!==this.root;){if(N=N.parentNode,N===null)return null;if(this._internalFilter(N)===c.FILTER_ACCEPT)return this._currentNode=N,N}return null}},firstChild:{value:function(){return u(this,"first")}},lastChild:{value:function(){return u(this,"last")}},previousSibling:{value:function(){return s(this,"previous")}},nextSibling:{value:function(){return s(this,"next")}},previousNode:{value:function(){var N,O,F,R;for(N=this._currentNode;N!==this._root;){for(F=N.previousSibling;F;F=N.previousSibling)if(N=F,O=this._internalFilter(N),O!==c.FILTER_REJECT){for(R=N.lastChild;R&&(N=R,O=this._internalFilter(N),O!==c.FILTER_REJECT);R=N.lastChild);if(O===c.FILTER_ACCEPT)return this._currentNode=N,N}if(N===this.root||N.parentNode===null)return null;if(N=N.parentNode,this._internalFilter(N)===c.FILTER_ACCEPT)return this._currentNode=N,N}return null}},nextNode:{value:function(){var N,O,F,R;N=this._currentNode,O=c.FILTER_ACCEPT;e:for(;;){for(F=N.firstChild;F;F=N.firstChild){if(N=F,O=this._internalFilter(N),O===c.FILTER_ACCEPT)return this._currentNode=N,N;if(O===c.FILTER_REJECT)break}for(R=t.nextSkippingChildren(N,this.root);R;R=t.nextSkippingChildren(N,this.root)){if(N=R,O=this._internalFilter(N),O===c.FILTER_ACCEPT)return this._currentNode=N,N;if(O===c.FILTER_SKIP)continue e}return null}}},toString:{value:function(){return"[object TreeWalker]"}}})}}),mi=le({"external/npm/node_modules/domino/lib/NodeIterator.js"(b,w){"use strict";w.exports=u;var h=Jr(),c=La(),t=Be();function a(s,g,d){return d?c.next(s,g):s===g?null:c.previous(s,null)}function o(s,g){for(;g;g=g.parentNode)if(s===g)return!0;return!1}function l(s,g){var d,N;for(d=s._referenceNode,N=s._pointerBeforeReferenceNode;;){if(N===g)N=!N;else if(d=a(d,s._root,g),d===null)return null;var O=s._internalFilter(d);if(O===h.FILTER_ACCEPT)break}return s._referenceNode=d,s._pointerBeforeReferenceNode=N,d}function u(s,g,d){(!s||!s.nodeType)&&t.NotSupportedError(),this._root=s,this._referenceNode=s,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(g)||0,this._filter=d||null,this._active=!1,s.doc._attachNodeIterator(this)}Object.defineProperties(u.prototype,{root:{get:function(){return this._root}},referenceNode:{get:function(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},_internalFilter:{value:function(g){var d,N;if(this._active&&t.InvalidStateError(),!(1<<g.nodeType-1&this._whatToShow))return h.FILTER_SKIP;if(N=this._filter,N===null)d=h.FILTER_ACCEPT;else{this._active=!0;try{typeof N=="function"?d=N(g):d=N.acceptNode(g)}finally{this._active=!1}}return+d}},_preremove:{value:function(g){if(!o(g,this._root)&&o(g,this._referenceNode)){if(this._pointerBeforeReferenceNode){for(var d=g;d.lastChild;)d=d.lastChild;if(d=c.next(d,this.root),d){this._referenceNode=d;return}this._pointerBeforeReferenceNode=!1}if(g.previousSibling===null)this._referenceNode=g.parentNode;else{this._referenceNode=g.previousSibling;var N;for(N=this._referenceNode.lastChild;N;N=this._referenceNode.lastChild)this._referenceNode=N}}}},nextNode:{value:function(){return l(this,!0)}},previousNode:{value:function(){return l(this,!1)}},detach:{value:function(){}},toString:{value:function(){return"[object NodeIterator]"}}})}}),Cn=le({"external/npm/node_modules/domino/lib/URL.js"(b,w){"use strict";w.exports=h;function h(c){if(!c)return Object.create(h.prototype);this.url=c.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var t=h.pattern.exec(this.url);if(t){if(t[2]&&(this.scheme=t[2]),t[4]){var a=t[4].match(h.userinfoPattern);if(a&&(this.username=a[1],this.password=a[3],t[4]=t[4].substring(a[0].length)),t[4].match(h.portPattern)){var o=t[4].lastIndexOf(":");this.host=t[4].substring(0,o),this.port=t[4].substring(o+1)}else this.host=t[4]}t[5]&&(this.path=t[5]),t[6]&&(this.query=t[7]),t[8]&&(this.fragment=t[9])}}h.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/,h.userinfoPattern=/^([^@:]*)(:([^@]*))?@/,h.portPattern=/:\d+$/,h.authorityPattern=/^[^:\/?#]+:\/\//,h.hierarchyPattern=/^[^:\/?#]+:\//,h.percentEncode=function(t){var a=t.charCodeAt(0);if(a<256)return"%"+a.toString(16);throw Error("can't percent-encode codepoints > 255 yet")},h.prototype={constructor:h,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return h.authorityPattern.test(this.url)},isHierarchical:function(){return h.hierarchyPattern.test(this.url)},toString:function(){var c="";return this.scheme!==void 0&&(c+=this.scheme+":"),this.isAbsolute()&&(c+="//",(this.username||this.password)&&(c+=this.username||"",this.password&&(c+=":"+this.password),c+="@"),this.host&&(c+=this.host)),this.port!==void 0&&(c+=":"+this.port),this.path!==void 0&&(c+=this.path),this.query!==void 0&&(c+="?"+this.query),this.fragment!==void 0&&(c+="#"+this.fragment),c},resolve:function(c){var t=this,a=new h(c),o=new h;return a.scheme!==void 0?(o.scheme=a.scheme,o.username=a.username,o.password=a.password,o.host=a.host,o.port=a.port,o.path=u(a.path),o.query=a.query):(o.scheme=t.scheme,a.host!==void 0?(o.username=a.username,o.password=a.password,o.host=a.host,o.port=a.port,o.path=u(a.path),o.query=a.query):(o.username=t.username,o.password=t.password,o.host=t.host,o.port=t.port,a.path?(a.path.charAt(0)==="/"?o.path=u(a.path):(o.path=l(t.path,a.path),o.path=u(o.path)),o.query=a.query):(o.path=t.path,a.query!==void 0?o.query=a.query:o.query=t.query))),o.fragment=a.fragment,o.toString();function l(s,g){if(t.host!==void 0&&!t.path)return"/"+g;var d=s.lastIndexOf("/");return d===-1?g:s.substring(0,d+1)+g}function u(s){if(!s)return s;for(var g="";s.length>0;){if(s==="."||s===".."){s="";break}var d=s.substring(0,2),N=s.substring(0,3),O=s.substring(0,4);if(N==="../")s=s.substring(3);else if(d==="./")s=s.substring(2);else if(N==="/./")s="/"+s.substring(3);else if(d==="/."&&s.length===2)s="/";else if(O==="/../"||N==="/.."&&s.length===3)s="/"+s.substring(4),g=g.replace(/\/?[^\/]*$/,"");else{var F=s.match(/(\/?([^\/]*))/)[0];g+=F,s=s.substring(F.length)}}return g}}}}}),gi=le({"external/npm/node_modules/domino/lib/CustomEvent.js"(b,w){"use strict";w.exports=c;var h=br();function c(t,a){h.call(this,t,a)}c.prototype=Object.create(h.prototype,{constructor:{value:c}})}}),Ca=le({"external/npm/node_modules/domino/lib/events.js"(b,w){"use strict";w.exports={Event:br(),UIEvent:da(),MouseEvent:pa(),CustomEvent:gi()}}}),_i=le({"external/npm/node_modules/domino/lib/style_parser.js"(b){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.hyphenate=b.parse=void 0;function w(c){let t=[],a=0,o=0,l=0,u=0,s=0,g=null;for(;a<c.length;)switch(c.charCodeAt(a++)){case 40:o++;break;case 41:o--;break;case 39:l===0?l=39:l===39&&c.charCodeAt(a-1)!==92&&(l=0);break;case 34:l===0?l=34:l===34&&c.charCodeAt(a-1)!==92&&(l=0);break;case 58:!g&&o===0&&l===0&&(g=h(c.substring(s,a-1).trim()),u=a);break;case 59:if(g&&u>0&&o===0&&l===0){let N=c.substring(u,a-1).trim();t.push(g,N),s=a,u=0,g=null}break}if(g&&u){let d=c.slice(u).trim();t.push(g,d)}return t}b.parse=w;function h(c){return c.replace(/[a-z][A-Z]/g,t=>t.charAt(0)+"-"+t.charAt(1)).toLowerCase()}b.hyphenate=h}}),Dn=le({"external/npm/node_modules/domino/lib/CSSStyleDeclaration.js"(b,w){"use strict";var{parse:h}=_i();w.exports=function(u){let s=new t(u),g={get:function(d,N){return N in d?d[N]:d.getPropertyValue(c(N))},has:function(d,N){return!0},set:function(d,N,O){return N in d?d[N]=O:d.setProperty(c(N),O!=null?O:void 0),!0}};return new Proxy(s,g)};function c(u){return u.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function t(u){this._element=u}var a="!important";function o(u){let s={property:{},priority:{}};if(!u)return s;let g=h(u);if(g.length<2)return s;for(let d=0;d<g.length;d+=2){let N=g[d],O=g[d+1];O.endsWith(a)&&(s.priority[N]="important",O=O.slice(0,-a.length).trim()),s.property[N]=O}return s}var l={};t.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var u=this.cssText;this._parsedStyles=o(u),this._lastParsedText=u,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var u=this._parsed,s="";for(var g in u.property)s&&(s+=" "),s+=g+": "+u.property[g],u.priority[g]&&(s+=" !"+u.priority[g]),s+=";";this.cssText=s,this._lastParsedText=s,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(u){this._element.setAttribute("style",u)}},length:{get:function(){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names.length}},item:{value:function(u){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names[u]}},getPropertyValue:{value:function(u){return u=u.toLowerCase(),this._parsed.property[u]||""}},getPropertyPriority:{value:function(u){return u=u.toLowerCase(),this._parsed.priority[u]||""}},setProperty:{value:function(u,s,g){if(u=u.toLowerCase(),s==null&&(s=""),g==null&&(g=""),s!==l&&(s=""+s),s=s.trim(),s===""){this.removeProperty(u);return}if(!(g!==""&&g!==l&&!/^important$/i.test(g))){var d=this._parsed;if(s===l){if(!d.property[u])return;g!==""?d.priority[u]="important":delete d.priority[u]}else{if(s.includes(";")&&!s.includes("data:"))return;var N=o(u+":"+s);if(Object.getOwnPropertyNames(N.property).length===0||Object.getOwnPropertyNames(N.priority).length!==0)return;for(var O in N.property)d.property[O]=N.property[O],g!==l&&(g!==""?d.priority[O]="important":d.priority[O]&&delete d.priority[O])}this._serialize()}}},setPropertyValue:{value:function(u,s){return this.setProperty(u,s,l)}},setPropertyPriority:{value:function(u,s){return this.setProperty(u,l,s)}},removeProperty:{value:function(u){u=u.toLowerCase();var s=this._parsed;u in s.property&&(delete s.property[u],delete s.priority[u],this._serialize())}}})}}),Da=le({"external/npm/node_modules/domino/lib/URLUtils.js"(b,w){"use strict";var h=Cn();w.exports=c;function c(){}c.prototype=Object.create(Object.prototype,{_url:{get:function(){return new h(this.href)}},protocol:{get:function(){var t=this._url;return t&&t.scheme?t.scheme+":":":"},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&(t=t.replace(/:+$/,""),t=t.replace(/[^-+\.a-zA-Z0-9]/g,h.percentEncode),t.length>0&&(o.scheme=t,a=o.toString())),this.href=a}},host:{get:function(){var t=this._url;return t.isAbsolute()&&t.isAuthorityBased()?t.host+(t.port?":"+t.port:""):""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&o.isAuthorityBased()&&(t=t.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,h.percentEncode),t.length>0&&(o.host=t,delete o.port,a=o.toString())),this.href=a}},hostname:{get:function(){var t=this._url;return t.isAbsolute()&&t.isAuthorityBased()?t.host:""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&o.isAuthorityBased()&&(t=t.replace(/^\/+/,""),t=t.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,h.percentEncode),t.length>0&&(o.host=t,a=o.toString())),this.href=a}},port:{get:function(){var t=this._url;return t.isAbsolute()&&t.isAuthorityBased()&&t.port!==void 0?t.port:""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&o.isAuthorityBased()&&(t=""+t,t=t.replace(/[^0-9].*$/,""),t=t.replace(/^0+/,""),t.length===0&&(t="0"),parseInt(t,10)<=65535&&(o.port=t,a=o.toString())),this.href=a}},pathname:{get:function(){var t=this._url;return t.isAbsolute()&&t.isHierarchical()?t.path:""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&o.isHierarchical()&&(t.charAt(0)!=="/"&&(t="/"+t),t=t.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,h.percentEncode),o.path=t,a=o.toString()),this.href=a}},search:{get:function(){var t=this._url;return t.isAbsolute()&&t.isHierarchical()&&t.query!==void 0?"?"+t.query:""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&o.isHierarchical()&&(t.charAt(0)==="?"&&(t=t.substring(1)),t=t.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,h.percentEncode),o.query=t,a=o.toString()),this.href=a}},hash:{get:function(){var t=this._url;return t==null||t.fragment==null||t.fragment===""?"":"#"+t.fragment},set:function(t){var a=this.href,o=new h(a);t.charAt(0)==="#"&&(t=t.substring(1)),t=t.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,h.percentEncode),o.fragment=t,a=o.toString(),this.href=a}},username:{get:function(){var t=this._url;return t.username||""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&(t=t.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,h.percentEncode),o.username=t,a=o.toString()),this.href=a}},password:{get:function(){var t=this._url;return t.password||""},set:function(t){var a=this.href,o=new h(a);o.isAbsolute()&&(t===""?o.password=null:(t=t.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,h.percentEncode),o.password=t),a=o.toString()),this.href=a}},origin:{get:function(){var t=this._url;if(t==null)return"";var a=function(o){var l=[t.scheme,t.host,+t.port||o];return l[0]+"://"+l[1]+(l[2]===o?"":":"+l[2])};switch(t.scheme){case"ftp":return a(21);case"gopher":return a(70);case"http":case"ws":return a(80);case"https":case"wss":return a(443);default:return t.scheme+"://"}}}}),c._inherit=function(t){Object.getOwnPropertyNames(c.prototype).forEach(function(a){if(!(a==="constructor"||a==="href")){var o=Object.getOwnPropertyDescriptor(c.prototype,a);Object.defineProperty(t,a,o)}})}}}),Aa=le({"external/npm/node_modules/domino/lib/defineElement.js"(b,w){"use strict";var h=ba(),c=Nn().isApiWritable;w.exports=function(l,u,s,g){var d=l.ctor;if(d){var N=l.props||{};if(l.attributes)for(var O in l.attributes){var F=l.attributes[O];(typeof F!="object"||Array.isArray(F))&&(F={type:F}),F.name||(F.name=O.toLowerCase()),N[O]=h.property(F)}N.constructor={value:d,writable:c},d.prototype=Object.create((l.superclass||u).prototype,N),l.events&&o(d,l.events),s[l.name]=d}else d=u;return(l.tags||l.tag&&[l.tag]||[]).forEach(function(R){g[R]=d}),d};function t(l,u,s,g){this.body=l,this.document=u,this.form=s,this.element=g}t.prototype.build=function(){return()=>{}};function a(l,u,s,g){var d=l.ownerDocument||Object.create(null),N=l.form||Object.create(null);l[u]=new t(g,d,N,l).build()}function o(l,u){var s=l.prototype;u.forEach(function(g){Object.defineProperty(s,"on"+g,{get:function(){return this._getEventHandler(g)},set:function(d){this._setEventHandler(g,d)}}),h.registerChangeHandler(l,"on"+g,a)})}}}),An=le({"external/npm/node_modules/domino/lib/htmlelts.js"(b){"use strict";var w=Ge(),h=Er(),c=Dn(),t=Be(),a=Da(),o=Aa(),l=b.elements={},u=Object.create(null);b.createElement=function(v,p,S){var _=u[p]||ee;return new _(v,p,S)};function s(v){return o(v,R,l,u)}function g(v){return{get:function(){var p=this._getattr(v);if(p===null)return"";var S=this.doc._resolve(p);return S===null?p:S},set:function(p){this._setattr(v,p)}}}function d(v){return{get:function(){var p=this._getattr(v);return p===null?null:p.toLowerCase()==="use-credentials"?"use-credentials":"anonymous"},set:function(p){p==null?this.removeAttribute(v):this._setattr(v,p)}}}var N={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""},O={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},F=function(v,p,S){R.call(this,v,p,S),this._form=null},R=b.HTMLElement=s({superclass:h,name:"HTMLElement",ctor:function(p,S,_){h.call(this,p,S,t.NAMESPACE.HTML,_)},props:{dangerouslySetInnerHTML:{set:function(v){this._innerHTML=v}},innerHTML:{get:function(){return this.serialize()},set:function(v){var p=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);p.parse(v===null?"":String(v),!0);for(var S=this instanceof u.template?this.content:this;S.hasChildNodes();)S.removeChild(S.firstChild);S.appendChild(p._asDocumentFragment())}},style:{get:function(){return this._style||(this._style=new c(this)),this._style},set:function(v){v==null&&(v=""),this._setattr("style",String(v))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(!this._click_in_progress){this._click_in_progress=!0;try{this._pre_click_activation_steps&&this._pre_click_activation_steps();var v=this.ownerDocument.createEvent("MouseEvent");v.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null);var p=this.dispatchEvent(v);p?this._post_click_activation_steps&&this._post_click_activation_steps(v):this._cancelled_activation_steps&&this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}}},submit:{value:t.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},draggable:{type:["true","false"],treatNullAsEmptyString:!0},spellcheck:{type:["true","false"],missing:""},enterKeyHint:{type:["enter","done","go","next","previous","search","send"],missing:""},autoCapitalize:{type:["off","on","none","sentences","words","characters"],missing:""},autoFocus:Boolean,accessKey:String,nonce:String,hidden:Boolean,translate:{type:["no","yes"],missing:""},tabIndex:{type:"long",default:function(){return this.tagName in O||this.contentEditable?0:-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),ee=s({name:"HTMLUnknownElement",ctor:function(p,S,_){R.call(this,p,S,_)}}),P={form:{get:function(){return this._form}}};s({tag:"a",name:"HTMLAnchorElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{_post_click_activation_steps:{value:function(v){this.href&&(this.ownerDocument.defaultView.location=this.href)}}},attributes:{href:g,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:N,coords:String,charset:String,name:String,rev:String,shape:String}}),a._inherit(u.a.prototype),s({tag:"area",name:"HTMLAreaElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:g,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:N,noHref:Boolean}}),a._inherit(u.area.prototype),s({tag:"br",name:"HTMLBRElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{clear:String}}),s({tag:"base",name:"HTMLBaseElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{target:String}}),s({tag:"body",name:"HTMLBodyElement",ctor:function(p,S,_){R.call(this,p,S,_)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}}),s({tag:"button",name:"HTMLButtonElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formAction:g,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}}),s({tag:"dl",name:"HTMLDListElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{compact:Boolean}}),s({tag:"data",name:"HTMLDataElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{value:String}}),s({tag:"datalist",name:"HTMLDataListElement",ctor:function(p,S,_){R.call(this,p,S,_)}}),s({tag:"details",name:"HTMLDetailsElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{open:Boolean}}),s({tag:"div",name:"HTMLDivElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{align:String}}),s({tag:"embed",name:"HTMLEmbedElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{src:g,type:String,width:String,height:String,align:String,name:String}}),s({tag:"fieldset",name:"HTMLFieldSetElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{disabled:Boolean,name:String}}),s({tag:"form",name:"HTMLFormElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}}),s({tag:"hr",name:"HTMLHRElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}}),s({tag:"head",name:"HTMLHeadElement",ctor:function(p,S,_){R.call(this,p,S,_)}}),s({tags:["h1","h2","h3","h4","h5","h6"],name:"HTMLHeadingElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{align:String}}),s({tag:"html",name:"HTMLHtmlElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{xmlns:g,version:String}}),s({tag:"iframe",name:"HTMLIFrameElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{src:g,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allow:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:N,loading:{type:["eager","lazy"],treatNullAsEmptyString:!0},align:String,scrolling:String,frameBorder:String,longDesc:g,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"img",name:"HTMLImageElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{alt:String,src:g,srcset:String,crossOrigin:d,useMap:String,isMap:Boolean,sizes:String,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:N,loading:{type:["eager","lazy"],missing:""},name:String,lowsrc:g,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:g,border:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"input",name:"HTMLInputElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:{form:P.form,_post_click_activation_steps:{value:function(v){if(this.type==="checkbox")this.checked=!this.checked;else if(this.type==="radio")for(var p=this.form.getElementsByName(this.name),S=p.length-1;S>=0;S--){var _=p[S];_.checked=_===this}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:g,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}}),s({tag:"keygen",name:"HTMLKeygenElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}}),s({tag:"li",name:"HTMLLIElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{value:{type:"long",default:0},type:String}}),s({tag:"label",name:"HTMLLabelElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{htmlFor:{name:"for",type:String}}}),s({tag:"legend",name:"HTMLLegendElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{align:String}}),s({tag:"link",name:"HTMLLinkElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{href:g,rel:String,media:String,hreflang:String,type:String,crossOrigin:d,nonce:String,integrity:String,referrerPolicy:N,imageSizes:String,imageSrcset:String,charset:String,rev:String,target:String}}),s({tag:"map",name:"HTMLMapElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{name:String}}),s({tag:"menu",name:"HTMLMenuElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}}),s({tag:"meta",name:"HTMLMetaElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}}),s({tag:"meter",name:"HTMLMeterElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P}),s({tags:["ins","del"],name:"HTMLModElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{cite:g,dateTime:String}}),s({tag:"ol",name:"HTMLOListElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{_numitems:{get:function(){var v=0;return this.childNodes.forEach(function(p){p.nodeType===w.ELEMENT_NODE&&p.tagName==="LI"&&v++}),v}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){return this.reversed?this._numitems:1}},compact:Boolean}}),s({tag:"object",name:"HTMLObjectElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{data:g,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:g,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"optgroup",name:"HTMLOptGroupElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{disabled:Boolean,label:String}}),s({tag:"option",name:"HTMLOptionElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{form:{get:function(){for(var v=this.parentNode;v&&v.nodeType===w.ELEMENT_NODE;){if(v.localName==="select")return v.form;v=v.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(v){this._setattr("value",v)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(v){this.textContent=v}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}}),s({tag:"output",name:"HTMLOutputElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{name:String}}),s({tag:"p",name:"HTMLParagraphElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{align:String}}),s({tag:"param",name:"HTMLParamElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{name:String,value:String,type:String,valueType:String}}),s({tags:["pre","listing","xmp"],name:"HTMLPreElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{width:{type:"long",default:0}}}),s({tag:"progress",name:"HTMLProgressElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:P,attributes:{max:{type:Number,float:!0,default:1,min:0}}}),s({tags:["q","blockquote"],name:"HTMLQuoteElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{cite:g}}),s({tag:"script",name:"HTMLScriptElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{text:{get:function(){for(var v="",p=0,S=this.childNodes.length;p<S;p++){var _=this.childNodes[p];_.nodeType===w.TEXT_NODE&&(v+=_._data)}return v},set:function(v){this.removeChildren(),v!==null&&v!==""&&this.appendChild(this.ownerDocument.createTextNode(v))}}},attributes:{src:g,type:String,charset:String,referrerPolicy:N,defer:Boolean,async:Boolean,nomodule:Boolean,crossOrigin:d,nonce:String,integrity:String}}),s({tag:"select",name:"HTMLSelectElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:{form:P.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}}),s({tag:"span",name:"HTMLSpanElement",ctor:function(p,S,_){R.call(this,p,S,_)}}),s({tag:"style",name:"HTMLStyleElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{media:String,type:String,scoped:Boolean}}),s({tag:"caption",name:"HTMLTableCaptionElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{align:String}}),s({name:"HTMLTableCellElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),s({tags:["col","colgroup"],name:"HTMLTableColElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}}),s({tag:"table",name:"HTMLTableElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"template",name:"HTMLTemplateElement",ctor:function(p,S,_){R.call(this,p,S,_),this._contentFragment=p._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}}),s({tag:"tr",name:"HTMLTableRowElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),s({tags:["thead","tfoot","tbody"],name:"HTMLTableSectionElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}}),s({tag:"textarea",name:"HTMLTextAreaElement",ctor:function(p,S,_){F.call(this,p,S,_)},props:{form:P.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(v){this.textContent=v}},value:{get:function(){return this.defaultValue},set:function(v){this.defaultValue=v}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}}),s({tag:"time",name:"HTMLTimeElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{dateTime:String,pubDate:Boolean}}),s({tag:"title",name:"HTMLTitleElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{text:{get:function(){return this.textContent}}}}),s({tag:"ul",name:"HTMLUListElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{type:String,compact:Boolean}}),s({name:"HTMLMediaElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{src:g,crossOrigin:d,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}}),s({name:"HTMLAudioElement",tag:"audio",superclass:l.HTMLMediaElement,ctor:function(p,S,_){l.HTMLMediaElement.call(this,p,S,_)}}),s({name:"HTMLVideoElement",tag:"video",superclass:l.HTMLMediaElement,ctor:function(p,S,_){l.HTMLMediaElement.call(this,p,S,_)},attributes:{poster:g,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}}),s({tag:"td",name:"HTMLTableDataCellElement",superclass:l.HTMLTableCellElement,ctor:function(p,S,_){l.HTMLTableCellElement.call(this,p,S,_)}}),s({tag:"th",name:"HTMLTableHeaderCellElement",superclass:l.HTMLTableCellElement,ctor:function(p,S,_){l.HTMLTableCellElement.call(this,p,S,_)}}),s({tag:"frameset",name:"HTMLFrameSetElement",ctor:function(p,S,_){R.call(this,p,S,_)}}),s({tag:"frame",name:"HTMLFrameElement",ctor:function(p,S,_){R.call(this,p,S,_)}}),s({tag:"canvas",name:"HTMLCanvasElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{getContext:{value:t.nyi},probablySupportsContext:{value:t.nyi},setContext:{value:t.nyi},transferControlToProxy:{value:t.nyi},toDataURL:{value:t.nyi},toBlob:{value:t.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}}),s({tag:"dialog",name:"HTMLDialogElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{show:{value:t.nyi},showModal:{value:t.nyi},close:{value:t.nyi}},attributes:{open:Boolean,returnValue:String}}),s({tag:"menuitem",name:"HTMLMenuItemElement",ctor:function(p,S,_){R.call(this,p,S,_)},props:{_label:{get:function(){var v=this._getattr("label");return v!==null&&v!==""?v:(v=this.textContent,v.replace(/[ \t\n\f\r]+/g," ").trim())}},label:{get:function(){var v=this._getattr("label");return v!==null?v:this._label},set:function(v){this._setattr("label",v)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:g,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}}),s({tag:"source",name:"HTMLSourceElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{srcset:String,sizes:String,media:String,src:g,type:String,width:String,height:String}}),s({tag:"track",name:"HTMLTrackElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{src:g,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:t.nyi},track:{get:t.nyi}}}),s({tag:"font",name:"HTMLFontElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}}),s({tag:"dir",name:"HTMLDirectoryElement",ctor:function(p,S,_){R.call(this,p,S,_)},attributes:{compact:Boolean}}),s({tags:["abbr","address","article","aside","b","bdi","bdo","cite","content","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})}}),Ma=le({"external/npm/node_modules/domino/lib/svg.js"(b){"use strict";var w=Er(),h=Aa(),c=Be(),t=Dn(),a=b.elements={},o=Object.create(null);b.createElement=function(s,g,d){var N=o[g]||u;return new N(s,g,d)};function l(s){return h(s,u,a,o)}var u=l({superclass:w,name:"SVGElement",ctor:function(g,d,N){w.call(this,g,d,c.NAMESPACE.SVG,N)},props:{style:{get:function(){return this._style||(this._style=new t(this)),this._style}}}});l({name:"SVGSVGElement",ctor:function(g,d,N){u.call(this,g,d,N)},tag:"svg",props:{createSVGRect:{value:function(){return b.createElement(this.ownerDocument,"rect",null)}}}}),l({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})}}),bi=le({"external/npm/node_modules/domino/lib/MutationConstants.js"(b,w){"use strict";w.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}}}),Mn=le({"external/npm/node_modules/domino/lib/Document.js"(b,w){"use strict";w.exports=G;var h=Ge(),c=ar(),t=wn(),a=Er(),o=Na(),l=wa(),u=br(),s=Sa(),g=ka(),d=en(),N=pi(),O=mi(),F=Jr(),R=Cn(),ee=kn(),P=Ca(),v=Sn(),p=An(),S=Ma(),_=Be(),re=bi(),ae=_.NAMESPACE,$=Nn().isApiWritable;function G(T,A){t.call(this),this.nodeType=h.DOCUMENT_NODE,this.isHTML=T,this._address=A||"about:blank",this.readyState="loading",this.implementation=new d(this),this.ownerDocument=null,this._contentType=T?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var D={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},B={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},W=function(T,A,X){return{get:function(){var me=T.call(this);return me?me[A]:X},set:function(me){var Ie=T.call(this);Ie&&(Ie[A]=me)}}};function f(T,A){var X,me,Ie;return T===""&&(T=null),v.isValidQName(A)||_.InvalidCharacterError(),X=null,me=A,Ie=A.indexOf(":"),Ie>=0&&(X=A.substring(0,Ie),me=A.substring(Ie+1)),X!==null&&T===null&&_.NamespaceError(),X==="xml"&&T!==ae.XML&&_.NamespaceError(),(X==="xmlns"||A==="xmlns")&&T!==ae.XMLNS&&_.NamespaceError(),T===ae.XMLNS&&!(X==="xmlns"||A==="xmlns")&&_.NamespaceError(),{namespace:T,prefix:X,localName:me}}G.prototype=Object.create(t.prototype,{_setMutationHandler:{value:function(T){this.mutationHandler=T}},_dispatchRendererEvent:{value:function(T,A,X){var me=this._nodes[T];me&&me._dispatchEvent(new u(A,X),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:_.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(T){return new o(this,String(T))}},createComment:{value:function(T){return new l(this,T)}},createDocumentFragment:{value:function(){return new s(this)}},createProcessingInstruction:{value:function(T,A){return(!v.isValidName(T)||A.indexOf("?>")!==-1)&&_.InvalidCharacterError(),new g(this,T,A)}},createAttribute:{value:function(T){return T=String(T),v.isValidName(T)||_.InvalidCharacterError(),this.isHTML&&(T=_.toASCIILowerCase(T)),new a._Attr(null,T,null,null,"")}},createAttributeNS:{value:function(T,A){T=T==null||T===""?null:String(T),A=String(A);var X=f(T,A);return new a._Attr(null,X.localName,X.prefix,X.namespace,"")}},createElement:{value:function(T){return T=String(T),v.isValidName(T)||_.InvalidCharacterError(),this.isHTML?(/[A-Z]/.test(T)&&(T=_.toASCIILowerCase(T)),p.createElement(this,T,null)):this.contentType==="application/xhtml+xml"?p.createElement(this,T,null):new a(this,T,null,null)},writable:$},createElementNS:{value:function(T,A){T=T==null||T===""?null:String(T),A=String(A);var X=f(T,A);return this._createElementNS(X.localName,X.namespace,X.prefix)},writable:$},_createElementNS:{value:function(T,A,X){return A===ae.HTML?p.createElement(this,T,X):A===ae.SVG?S.createElement(this,T,X):new a(this,T,A,X)}},createEvent:{value:function(A){A=A.toLowerCase();var X=B[A]||A,me=P[D[X]];if(me){var Ie=new me;return Ie._initialized=!1,Ie}else _.NotSupportedError()}},createTreeWalker:{value:function(T,A,X){if(!T)throw new TypeError("root argument is required");if(!(T instanceof h))throw new TypeError("root not a node");return A=A===void 0?F.SHOW_ALL:+A,X=X===void 0?null:X,new N(T,A,X)}},createNodeIterator:{value:function(T,A,X){if(!T)throw new TypeError("root argument is required");if(!(T instanceof h))throw new TypeError("root not a node");return A=A===void 0?F.SHOW_ALL:+A,X=X===void 0?null:X,new O(T,A,X)}},_attachNodeIterator:{value:function(T){this._nodeIterators||(this._nodeIterators=[]),this._nodeIterators.push(T)}},_detachNodeIterator:{value:function(T){var A=this._nodeIterators.indexOf(T);this._nodeIterators.splice(A,1)}},_preremoveNodeIterators:{value:function(T){this._nodeIterators&&this._nodeIterators.forEach(function(A){A._preremove(T)})}},_updateDocTypeElement:{value:function(){this.doctype=this.documentElement=null;for(var A=this.firstChild;A!==null;A=A.nextSibling)A.nodeType===h.DOCUMENT_TYPE_NODE?this.doctype=A:A.nodeType===h.ELEMENT_NODE&&(this.documentElement=A)}},insertBefore:{value:function(A,X){return h.prototype.insertBefore.call(this,A,X),this._updateDocTypeElement(),A}},replaceChild:{value:function(A,X){return h.prototype.replaceChild.call(this,A,X),this._updateDocTypeElement(),X}},removeChild:{value:function(A){return h.prototype.removeChild.call(this,A),this._updateDocTypeElement(),A}},getElementById:{value:function(T){var A=this.byId[T];return A?A instanceof z?A.getFirst():A:null}},_hasMultipleElementsWithId:{value:function(T){return this.byId[T]instanceof z}},getElementsByName:{value:a.prototype.getElementsByName},getElementsByTagName:{value:a.prototype.getElementsByTagName},getElementsByTagNameNS:{value:a.prototype.getElementsByTagNameNS},getElementsByClassName:{value:a.prototype.getElementsByClassName},adoptNode:{value:function(A){return A.nodeType===h.DOCUMENT_NODE&&_.NotSupportedError(),A.nodeType===h.ATTRIBUTE_NODE||(A.parentNode&&A.parentNode.removeChild(A),A.ownerDocument!==this&&M(A,this)),A}},importNode:{value:function(A,X){return this.adoptNode(A.cloneNode(X))},writable:$},origin:{get:function(){return null}},characterSet:{get:function(){return"UTF-8"}},contentType:{get:function(){return this._contentType}},URL:{get:function(){return this._address}},domain:{get:_.nyi,set:_.nyi},referrer:{get:_.nyi},cookie:{get:_.nyi,set:_.nyi},lastModified:{get:_.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:_.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var T=this._titleElement,A=T?T.textContent:"";return A.replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(T){var A=this._titleElement,X=this.head;!A&&!X||(A||(A=this.createElement("title"),X.appendChild(A)),A.textContent=T)}},dir:W(function(){var T=this.documentElement;if(T&&T.tagName==="HTML")return T},"dir",""),fgColor:W(function(){return this.body},"text",""),linkColor:W(function(){return this.body},"link",""),vlinkColor:W(function(){return this.body},"vLink",""),alinkColor:W(function(){return this.body},"aLink",""),bgColor:W(function(){return this.body},"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return n(this.documentElement,"body")},set:_.nyi},head:{get:function(){return n(this.documentElement,"head")}},images:{get:_.nyi},embeds:{get:_.nyi},plugins:{get:_.nyi},links:{get:_.nyi},forms:{get:_.nyi},scripts:{get:_.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:_.nyi},outerHTML:{get:function(){return this.serialize()},set:_.nyi},write:{value:function(T){if(this.isHTML||_.InvalidStateError(),!!this._parser){this._parser;var A=arguments.join("");this._parser.parse(A)}}},writeln:{value:function(A){this.write(Array.prototype.join.call(arguments,"")+`
`)}},open:{value:function(){this.documentElement=null}},close:{value:function(){this.readyState="interactive",this._dispatchEvent(new u("readystatechange"),!0),this._dispatchEvent(new u("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new u("readystatechange"),!0),this.defaultView&&this.defaultView._dispatchEvent(new u("load"),!0)}},clone:{value:function(){var A=new G(this.isHTML,this._address);return A._quirks=this._quirks,A._contentType=this._contentType,A}},cloneNode:{value:function(A){var X=h.prototype.cloneNode.call(this,!1);if(A)for(var me=this.firstChild;me!==null;me=me.nextSibling)X._appendChild(X.importNode(me,!0));return X._updateDocTypeElement(),X}},isEqual:{value:function(A){return!0}},mutateValue:{value:function(T){this.mutationHandler&&this.mutationHandler({type:re.VALUE,target:T,data:T.data})}},mutateAttr:{value:function(T,A){this.mutationHandler&&this.mutationHandler({type:re.ATTR,target:T.ownerElement,attr:T})}},mutateRemoveAttr:{value:function(T){this.mutationHandler&&this.mutationHandler({type:re.REMOVE_ATTR,target:T.ownerElement,attr:T})}},mutateRemove:{value:function(T){this.mutationHandler&&this.mutationHandler({type:re.REMOVE,target:T.parentNode,node:T}),C(T)}},mutateInsert:{value:function(T){E(T),this.mutationHandler&&this.mutationHandler({type:re.INSERT,target:T.parentNode,node:T})}},mutateMove:{value:function(T){this.mutationHandler&&this.mutationHandler({type:re.MOVE,target:T})}},addId:{value:function(A,X){var me=this.byId[A];me?(me instanceof z||(me=new z(me),this.byId[A]=me),me.add(X)):this.byId[A]=X}},delId:{value:function(A,X){var me=this.byId[A];_.assert(me),me instanceof z?(me.del(X),me.length===1&&(this.byId[A]=me.downgrade())):this.byId[A]=void 0}},_resolve:{value:function(T){return new R(this._documentBaseURL).resolve(T)}},_documentBaseURL:{get:function(){var T=this._address;T==="about:blank"&&(T="/");var A=this.querySelector("base[href]");return A?new R(T).resolve(A.getAttribute("href")):T}},_templateDoc:{get:function(){if(!this._templateDocCache){var T=new G(this.isHTML,this._address);this._templateDocCache=T._templateDocCache=T}return this._templateDocCache}},querySelector:{value:function(T){return ee(T,this)[0]}},querySelectorAll:{value:function(T){var A=ee(T,this);return A.item?A:new c(A)}}});var i=["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"];i.forEach(function(T){Object.defineProperty(G.prototype,"on"+T,{get:function(){return this._getEventHandler(T)},set:function(A){this._setEventHandler(T,A)}})});function n(T,A){if(T&&T.isHTML){for(var X=T.firstChild;X!==null;X=X.nextSibling)if(X.nodeType===h.ELEMENT_NODE&&X.localName===A&&X.namespaceURI===ae.HTML)return X}return null}function m(T){if(T._nid=T.ownerDocument._nextnid++,T.ownerDocument._nodes[T._nid]=T,T.nodeType===h.ELEMENT_NODE){var A=T.getAttribute("id");A&&T.ownerDocument.addId(A,T),T._roothook&&T._roothook()}}function L(T){if(T.nodeType===h.ELEMENT_NODE){var A=T.getAttribute("id");A&&T.ownerDocument.delId(A,T)}T.ownerDocument._nodes[T._nid]=void 0,T._nid=void 0}function E(T){if(m(T),T.nodeType===h.ELEMENT_NODE)for(var A=T.firstChild;A!==null;A=A.nextSibling)E(A)}function C(T){L(T);for(var A=T.firstChild;A!==null;A=A.nextSibling)C(A)}function M(T,A){T.ownerDocument=A,T._lastModTime=void 0,Object.prototype.hasOwnProperty.call(T,"_tagName")&&(T._tagName=void 0);for(var X=T.firstChild;X!==null;X=X.nextSibling)M(X,A)}function z(T){this.nodes=Object.create(null),this.nodes[T._nid]=T,this.length=1,this.firstNode=void 0}z.prototype.add=function(T){this.nodes[T._nid]||(this.nodes[T._nid]=T,this.length++,this.firstNode=void 0)},z.prototype.del=function(T){this.nodes[T._nid]&&(delete this.nodes[T._nid],this.length--,this.firstNode=void 0)},z.prototype.getFirst=function(){if(!this.firstNode){var T;for(T in this.nodes)(this.firstNode===void 0||this.firstNode.compareDocumentPosition(this.nodes[T])&h.DOCUMENT_POSITION_PRECEDING)&&(this.firstNode=this.nodes[T])}return this.firstNode},z.prototype.downgrade=function(){if(this.length===1){var T;for(T in this.nodes)return this.nodes[T]}return this}}}),xn=le({"external/npm/node_modules/domino/lib/DocumentType.js"(b,w){"use strict";w.exports=a;var h=Ge(),c=ya(),t=Ln();function a(o,l,u,s){c.call(this),this.nodeType=h.DOCUMENT_TYPE_NODE,this.ownerDocument=o||null,this.name=l,this.publicId=u||"",this.systemId=s||""}a.prototype=Object.create(c.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function(){return new a(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function(l){return this.name===l.name&&this.publicId===l.publicId&&this.systemId===l.systemId}}}),Object.defineProperties(a.prototype,t)}}),In=le({"external/npm/node_modules/domino/lib/HTMLParser.js"(b,w){"use strict";w.exports=de;var h=Mn(),c=xn(),t=Ge(),a=Be().NAMESPACE,o=An(),l=o.elements,u=Function.prototype.apply.bind(Array.prototype.push),s=-1,g=1,d=2,N=3,O=4,F=5,R=[],ee=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,P="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",v=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,p=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,S=Object.create(null);S[a.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0},S[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0},S[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var _=Object.create(null);_[a.HTML]={__proto__:null,address:!0,div:!0,p:!0};var re=Object.create(null);re[a.HTML]={__proto__:null,dd:!0,dt:!0};var ae=Object.create(null);ae[a.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var $=Object.create(null);$[a.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var G=Object.create(null);G[a.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var D=Object.create(null);D[a.HTML]={__proto__:null,table:!0,template:!0,html:!0};var B=Object.create(null);B[a.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var W=Object.create(null);W[a.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var f=Object.create(null);f[a.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var i=Object.create(null);i[a.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0},i[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0},i[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var n=Object.create(i);n[a.HTML]=Object.create(i[a.HTML]),n[a.HTML].ol=!0,n[a.HTML].ul=!0;var m=Object.create(i);m[a.HTML]=Object.create(i[a.HTML]),m[a.HTML].button=!0;var L=Object.create(null);L[a.HTML]={__proto__:null,html:!0,table:!0,template:!0};var E=Object.create(null);E[a.HTML]={__proto__:null,optgroup:!0,option:!0};var C=Object.create(null);C[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var M=Object.create(null);M[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var z={__proto__:null,"xlink:actuate":a.XLINK,"xlink:arcrole":a.XLINK,"xlink:href":a.XLINK,"xlink:role":a.XLINK,"xlink:show":a.XLINK,"xlink:title":a.XLINK,"xlink:type":a.XLINK,"xml:base":a.XML,"xml:lang":a.XML,"xml:space":a.XML,xmlns:a.XMLNS,"xmlns:xlink":a.XMLNS},T={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},A={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},X={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},me={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},Ie=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,Xe=32,Q=/[^\r"&\u0000]+/g,Z=/[^\r'&\u0000]+/g,H=/[^\r\t\n\f &>\u0000]+/g,K=/[^\r\t\n\f \/>A-Z\u0000]+/g,ce=/[^\r\t\n\f \/=>A-Z\u0000]+/g,he=/[^\]\r\u0000\uffff]*/g,se=/[^&<\r\u0000\uffff]*/g,ge=/[^<\r\u0000\uffff]*/g,be=/[^\r\u0000\uffff]*/g,_e=/(?:(\/)?([a-z]+)>)|[\s\S]/g,Ae=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,Se=/[^\x09\x0A\x0C\x0D\x20]/,Qe=/[^\x09\x0A\x0C\x0D\x20]/g,Ze=/[^\x00\x09\x0A\x0C\x0D\x20]/,Ve=/^[\x09\x0A\x0C\x0D\x20]+/,Et=/\x00/g;function Fe(U){var V=16384;if(U.length<V)return String.fromCharCode.apply(String,U);for(var oe="",te=0;te<U.length;te+=V)oe+=String.fromCharCode.apply(String,U.slice(te,te+V));return oe}function tn(U){for(var V=[],oe=0;oe<U.length;oe++)V[oe]=U.charCodeAt(oe);return V}function Ne(U,V){if(typeof V=="string")return U.namespaceURI===a.HTML&&U.localName===V;var oe=V[U.namespaceURI];return oe&&oe[U.localName]}function sr(U){return Ne(U,C)}function ft(U){if(Ne(U,M))return!0;if(U.namespaceURI===a.MATHML&&U.localName==="annotation-xml"){var V=U.getAttribute("encoding");if(V&&(V=V.toLowerCase()),V==="text/html"||V==="application/xhtml+xml")return!0}return!1}function vr(U){return U in A?A[U]:U}function Pt(U){for(var V=0,oe=U.length;V<oe;V++)U[V][0]in T&&(U[V][0]=T[U[V][0]])}function ir(U){for(var V=0,oe=U.length;V<oe;V++)if(U[V][0]==="definitionurl"){U[V][0]="definitionURL";break}}function Xt(U){for(var V=0,oe=U.length;V<oe;V++)U[V][0]in z&&U[V].push(z[U[V][0]])}function vt(U,V){for(var oe=0,te=U.length;oe<te;oe++){var xe=U[oe][0],ne=U[oe][1];V.hasAttribute(xe)||V._setAttribute(xe,ne)}}de.ElementStack=function(){this.elements=[],this.top=null},de.ElementStack.prototype.push=function(U){this.elements.push(U),this.top=U},de.ElementStack.prototype.pop=function(U){this.elements.pop(),this.top=this.elements[this.elements.length-1]},de.ElementStack.prototype.popTag=function(U){for(var V=this.elements.length-1;V>0;V--){var oe=this.elements[V];if(Ne(oe,U))break}this.elements.length=V,this.top=this.elements[V-1]},de.ElementStack.prototype.popElementType=function(U){for(var V=this.elements.length-1;V>0&&!(this.elements[V]instanceof U);V--);this.elements.length=V,this.top=this.elements[V-1]},de.ElementStack.prototype.popElement=function(U){for(var V=this.elements.length-1;V>0&&this.elements[V]!==U;V--);this.elements.length=V,this.top=this.elements[V-1]},de.ElementStack.prototype.removeElement=function(U){if(this.top===U)this.pop();else{var V=this.elements.lastIndexOf(U);V!==-1&&this.elements.splice(V,1)}},de.ElementStack.prototype.clearToContext=function(U){for(var V=this.elements.length-1;V>0&&!Ne(this.elements[V],U);V--);this.elements.length=V+1,this.top=this.elements[V]},de.ElementStack.prototype.contains=function(U){return this.inSpecificScope(U,Object.create(null))},de.ElementStack.prototype.inSpecificScope=function(U,V){for(var oe=this.elements.length-1;oe>=0;oe--){var te=this.elements[oe];if(Ne(te,U))return!0;if(Ne(te,V))return!1}return!1},de.ElementStack.prototype.elementInSpecificScope=function(U,V){for(var oe=this.elements.length-1;oe>=0;oe--){var te=this.elements[oe];if(te===U)return!0;if(Ne(te,V))return!1}return!1},de.ElementStack.prototype.elementTypeInSpecificScope=function(U,V){for(var oe=this.elements.length-1;oe>=0;oe--){var te=this.elements[oe];if(te instanceof U)return!0;if(Ne(te,V))return!1}return!1},de.ElementStack.prototype.inScope=function(U){return this.inSpecificScope(U,i)},de.ElementStack.prototype.elementInScope=function(U){return this.elementInSpecificScope(U,i)},de.ElementStack.prototype.elementTypeInScope=function(U){return this.elementTypeInSpecificScope(U,i)},de.ElementStack.prototype.inButtonScope=function(U){return this.inSpecificScope(U,m)},de.ElementStack.prototype.inListItemScope=function(U){return this.inSpecificScope(U,n)},de.ElementStack.prototype.inTableScope=function(U){return this.inSpecificScope(U,L)},de.ElementStack.prototype.inSelectScope=function(U){for(var V=this.elements.length-1;V>=0;V--){var oe=this.elements[V];if(oe.namespaceURI!==a.HTML)return!1;var te=oe.localName;if(te===U)return!0;if(te!=="optgroup"&&te!=="option")return!1}return!1},de.ElementStack.prototype.generateImpliedEndTags=function(U,V){for(var oe=V?G:$,te=this.elements.length-1;te>=0;te--){var xe=this.elements[te];if(U&&Ne(xe,U)||!Ne(this.elements[te],oe))break}this.elements.length=te+1,this.top=this.elements[te]},de.ActiveFormattingElements=function(){this.list=[],this.attrs=[]},de.ActiveFormattingElements.prototype.MARKER={localName:"|"},de.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)},de.ActiveFormattingElements.prototype.push=function(U,V){for(var oe=0,te=this.list.length-1;te>=0&&this.list[te]!==this.MARKER;te--)if(Bt(U,this.list[te],this.attrs[te])&&(oe++,oe===3)){this.list.splice(te,1),this.attrs.splice(te,1);break}this.list.push(U);for(var xe=[],ne=0;ne<V.length;ne++)xe[ne]=V[ne];this.attrs.push(xe);function Bt(Tt,Ft,ht){if(Tt.localName!==Ft.localName||Tt._numattrs!==ht.length)return!1;for(var We=0,Tr=ht.length;We<Tr;We++){var Ut=ht[We][0],x=ht[We][1];if(!Tt.hasAttribute(Ut)||Tt.getAttribute(Ut)!==x)return!1}return!0}},de.ActiveFormattingElements.prototype.clearToMarker=function(){for(var U=this.list.length-1;U>=0&&this.list[U]!==this.MARKER;U--);U<0&&(U=0),this.list.length=U,this.attrs.length=U},de.ActiveFormattingElements.prototype.findElementByTag=function(U){for(var V=this.list.length-1;V>=0;V--){var oe=this.list[V];if(oe===this.MARKER)break;if(oe.localName===U)return oe}return null},de.ActiveFormattingElements.prototype.indexOf=function(U){return this.list.lastIndexOf(U)},de.ActiveFormattingElements.prototype.remove=function(U){var V=this.list.lastIndexOf(U);V!==-1&&(this.list.splice(V,1),this.attrs.splice(V,1))},de.ActiveFormattingElements.prototype.replace=function(U,V,oe){var te=this.list.lastIndexOf(U);te!==-1&&(this.list[te]=V,this.attrs[te]=oe)},de.ActiveFormattingElements.prototype.insertAfter=function(U,V){var oe=this.list.lastIndexOf(U);oe!==-1&&(this.list.splice(oe,0,V),this.attrs.splice(oe,0,V))};function de(U,V,oe){var te=null,xe=0,ne=0,Bt=!1,Tt=!1,Ft=0,ht=[],We="",Tr=!0,Ut=0,x=Te,yt,Oe,Ce="",yr="",De=[],Ye="",Ke="",Me=[],Nt=[],wt=[],St=[],rt=[],Nr=!1,j=As,dt=null,pt=[],k=new de.ElementStack,ve=new de.ActiveFormattingElements,jt=V!==void 0,wr=null,mt=null,Sr=!0;V&&(Sr=V.ownerDocument._scripting_enabled),oe&&oe.scripting_enabled===!1&&(Sr=!1);var He=!0,rn=!1,kr,nn,Y=[],kt=!1,Vt=!1,Lr={document:function(){return we},_asDocumentFragment:function(){for(var e=we.createDocumentFragment(),r=we.firstChild;r.hasChildNodes();)e.appendChild(r.firstChild);return e},pause:function(){Ut++},resume:function(){Ut--,this.parse("")},parse:function(e,r,y){var I;return Ut>0?(We+=e,!0):(Ft===0?(We&&(e=We+e,We=""),r&&(e+="\uFFFF",Bt=!0),te=e,xe=e.length,ne=0,Tr&&(Tr=!1,te.charCodeAt(0)===65279&&(ne=1)),Ft++,I=On(y),We=te.substring(ne,xe),Ft--):(Ft++,ht.push(te,xe,ne),te=e,xe=e.length,ne=0,On(),I=!1,We=te.substring(ne,xe),ne=ht.pop(),xe=ht.pop(),te=ht.pop(),We&&(te=We+te.substring(ne),xe=te.length,ne=0,We=""),Ft--),I)}},we=new h(!0,U);if(we._parser=Lr,we._scripting_enabled=Sr,V){if(V.ownerDocument._quirks&&(we._quirks=!0),V.ownerDocument._limitedQuirks&&(we._limitedQuirks=!0),V.namespaceURI===a.HTML)switch(V.localName){case"title":case"textarea":x=At;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":x=ln;break}var Rn=we.createElement("html");we._appendChild(Rn),k.push(Rn),V instanceof l.HTMLTemplateElement&&pt.push(En),hr();for(var or=V;or!==null;or=or.parentElement)if(or instanceof l.HTMLFormElement){mt=or;break}}function On(e){for(var r,y,I,q;ne<xe;){if(Ut>0||e&&e())return!0;switch(typeof x.lookahead){case"undefined":if(r=te.charCodeAt(ne++),Tt&&(Tt=!1,r===10)){ne++;continue}switch(r){case 13:ne<xe?te.charCodeAt(ne)===10&&ne++:Tt=!0,x(10);break;case 65535:if(Bt&&ne===xe){x(s);break}default:x(r);break}break;case"number":r=te.charCodeAt(ne);var J=x.lookahead,fe=!0;if(J<0&&(fe=!1,J=-J),J<xe-ne)y=fe?te.substring(ne,ne+J):null,q=!1;else if(Bt)y=fe?te.substring(ne,xe):null,q=!0,r===65535&&ne===xe-1&&(r=s);else return!0;x(r,y,q);break;case"string":r=te.charCodeAt(ne),I=x.lookahead;var ye=te.indexOf(I,ne);if(ye!==-1)y=te.substring(ne,ye+I.length),q=!1;else{if(!Bt)return!0;y=te.substring(ne,xe),r===65535&&ne===xe-1&&(r=s),q=!0}x(r,y,q);break}}return!1}function Lt(e,r){for(var y=0;y<rt.length;y++)if(rt[y][0]===e)return;r!==void 0?rt.push([e,r]):rt.push([e])}function Ra(){Ae.lastIndex=ne-1;var e=Ae.exec(te);if(!e)throw new Error("should never happen");var r=e[1];if(!r)return!1;var y=e[2],I=y.length;switch(y[0]){case'"':case"'":y=y.substring(1,I-1),ne+=e[0].length-1,x=dn;break;default:x=ct,ne+=e[0].length-1,y=y.substring(0,I-1);break}for(var q=0;q<rt.length;q++)if(rt[q][0]===r)return!0;return rt.push([r,y]),!0}function Oa(){Nr=!1,Ce="",rt.length=0}function cr(){Nr=!0,Ce="",rt.length=0}function gt(){De.length=0}function an(){Ye=""}function sn(){Ke=""}function Hn(){Me.length=0}function Yt(){Nt.length=0,wt=null,St=null}function Cr(){wt=[]}function Ct(){St=[]}function ke(){rn=!0}function Ha(){return k.top&&k.top.namespaceURI!=="http://www.w3.org/1999/xhtml"}function $e(e){return yr===e}function Qt(){if(Y.length>0){var e=Fe(Y);if(Y.length=0,Vt&&(Vt=!1,e[0]===`
`&&(e=e.substring(1)),e.length===0))return;Pe(g,e),kt=!1}Vt=!1}function lr(e){e.lastIndex=ne-1;var r=e.exec(te);if(r&&r.index===ne-1)return r=r[0],ne+=r.length-1,Bt&&ne===xe&&(r=r.slice(0,-1),ne--),r;throw new Error("should never happen")}function ur(e){e.lastIndex=ne-1;var r=e.exec(te)[0];return r?(qa(r),ne+=r.length-1,!0):!1}function qa(e){Y.length>0&&Qt(),!(Vt&&(Vt=!1,e[0]===`
`&&(e=e.substring(1)),e.length===0))&&Pe(g,e)}function _t(){if(Nr)Pe(N,Ce);else{var e=Ce;Ce="",yr=e,Pe(d,e,rt)}}function Pa(){if(ne===xe)return!1;_e.lastIndex=ne;var e=_e.exec(te);if(!e)throw new Error("should never happen");var r=e[2];if(!r)return!1;var y=e[1];return y?(ne+=r.length+2,Pe(N,r)):(ne+=r.length+1,yr=r,Pe(d,r,R)),!0}function Ba(){Nr?Pe(N,Ce,null,!0):Pe(d,Ce,rt,!0)}function Le(){Pe(F,Fe(Nt),wt?Fe(wt):void 0,St?Fe(St):void 0)}function Ee(){Qt(),j(s),we.modclock=1}var Pe=Lr.insertToken=function(r,y,I,q){Qt();var J=k.top;!J||J.namespaceURI===a.HTML?j(r,y,I,q):r!==d&&r!==g?$n(r,y,I,q):sr(J)&&(r===g||r===d&&y!=="mglyph"&&y!=="malignmark")||r===d&&y==="svg"&&J.namespaceURI===a.MATHML&&J.localName==="annotation-xml"||ft(J)?(nn=!0,j(r,y,I,q),nn=!1):$n(r,y,I,q)};function st(e){var r=k.top;Dt&&Ne(r,ae)?Ar(function(y){return y.createComment(e)}):(r instanceof l.HTMLTemplateElement&&(r=r.content),r._appendChild(r.ownerDocument.createComment(e)))}function it(e){var r=k.top;if(Dt&&Ne(r,ae))Ar(function(I){return I.createTextNode(e)});else{r instanceof l.HTMLTemplateElement&&(r=r.content);var y=r.lastChild;y&&y.nodeType===t.TEXT_NODE?y.appendData(e):r._appendChild(r.ownerDocument.createTextNode(e))}}function fr(e,r,y){var I=o.createElement(e,r,null);if(y)for(var q=0,J=y.length;q<J;q++)I._setAttribute(y[q][0],y[q][1]);return I}var Dt=!1;function pe(e,r){var y=Dr(function(I){return fr(I,e,r)});return Ne(y,f)&&(y._form=mt),y}function Dr(e){var r;return Dt&&Ne(k.top,ae)?r=Ar(e):k.top instanceof l.HTMLTemplateElement?(r=e(k.top.content.ownerDocument),k.top.content._appendChild(r)):(r=e(k.top.ownerDocument),k.top._appendChild(r)),k.push(r),r}function on(e,r,y){return Dr(function(I){var q=I._createElementNS(e,y,null);if(r)for(var J=0,fe=r.length;J<fe;J++){var ye=r[J];ye.length===2?q._setAttribute(ye[0],ye[1]):q._setAttributeNS(ye[2],ye[0],ye[1])}return q})}function qn(e){for(var r=k.elements.length-1;r>=0;r--)if(k.elements[r]instanceof e)return r;return-1}function Ar(e){var r,y,I=-1,q=-1,J;if(I=qn(l.HTMLTableElement),q=qn(l.HTMLTemplateElement),q>=0&&(I<0||q>I)?r=k.elements[q]:I>=0&&(r=k.elements[I].parentNode,r?y=k.elements[I]:r=k.elements[I-1]),r||(r=k.elements[0]),r instanceof l.HTMLTemplateElement&&(r=r.content),J=e(r.ownerDocument),J.nodeType===t.TEXT_NODE){var fe;if(y?fe=y.previousSibling:fe=r.lastChild,fe&&fe.nodeType===t.TEXT_NODE)return fe.appendData(J.data),J}return y?r.insertBefore(J,y):r._appendChild(J),J}function hr(){for(var e=!1,r=k.elements.length-1;r>=0;r--){var y=k.elements[r];if(r===0&&(e=!0,jt&&(y=V)),y.namespaceURI===a.HTML){var I=y.localName;switch(I){case"select":for(var q=r;q>0;){var J=k.elements[--q];if(J instanceof l.HTMLTemplateElement)break;if(J instanceof l.HTMLTableElement){j=Gr;return}}j=bt;return;case"tr":j=mr;return;case"tbody":case"tfoot":case"thead":j=Wt;return;case"caption":j=bn;return;case"colgroup":j=Vr;return;case"table":j=Je;return;case"template":j=pt[pt.length-1];return;case"body":j=ue;return;case"frameset":j=vn;return;case"html":wr===null?j=Ur:j=_n;return;default:if(!e){if(I==="head"){j=qe;return}if(I==="td"||I==="th"){j=$t;return}}}}if(e){j=ue;return}}}function Mr(e,r){pe(e,r),x=dr,dt=j,j=jr}function Fa(e,r){pe(e,r),x=At,dt=j,j=jr}function cn(e,r){return{elt:fr(e,ve.list[r].localName,ve.attrs[r]),attrs:ve.attrs[r]}}function ze(){if(ve.list.length!==0){var e=ve.list[ve.list.length-1];if(e!==ve.MARKER&&k.elements.lastIndexOf(e)===-1){for(var r=ve.list.length-2;r>=0&&(e=ve.list[r],!(e===ve.MARKER||k.elements.lastIndexOf(e)!==-1));r--);for(r=r+1;r<ve.list.length;r++){var y=Dr(function(I){return cn(I,r).elt});ve.list[r]=y}}}}var xr={localName:"BM"};function Ua(e){if(Ne(k.top,e)&&ve.indexOf(k.top)===-1)return k.pop(),!0;for(var r=0;r<8;){r++;var y=ve.findElementByTag(e);if(!y)return!1;var I=k.elements.lastIndexOf(y);if(I===-1)return ve.remove(y),!0;if(!k.elementInScope(y))return!0;for(var q=null,J,fe=I+1;fe<k.elements.length;fe++)if(Ne(k.elements[fe],S)){q=k.elements[fe],J=fe;break}if(q){var ye=k.elements[I-1];ve.insertAfter(y,xr);for(var Re=q,je=q,et=J,nt,Kt=0;Kt++,Re=k.elements[--et],Re!==y;){if(nt=ve.indexOf(Re),Kt>3&&nt!==-1&&(ve.remove(Re),nt=-1),nt===-1){k.removeElement(Re);continue}var Ot=cn(ye.ownerDocument,nt);ve.replace(Re,Ot.elt,Ot.attrs),k.elements[et]=Ot.elt,Re=Ot.elt,je===q&&(ve.remove(xr),ve.insertAfter(Ot.elt,xr)),Re._appendChild(je),je=Re}Dt&&Ne(ye,ae)?Ar(function(){return je}):ye instanceof l.HTMLTemplateElement?ye.content._appendChild(je):ye._appendChild(je);for(var gr=cn(q.ownerDocument,ve.indexOf(y));q.hasChildNodes();)gr.elt._appendChild(q.firstChild);q._appendChild(gr.elt),ve.remove(y),ve.replace(xr,gr.elt,gr.attrs),k.removeElement(y);var Os=k.elements.lastIndexOf(q);k.elements.splice(Os+1,0,gr.elt)}else return k.popElement(y),ve.remove(y),!0}return!0}function ja(){k.pop(),j=dt}function Gt(){delete we._parser,k.elements.length=0,we.defaultView&&we.defaultView.dispatchEvent(new l.Event("load",{}))}function ie(e,r){x=r,ne--}function Te(e){switch(e){case 38:yt=Te,x=pr;break;case 60:if(Pa())break;x=Va;break;case 0:Y.push(e),kt=!0;break;case-1:Ee();break;default:ur(se)||Y.push(e);break}}function At(e){switch(e){case 38:yt=At,x=pr;break;case 60:x=za;break;case 0:Y.push(65533),kt=!0;break;case-1:Ee();break;default:Y.push(e);break}}function dr(e){switch(e){case 60:x=Ka;break;case 0:Y.push(65533);break;case-1:Ee();break;default:ur(ge)||Y.push(e);break}}function Mt(e){switch(e){case 60:x=Qa;break;case 0:Y.push(65533);break;case-1:Ee();break;default:ur(ge)||Y.push(e);break}}function ln(e){switch(e){case 0:Y.push(65533);break;case-1:Ee();break;default:ur(be)||Y.push(e);break}}function Va(e){switch(e){case 33:x=Un;break;case 47:x=Ga;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Oa(),ie(e,Pn);break;case 63:ie(e,Hr);break;default:Y.push(60),ie(e,Te);break}}function Ga(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:cr(),ie(e,Pn);break;case 62:x=Te;break;case-1:Y.push(60),Y.push(47),Ee();break;default:ie(e,Hr);break}}function Pn(e){switch(e){case 9:case 10:case 12:case 32:x=ct;break;case 47:x=It;break;case 62:x=Te,_t();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce+=String.fromCharCode(e+32);break;case 0:Ce+="\uFFFD";break;case-1:Ee();break;default:Ce+=lr(K);break}}function za(e){e===47?(gt(),x=Za):(Y.push(60),ie(e,At))}function Za(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:cr(),ie(e,Wa);break;default:Y.push(60),Y.push(47),ie(e,At);break}}function Wa(e){switch(e){case 9:case 10:case 12:case 32:if($e(Ce)){x=ct;return}break;case 47:if($e(Ce)){x=It;return}break;case 62:if($e(Ce)){x=Te,_t();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce+=String.fromCharCode(e+32),De.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ce+=String.fromCharCode(e),De.push(e);return;default:break}Y.push(60),Y.push(47),u(Y,De),ie(e,At)}function Ka(e){e===47?(gt(),x=Xa):(Y.push(60),ie(e,dr))}function Xa(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:cr(),ie(e,Ya);break;default:Y.push(60),Y.push(47),ie(e,dr);break}}function Ya(e){switch(e){case 9:case 10:case 12:case 32:if($e(Ce)){x=ct;return}break;case 47:if($e(Ce)){x=It;return}break;case 62:if($e(Ce)){x=Te,_t();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce+=String.fromCharCode(e+32),De.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ce+=String.fromCharCode(e),De.push(e);return;default:break}Y.push(60),Y.push(47),u(Y,De),ie(e,dr)}function Qa(e){switch(e){case 47:gt(),x=$a;break;case 33:x=es,Y.push(60),Y.push(33);break;default:Y.push(60),ie(e,Mt);break}}function $a(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:cr(),ie(e,Ja);break;default:Y.push(60),Y.push(47),ie(e,Mt);break}}function Ja(e){switch(e){case 9:case 10:case 12:case 32:if($e(Ce)){x=ct;return}break;case 47:if($e(Ce)){x=It;return}break;case 62:if($e(Ce)){x=Te,_t();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce+=String.fromCharCode(e+32),De.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ce+=String.fromCharCode(e),De.push(e);return;default:break}Y.push(60),Y.push(47),u(Y,De),ie(e,Mt)}function es(e){e===45?(x=ts,Y.push(45)):ie(e,Mt)}function ts(e){e===45?(x=Bn,Y.push(45)):ie(e,Mt)}function ot(e){switch(e){case 45:x=rs,Y.push(45);break;case 60:x=un;break;case 0:Y.push(65533);break;case-1:Ee();break;default:Y.push(e);break}}function rs(e){switch(e){case 45:x=Bn,Y.push(45);break;case 60:x=un;break;case 0:x=ot,Y.push(65533);break;case-1:Ee();break;default:x=ot,Y.push(e);break}}function Bn(e){switch(e){case 45:Y.push(45);break;case 60:x=un;break;case 62:x=Mt,Y.push(62);break;case 0:x=ot,Y.push(65533);break;case-1:Ee();break;default:x=ot,Y.push(e);break}}function un(e){switch(e){case 47:gt(),x=ns;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:gt(),Y.push(60),ie(e,ss);break;default:Y.push(60),ie(e,ot);break}}function ns(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:cr(),ie(e,as);break;default:Y.push(60),Y.push(47),ie(e,ot);break}}function as(e){switch(e){case 9:case 10:case 12:case 32:if($e(Ce)){x=ct;return}break;case 47:if($e(Ce)){x=It;return}break;case 62:if($e(Ce)){x=Te,_t();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ce+=String.fromCharCode(e+32),De.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ce+=String.fromCharCode(e),De.push(e);return;default:break}Y.push(60),Y.push(47),u(Y,De),ie(e,ot)}function ss(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:Fe(De)==="script"?x=xt:x=ot,Y.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De.push(e+32),Y.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:De.push(e),Y.push(e);break;default:ie(e,ot);break}}function xt(e){switch(e){case 45:x=is,Y.push(45);break;case 60:x=fn,Y.push(60);break;case 0:Y.push(65533);break;case-1:Ee();break;default:Y.push(e);break}}function is(e){switch(e){case 45:x=os,Y.push(45);break;case 60:x=fn,Y.push(60);break;case 0:x=xt,Y.push(65533);break;case-1:Ee();break;default:x=xt,Y.push(e);break}}function os(e){switch(e){case 45:Y.push(45);break;case 60:x=fn,Y.push(60);break;case 62:x=Mt,Y.push(62);break;case 0:x=xt,Y.push(65533);break;case-1:Ee();break;default:x=xt,Y.push(e);break}}function fn(e){e===47?(gt(),x=cs,Y.push(47)):ie(e,xt)}function cs(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:Fe(De)==="script"?x=ot:x=xt,Y.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:De.push(e+32),Y.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:De.push(e),Y.push(e);break;default:ie(e,xt);break}}function ct(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:x=It;break;case 62:x=Te,_t();break;case-1:Ee();break;case 61:an(),Ye+=String.fromCharCode(e),x=hn;break;default:if(Ra())break;an(),ie(e,hn);break}}function hn(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:case-1:ie(e,ls);break;case 61:x=Fn;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ye+=String.fromCharCode(e+32);break;case 0:Ye+="\uFFFD";break;case 34:case 39:case 60:default:Ye+=lr(ce);break}}function ls(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:Lt(Ye),x=It;break;case 61:x=Fn;break;case 62:x=Te,Lt(Ye),_t();break;case-1:Lt(Ye),Ee();break;default:Lt(Ye),an(),ie(e,hn);break}}function Fn(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:sn(),x=Ir;break;case 39:sn(),x=Rr;break;case 62:default:sn(),ie(e,Or);break}}function Ir(e){switch(e){case 34:Lt(Ye,Ke),x=dn;break;case 38:yt=Ir,x=pr;break;case 0:Ke+="\uFFFD";break;case-1:Ee();break;case 10:Ke+=String.fromCharCode(e);break;default:Ke+=lr(Q);break}}function Rr(e){switch(e){case 39:Lt(Ye,Ke),x=dn;break;case 38:yt=Rr,x=pr;break;case 0:Ke+="\uFFFD";break;case-1:Ee();break;case 10:Ke+=String.fromCharCode(e);break;default:Ke+=lr(Z);break}}function Or(e){switch(e){case 9:case 10:case 12:case 32:Lt(Ye,Ke),x=ct;break;case 38:yt=Or,x=pr;break;case 62:Lt(Ye,Ke),x=Te,_t();break;case 0:Ke+="\uFFFD";break;case-1:ne--,x=Te;break;case 34:case 39:case 60:case 61:case 96:default:Ke+=lr(H);break}}function dn(e){switch(e){case 9:case 10:case 12:case 32:x=ct;break;case 47:x=It;break;case 62:x=Te,_t();break;case-1:Ee();break;default:ie(e,ct);break}}function It(e){switch(e){case 62:x=Te,Ba(!0);break;case-1:Ee();break;default:ie(e,ct);break}}function Hr(e,r,y){var I=r.length;y?ne+=I-1:ne+=I;var q=r.substring(0,I-1);q=q.replace(/\u0000/g,"\uFFFD"),q=q.replace(/\u000D\u000A/g,`
`),q=q.replace(/\u000D/g,`
`),Pe(O,q),x=Te}Hr.lookahead=">";function Un(e,r,y){if(r[0]==="-"&&r[1]==="-"){ne+=2,Hn(),x=us;return}r.toUpperCase()==="DOCTYPE"?(ne+=7,x=_s):r==="[CDATA["&&Ha()?(ne+=7,x=gn):x=Hr}Un.lookahead=7;function us(e){switch(Hn(),e){case 45:x=fs;break;case 62:x=Te,Pe(O,Fe(Me));break;default:ie(e,zt);break}}function fs(e){switch(e){case 45:x=qr;break;case 62:x=Te,Pe(O,Fe(Me));break;case-1:Pe(O,Fe(Me)),Ee();break;default:Me.push(45),ie(e,zt);break}}function zt(e){switch(e){case 60:Me.push(e),x=hs;break;case 45:x=pn;break;case 0:Me.push(65533);break;case-1:Pe(O,Fe(Me)),Ee();break;default:Me.push(e);break}}function hs(e){switch(e){case 33:Me.push(e),x=ds;break;case 60:Me.push(e);break;default:ie(e,zt);break}}function ds(e){switch(e){case 45:x=ps;break;default:ie(e,zt);break}}function ps(e){switch(e){case 45:x=ms;break;default:ie(e,pn);break}}function ms(e){switch(e){case 62:case-1:ie(e,qr);break;default:ie(e,qr);break}}function pn(e){switch(e){case 45:x=qr;break;case-1:Pe(O,Fe(Me)),Ee();break;default:Me.push(45),ie(e,zt);break}}function qr(e){switch(e){case 62:x=Te,Pe(O,Fe(Me));break;case 33:x=gs;break;case 45:Me.push(45);break;case-1:Pe(O,Fe(Me)),Ee();break;default:Me.push(45),Me.push(45),ie(e,zt);break}}function gs(e){switch(e){case 45:Me.push(45),Me.push(45),Me.push(33),x=pn;break;case 62:x=Te,Pe(O,Fe(Me));break;case-1:Pe(O,Fe(Me)),Ee();break;default:Me.push(45),Me.push(45),Me.push(33),ie(e,zt);break}}function _s(e){switch(e){case 9:case 10:case 12:case 32:x=jn;break;case-1:Yt(),ke(),Le(),Ee();break;default:ie(e,jn);break}}function jn(e){switch(e){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Yt(),Nt.push(e+32),x=mn;break;case 0:Yt(),Nt.push(65533),x=mn;break;case 62:Yt(),ke(),x=Te,Le();break;case-1:Yt(),ke(),Le(),Ee();break;default:Yt(),Nt.push(e),x=mn;break}}function mn(e){switch(e){case 9:case 10:case 12:case 32:x=Vn;break;case 62:x=Te,Le();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Nt.push(e+32);break;case 0:Nt.push(65533);break;case-1:ke(),Le(),Ee();break;default:Nt.push(e);break}}function Vn(e,r,y){switch(e){case 9:case 10:case 12:case 32:ne+=1;break;case 62:x=Te,ne+=1,Le();break;case-1:ke(),Le(),Ee();break;default:r=r.toUpperCase(),r==="PUBLIC"?(ne+=6,x=bs):r==="SYSTEM"?(ne+=6,x=Ts):(ke(),x=Rt);break}}Vn.lookahead=6;function bs(e){switch(e){case 9:case 10:case 12:case 32:x=Es;break;case 34:Cr(),x=Gn;break;case 39:Cr(),x=zn;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:ke(),x=Rt;break}}function Es(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:Cr(),x=Gn;break;case 39:Cr(),x=zn;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:ke(),x=Rt;break}}function Gn(e){switch(e){case 34:x=Zn;break;case 0:wt.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:wt.push(e);break}}function zn(e){switch(e){case 39:x=Zn;break;case 0:wt.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:wt.push(e);break}}function Zn(e){switch(e){case 9:case 10:case 12:case 32:x=vs;break;case 62:x=Te,Le();break;case 34:Ct(),x=Pr;break;case 39:Ct(),x=Br;break;case-1:ke(),Le(),Ee();break;default:ke(),x=Rt;break}}function vs(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:x=Te,Le();break;case 34:Ct(),x=Pr;break;case 39:Ct(),x=Br;break;case-1:ke(),Le(),Ee();break;default:ke(),x=Rt;break}}function Ts(e){switch(e){case 9:case 10:case 12:case 32:x=ys;break;case 34:Ct(),x=Pr;break;case 39:Ct(),x=Br;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:ke(),x=Rt;break}}function ys(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:Ct(),x=Pr;break;case 39:Ct(),x=Br;break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:ke(),x=Rt;break}}function Pr(e){switch(e){case 34:x=Wn;break;case 0:St.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:St.push(e);break}}function Br(e){switch(e){case 39:x=Wn;break;case 0:St.push(65533);break;case 62:ke(),x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:St.push(e);break}}function Wn(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:x=Te,Le();break;case-1:ke(),Le(),Ee();break;default:x=Rt;break}}function Rt(e){switch(e){case 62:x=Te,Le();break;case-1:Le(),Ee();break;default:break}}function gn(e){switch(e){case 93:x=Ns;break;case-1:Ee();break;case 0:kt=!0;default:ur(he)||Y.push(e);break}}function Ns(e){switch(e){case 93:x=ws;break;default:Y.push(93),ie(e,gn);break}}function ws(e){switch(e){case 93:Y.push(93);break;case 62:Qt(),x=Te;break;default:Y.push(93),Y.push(93),ie(e,gn);break}}function pr(e){switch(gt(),De.push(38),e){case 9:case 10:case 12:case 32:case 60:case 38:case-1:ie(e,Zt);break;case 35:De.push(e),x=Ss;break;default:ie(e,Kn);break}}function Kn(e){Ie.lastIndex=ne;var r=Ie.exec(te);if(!r)throw new Error("should never happen");var y=r[1];if(!y){x=Zt;return}switch(ne+=y.length,u(De,tn(y)),yt){case Ir:case Rr:case Or:if(y[y.length-1]!==";"&&/[=A-Za-z0-9]/.test(te[ne])){x=Zt;return}break;default:break}gt();var I=me[y];typeof I=="number"?De.push(I):u(De,I),x=Zt}Kn.lookahead=-Xe;function Ss(e){switch(Oe=0,e){case 120:case 88:De.push(e),x=ks;break;default:ie(e,Ls);break}}function ks(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:ie(e,Cs);break;default:ie(e,Zt);break}}function Ls(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:ie(e,Ds);break;default:ie(e,Zt);break}}function Cs(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:Oe*=16,Oe+=e-55;break;case 97:case 98:case 99:case 100:case 101:case 102:Oe*=16,Oe+=e-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:Oe*=16,Oe+=e-48;break;case 59:x=Fr;break;default:ie(e,Fr);break}}function Ds(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:Oe*=10,Oe+=e-48;break;case 59:x=Fr;break;default:ie(e,Fr);break}}function Fr(e){Oe in X?Oe=X[Oe]:(Oe>1114111||Oe>=55296&&Oe<57344)&&(Oe=65533),gt(),Oe<=65535?De.push(Oe):(Oe=Oe-65536,De.push(55296+(Oe>>10)),De.push(56320+(Oe&1023))),ie(e,Zt)}function Zt(e){switch(yt){case Ir:case Rr:case Or:Ke+=Fe(De);break;default:u(Y,De);break}ie(e,yt)}function As(e,r,y,I){switch(e){case 1:if(r=r.replace(Ve,""),r.length===0)return;break;case 4:we._appendChild(we.createComment(r));return;case 5:var q=r,J=y,fe=I;we.appendChild(new c(we,q,J,fe)),rn||q.toLowerCase()!=="html"||ee.test(J)||fe&&fe.toLowerCase()===P||fe===void 0&&v.test(J)?we._quirks=!0:(p.test(J)||fe!==void 0&&v.test(J))&&(we._limitedQuirks=!0),j=Xn;return}we._quirks=!0,j=Xn,j(e,r,y,I)}function Xn(e,r,y,I){var q;switch(e){case 1:if(r=r.replace(Ve,""),r.length===0)return;break;case 5:return;case 4:we._appendChild(we.createComment(r));return;case 2:if(r==="html"){q=fr(we,r,y),k.push(q),we.appendChild(q),j=Ur;return}break;case 3:switch(r){case"html":case"head":case"body":case"br":break;default:return}}q=fr(we,"html",null),k.push(q),we.appendChild(q),j=Ur,j(e,r,y,I)}function Ur(e,r,y,I){switch(e){case 1:if(r=r.replace(Ve,""),r.length===0)return;break;case 5:return;case 4:st(r);return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"head":var q=pe(r,y);wr=q,j=qe;return}break;case 3:switch(r){case"html":case"head":case"body":case"br":break;default:return}}Ur(d,"head",null),j(e,r,y,I)}function qe(e,r,y,I){switch(e){case 1:var q=r.match(Ve);if(q&&(it(q[0]),r=r.substring(q[0].length)),r.length===0)return;break;case 4:st(r);return;case 5:return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"meta":case"base":case"basefont":case"bgsound":case"link":pe(r,y),k.pop();return;case"title":Fa(r,y);return;case"noscript":if(!Sr){pe(r,y),j=Yn;return}case"noframes":case"style":Mr(r,y);return;case"script":Dr(function(J){var fe=fr(J,r,y);return fe._parser_inserted=!0,fe._force_async=!1,jt&&(fe._already_started=!0),Qt(),fe}),x=Mt,dt=j,j=jr;return;case"template":pe(r,y),ve.insertMarker(),He=!1,j=En,pt.push(j);return;case"head":return}break;case 3:switch(r){case"head":k.pop(),j=_n;return;case"body":case"html":case"br":break;case"template":if(!k.contains("template"))return;k.generateImpliedEndTags(null,"thorough"),k.popTag("template"),ve.clearToMarker(),pt.pop(),hr();return;default:return}break}qe(N,"head",null),j(e,r,y,I)}function Yn(e,r,y,I){switch(e){case 5:return;case 4:qe(e,r);return;case 1:var q=r.match(Ve);if(q&&(qe(e,q[0]),r=r.substring(q[0].length)),r.length===0)return;break;case 2:switch(r){case"html":ue(e,r,y,I);return;case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":qe(e,r,y);return;case"head":case"noscript":return}break;case 3:switch(r){case"noscript":k.pop(),j=qe;return;case"br":break;default:return}break}Yn(N,"noscript",null),j(e,r,y,I)}function _n(e,r,y,I){switch(e){case 1:var q=r.match(Ve);if(q&&(it(q[0]),r=r.substring(q[0].length)),r.length===0)return;break;case 4:st(r);return;case 5:return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"body":pe(r,y),He=!1,j=ue;return;case"frameset":pe(r,y),j=vn;return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":k.push(wr),qe(d,r,y),k.removeElement(wr);return;case"head":return}break;case 3:switch(r){case"template":return qe(e,r,y,I);case"body":case"html":case"br":break;default:return}break}_n(d,"body",null),He=!0,j(e,r,y,I)}function ue(e,r,y,I){var q,J,fe,ye;switch(e){case 1:if(kt&&(r=r.replace(Et,""),r.length===0))return;He&&Se.test(r)&&(He=!1),ze(),it(r);return;case 5:return;case 4:st(r);return;case-1:if(pt.length)return En(e);Gt();return;case 2:switch(r){case"html":if(k.contains("template"))return;vt(y,k.elements[0]);return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":qe(d,r,y);return;case"body":if(q=k.elements[1],!q||!(q instanceof l.HTMLBodyElement)||k.contains("template"))return;He=!1,vt(y,q);return;case"frameset":if(!He||(q=k.elements[1],!q||!(q instanceof l.HTMLBodyElement)))return;for(q.parentNode&&q.parentNode.removeChild(q);!(k.top instanceof l.HTMLHtmlElement);)k.pop();pe(r,y),j=vn;return;case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":k.inButtonScope("p")&&ue(N,"p"),pe(r,y);return;case"menu":k.inButtonScope("p")&&ue(N,"p"),Ne(k.top,"menuitem")&&k.pop(),pe(r,y);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":k.inButtonScope("p")&&ue(N,"p"),k.top instanceof l.HTMLHeadingElement&&k.pop(),pe(r,y);return;case"pre":case"listing":k.inButtonScope("p")&&ue(N,"p"),pe(r,y),Vt=!0,He=!1;return;case"form":if(mt&&!k.contains("template"))return;k.inButtonScope("p")&&ue(N,"p"),ye=pe(r,y),k.contains("template")||(mt=ye);return;case"li":for(He=!1,J=k.elements.length-1;J>=0;J--){if(fe=k.elements[J],fe instanceof l.HTMLLIElement){ue(N,"li");break}if(Ne(fe,S)&&!Ne(fe,_))break}k.inButtonScope("p")&&ue(N,"p"),pe(r,y);return;case"dd":case"dt":for(He=!1,J=k.elements.length-1;J>=0;J--){if(fe=k.elements[J],Ne(fe,re)){ue(N,fe.localName);break}if(Ne(fe,S)&&!Ne(fe,_))break}k.inButtonScope("p")&&ue(N,"p"),pe(r,y);return;case"plaintext":k.inButtonScope("p")&&ue(N,"p"),pe(r,y),x=ln;return;case"button":k.inScope("button")?(ue(N,"button"),j(e,r,y,I)):(ze(),pe(r,y),He=!1);return;case"a":var Re=ve.findElementByTag("a");Re&&(ue(N,r),ve.remove(Re),k.removeElement(Re));case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":ze(),ve.push(pe(r,y),y);return;case"nobr":ze(),k.inScope(r)&&(ue(N,r),ze()),ve.push(pe(r,y),y);return;case"applet":case"marquee":case"object":ze(),pe(r,y),ve.insertMarker(),He=!1;return;case"table":!we._quirks&&k.inButtonScope("p")&&ue(N,"p"),pe(r,y),He=!1,j=Je;return;case"area":case"br":case"embed":case"img":case"keygen":case"wbr":ze(),pe(r,y),k.pop(),He=!1;return;case"input":ze(),ye=pe(r,y),k.pop();var je=ye.getAttribute("type");(!je||je.toLowerCase()!=="hidden")&&(He=!1);return;case"param":case"source":case"track":pe(r,y),k.pop();return;case"hr":k.inButtonScope("p")&&ue(N,"p"),Ne(k.top,"menuitem")&&k.pop(),pe(r,y),k.pop(),He=!1;return;case"image":ue(d,"img",y,I);return;case"textarea":pe(r,y),Vt=!0,He=!1,x=At,dt=j,j=jr;return;case"xmp":k.inButtonScope("p")&&ue(N,"p"),ze(),He=!1,Mr(r,y);return;case"iframe":He=!1,Mr(r,y);return;case"noembed":Mr(r,y);return;case"select":ze(),pe(r,y),He=!1,j===Je||j===bn||j===Wt||j===mr||j===$t?j=Gr:j=bt;return;case"optgroup":case"option":k.top instanceof l.HTMLOptionElement&&ue(N,"option"),ze(),pe(r,y);return;case"menuitem":Ne(k.top,"menuitem")&&k.pop(),ze(),pe(r,y);return;case"rb":case"rtc":k.inScope("ruby")&&k.generateImpliedEndTags(),pe(r,y);return;case"rp":case"rt":k.inScope("ruby")&&k.generateImpliedEndTags("rtc"),pe(r,y);return;case"math":ze(),ir(y),Xt(y),on(r,y,a.MATHML),I&&k.pop();return;case"svg":ze(),Pt(y),Xt(y),on(r,y,a.SVG),I&&k.pop();return;case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}ze(),pe(r,y);return;case 3:switch(r){case"template":qe(N,r,y);return;case"body":if(!k.inScope("body"))return;j=Qn;return;case"html":if(!k.inScope("body"))return;j=Qn,j(e,r,y);return;case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!k.inScope(r))return;k.generateImpliedEndTags(),k.popTag(r);return;case"form":if(k.contains("template")){if(!k.inScope("form"))return;k.generateImpliedEndTags(),k.popTag("form")}else{var et=mt;if(mt=null,!et||!k.elementInScope(et))return;k.generateImpliedEndTags(),k.removeElement(et)}return;case"p":k.inButtonScope(r)?(k.generateImpliedEndTags(r),k.popTag(r)):(ue(d,r,null),j(e,r,y,I));return;case"li":if(!k.inListItemScope(r))return;k.generateImpliedEndTags(r),k.popTag(r);return;case"dd":case"dt":if(!k.inScope(r))return;k.generateImpliedEndTags(r),k.popTag(r);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!k.elementTypeInScope(l.HTMLHeadingElement))return;k.generateImpliedEndTags(),k.popElementType(l.HTMLHeadingElement);return;case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":var nt=Ua(r);if(nt)return;break;case"applet":case"marquee":case"object":if(!k.inScope(r))return;k.generateImpliedEndTags(),k.popTag(r),ve.clearToMarker();return;case"br":ue(d,r,null);return}for(J=k.elements.length-1;J>=0;J--)if(fe=k.elements[J],Ne(fe,r)){k.generateImpliedEndTags(r),k.popElement(fe);break}else if(Ne(fe,S))return;return}}function jr(e,r,y,I){switch(e){case 1:it(r);return;case-1:k.top instanceof l.HTMLScriptElement&&(k.top._already_started=!0),k.pop(),j=dt,j(e);return;case 3:r==="script"?ja():(k.pop(),j=dt);return;default:return}}function Je(e,r,y,I){function q(fe){for(var ye=0,Re=fe.length;ye<Re;ye++)if(fe[ye][0]==="type")return fe[ye][1].toLowerCase();return null}switch(e){case 1:if(nn){ue(e,r,y,I);return}else if(Ne(k.top,ae)){kr=[],dt=j,j=Ms,j(e,r,y,I);return}break;case 4:st(r);return;case 5:return;case 2:switch(r){case"caption":k.clearToContext(D),ve.insertMarker(),pe(r,y),j=bn;return;case"colgroup":k.clearToContext(D),pe(r,y),j=Vr;return;case"col":Je(d,"colgroup",null),j(e,r,y,I);return;case"tbody":case"tfoot":case"thead":k.clearToContext(D),pe(r,y),j=Wt;return;case"td":case"th":case"tr":Je(d,"tbody",null),j(e,r,y,I);return;case"table":if(!k.inTableScope(r))return;Je(N,r),j(e,r,y,I);return;case"style":case"script":case"template":qe(e,r,y,I);return;case"input":var J=q(y);if(J!=="hidden")break;pe(r,y),k.pop();return;case"form":if(mt||k.contains("template"))return;mt=pe(r,y),k.popElement(mt);return}break;case 3:switch(r){case"table":if(!k.inTableScope(r))return;k.popTag(r),hr();return;case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":qe(e,r,y,I);return}break;case-1:ue(e,r,y,I);return}Dt=!0,ue(e,r,y,I),Dt=!1}function Ms(e,r,y,I){if(e===g){if(kt&&(r=r.replace(Et,""),r.length===0))return;kr.push(r)}else{var q=kr.join("");kr.length=0,Se.test(q)?(Dt=!0,ue(g,q),Dt=!1):it(q),j=dt,j(e,r,y,I)}}function bn(e,r,y,I){function q(){return k.inTableScope("caption")?(k.generateImpliedEndTags(),k.popTag("caption"),ve.clearToMarker(),j=Je,!0):!1}switch(e){case 2:switch(r){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":q()&&j(e,r,y,I);return}break;case 3:switch(r){case"caption":q();return;case"table":q()&&j(e,r,y,I);return;case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}break}ue(e,r,y,I)}function Vr(e,r,y,I){switch(e){case 1:var q=r.match(Ve);if(q&&(it(q[0]),r=r.substring(q[0].length)),r.length===0)return;break;case 4:st(r);return;case 5:return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"col":pe(r,y),k.pop();return;case"template":qe(e,r,y,I);return}break;case 3:switch(r){case"colgroup":if(!Ne(k.top,"colgroup"))return;k.pop(),j=Je;return;case"col":return;case"template":qe(e,r,y,I);return}break;case-1:ue(e,r,y,I);return}Ne(k.top,"colgroup")&&(Vr(N,"colgroup"),j(e,r,y,I))}function Wt(e,r,y,I){function q(){!k.inTableScope("tbody")&&!k.inTableScope("thead")&&!k.inTableScope("tfoot")||(k.clearToContext(B),Wt(N,k.top.localName,null),j(e,r,y,I))}switch(e){case 2:switch(r){case"tr":k.clearToContext(B),pe(r,y),j=mr;return;case"th":case"td":Wt(d,"tr",null),j(e,r,y,I);return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":q();return}break;case 3:switch(r){case"table":q();return;case"tbody":case"tfoot":case"thead":k.inTableScope(r)&&(k.clearToContext(B),k.pop(),j=Je);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}break}Je(e,r,y,I)}function mr(e,r,y,I){function q(){return k.inTableScope("tr")?(k.clearToContext(W),k.pop(),j=Wt,!0):!1}switch(e){case 2:switch(r){case"th":case"td":k.clearToContext(W),pe(r,y),j=$t,ve.insertMarker();return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":q()&&j(e,r,y,I);return}break;case 3:switch(r){case"tr":q();return;case"table":q()&&j(e,r,y,I);return;case"tbody":case"tfoot":case"thead":k.inTableScope(r)&&q()&&j(e,r,y,I);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}break}Je(e,r,y,I)}function $t(e,r,y,I){switch(e){case 2:switch(r){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":k.inTableScope("td")?($t(N,"td"),j(e,r,y,I)):k.inTableScope("th")&&($t(N,"th"),j(e,r,y,I));return}break;case 3:switch(r){case"td":case"th":if(!k.inTableScope(r))return;k.generateImpliedEndTags(),k.popTag(r),ve.clearToMarker(),j=mr;return;case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!k.inTableScope(r))return;$t(N,k.inTableScope("td")?"td":"th"),j(e,r,y,I);return}break}ue(e,r,y,I)}function bt(e,r,y,I){switch(e){case 1:if(kt&&(r=r.replace(Et,""),r.length===0))return;it(r);return;case 4:st(r);return;case 5:return;case-1:ue(e,r,y,I);return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"option":k.top instanceof l.HTMLOptionElement&&bt(N,r),pe(r,y);return;case"optgroup":k.top instanceof l.HTMLOptionElement&&bt(N,"option"),k.top instanceof l.HTMLOptGroupElement&&bt(N,r),pe(r,y);return;case"select":bt(N,r);return;case"input":case"keygen":case"textarea":if(!k.inSelectScope("select"))return;bt(N,"select"),j(e,r,y,I);return;case"script":case"template":qe(e,r,y,I);return}break;case 3:switch(r){case"optgroup":k.top instanceof l.HTMLOptionElement&&k.elements[k.elements.length-2]instanceof l.HTMLOptGroupElement&&bt(N,"option"),k.top instanceof l.HTMLOptGroupElement&&k.pop();return;case"option":k.top instanceof l.HTMLOptionElement&&k.pop();return;case"select":if(!k.inSelectScope(r))return;k.popTag(r),hr();return;case"template":qe(e,r,y,I);return}break}}function Gr(e,r,y,I){switch(r){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(e){case 2:Gr(N,"select"),j(e,r,y,I);return;case 3:k.inTableScope(r)&&(Gr(N,"select"),j(e,r,y,I));return}}bt(e,r,y,I)}function En(e,r,y,I){function q(J){j=J,pt[pt.length-1]=j,j(e,r,y,I)}switch(e){case 1:case 4:case 5:ue(e,r,y,I);return;case-1:k.contains("template")?(k.popTag("template"),ve.clearToMarker(),pt.pop(),hr(),j(e,r,y,I)):Gt();return;case 2:switch(r){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":qe(e,r,y,I);return;case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":q(Je);return;case"col":q(Vr);return;case"tr":q(Wt);return;case"td":case"th":q(mr);return}q(ue);return;case 3:switch(r){case"template":qe(e,r,y,I);return;default:return}}}function Qn(e,r,y,I){switch(e){case 1:if(Se.test(r))break;ue(e,r);return;case 4:k.elements[0]._appendChild(we.createComment(r));return;case 5:return;case-1:Gt();return;case 2:if(r==="html"){ue(e,r,y,I);return}break;case 3:if(r==="html"){if(jt)return;j=Is;return}break}j=ue,j(e,r,y,I)}function vn(e,r,y,I){switch(e){case 1:r=r.replace(Qe,""),r.length>0&&it(r);return;case 4:st(r);return;case 5:return;case-1:Gt();return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"frameset":pe(r,y);return;case"frame":pe(r,y),k.pop();return;case"noframes":qe(e,r,y,I);return}break;case 3:if(r==="frameset"){if(jt&&k.top instanceof l.HTMLHtmlElement)return;k.pop(),!jt&&!(k.top instanceof l.HTMLFrameSetElement)&&(j=xs);return}break}}function xs(e,r,y,I){switch(e){case 1:r=r.replace(Qe,""),r.length>0&&it(r);return;case 4:st(r);return;case 5:return;case-1:Gt();return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"noframes":qe(e,r,y,I);return}break;case 3:if(r==="html"){j=Rs;return}break}}function Is(e,r,y,I){switch(e){case 1:if(Se.test(r))break;ue(e,r,y,I);return;case 4:we._appendChild(we.createComment(r));return;case 5:ue(e,r,y,I);return;case-1:Gt();return;case 2:if(r==="html"){ue(e,r,y,I);return}break}j=ue,j(e,r,y,I)}function Rs(e,r,y,I){switch(e){case 1:r=r.replace(Qe,""),r.length>0&&ue(e,r,y,I);return;case 4:we._appendChild(we.createComment(r));return;case 5:ue(e,r,y,I);return;case-1:Gt();return;case 2:switch(r){case"html":ue(e,r,y,I);return;case"noframes":qe(e,r,y,I);return}break}}function $n(e,r,y,I){function q(Re){for(var je=0,et=Re.length;je<et;je++)switch(Re[je][0]){case"color":case"face":case"size":return!0}return!1}var J;switch(e){case 1:He&&Ze.test(r)&&(He=!1),kt&&(r=r.replace(Et,"\uFFFD")),it(r);return;case 4:st(r);return;case 5:return;case 2:switch(r){case"font":if(!q(y))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(jt)break;do k.pop(),J=k.top;while(J.namespaceURI!==a.HTML&&!sr(J)&&!ft(J));Pe(e,r,y,I);return}J=k.elements.length===1&&jt?V:k.top,J.namespaceURI===a.MATHML?ir(y):J.namespaceURI===a.SVG&&(r=vr(r),Pt(y)),Xt(y),on(r,y,J.namespaceURI),I&&(r==="script"&&(J.namespaceURI,a.SVG),k.pop());return;case 3:if(J=k.top,r==="script"&&J.namespaceURI===a.SVG&&J.localName==="script")k.pop();else for(var fe=k.elements.length-1,ye=k.elements[fe];;){if(ye.localName.toLowerCase()===r){k.popElement(ye);break}if(ye=k.elements[--fe],ye.namespaceURI===a.HTML){j(e,r,y,I);break}}return}}return Lr.testTokenizer=function(e,r,y,I){var q=[];switch(r){case"PCDATA state":x=Te;break;case"RCDATA state":x=At;break;case"RAWTEXT state":x=dr;break;case"PLAINTEXT state":x=ln;break}if(y&&(yr=y),Pe=function(fe,ye,Re,je){switch(Qt(),fe){case 1:q.length>0&&q[q.length-1][0]==="Character"?q[q.length-1][1]+=ye:q.push(["Character",ye]);break;case 4:q.push(["Comment",ye]);break;case 5:q.push(["DOCTYPE",ye,Re===void 0?null:Re,je===void 0?null:je,!rn]);break;case 2:for(var et=Object.create(null),nt=0;nt<Re.length;nt++){var Kt=Re[nt];Kt.length===1?et[Kt[0]]="":et[Kt[0]]=Kt[1]}var Ot=["StartTag",ye,et];je&&Ot.push(!0),q.push(Ot);break;case 3:q.push(["EndTag",ye]);break;case-1:break}},!I)this.parse(e,!0);else{for(var J=0;J<e.length;J++)this.parse(e[J]);this.parse("",!0)}return q},Lr}}}),en=le({"external/npm/node_modules/domino/lib/DOMImplementation.js"(b,w){"use strict";w.exports=l;var h=Mn(),c=xn(),t=In(),a=Be(),o=Sn();function l(s){this.contextObject=s}var u={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};l.prototype={hasFeature:function(g,d){var N=u[(g||"").toLowerCase()];return N&&N[d||""]||!1},createDocumentType:function(g,d,N){return o.isValidQName(g)||a.InvalidCharacterError(),new c(this.contextObject,g,d,N)},createDocument:function(g,d,N){var O=new h(!1,null),F;return d?F=O.createElementNS(g,d):F=null,N&&O.appendChild(N),F&&O.appendChild(F),g===a.NAMESPACE.HTML?O._contentType="application/xhtml+xml":g===a.NAMESPACE.SVG?O._contentType="image/svg+xml":O._contentType="application/xml",O},createHTMLDocument:function(g){var d=new h(!0,null);d.appendChild(new c(d,"html"));var N=d.createElement("html");d.appendChild(N);var O=d.createElement("head");if(N.appendChild(O),g!==void 0){var F=d.createElement("title");O.appendChild(F),F.appendChild(d.createTextNode(g))}return N.appendChild(d.createElement("body")),d.modclock=1,d},mozSetOutputMutationHandler:function(s,g){s.mutationHandler=g},mozGetInputMutationHandler:function(s){a.nyi()},mozHTMLParser:t}}}),Ei=le({"external/npm/node_modules/domino/lib/Location.js"(b,w){"use strict";var h=Cn(),c=Da();w.exports=t;function t(a,o){this._window=a,this._href=o}t.prototype=Object.create(c.prototype,{constructor:{value:t},href:{get:function(){return this._href},set:function(a){this.assign(a)}},assign:{value:function(a){var o=new h(this._href),l=o.resolve(a);this._href=l}},replace:{value:function(a){this.assign(a)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})}}),vi=le({"external/npm/node_modules/domino/lib/NavigatorID.js"(b,w){"use strict";var h=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}});w.exports=h}}),Ti=le({"external/npm/node_modules/domino/lib/WindowTimers.js"(b,w){"use strict";var h={setTimeout,clearTimeout,setInterval,clearInterval};w.exports=h}}),xa=le({"external/npm/node_modules/domino/lib/impl.js"(b,w){"use strict";var h=Be();b=w.exports={CSSStyleDeclaration:Dn(),CharacterData:$r(),Comment:wa(),DOMException:yn(),DOMImplementation:en(),DOMTokenList:Ea(),Document:Mn(),DocumentFragment:Sa(),DocumentType:xn(),Element:Er(),HTMLParser:In(),NamedNodeMap:Ta(),Node:Ge(),NodeList:ar(),NodeFilter:Jr(),ProcessingInstruction:ka(),Text:Na(),Window:Ia()},h.merge(b,Ca()),h.merge(b,An().elements),h.merge(b,Ma().elements)}}),Ia=le({"external/npm/node_modules/domino/lib/Window.js"(b,w){"use strict";var h=en(),c=ma(),t=Ei(),a=Be();w.exports=o;function o(l){this.document=l||new h(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new t(this,this.document._address||"about:blank")}o.prototype=Object.create(c.prototype,{console:{value:console},history:{value:{back:a.nyi,forward:a.nyi,go:a.nyi}},navigator:{value:vi()},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(l){this._setEventHandler("load",l)}},getComputedStyle:{value:function(u){return u.style}}}),a.expose(Ti(),o),a.expose(xa(),o)}}),yi=le({"external/npm/node_modules/domino/lib/index.js"(b){var w=en(),h=In(),c=Ia(),t=xa();b.createDOMImplementation=function(){return new w(null)},b.createDocument=function(a,o){if(a||o){var l=new h;return l.parse(a||"",!0),l.document()}return new w(null).createHTMLDocument("")},b.createIncrementalHTMLParser=function(){var a=new h;return{write:function(o){o.length>0&&a.parse(o,!1,function(){return!0})},end:function(o){a.parse(o||"",!0,function(){return!0})},process:function(o){return a.parse("",!1,o)},document:function(){return a.document()}}},b.createWindow=function(a,o){var l=b.createDocument(a);return o!==void 0&&(l._address=o),new t.Window(l)},b.impl=t}}),ha=yi();function Ni(){Object.assign(globalThis,ha.impl),globalThis.KeyboardEvent=ha.impl.Event}Ni();
