import{$ as Or,$a as qr,$b as yt,Aa as Nr,Ab as Gr,B as tt,C as et,E as Re,Ea as $,F as rt,Fa as jr,G as wr,H as Ht,I as V,<PERSON>a as De,J as Se,Ja as st,K as br,<PERSON> as Me,L as Ir,La as Lr,Mb as Qr,N as Ar,Nb as Zr,O as P,Oa as kr,Ob as Ee,P as Dr,Pa as zr,Pb as Oe,Q as w,Qa as Te,Qb as Kr,R as I,Ra as Fr,Rb as Zt,Sb as Kt,T as S,Ta as _r,Tb as Yt,U as Mr,V as Tr,Vb as Yr,W as U,Wb as Xr,X as we,Xb as Jr,Y as B,Yb as Ct,Z as p,Zb as tn,_ as Er,_b as en,a as mr,aa as Wt,b as Cr,ba as Ur,ca as xr,d as me,da as Gt,db as Vr,e as <PERSON>,f as _,fa as Pr,g as O,ha as nt,i as q,ia as H,j as A,jb as Br,k as d,ka as Qt,l as vt,lb as Hr,m as yr,mb as Wr,n as Rr,nc as rn,p as C,pa as be,q as Bt,qa as $r,r as M,ra as Ie,s as mt,t as Sr,u as ye,ua as Ae,va as it,yc as nn,z as Y}from"./chunk-MCI2ITGN.js";import{a as h,b as D,h as vr}from"./chunk-WVXK5ZBG.js";var g="primary",Nt=Symbol("RouteTitle"),Ne=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let i=this.params[t];return Array.isArray(i)?i[0]:i}return null}getAll(t){if(this.has(t)){let i=this.params[t];return Array.isArray(i)?i:[i]}return[]}get keys(){return Object.keys(this.params)}};function lt(e){return new Ne(e)}function Kn(e,t,i){let r=i.path.split("/");if(r.length>e.length||i.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let n={};for(let s=0;s<r.length;s++){let o=r[s],a=e[s];if(o.startsWith(":"))n[o.substring(1)]=a;else if(o!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:n}}function Yn(e,t){if(e.length!==t.length)return!1;for(let i=0;i<e.length;++i)if(!j(e[i],t[i]))return!1;return!0}function j(e,t){let i=e?je(e):void 0,r=t?je(t):void 0;if(!i||!r||i.length!=r.length)return!1;let n;for(let s=0;s<i.length;s++)if(n=i[s],!pn(e[n],t[n]))return!1;return!0}function je(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function pn(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let i=[...e].sort(),r=[...t].sort();return i.every((n,s)=>r[s]===n)}else return e===t}function gn(e){return e.length>0?e[e.length-1]:null}function K(e){return yr(e)?e:Zr(e)?A(Promise.resolve(e)):d(e)}var Xn={exact:mn,subset:Cn},vn={exact:Jn,subset:ti,ignored:()=>!0};function sn(e,t,i){return Xn[i.paths](e.root,t.root,i.matrixParams)&&vn[i.queryParams](e.queryParams,t.queryParams)&&!(i.fragment==="exact"&&e.fragment!==t.fragment)}function Jn(e,t){return j(e,t)}function mn(e,t,i){if(!J(e.segments,t.segments)||!te(e.segments,t.segments,i)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!mn(e.children[r],t.children[r],i))return!1;return!0}function ti(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(i=>pn(e[i],t[i]))}function Cn(e,t,i){return yn(e,t,t.segments,i)}function yn(e,t,i,r){if(e.segments.length>i.length){let n=e.segments.slice(0,i.length);return!(!J(n,i)||t.hasChildren()||!te(n,i,r))}else if(e.segments.length===i.length){if(!J(e.segments,i)||!te(e.segments,i,r))return!1;for(let n in t.children)if(!e.children[n]||!Cn(e.children[n],t.children[n],r))return!1;return!0}else{let n=i.slice(0,e.segments.length),s=i.slice(e.segments.length);return!J(e.segments,n)||!te(e.segments,n,r)||!e.children[g]?!1:yn(e.children[g],t,s,r)}}function te(e,t,i){return t.every((r,n)=>vn[i](e[n].parameters,r.parameters))}var W=class{constructor(t=new m([],{}),i={},r=null){this.root=t,this.queryParams=i,this.fragment=r}get queryParamMap(){var t;return(t=this._queryParamMap)!=null||(this._queryParamMap=lt(this.queryParams)),this._queryParamMap}toString(){return ni.serialize(this)}},m=class{constructor(t,i){this.segments=t,this.children=i,this.parent=null,Object.values(i).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ee(this)}},X=class{constructor(t,i){this.path=t,this.parameters=i}get parameterMap(){var t;return(t=this._parameterMap)!=null||(this._parameterMap=lt(this.parameters)),this._parameterMap}toString(){return Sn(this)}};function ei(e,t){return J(e,t)&&e.every((i,r)=>j(i.parameters,t[r].parameters))}function J(e,t){return e.length!==t.length?!1:e.every((i,r)=>i.path===t[r].path)}function ri(e,t){let i=[];return Object.entries(e.children).forEach(([r,n])=>{r===g&&(i=i.concat(t(n,r)))}),Object.entries(e.children).forEach(([r,n])=>{r!==g&&(i=i.concat(t(n,r)))}),i}var jt=(()=>{let t=class t{};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:()=>new Dt,providedIn:"root"});let e=t;return e})(),Dt=class{parse(t){let i=new ke(t);return new W(i.parseRootSegment(),i.parseQueryParams(),i.parseFragment())}serialize(t){let i=`/${Rt(t.root,!0)}`,r=oi(t.queryParams),n=typeof t.fragment=="string"?`#${ii(t.fragment)}`:"";return`${i}${r}${n}`}},ni=new Dt;function ee(e){return e.segments.map(t=>Sn(t)).join("/")}function Rt(e,t){if(!e.hasChildren())return ee(e);if(t){let i=e.children[g]?Rt(e.children[g],!1):"",r=[];return Object.entries(e.children).forEach(([n,s])=>{n!==g&&r.push(`${n}:${Rt(s,!1)}`)}),r.length>0?`${i}(${r.join("//")})`:i}else{let i=ri(e,(r,n)=>n===g?[Rt(e.children[g],!1)]:[`${n}:${Rt(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[g]!=null?`${ee(e)}/${i[0]}`:`${ee(e)}/(${i.join("//")})`}}function Rn(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Xt(e){return Rn(e).replace(/%3B/gi,";")}function ii(e){return encodeURI(e)}function Le(e){return Rn(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function re(e){return decodeURIComponent(e)}function on(e){return re(e.replace(/\+/g,"%20"))}function Sn(e){return`${Le(e.path)}${si(e.parameters)}`}function si(e){return Object.entries(e).map(([t,i])=>`;${Le(t)}=${Le(i)}`).join("")}function oi(e){let t=Object.entries(e).map(([i,r])=>Array.isArray(r)?r.map(n=>`${Xt(i)}=${Xt(n)}`).join("&"):`${Xt(i)}=${Xt(r)}`).filter(i=>i);return t.length?`?${t.join("&")}`:""}var ai=/^[^\/()?;#]+/;function Ue(e){let t=e.match(ai);return t?t[0]:""}var ui=/^[^\/()?;=#]+/;function ci(e){let t=e.match(ui);return t?t[0]:""}var li=/^[^=?&#]+/;function hi(e){let t=e.match(li);return t?t[0]:""}var di=/^[^&#]+/;function fi(e){let t=e.match(di);return t?t[0]:""}var ke=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new m([],{}):new m([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let i={};this.peekStartsWith("/(")&&(this.capture("/"),i=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(i).length>0)&&(r[g]=new m(t,i)),r}parseSegment(){let t=Ue(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new I(4009,!1);return this.capture(t),new X(re(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let i=ci(this.remaining);if(!i)return;this.capture(i);let r="";if(this.consumeOptional("=")){let n=Ue(this.remaining);n&&(r=n,this.capture(r))}t[re(i)]=re(r)}parseQueryParam(t){let i=hi(this.remaining);if(!i)return;this.capture(i);let r="";if(this.consumeOptional("=")){let o=fi(this.remaining);o&&(r=o,this.capture(r))}let n=on(i),s=on(r);if(t.hasOwnProperty(n)){let o=t[n];Array.isArray(o)||(o=[o],t[n]=o),o.push(s)}else t[n]=s}parseParens(t){let i={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Ue(this.remaining),n=this.remaining[r.length];if(n!=="/"&&n!==")"&&n!==";")throw new I(4010,!1);let s;r.indexOf(":")>-1?(s=r.slice(0,r.indexOf(":")),this.capture(s),this.capture(":")):t&&(s=g);let o=this.parseChildren();i[s]=Object.keys(o).length===1?o[g]:new m([],o),this.consumeOptional("//")}return i}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new I(4011,!1)}};function wn(e){return e.segments.length>0?new m([],{[g]:e}):e}function bn(e){let t={};for(let[r,n]of Object.entries(e.children)){let s=bn(n);if(r===g&&s.segments.length===0&&s.hasChildren())for(let[o,a]of Object.entries(s.children))t[o]=a;else(s.segments.length>0||s.hasChildren())&&(t[r]=s)}let i=new m(e.segments,t);return pi(i)}function pi(e){if(e.numberOfChildren===1&&e.children[g]){let t=e.children[g];return new m(e.segments.concat(t.segments),t.children)}return e}function ht(e){return e instanceof W}function gi(e,t,i=null,r=null){let n=In(e);return An(n,t,i,r)}function In(e){let t;function i(s){let o={};for(let c of s.children){let u=i(c);o[c.outlet]=u}let a=new m(s.url,o);return s===e&&(t=a),a}let r=i(e.root),n=wn(r);return t!=null?t:n}function An(e,t,i,r){let n=e;for(;n.parent;)n=n.parent;if(t.length===0)return xe(n,n,n,i,r);let s=vi(t);if(s.toRoot())return xe(n,n,new m([],{}),i,r);let o=mi(s,n,e),a=o.processChildren?bt(o.segmentGroup,o.index,s.commands):Mn(o.segmentGroup,o.index,s.commands);return xe(n,o.segmentGroup,a,i,r)}function ne(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Mt(e){return typeof e=="object"&&e!=null&&e.outlets}function xe(e,t,i,r,n){let s={};r&&Object.entries(r).forEach(([c,u])=>{s[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let o;e===t?o=i:o=Dn(e,t,i);let a=wn(bn(o));return new W(a,s,n)}function Dn(e,t,i){let r={};return Object.entries(e.children).forEach(([n,s])=>{s===t?r[n]=i:r[n]=Dn(s,t,i)}),new m(e.segments,r)}var ie=class{constructor(t,i,r){if(this.isAbsolute=t,this.numberOfDoubleDots=i,this.commands=r,t&&r.length>0&&ne(r[0]))throw new I(4003,!1);let n=r.find(Mt);if(n&&n!==gn(r))throw new I(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function vi(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ie(!0,0,e);let t=0,i=!1,r=e.reduce((n,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let a={};return Object.entries(s.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...n,{outlets:a}]}if(s.segmentPath)return[...n,s.segmentPath]}return typeof s!="string"?[...n,s]:o===0?(s.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?i=!0:a===".."?t++:a!=""&&n.push(a))}),n):[...n,s]},[]);return new ie(i,t,r)}var ut=class{constructor(t,i,r){this.segmentGroup=t,this.processChildren=i,this.index=r}};function mi(e,t,i){if(e.isAbsolute)return new ut(t,!0,0);if(!i)return new ut(t,!1,NaN);if(i.parent===null)return new ut(i,!0,0);let r=ne(e.commands[0])?0:1,n=i.segments.length-1+r;return Ci(i,n,e.numberOfDoubleDots)}function Ci(e,t,i){let r=e,n=t,s=i;for(;s>n;){if(s-=n,r=r.parent,!r)throw new I(4005,!1);n=r.segments.length}return new ut(r,!1,n-s)}function yi(e){return Mt(e[0])?e[0].outlets:{[g]:e}}function Mn(e,t,i){if(e!=null||(e=new m([],{})),e.segments.length===0&&e.hasChildren())return bt(e,t,i);let r=Ri(e,t,i),n=i.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let s=new m(e.segments.slice(0,r.pathIndex),{});return s.children[g]=new m(e.segments.slice(r.pathIndex),e.children),bt(s,0,n)}else return r.match&&n.length===0?new m(e.segments,{}):r.match&&!e.hasChildren()?ze(e,t,i):r.match?bt(e,0,n):ze(e,t,i)}function bt(e,t,i){if(i.length===0)return new m(e.segments,{});{let r=yi(i),n={};if(Object.keys(r).some(s=>s!==g)&&e.children[g]&&e.numberOfChildren===1&&e.children[g].segments.length===0){let s=bt(e.children[g],t,i);return new m(e.segments,s.children)}return Object.entries(r).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(n[s]=Mn(e.children[s],t,o))}),Object.entries(e.children).forEach(([s,o])=>{r[s]===void 0&&(n[s]=o)}),new m(e.segments,n)}}function Ri(e,t,i){let r=0,n=t,s={match:!1,pathIndex:0,commandIndex:0};for(;n<e.segments.length;){if(r>=i.length)return s;let o=e.segments[n],a=i[r];if(Mt(a))break;let c=`${a}`,u=r<i.length-1?i[r+1]:null;if(n>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!un(c,u,o))return s;r+=2}else{if(!un(c,{},o))return s;r++}n++}return{match:!0,pathIndex:n,commandIndex:r}}function ze(e,t,i){let r=e.segments.slice(0,t),n=0;for(;n<i.length;){let s=i[n];if(Mt(s)){let c=Si(s.outlets);return new m(r,c)}if(n===0&&ne(i[0])){let c=e.segments[t];r.push(new X(c.path,an(i[0]))),n++;continue}let o=Mt(s)?s.outlets[g]:`${s}`,a=n<i.length-1?i[n+1]:null;o&&a&&ne(a)?(r.push(new X(o,an(a))),n+=2):(r.push(new X(o,{})),n++)}return new m(r,{})}function Si(e){let t={};return Object.entries(e).forEach(([i,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[i]=ze(new m([],{}),0,r))}),t}function an(e){let t={};return Object.entries(e).forEach(([i,r])=>t[i]=`${r}`),t}function un(e,t,i){return e==i.path&&j(t,i.parameters)}var It="imperative",R=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(R||{}),x=class{constructor(t,i){this.id=t,this.url=i}},dt=class extends x{constructor(t,i,r="imperative",n=null){super(t,i),this.type=R.NavigationStart,this.navigationTrigger=r,this.restoredState=n}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},N=class extends x{constructor(t,i,r){super(t,i),this.urlAfterRedirects=r,this.type=R.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},E=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(E||{}),se=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(se||{}),G=class extends x{constructor(t,i,r,n){super(t,i),this.reason=r,this.code=n,this.type=R.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Q=class extends x{constructor(t,i,r,n){super(t,i),this.reason=r,this.code=n,this.type=R.NavigationSkipped}},Tt=class extends x{constructor(t,i,r,n){super(t,i),this.error=r,this.target=n,this.type=R.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},oe=class extends x{constructor(t,i,r,n){super(t,i),this.urlAfterRedirects=r,this.state=n,this.type=R.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Fe=class extends x{constructor(t,i,r,n){super(t,i),this.urlAfterRedirects=r,this.state=n,this.type=R.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},_e=class extends x{constructor(t,i,r,n,s){super(t,i),this.urlAfterRedirects=r,this.state=n,this.shouldActivate=s,this.type=R.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},qe=class extends x{constructor(t,i,r,n){super(t,i),this.urlAfterRedirects=r,this.state=n,this.type=R.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ve=class extends x{constructor(t,i,r,n){super(t,i),this.urlAfterRedirects=r,this.state=n,this.type=R.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Be=class{constructor(t){this.route=t,this.type=R.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},He=class{constructor(t){this.route=t,this.type=R.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},We=class{constructor(t){this.snapshot=t,this.type=R.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ge=class{constructor(t){this.snapshot=t,this.type=R.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Qe=class{constructor(t){this.snapshot=t,this.type=R.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ze=class{constructor(t){this.snapshot=t,this.type=R.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ae=class{constructor(t,i,r){this.routerEvent=t,this.position=i,this.anchor=r,this.type=R.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},Et=class{},Ot=class{constructor(t){this.url=t}};var Ke=class{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new Lt,this.attachRef=null}},Lt=(()=>{let t=class t{constructor(){this.contexts=new Map}onChildOutletCreated(r,n){let s=this.getOrCreateContext(r);s.outlet=n,this.contexts.set(r,s)}onChildOutletDestroyed(r){let n=this.getContext(r);n&&(n.outlet=null,n.attachRef=null)}onOutletDeactivated(){let r=this.contexts;return this.contexts=new Map,r}onOutletReAttached(r){this.contexts=r}getOrCreateContext(r){let n=this.getContext(r);return n||(n=new Ke,this.contexts.set(r,n)),n}getContext(r){return this.contexts.get(r)||null}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),ue=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let i=this.pathFromRoot(t);return i.length>1?i[i.length-2]:null}children(t){let i=Ye(t,this._root);return i?i.children.map(r=>r.value):[]}firstChild(t){let i=Ye(t,this._root);return i&&i.children.length>0?i.children[0].value:null}siblings(t){let i=Xe(t,this._root);return i.length<2?[]:i[i.length-2].children.map(n=>n.value).filter(n=>n!==t)}pathFromRoot(t){return Xe(t,this._root).map(i=>i.value)}};function Ye(e,t){if(e===t.value)return t;for(let i of t.children){let r=Ye(e,i);if(r)return r}return null}function Xe(e,t){if(e===t.value)return[t];for(let i of t.children){let r=Xe(e,i);if(r.length)return r.unshift(t),r}return[]}var T=class{constructor(t,i){this.value=t,this.children=i}toString(){return`TreeNode(${this.value})`}};function at(e){let t={};return e&&e.children.forEach(i=>t[i.value.outlet]=i),t}var ce=class extends ue{constructor(t,i){super(t),this.snapshot=i,ur(this,t)}toString(){return this.snapshot.toString()}};function Tn(e){let t=wi(e),i=new O([new X("",{})]),r=new O({}),n=new O({}),s=new O({}),o=new O(""),a=new Z(i,r,s,o,n,g,e,t.root);return a.snapshot=t.root,new ce(new T(a,[]),t)}function wi(e){let t={},i={},r={},n="",s=new Ut([],t,r,n,i,g,e,null,{});return new le("",new T(s,[]))}var Z=class{constructor(t,i,r,n,s,o,a,c){var u,l;this.urlSubject=t,this.paramsSubject=i,this.queryParamsSubject=r,this.fragmentSubject=n,this.dataSubject=s,this.outlet=o,this.component=a,this._futureSnapshot=c,this.title=(l=(u=this.dataSubject)==null?void 0:u.pipe(C(f=>f[Nt])))!=null?l:d(void 0),this.url=t,this.params=i,this.queryParams=r,this.fragment=n,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var t;return(t=this._paramMap)!=null||(this._paramMap=this.params.pipe(C(i=>lt(i)))),this._paramMap}get queryParamMap(){var t;return(t=this._queryParamMap)!=null||(this._queryParamMap=this.queryParams.pipe(C(i=>lt(i)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ar(e,t,i="emptyOnly"){var s,o;let r,{routeConfig:n}=e;return t!==null&&(i==="always"||(n==null?void 0:n.path)===""||!t.component&&!((s=t.routeConfig)!=null&&s.loadComponent))?r={params:h(h({},t.params),e.params),data:h(h({},t.data),e.data),resolve:h(h(h(h({},e.data),t.data),n==null?void 0:n.data),e._resolvedData)}:r={params:h({},e.params),data:h({},e.data),resolve:h(h({},e.data),(o=e._resolvedData)!=null?o:{})},n&&On(n)&&(r.resolve[Nt]=n.title),r}var Ut=class{get title(){var t;return(t=this.data)==null?void 0:t[Nt]}constructor(t,i,r,n,s,o,a,c,u){this.url=t,this.params=i,this.queryParams=r,this.fragment=n,this.data=s,this.outlet=o,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var t;return(t=this._paramMap)!=null||(this._paramMap=lt(this.params)),this._paramMap}get queryParamMap(){var t;return(t=this._queryParamMap)!=null||(this._queryParamMap=lt(this.queryParams)),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),i=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${i}')`}},le=class extends ue{constructor(t,i){super(i),this.url=t,ur(this,i)}toString(){return En(this._root)}};function ur(e,t){t.value._routerState=e,t.children.forEach(i=>ur(e,i))}function En(e){let t=e.children.length>0?` { ${e.children.map(En).join(", ")} } `:"";return`${e.value}${t}`}function Pe(e){if(e.snapshot){let t=e.snapshot,i=e._futureSnapshot;e.snapshot=i,j(t.queryParams,i.queryParams)||e.queryParamsSubject.next(i.queryParams),t.fragment!==i.fragment&&e.fragmentSubject.next(i.fragment),j(t.params,i.params)||e.paramsSubject.next(i.params),Yn(t.url,i.url)||e.urlSubject.next(i.url),j(t.data,i.data)||e.dataSubject.next(i.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Je(e,t){let i=j(e.params,t.params)&&ei(e.url,t.url),r=!e.parent!=!t.parent;return i&&!r&&(!e.parent||Je(e.parent,t.parent))}function On(e){return typeof e.title=="string"||e.title===null}var bi=(()=>{let t=class t{constructor(){this.activated=null,this._activatedRoute=null,this.name=g,this.activateEvents=new it,this.deactivateEvents=new it,this.attachEvents=new it,this.detachEvents=new it,this.parentContexts=p(Lt),this.location=p(Lr),this.changeDetector=p(Kt),this.environmentInjector=p(nt),this.inputBinder=p(pe,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(r){if(r.name){let{firstChange:n,previousValue:s}=r.name;if(n)return;this.isTrackedInParentContexts(s)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(s)),this.initializeOutletWithName()}}ngOnDestroy(){var r;this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),(r=this.inputBinder)==null||r.unsubscribeFromRouteData(this)}isTrackedInParentContexts(r){var n;return((n=this.parentContexts.getContext(r))==null?void 0:n.outlet)===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let r=this.parentContexts.getContext(this.name);r!=null&&r.route&&(r.attachRef?this.attach(r.attachRef,r.route):this.activateWith(r.route,r.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new I(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new I(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new I(4012,!1);this.location.detach();let r=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(r.instance),r}attach(r,n){var s;this.activated=r,this._activatedRoute=n,this.location.insert(r.hostView),(s=this.inputBinder)==null||s.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(r.instance)}deactivate(){if(this.activated){let r=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(r)}}activateWith(r,n){var l;if(this.isActivated)throw new I(4013,!1);this._activatedRoute=r;let s=this.location,a=r.snapshot.component,c=this.parentContexts.getOrCreateContext(this.name).children,u=new tr(r,c,s.injector);this.activated=s.createComponent(a,{index:s.length,injector:u,environmentInjector:n!=null?n:this.environmentInjector}),this.changeDetector.markForCheck(),(l=this.inputBinder)==null||l.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}};t.\u0275fac=function(n){return new(n||t)},t.\u0275dir=Gt({type:t,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Qt]});let e=t;return e})(),tr=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,i,r){this.route=t,this.childContexts=i,this.parent=r}get(t,i){return t===Z?this.route:t===Lt?this.childContexts:this.parent.get(t,i)}},pe=new U(""),cn=(()=>{let t=class t{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(r){this.unsubscribeFromRouteData(r),this.subscribeToRouteData(r)}unsubscribeFromRouteData(r){var n;(n=this.outletDataSubscriptions.get(r))==null||n.unsubscribe(),this.outletDataSubscriptions.delete(r)}subscribeToRouteData(r){let{activatedRoute:n}=r,s=Bt([n.queryParams,n.params,n.data]).pipe(P(([o,a,c],u)=>(c=h(h(h({},o),a),c),u===0?d(c):Promise.resolve(c)))).subscribe(o=>{if(!r.isActivated||!r.activatedComponentRef||r.activatedRoute!==n||n.component===null){this.unsubscribeFromRouteData(r);return}let a=Yr(n.component);if(!a){this.unsubscribeFromRouteData(r);return}for(let{templateName:c}of a.inputs)r.activatedComponentRef.setInput(c,o[c])});this.outletDataSubscriptions.set(r,s)}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:t.\u0275fac});let e=t;return e})();function Ii(e,t,i){let r=xt(e,t._root,i?i._root:void 0);return new ce(r,t)}function xt(e,t,i){if(i&&e.shouldReuseRoute(t.value,i.value.snapshot)){let r=i.value;r._futureSnapshot=t.value;let n=Ai(e,t,i);return new T(r,n)}else{if(e.shouldAttach(t.value)){let s=e.retrieve(t.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=t.value,o.children=t.children.map(a=>xt(e,a)),o}}let r=Di(t.value),n=t.children.map(s=>xt(e,s));return new T(r,n)}}function Ai(e,t,i){return t.children.map(r=>{for(let n of i.children)if(e.shouldReuseRoute(r.value,n.value.snapshot))return xt(e,r,n);return xt(e,r)})}function Di(e){return new Z(new O(e.url),new O(e.params),new O(e.queryParams),new O(e.fragment),new O(e.data),e.outlet,e.component,e)}var Un="ngNavigationCancelingError";function xn(e,t){let{redirectTo:i,navigationBehaviorOptions:r}=ht(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,n=Pn(!1,E.Redirect);return n.url=i,n.navigationBehaviorOptions=r,n}function Pn(e,t){let i=new Error(`NavigationCancelingError: ${e||""}`);return i[Un]=!0,i.cancellationCode=t,i}function Mi(e){return $n(e)&&ht(e.url)}function $n(e){return!!e&&e[Un]}var Ti=(()=>{let t=class t{};t.\u0275fac=function(n){return new(n||t)},t.\u0275cmp=Ur({type:t,selectors:[["ng-component"]],standalone:!0,features:[Gr],decls:1,vars:0,template:function(n,s){n&1&&qr(0,"router-outlet")},dependencies:[bi],encapsulation:2});let e=t;return e})();function Ei(e,t){var i;return e.providers&&!e._injector&&(e._injector=Te(e.providers,t,`Route: ${e.path}`)),(i=e._injector)!=null?i:t}function cr(e){let t=e.children&&e.children.map(cr),i=t?D(h({},e),{children:t}):h({},e);return!i.component&&!i.loadComponent&&(t||i.loadChildren)&&i.outlet&&i.outlet!==g&&(i.component=Ti),i}function L(e){return e.outlet||g}function Oi(e,t){let i=e.filter(r=>L(r)===t);return i.push(...e.filter(r=>L(r)!==t)),i}function kt(e){var t;if(!e)return null;if((t=e.routeConfig)!=null&&t._injector)return e.routeConfig._injector;for(let i=e.parent;i;i=i.parent){let r=i.routeConfig;if(r!=null&&r._loadedInjector)return r._loadedInjector;if(r!=null&&r._injector)return r._injector}return null}var Ui=(e,t,i,r)=>C(n=>(new er(t,n.targetRouterState,n.currentRouterState,i,r).activate(e),n)),er=class{constructor(t,i,r,n,s){this.routeReuseStrategy=t,this.futureState=i,this.currState=r,this.forwardEvent=n,this.inputBindingEnabled=s}activate(t){let i=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(i,r,t),Pe(this.futureState.root),this.activateChildRoutes(i,r,t)}deactivateChildRoutes(t,i,r){let n=at(i);t.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,n[o],r),delete n[o]}),Object.values(n).forEach(s=>{this.deactivateRouteAndItsChildren(s,r)})}deactivateRoutes(t,i,r){let n=t.value,s=i?i.value:null;if(n===s)if(n.component){let o=r.getContext(n.outlet);o&&this.deactivateChildRoutes(t,i,o.children)}else this.deactivateChildRoutes(t,i,r);else s&&this.deactivateRouteAndItsChildren(i,r)}deactivateRouteAndItsChildren(t,i){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,i):this.deactivateRouteAndOutlet(t,i)}detachAndStoreRouteSubtree(t,i){let r=i.getContext(t.value.outlet),n=r&&t.value.component?r.children:i,s=at(t);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,n);if(r&&r.outlet){let o=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:o,route:t,contexts:a})}}deactivateRouteAndOutlet(t,i){let r=i.getContext(t.value.outlet),n=r&&t.value.component?r.children:i,s=at(t);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,n);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,i,r){let n=at(i);t.children.forEach(s=>{this.activateRoutes(s,n[s.value.outlet],r),this.forwardEvent(new Ze(s.value.snapshot))}),t.children.length&&this.forwardEvent(new Ge(t.value.snapshot))}activateRoutes(t,i,r){let n=t.value,s=i?i.value:null;if(Pe(n),n===s)if(n.component){let o=r.getOrCreateContext(n.outlet);this.activateChildRoutes(t,i,o.children)}else this.activateChildRoutes(t,i,r);else if(n.component){let o=r.getOrCreateContext(n.outlet);if(this.routeReuseStrategy.shouldAttach(n.snapshot)){let a=this.routeReuseStrategy.retrieve(n.snapshot);this.routeReuseStrategy.store(n.snapshot,null),o.children.onOutletReAttached(a.contexts),o.attachRef=a.componentRef,o.route=a.route.value,o.outlet&&o.outlet.attach(a.componentRef,a.route.value),Pe(a.route.value),this.activateChildRoutes(t,null,o.children)}else{let a=kt(n.snapshot);o.attachRef=null,o.route=n,o.injector=a,o.outlet&&o.outlet.activateWith(n,o.injector),this.activateChildRoutes(t,null,o.children)}}else this.activateChildRoutes(t,null,r)}},he=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},ct=class{constructor(t,i){this.component=t,this.route=i}};function xi(e,t,i){let r=e._root,n=t?t._root:null;return St(r,n,i,[r.value])}function Pi(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function pt(e,t){let i=Symbol(),r=t.get(e,i);return r===i?typeof e=="function"&&!Tr(e)?e:t.get(e):r}function St(e,t,i,r,n={canDeactivateChecks:[],canActivateChecks:[]}){let s=at(t);return e.children.forEach(o=>{$i(o,s[o.value.outlet],i,r.concat([o.value]),n),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,a])=>At(a,i.getContext(o),n)),n}function $i(e,t,i,r,n={canDeactivateChecks:[],canActivateChecks:[]}){let s=e.value,o=t?t.value:null,a=i?i.getContext(e.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let c=Ni(o,s,s.routeConfig.runGuardsAndResolvers);c?n.canActivateChecks.push(new he(r)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?St(e,t,a?a.children:null,r,n):St(e,t,i,r,n),c&&a&&a.outlet&&a.outlet.isActivated&&n.canDeactivateChecks.push(new ct(a.outlet.component,o))}else o&&At(t,a,n),n.canActivateChecks.push(new he(r)),s.component?St(e,null,a?a.children:null,r,n):St(e,null,i,r,n);return n}function Ni(e,t,i){if(typeof i=="function")return i(e,t);switch(i){case"pathParamsChange":return!J(e.url,t.url);case"pathParamsOrQueryParamsChange":return!J(e.url,t.url)||!j(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Je(e,t)||!j(e.queryParams,t.queryParams);case"paramsChange":default:return!Je(e,t)}}function At(e,t,i){let r=at(e),n=e.value;Object.entries(r).forEach(([s,o])=>{n.component?t?At(o,t.children.getContext(s),i):At(o,null,i):At(o,t,i)}),n.component?t&&t.outlet&&t.outlet.isActivated?i.canDeactivateChecks.push(new ct(t.outlet.component,n)):i.canDeactivateChecks.push(new ct(null,n)):i.canDeactivateChecks.push(new ct(null,n))}function zt(e){return typeof e=="function"}function ji(e){return typeof e=="boolean"}function Li(e){return e&&zt(e.canLoad)}function ki(e){return e&&zt(e.canActivate)}function zi(e){return e&&zt(e.canActivateChild)}function Fi(e){return e&&zt(e.canDeactivate)}function _i(e){return e&&zt(e.canMatch)}function Nn(e){return e instanceof Rr||(e==null?void 0:e.name)==="EmptyError"}var Jt=Symbol("INITIAL_VALUE");function ft(){return P(e=>Bt(e.map(t=>t.pipe(rt(1),Ar(Jt)))).pipe(C(t=>{for(let i of t)if(i!==!0){if(i===Jt)return Jt;if(i===!1||i instanceof W)return i}return!0}),Y(t=>t!==Jt),rt(1)))}function qi(e,t){return M(i=>{let{targetSnapshot:r,currentSnapshot:n,guards:{canActivateChecks:s,canDeactivateChecks:o}}=i;return o.length===0&&s.length===0?d(D(h({},i),{guardsResult:!0})):Vi(o,r,n,e).pipe(M(a=>a&&ji(a)?Bi(r,s,e,t):d(a)),C(a=>D(h({},i),{guardsResult:a})))})}function Vi(e,t,i,r){return A(e).pipe(M(n=>Zi(n.component,n.route,i,t,r)),V(n=>n!==!0,!0))}function Bi(e,t,i,r){return A(t).pipe(et(n=>Sr(Wi(n.route.parent,r),Hi(n.route,r),Qi(e,n.path,i),Gi(e,n.route,i))),V(n=>n!==!0,!0))}function Hi(e,t){return e!==null&&t&&t(new Qe(e)),d(!0)}function Wi(e,t){return e!==null&&t&&t(new We(e)),d(!0)}function Gi(e,t,i){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return d(!0);let n=r.map(s=>ye(()=>{var u;let o=(u=kt(t))!=null?u:i,a=pt(s,o),c=ki(a)?a.canActivate(t,e):H(o,()=>a(t,e));return K(c).pipe(V())}));return d(n).pipe(ft())}function Qi(e,t,i){let r=t[t.length-1],s=t.slice(0,t.length-1).reverse().map(o=>Pi(o)).filter(o=>o!==null).map(o=>ye(()=>{let a=o.guards.map(c=>{var y;let u=(y=kt(o.node))!=null?y:i,l=pt(c,u),f=zi(l)?l.canActivateChild(r,e):H(u,()=>l(r,e));return K(f).pipe(V())});return d(a).pipe(ft())}));return d(s).pipe(ft())}function Zi(e,t,i,r,n){let s=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!s||s.length===0)return d(!0);let o=s.map(a=>{var f;let c=(f=kt(t))!=null?f:n,u=pt(a,c),l=Fi(u)?u.canDeactivate(e,t,i,r):H(c,()=>u(e,t,i,r));return K(l).pipe(V())});return d(o).pipe(ft())}function Ki(e,t,i,r){let n=t.canLoad;if(n===void 0||n.length===0)return d(!0);let s=n.map(o=>{let a=pt(o,e),c=Li(a)?a.canLoad(t,i):H(e,()=>a(t,i));return K(c)});return d(s).pipe(ft(),jn(r))}function jn(e){return Cr(w(t=>{if(ht(t))throw xn(e,t)}),C(t=>t===!0))}function Yi(e,t,i,r){let n=t.canMatch;if(!n||n.length===0)return d(!0);let s=n.map(o=>{let a=pt(o,e),c=_i(a)?a.canMatch(t,i):H(e,()=>a(t,i));return K(c)});return d(s).pipe(ft(),jn(r))}var Pt=class{constructor(t){this.segmentGroup=t||null}},de=class extends Error{constructor(t){super(),this.urlTree=t}};function ot(e){return vt(new Pt(e))}function Xi(e){return vt(new I(4e3,!1))}function Ji(e){return vt(Pn(!1,E.GuardRejected))}var rr=class{constructor(t,i){this.urlSerializer=t,this.urlTree=i}lineralizeSegments(t,i){let r=[],n=i.root;for(;;){if(r=r.concat(n.segments),n.numberOfChildren===0)return d(r);if(n.numberOfChildren>1||!n.children[g])return Xi(t.redirectTo);n=n.children[g]}}applyRedirectCommands(t,i,r){let n=this.applyRedirectCreateUrlTree(i,this.urlSerializer.parse(i),t,r);if(i.startsWith("/"))throw new de(n);return n}applyRedirectCreateUrlTree(t,i,r,n){let s=this.createSegmentGroup(t,i.root,r,n);return new W(s,this.createQueryParams(i.queryParams,this.urlTree.queryParams),i.fragment)}createQueryParams(t,i){let r={};return Object.entries(t).forEach(([n,s])=>{if(typeof s=="string"&&s.startsWith(":")){let a=s.substring(1);r[n]=i[a]}else r[n]=s}),r}createSegmentGroup(t,i,r,n){let s=this.createSegments(t,i.segments,r,n),o={};return Object.entries(i.children).forEach(([a,c])=>{o[a]=this.createSegmentGroup(t,c,r,n)}),new m(s,o)}createSegments(t,i,r,n){return i.map(s=>s.path.startsWith(":")?this.findPosParam(t,s,n):this.findOrReturn(s,r))}findPosParam(t,i,r){let n=r[i.path.substring(1)];if(!n)throw new I(4001,!1);return n}findOrReturn(t,i){let r=0;for(let n of i){if(n.path===t.path)return i.splice(r),n;r++}return t}},nr={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function ts(e,t,i,r,n){let s=lr(e,t,i);return s.matched?(r=Ei(t,r),Yi(r,t,i,n).pipe(C(o=>o===!0?s:h({},nr)))):d(s)}function lr(e,t,i){var a,c;if(t.path==="**")return es(i);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||i.length>0)?h({},nr):{matched:!0,consumedSegments:[],remainingSegments:i,parameters:{},positionalParamSegments:{}};let n=(t.matcher||Kn)(i,e,t);if(!n)return h({},nr);let s={};Object.entries((a=n.posParams)!=null?a:{}).forEach(([u,l])=>{s[u]=l.path});let o=n.consumed.length>0?h(h({},s),n.consumed[n.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:n.consumed,remainingSegments:i.slice(n.consumed.length),parameters:o,positionalParamSegments:(c=n.posParams)!=null?c:{}}}function es(e){return{matched:!0,parameters:e.length>0?gn(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function ln(e,t,i,r){return i.length>0&&is(e,i,r)?{segmentGroup:new m(t,ns(r,new m(i,e.children))),slicedSegments:[]}:i.length===0&&ss(e,i,r)?{segmentGroup:new m(e.segments,rs(e,i,r,e.children)),slicedSegments:i}:{segmentGroup:new m(e.segments,e.children),slicedSegments:i}}function rs(e,t,i,r){let n={};for(let s of i)if(ge(e,t,s)&&!r[L(s)]){let o=new m([],{});n[L(s)]=o}return h(h({},r),n)}function ns(e,t){let i={};i[g]=t;for(let r of e)if(r.path===""&&L(r)!==g){let n=new m([],{});i[L(r)]=n}return i}function is(e,t,i){return i.some(r=>ge(e,t,r)&&L(r)!==g)}function ss(e,t,i){return i.some(r=>ge(e,t,r))}function ge(e,t,i){return(e.hasChildren()||t.length>0)&&i.pathMatch==="full"?!1:i.path===""}function os(e,t,i,r){return L(e)!==r&&(r===g||!ge(t,i,e))?!1:lr(t,e,i).matched}function as(e,t,i){return t.length===0&&!e.children[i]}var ir=class{};function us(e,t,i,r,n,s,o="emptyOnly"){return new sr(e,t,i,r,n,o,s).recognize()}var cs=31,sr=class{constructor(t,i,r,n,s,o,a){this.injector=t,this.configLoader=i,this.rootComponentType=r,this.config=n,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=a,this.applyRedirects=new rr(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new I(4002,`'${t.segmentGroup}'`)}recognize(){let t=ln(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(C(i=>{let r=new Ut([],Object.freeze({}),Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,{},g,this.rootComponentType,null,{}),n=new T(r,i),s=new le("",n),o=gi(r,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),this.inheritParamsAndData(s._root,null),{state:s,tree:o}}))}match(t){return this.processSegmentGroup(this.injector,this.config,t,g).pipe(tt(r=>{if(r instanceof de)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Pt?this.noMatchError(r):r}))}inheritParamsAndData(t,i){let r=t.value,n=ar(r,i,this.paramsInheritanceStrategy);r.params=Object.freeze(n.params),r.data=Object.freeze(n.data),t.children.forEach(s=>this.inheritParamsAndData(s,r))}processSegmentGroup(t,i,r,n){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,i,r):this.processSegment(t,i,r,r.segments,n,!0).pipe(C(s=>s instanceof T?[s]:[]))}processChildren(t,i,r){let n=[];for(let s of Object.keys(r.children))s==="primary"?n.unshift(s):n.push(s);return A(n).pipe(et(s=>{let o=r.children[s],a=Oi(i,s);return this.processSegmentGroup(t,a,o,s)}),Ir((s,o)=>(s.push(...o),s)),Re(null),br(),M(s=>{if(s===null)return ot(r);let o=Ln(s);return ls(o),d(o)}))}processSegment(t,i,r,n,s,o){return A(i).pipe(et(a=>{var c;return this.processSegmentAgainstRoute((c=a._injector)!=null?c:t,i,a,r,n,s,o).pipe(tt(u=>{if(u instanceof Pt)return d(null);throw u}))}),V(a=>!!a),tt(a=>{if(Nn(a))return as(r,n,s)?d(new ir):ot(r);throw a}))}processSegmentAgainstRoute(t,i,r,n,s,o,a){return os(r,n,s,o)?r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,n,r,s,o):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,n,i,r,s,o):ot(n):ot(n)}expandSegmentAgainstRouteUsingRedirect(t,i,r,n,s,o){let{matched:a,consumedSegments:c,positionalParamSegments:u,remainingSegments:l}=lr(i,n,s);if(!a)return ot(i);n.redirectTo.startsWith("/")&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>cs&&(this.allowRedirects=!1));let f=this.applyRedirects.applyRedirectCommands(c,n.redirectTo,u);return this.applyRedirects.lineralizeSegments(n,f).pipe(M(y=>this.processSegment(t,r,i,y.concat(l),o,!1)))}matchSegmentAgainstRoute(t,i,r,n,s){let o=ts(i,r,n,t,this.urlSerializer);return r.path==="**"&&(i.children={}),o.pipe(P(a=>{var c;return a.matched?(t=(c=r._injector)!=null?c:t,this.getChildConfig(t,r,n).pipe(P(({routes:u})=>{var qt,Vt,gr;let l=(qt=r._loadedInjector)!=null?qt:t,{consumedSegments:f,remainingSegments:y,parameters:v}=a,b=new Ut(f,v,Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,ds(r),L(r),(gr=(Vt=r.component)!=null?Vt:r._loadedComponent)!=null?gr:null,r,fs(r)),{segmentGroup:z,slicedSegments:F}=ln(i,f,y,u);if(F.length===0&&z.hasChildren())return this.processChildren(l,u,z).pipe(C(gt=>gt===null?null:new T(b,gt)));if(u.length===0&&F.length===0)return d(new T(b,[]));let ve=L(r)===s;return this.processSegment(l,u,z,F,ve?g:s,!0).pipe(C(gt=>new T(b,gt instanceof T?[gt]:[])))}))):ot(i)}))}getChildConfig(t,i,r){return i.children?d({routes:i.children,injector:t}):i.loadChildren?i._loadedRoutes!==void 0?d({routes:i._loadedRoutes,injector:i._loadedInjector}):Ki(t,i,r,this.urlSerializer).pipe(M(n=>n?this.configLoader.loadChildren(t,i).pipe(w(s=>{i._loadedRoutes=s.routes,i._loadedInjector=s.injector})):Ji(i))):d({routes:[],injector:t})}};function ls(e){e.sort((t,i)=>t.value.outlet===g?-1:i.value.outlet===g?1:t.value.outlet.localeCompare(i.value.outlet))}function hs(e){let t=e.value.routeConfig;return t&&t.path===""}function Ln(e){let t=[],i=new Set;for(let r of e){if(!hs(r)){t.push(r);continue}let n=t.find(s=>r.value.routeConfig===s.value.routeConfig);n!==void 0?(n.children.push(...r.children),i.add(n)):t.push(r)}for(let r of i){let n=Ln(r.children);t.push(new T(r.value,n))}return t.filter(r=>!i.has(r))}function ds(e){return e.data||{}}function fs(e){return e.resolve||{}}function ps(e,t,i,r,n,s){return M(o=>us(e,t,i,r,o.extractedUrl,n,s).pipe(C(({state:a,tree:c})=>D(h({},o),{targetSnapshot:a,urlAfterRedirects:c}))))}function gs(e,t){return M(i=>{let{targetSnapshot:r,guards:{canActivateChecks:n}}=i;if(!n.length)return d(i);let s=new Set(n.map(c=>c.route)),o=new Set;for(let c of s)if(!o.has(c))for(let u of kn(c))o.add(u);let a=0;return A(o).pipe(et(c=>s.has(c)?vs(c,r,e,t):(c.data=ar(c,c.parent,e).resolve,d(void 0))),w(()=>a++),Se(1),M(c=>a===o.size?d(i):q))})}function kn(e){let t=e.children.map(i=>kn(i)).flat();return[e,...t]}function vs(e,t,i,r){let n=e.routeConfig,s=e._resolve;return(n==null?void 0:n.title)!==void 0&&!On(n)&&(s[Nt]=n.title),ms(s,e,t,r).pipe(C(o=>(e._resolvedData=o,e.data=ar(e,e.parent,i).resolve,null)))}function ms(e,t,i,r){let n=je(e);if(n.length===0)return d({});let s={};return A(n).pipe(M(o=>Cs(e[o],t,i,r).pipe(V(),w(a=>{s[o]=a}))),Se(1),wr(s),tt(o=>Nn(o)?q:vt(o)))}function Cs(e,t,i,r){var a;let n=(a=kt(t))!=null?a:r,s=pt(e,n),o=s.resolve?s.resolve(t,i):H(n,()=>s(t,i));return K(o)}function $e(e){return P(t=>{let i=e(t);return i?A(i).pipe(C(()=>t)):d(t)})}var zn=(()=>{let t=class t{buildTitle(r){var o;let n,s=r.root;for(;s!==void 0;)n=(o=this.getResolvedTitleForRoute(s))!=null?o:n,s=s.children.find(a=>a.outlet===g);return n}getResolvedTitleForRoute(r){return r.data[Nt]}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:()=>p(ys),providedIn:"root"});let e=t;return e})(),ys=(()=>{let t=class t extends zn{constructor(r){super(),this.title=r}updateTitle(r){let n=this.buildTitle(r);n!==void 0&&this.title.setTitle(n)}};t.\u0275fac=function(n){return new(n||t)(B(nn))},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Ft=new U("",{providedIn:"root",factory:()=>({})}),$t=new U(""),hr=(()=>{let t=class t{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=p(Zt)}loadComponent(r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return d(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let n=K(r.loadComponent()).pipe(C(Fn),w(o=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=o}),Ht(()=>{this.componentLoaders.delete(r)})),s=new Ce(n,()=>new _).pipe(me());return this.componentLoaders.set(r,s),s}loadChildren(r,n){if(this.childrenLoaders.get(n))return this.childrenLoaders.get(n);if(n._loadedRoutes)return d({routes:n._loadedRoutes,injector:n._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(n);let o=Rs(n,this.compiler,r,this.onLoadEndListener).pipe(Ht(()=>{this.childrenLoaders.delete(n)})),a=new Ce(o,()=>new _).pipe(me());return this.childrenLoaders.set(n,a),a}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function Rs(e,t,i,r){return K(e.loadChildren()).pipe(C(Fn),M(n=>n instanceof zr||Array.isArray(n)?d(n):A(t.compileModuleAsync(n))),C(n=>{r&&r(e);let s,o,a=!1;return Array.isArray(n)?(o=n,a=!0):(s=n.create(i).injector,o=s.get($t,[],{optional:!0,self:!0}).flat()),{routes:o.map(cr),injector:s}}))}function Ss(e){return e&&typeof e=="object"&&"default"in e}function Fn(e){return Ss(e)?e.default:e}var dr=(()=>{let t=class t{};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:()=>p(ws),providedIn:"root"});let e=t;return e})(),ws=(()=>{let t=class t{shouldProcessUrl(r){return!0}extract(r){return r}merge(r,n){return r}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),_n=new U(""),qn=new U("");function bs(e,t,i){let r=e.get(qn),n=e.get(Xr);return e.get(st).runOutsideAngular(()=>{if(!n.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let s,o=new Promise(u=>{s=u}),a=n.startViewTransition(()=>(s(),Is(e))),{onViewTransitionCreated:c}=r;return c&&H(e,()=>c({transition:a,from:t,to:i})),o})}function Is(e){return new Promise(t=>{Me(t,{injector:e})})}var fr=(()=>{let t=class t{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new _,this.transitionAbortSubject=new _,this.configLoader=p(hr),this.environmentInjector=p(nt),this.urlSerializer=p(jt),this.rootContexts=p(Lt),this.location=p(yt),this.inputBindingEnabled=p(pe,{optional:!0})!==null,this.titleStrategy=p(zn),this.options=p(Ft,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=p(dr),this.createViewTransition=p(_n,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>d(void 0),this.rootComponentType=null;let r=s=>this.events.next(new Be(s)),n=s=>this.events.next(new He(s));this.configLoader.onLoadEndListener=n,this.configLoader.onLoadStartListener=r}complete(){var r;(r=this.transitions)==null||r.complete()}handleNavigationRequest(r){var s;let n=++this.navigationId;(s=this.transitions)==null||s.next(D(h(h({},this.transitions.value),r),{id:n}))}setupNavigations(r,n,s){return this.transitions=new O({id:0,currentUrlTree:n,currentRawUrl:n,extractedUrl:this.urlHandlingStrategy.extract(n),urlAfterRedirects:this.urlHandlingStrategy.extract(n),rawUrl:n,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:It,restoredState:null,currentSnapshot:s.snapshot,targetSnapshot:null,currentRouterState:s,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Y(o=>o.id!==0),C(o=>D(h({},o),{extractedUrl:this.urlHandlingStrategy.extract(o.rawUrl)})),P(o=>{let a=!1,c=!1;return d(o).pipe(P(u=>{var y;if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",E.SupersededByNewNavigation),q;this.currentTransition=o,this.currentNavigation={id:u.id,initialUrl:u.rawUrl,extractedUrl:u.extractedUrl,trigger:u.source,extras:u.extras,previousNavigation:this.lastSuccessfulNavigation?D(h({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let l=!r.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),f=(y=u.extras.onSameUrlNavigation)!=null?y:r.onSameUrlNavigation;if(!l&&f!=="reload"){let v="";return this.events.next(new Q(u.id,this.urlSerializer.serialize(u.rawUrl),v,se.IgnoredSameUrlNavigation)),u.resolve(null),q}if(this.urlHandlingStrategy.shouldProcessUrl(u.rawUrl))return d(u).pipe(P(v=>{var z,F;let b=(z=this.transitions)==null?void 0:z.getValue();return this.events.next(new dt(v.id,this.urlSerializer.serialize(v.extractedUrl),v.source,v.restoredState)),b!==((F=this.transitions)==null?void 0:F.getValue())?q:Promise.resolve(v)}),ps(this.environmentInjector,this.configLoader,this.rootComponentType,r.config,this.urlSerializer,this.paramsInheritanceStrategy),w(v=>{o.targetSnapshot=v.targetSnapshot,o.urlAfterRedirects=v.urlAfterRedirects,this.currentNavigation=D(h({},this.currentNavigation),{finalUrl:v.urlAfterRedirects});let b=new oe(v.id,this.urlSerializer.serialize(v.extractedUrl),this.urlSerializer.serialize(v.urlAfterRedirects),v.targetSnapshot);this.events.next(b)}));if(l&&this.urlHandlingStrategy.shouldProcessUrl(u.currentRawUrl)){let{id:v,extractedUrl:b,source:z,restoredState:F,extras:ve}=u,qt=new dt(v,this.urlSerializer.serialize(b),z,F);this.events.next(qt);let Vt=Tn(this.rootComponentType).snapshot;return this.currentTransition=o=D(h({},u),{targetSnapshot:Vt,urlAfterRedirects:b,extras:D(h({},ve),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=b,d(o)}else{let v="";return this.events.next(new Q(u.id,this.urlSerializer.serialize(u.extractedUrl),v,se.IgnoredByUrlHandlingStrategy)),u.resolve(null),q}}),w(u=>{let l=new Fe(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}),C(u=>(this.currentTransition=o=D(h({},u),{guards:xi(u.targetSnapshot,u.currentSnapshot,this.rootContexts)}),o)),qi(this.environmentInjector,u=>this.events.next(u)),w(u=>{if(o.guardsResult=u.guardsResult,ht(u.guardsResult))throw xn(this.urlSerializer,u.guardsResult);let l=new _e(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot,!!u.guardsResult);this.events.next(l)}),Y(u=>u.guardsResult?!0:(this.cancelNavigationTransition(u,"",E.GuardRejected),!1)),$e(u=>{if(u.guards.canActivateChecks.length)return d(u).pipe(w(l=>{let f=new qe(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(f)}),P(l=>{let f=!1;return d(l).pipe(gs(this.paramsInheritanceStrategy,this.environmentInjector),w({next:()=>f=!0,complete:()=>{f||this.cancelNavigationTransition(l,"",E.NoDataFromResolver)}}))}),w(l=>{let f=new Ve(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(f)}))}),$e(u=>{let l=f=>{var v;let y=[];(v=f.routeConfig)!=null&&v.loadComponent&&!f.routeConfig._loadedComponent&&y.push(this.configLoader.loadComponent(f.routeConfig).pipe(w(b=>{f.component=b}),C(()=>{})));for(let b of f.children)y.push(...l(b));return y};return Bt(l(u.targetSnapshot.root)).pipe(Re(null),rt(1))}),$e(()=>this.afterPreactivation()),P(()=>{var y;let{currentSnapshot:u,targetSnapshot:l}=o,f=(y=this.createViewTransition)==null?void 0:y.call(this,this.environmentInjector,u.root,l.root);return f?A(f).pipe(C(()=>o)):d(o)}),C(u=>{let l=Ii(r.routeReuseStrategy,u.targetSnapshot,u.currentRouterState);return this.currentTransition=o=D(h({},u),{targetRouterState:l}),this.currentNavigation.targetRouterState=l,o}),w(()=>{this.events.next(new Et)}),Ui(this.rootContexts,r.routeReuseStrategy,u=>this.events.next(u),this.inputBindingEnabled),rt(1),w({next:u=>{var l;a=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new N(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects))),(l=this.titleStrategy)==null||l.updateTitle(u.targetRouterState.snapshot),u.resolve(!0)},complete:()=>{a=!0}}),Dr(this.transitionAbortSubject.pipe(w(u=>{throw u}))),Ht(()=>{var u;!a&&!c&&this.cancelNavigationTransition(o,"",E.SupersededByNewNavigation),((u=this.currentTransition)==null?void 0:u.id)===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),tt(u=>{var l;if(c=!0,$n(u))this.events.next(new G(o.id,this.urlSerializer.serialize(o.extractedUrl),u.message,u.cancellationCode)),Mi(u)?this.events.next(new Ot(u.url)):o.resolve(!1);else{this.events.next(new Tt(o.id,this.urlSerializer.serialize(o.extractedUrl),u,(l=o.targetSnapshot)!=null?l:void 0));try{o.resolve(r.errorHandler(u))}catch(f){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(f)}}return q}))}))}cancelNavigationTransition(r,n,s){let o=new G(r.id,this.urlSerializer.serialize(r.extractedUrl),n,s);this.events.next(o),r.resolve(!1)}isUpdatingInternalState(){var r,n;return((r=this.currentTransition)==null?void 0:r.extractedUrl.toString())!==((n=this.currentTransition)==null?void 0:n.currentUrlTree.toString())}isUpdatedBrowserUrl(){var n,s;return this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))).toString()!==((n=this.currentTransition)==null?void 0:n.extractedUrl.toString())&&!((s=this.currentTransition)!=null&&s.extras.skipLocationChange)}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function As(e){return e!==It}var Ds=(()=>{let t=class t{};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:()=>p(Ms),providedIn:"root"});let e=t;return e})(),or=class{shouldDetach(t){return!1}store(t,i){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,i){return t.routeConfig===i.routeConfig}},Ms=(()=>{let t=class t extends or{};t.\u0275fac=(()=>{let r;return function(s){return(r||(r=be(t)))(s||t)}})(),t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Vn=(()=>{let t=class t{};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:()=>p(Ts),providedIn:"root"});let e=t;return e})(),Ts=(()=>{let t=class t extends Vn{constructor(){super(...arguments),this.location=p(yt),this.urlSerializer=p(jt),this.options=p(Ft,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=p(dr),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new W,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=Tn(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){var r,n;return this.canceledNavigationResolution!=="computed"?this.currentPageId:(n=(r=this.restoredState())==null?void 0:r.\u0275routerPageId)!=null?n:this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(r){return this.location.subscribe(n=>{n.type==="popstate"&&r(n.url,n.state)})}handleRouterEvent(r,n){if(r instanceof dt)this.stateMemento=this.createStateMemento();else if(r instanceof Q)this.rawUrlTree=n.initialUrl;else if(r instanceof oe){if(this.urlUpdateStrategy==="eager"&&!n.extras.skipLocationChange){let s=this.urlHandlingStrategy.merge(n.finalUrl,n.initialUrl);this.setBrowserUrl(s,n)}}else r instanceof Et?(this.currentUrlTree=n.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(n.finalUrl,n.initialUrl),this.routerState=n.targetRouterState,this.urlUpdateStrategy==="deferred"&&(n.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,n))):r instanceof G&&(r.code===E.GuardRejected||r.code===E.NoDataFromResolver)?this.restoreHistory(n):r instanceof Tt?this.restoreHistory(n,!0):r instanceof N&&(this.lastSuccessfulId=r.id,this.currentPageId=this.browserPageId)}setBrowserUrl(r,n){let s=this.urlSerializer.serialize(r);if(this.location.isCurrentPathEqualTo(s)||n.extras.replaceUrl){let o=this.browserPageId,a=h(h({},n.extras.state),this.generateNgRouterState(n.id,o));this.location.replaceState(s,"",a)}else{let o=h(h({},n.extras.state),this.generateNgRouterState(n.id,this.browserPageId+1));this.location.go(s,"",o)}}restoreHistory(r,n=!1){if(this.canceledNavigationResolution==="computed"){let s=this.browserPageId,o=this.currentPageId-s;o!==0?this.location.historyGo(o):this.currentUrlTree===r.finalUrl&&o===0&&(this.resetState(r),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(n&&this.resetState(r),this.resetUrlToCurrentUrlTree())}resetState(r){var n;this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,(n=r.finalUrl)!=null?n:this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(r,n){return this.canceledNavigationResolution==="computed"?{navigationId:r,\u0275routerPageId:n}:{navigationId:r}}};t.\u0275fac=(()=>{let r;return function(s){return(r||(r=be(t)))(s||t)}})(),t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),wt=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(wt||{});function Bn(e,t){e.events.pipe(Y(i=>i instanceof N||i instanceof G||i instanceof Tt||i instanceof Q),C(i=>i instanceof N||i instanceof Q?wt.COMPLETE:(i instanceof G?i.code===E.Redirect||i.code===E.SupersededByNewNavigation:!1)?wt.REDIRECTING:wt.FAILED),Y(i=>i!==wt.REDIRECTING),rt(1)).subscribe(()=>{t()})}function Es(e){throw e}var Os={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Us={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},k=(()=>{let t=class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){var r,n;this.disposed=!1,this.isNgZoneEnabled=!1,this.console=p(Qr),this.stateManager=p(Vn),this.options=p(Ft,{optional:!0})||{},this.pendingTasks=p(Fr),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=p(fr),this.urlSerializer=p(jt),this.location=p(yt),this.urlHandlingStrategy=p(dr),this._events=new _,this.errorHandler=this.options.errorHandler||Es,this.navigated=!1,this.routeReuseStrategy=p(Ds),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=(n=(r=p($t,{optional:!0}))==null?void 0:r.flat())!=null?n:[],this.componentInputBindingEnabled=!!p(pe,{optional:!0}),this.eventsSubscription=new mr,this.isNgZoneEnabled=p(st)instanceof st&&st.isInAngularZone(),this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:s=>{this.console.warn(s)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let r=this.navigationTransitions.events.subscribe(n=>{try{let s=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(s!==null&&o!==null){if(this.stateManager.handleRouterEvent(n,o),n instanceof G&&n.code!==E.Redirect&&n.code!==E.SupersededByNewNavigation)this.navigated=!0;else if(n instanceof N)this.navigated=!0;else if(n instanceof Ot){let a=this.urlHandlingStrategy.merge(n.url,s.currentRawUrl),c={info:s.extras.info,skipLocationChange:s.extras.skipLocationChange,replaceUrl:this.urlUpdateStrategy==="eager"||As(s.source)};this.scheduleNavigation(a,It,null,c,{resolve:s.resolve,reject:s.reject,promise:s.promise})}}Ps(n)&&this._events.next(n)}catch(s){this.navigationTransitions.transitionAbortSubject.next(s)}});this.eventsSubscription.add(r)}resetRootComponentType(r){this.routerState.root.component=r,this.navigationTransitions.rootComponentType=r}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),It,this.stateManager.restoredState())}setUpLocationChangeListener(){var r;(r=this.nonRouterCurrentEntryChangeSubscription)!=null||(this.nonRouterCurrentEntryChangeSubscription=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,s)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",s)},0)}))}navigateToSyncWithBrowser(r,n,s){let o={replaceUrl:!0},a=s!=null&&s.navigationId?s:null;if(s){let u=h({},s);delete u.navigationId,delete u.\u0275routerPageId,Object.keys(u).length!==0&&(o.state=u)}let c=this.parseUrl(r);this.scheduleNavigation(c,n,a,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(r){this.config=r.map(cr),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(r,n={}){let{relativeTo:s,queryParams:o,fragment:a,queryParamsHandling:c,preserveFragment:u}=n,l=u?this.currentUrlTree.fragment:a,f=null;switch(c){case"merge":f=h(h({},this.currentUrlTree.queryParams),o);break;case"preserve":f=this.currentUrlTree.queryParams;break;default:f=o||null}f!==null&&(f=this.removeEmptyProps(f));let y;try{let v=s?s.snapshot:this.routerState.snapshot.root;y=In(v)}catch{(typeof r[0]!="string"||!r[0].startsWith("/"))&&(r=[]),y=this.currentUrlTree.root}return An(y,r,f,l!=null?l:null)}navigateByUrl(r,n={skipLocationChange:!1}){let s=ht(r)?r:this.parseUrl(r),o=this.urlHandlingStrategy.merge(s,this.rawUrlTree);return this.scheduleNavigation(o,It,null,n)}navigate(r,n={skipLocationChange:!1}){return xs(r),this.navigateByUrl(this.createUrlTree(r,n),n)}serializeUrl(r){return this.urlSerializer.serialize(r)}parseUrl(r){try{return this.urlSerializer.parse(r)}catch{return this.urlSerializer.parse("/")}}isActive(r,n){let s;if(n===!0?s=h({},Os):n===!1?s=h({},Us):s=n,ht(r))return sn(this.currentUrlTree,r,s);let o=this.parseUrl(r);return sn(this.currentUrlTree,o,s)}removeEmptyProps(r){return Object.entries(r).reduce((n,[s,o])=>(o!=null&&(n[s]=o),n),{})}scheduleNavigation(r,n,s,o,a){if(this.disposed)return Promise.resolve(!1);let c,u,l;a?(c=a.resolve,u=a.reject,l=a.promise):l=new Promise((y,v)=>{c=y,u=v});let f=this.pendingTasks.add();return Bn(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(f))}),this.navigationTransitions.handleNavigationRequest({source:n,restoredState:s,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:r,extras:o,resolve:c,reject:u,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(y=>Promise.reject(y))}};t.\u0275fac=function(n){return new(n||t)},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function xs(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new I(4008,!1)}function Ps(e){return!(e instanceof Et)&&!(e instanceof Ot)}var hn=(()=>{let t=class t{constructor(r,n,s,o,a,c){var l;this.router=r,this.route=n,this.tabIndexAttribute=s,this.renderer=o,this.el=a,this.locationStrategy=c,this.href=null,this.commands=null,this.onChanges=new _,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;let u=(l=a.nativeElement.tagName)==null?void 0:l.toLowerCase();this.isAnchorElement=u==="a"||u==="area",this.isAnchorElement?this.subscription=r.events.subscribe(f=>{f instanceof N&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(r){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",r)}ngOnChanges(r){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(r){r!=null?(this.commands=Array.isArray(r)?r:[r],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(r,n,s,o,a){let c=this.urlTree;if(c===null||this.isAnchorElement&&(r!==0||n||s||o||a||typeof this.target=="string"&&this.target!="_self"))return!0;let u={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(c,u),!this.isAnchorElement}ngOnDestroy(){var r;(r=this.subscription)==null||r.unsubscribe()}updateHref(){var s;let r=this.urlTree;this.href=r!==null&&this.locationStrategy?(s=this.locationStrategy)==null?void 0:s.prepareExternalUrl(this.router.serializeUrl(r)):null;let n=this.href===null?null:Nr(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",n)}applyAttributeValue(r,n){let s=this.renderer,o=this.el.nativeElement;n!==null?s.setAttribute(o,r,n):s.removeAttribute(o,r)}get urlTree(){return this.commands===null?null:this.router.createUrlTree(this.commands,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}};t.\u0275fac=function(n){return new(n||t)($(k),$(Z),$r("tabindex"),$(De),$(Ae),$(Ct))},t.\u0275dir=Gt({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(n,s){n&1&&Vr("click",function(a){return s.onClick(a.button,a.ctrlKey,a.shiftKey,a.altKey,a.metaKey)}),n&2&&_r("target",s.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[Wt.HasDecoratorInputTransform,"preserveFragment","preserveFragment",Yt],skipLocationChange:[Wt.HasDecoratorInputTransform,"skipLocationChange","skipLocationChange",Yt],replaceUrl:[Wt.HasDecoratorInputTransform,"replaceUrl","replaceUrl",Yt],routerLink:"routerLink"},standalone:!0,features:[kr,Qt]});let e=t;return e})(),Co=(()=>{let t=class t{get isActive(){return this._isActive}constructor(r,n,s,o,a){this.router=r,this.element=n,this.renderer=s,this.cdr=o,this.link=a,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new it,this.routerEventsSubscription=r.events.subscribe(c=>{c instanceof N&&this.update()})}ngAfterContentInit(){d(this.links.changes,d(null)).pipe(mt()).subscribe(r=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){var n;(n=this.linkInputChangesSubscription)==null||n.unsubscribe();let r=[...this.links.toArray(),this.link].filter(s=>!!s).map(s=>s.onChanges);this.linkInputChangesSubscription=A(r).pipe(mt()).subscribe(s=>{this._isActive!==this.isLinkActive(this.router)(s)&&this.update()})}set routerLinkActive(r){let n=Array.isArray(r)?r:r.split(" ");this.classes=n.filter(s=>!!s)}ngOnChanges(r){this.update()}ngOnDestroy(){var r;this.routerEventsSubscription.unsubscribe(),(r=this.linkInputChangesSubscription)==null||r.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let r=this.hasActiveLinks();this.classes.forEach(n=>{r?this.renderer.addClass(this.element.nativeElement,n):this.renderer.removeClass(this.element.nativeElement,n)}),r&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==r&&(this._isActive=r,this.cdr.markForCheck(),this.isActiveChange.emit(r))})}isLinkActive(r){let n=$s(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return s=>{let o=s.urlTree;return o?r.isActive(o,n):!1}}hasActiveLinks(){let r=this.isLinkActive(this.router);return this.link&&r(this.link)||this.links.some(r)}};t.\u0275fac=function(n){return new(n||t)($(k),$(Ae),$(De),$(Kt),$(hn,8))},t.\u0275dir=Gt({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(n,s,o){if(n&1&&Br(o,hn,5),n&2){let a;Hr(a=Wr())&&(s.links=a)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[Qt]});let e=t;return e})();function $s(e){return!!e.paths}var fe=class{};var Ns=(()=>{let t=class t{constructor(r,n,s,o,a){this.router=r,this.injector=s,this.preloadingStrategy=o,this.loader=a}setUpPreloading(){this.subscription=this.router.events.pipe(Y(r=>r instanceof N),et(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(r,n){var o,a,c;let s=[];for(let u of n){u.providers&&!u._injector&&(u._injector=Te(u.providers,r,`Route: ${u.path}`));let l=(o=u._injector)!=null?o:r,f=(a=u._loadedInjector)!=null?a:l;(u.loadChildren&&!u._loadedRoutes&&u.canLoad===void 0||u.loadComponent&&!u._loadedComponent)&&s.push(this.preloadConfig(l,u)),(u.children||u._loadedRoutes)&&s.push(this.processRoutes(f,(c=u.children)!=null?c:u._loadedRoutes))}return A(s).pipe(mt())}preloadConfig(r,n){return this.preloadingStrategy.preload(n,()=>{let s;n.loadChildren&&n.canLoad===void 0?s=this.loader.loadChildren(r,n):s=d(null);let o=s.pipe(M(a=>{var c;return a===null?d(void 0):(n._loadedRoutes=a.routes,n._loadedInjector=a.injector,this.processRoutes((c=a.injector)!=null?c:r,a.routes))}));if(n.loadComponent&&!n._loadedComponent){let a=this.loader.loadComponent(n);return A([o,a]).pipe(mt())}else return o})}};t.\u0275fac=function(n){return new(n||t)(B(k),B(Zt),B(nt),B(fe),B(hr))},t.\u0275prov=S({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Hn=new U(""),js=(()=>{let t=class t{constructor(r,n,s,o,a={}){this.urlSerializer=r,this.transitions=n,this.viewportScroller=s,this.zone=o,this.options=a,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},this.environmentInjector=p(nt),a.scrollPositionRestoration||(a.scrollPositionRestoration="disabled"),a.anchorScrolling||(a.anchorScrolling="disabled")}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(r=>{r instanceof dt?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=r.navigationTrigger,this.restoredId=r.restoredState?r.restoredState.navigationId:0):r instanceof N?(this.lastId=r.id,this.scheduleScrollEvent(r,this.urlSerializer.parse(r.urlAfterRedirects).fragment)):r instanceof Q&&r.code===se.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(r,this.urlSerializer.parse(r.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(r=>{r instanceof ae&&(r.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(r.position):r.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(r.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(r,n){this.zone.runOutsideAngular(()=>vr(this,null,function*(){yield new Promise(s=>{setTimeout(()=>{s()}),Me(()=>{s()},{injector:this.environmentInjector})}),this.zone.run(()=>{this.transitions.events.next(new ae(r,this.lastSource==="popstate"?this.store[this.restoredId]:null,n))})}))}ngOnDestroy(){var r,n;(r=this.routerEventsSubscription)==null||r.unsubscribe(),(n=this.scrollEventsSubscription)==null||n.unsubscribe()}};t.\u0275fac=function(n){jr()},t.\u0275prov=S({token:t,factory:t.\u0275fac});let e=t;return e})();function yo(e,...t){return Pr([{provide:$t,multi:!0,useValue:e},[],{provide:Z,useFactory:Wn,deps:[k]},{provide:Oe,multi:!0,useFactory:Gn},t.map(i=>i.\u0275providers)])}function Wn(e){return e.routerState.root}function _t(e,t){return{\u0275kind:e,\u0275providers:t}}function Gn(){let e=p(Ie);return t=>{var s,o;let i=e.get(Kr);if(t!==i.components[0])return;let r=e.get(k),n=e.get(Qn);e.get(pr)===1&&r.initialNavigation(),(s=e.get(Zn,null,we.Optional))==null||s.setUpPreloading(),(o=e.get(Hn,null,we.Optional))==null||o.init(),r.resetRootComponentType(i.componentTypes[0]),n.closed||(n.next(),n.complete(),n.unsubscribe())}}var Qn=new U("",{factory:()=>new _}),pr=new U("",{providedIn:"root",factory:()=>1});function Ls(){return _t(2,[{provide:pr,useValue:0},{provide:Ee,multi:!0,deps:[Ie],useFactory:t=>{let i=t.get(Jr,Promise.resolve());return()=>i.then(()=>new Promise(r=>{let n=t.get(k),s=t.get(Qn);Bn(n,()=>{r(!0)}),t.get(fr).afterPreactivation=()=>(r(!0),s.closed?d(void 0):s),n.initialNavigation()}))}}])}function ks(){return _t(3,[{provide:Ee,multi:!0,useFactory:()=>{let t=p(k);return()=>{t.setUpLocationChangeListener()}}},{provide:pr,useValue:2}])}var Zn=new U("");function zs(e){return _t(0,[{provide:Zn,useExisting:Ns},{provide:fe,useExisting:e}])}function Fs(){return _t(8,[cn,{provide:pe,useExisting:cn}])}function _s(e){let t=[{provide:_n,useValue:bs},{provide:qn,useValue:h({skipNextTransition:!!(e!=null&&e.skipInitialTransition)},e)}];return _t(9,t)}var dn=new U("ROUTER_FORROOT_GUARD"),qs=[yt,{provide:jt,useClass:Dt},k,Lt,{provide:Z,useFactory:Wn,deps:[k]},hr,[]],Ro=(()=>{let t=class t{constructor(r){}static forRoot(r,n){return{ngModule:t,providers:[qs,[],{provide:$t,multi:!0,useValue:r},{provide:dn,useFactory:Ws,deps:[[k,new Er,new Or]]},{provide:Ft,useValue:n||{}},n!=null&&n.useHash?Bs():Hs(),Vs(),n!=null&&n.preloadingStrategy?zs(n.preloadingStrategy).\u0275providers:[],n!=null&&n.initialNavigation?Gs(n):[],n!=null&&n.bindToComponentInputs?Fs().\u0275providers:[],n!=null&&n.enableViewTransitions?_s().\u0275providers:[],Qs()]}}static forChild(r){return{ngModule:t,providers:[{provide:$t,multi:!0,useValue:r}]}}};t.\u0275fac=function(n){return new(n||t)(B(dn,8))},t.\u0275mod=xr({type:t}),t.\u0275inj=Mr({});let e=t;return e})();function Vs(){return{provide:Hn,useFactory:()=>{let e=p(rn),t=p(st),i=p(Ft),r=p(fr),n=p(jt);return i.scrollOffset&&e.setOffset(i.scrollOffset),new js(n,r,e,t,i)}}}function Bs(){return{provide:Ct,useClass:en}}function Hs(){return{provide:Ct,useClass:tn}}function Ws(e){return"guarded"}function Gs(e){return[e.initialNavigation==="disabled"?ks().\u0275providers:[],e.initialNavigation==="enabledBlocking"?Ls().\u0275providers:[]]}var fn=new U("");function Qs(){return[{provide:fn,useFactory:Gn},{provide:Oe,multi:!0,useExisting:fn}]}export{N as a,Z as b,bi as c,k as d,hn as e,Co as f,yo as g,Ro as h};
