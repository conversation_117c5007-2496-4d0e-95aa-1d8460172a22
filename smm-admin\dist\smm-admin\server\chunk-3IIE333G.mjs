import './polyfills.server.mjs';
import{a as re,b as nt,d as Yc,i as Gr}from"./chunk-2FGBTQRU.mjs";function v(e){return typeof e=="function"}function fs(e){return v(e==null?void 0:e.lift)}function N(e){return t=>{if(fs(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Wr(e){return N((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Qc(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}function Zc(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ot(e){return this instanceof Ot?(this.v=e,this):new Ot(e)}function Kc(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(h){return function(p){return Promise.resolve(p).then(h,d)}}function a(h,p){r[h]&&(o[h]=function(y){return new Promise(function(m,g){i.push([h,y,m,g])>1||u(h,y)})},p&&(o[h]=p(o[h])))}function u(h,p){try{c(r[h](p))}catch(y){f(i[0][3],y)}}function c(h){h.value instanceof Ot?Promise.resolve(h.value.v).then(l,d):f(i[0][2],h)}function l(h){u("next",h)}function d(h){u("throw",h)}function f(h,p){h(p),i.shift(),i.length&&u(i[0][0],i[0][1])}}function Jc(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Zc=="function"?Zc(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var nn=e=>e&&typeof e.length=="number"&&typeof e!="function";function qr(e){return v(e==null?void 0:e.then)}function rn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Yr=rn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Rt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Y=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(v(r))try{r()}catch(i){t=i instanceof Yr?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Xc(i)}catch(s){t=t!=null?t:[],s instanceof Yr?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Yr(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Xc(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Rt(n,t)}remove(t){let{_finalizers:n}=this;n&&Rt(n,t),t instanceof e&&t._removeParent(this)}};Y.EMPTY=(()=>{let e=new Y;return e.closed=!0,e})();var hs=Y.EMPTY;function Zr(e){return e instanceof Y||e&&"closed"in e&&v(e.remove)&&v(e.add)&&v(e.unsubscribe)}function Xc(e){v(e)?e():e.unsubscribe()}var Pe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var on={setTimeout(e,t,...n){let{delegate:r}=on;return r!=null&&r.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=on;return((t==null?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function Qr(e){on.setTimeout(()=>{let{onUnhandledError:t}=Pe;if(t)t(e);else throw e})}function Zn(){}var el=ps("C",void 0,void 0);function tl(e){return ps("E",void 0,e)}function nl(e){return ps("N",e,void 0)}function ps(e,t,n){return{kind:e,value:t,error:n}}var Pt=null;function sn(e){if(Pe.useDeprecatedSynchronousErrorHandling){let t=!Pt;if(t&&(Pt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Pt;if(Pt=null,n)throw r}}else e()}function rl(e){Pe.useDeprecatedSynchronousErrorHandling&&Pt&&(Pt.errorThrown=!0,Pt.error=e)}var Ft=class extends Y{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Zr(t)&&t.add(this)):this.destination=kg}static create(t,n,r){return new Fe(t,n,r)}next(t){this.isStopped?ms(nl(t),this):this._next(t)}error(t){this.isStopped?ms(tl(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?ms(el,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Pg=Function.prototype.bind;function gs(e,t){return Pg.call(e,t)}var ys=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Kr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Kr(r)}else Kr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Kr(n)}}},Fe=class extends Ft{constructor(t,n,r){super();let o;if(v(t)||!t)o={next:t!=null?t:void 0,error:n!=null?n:void 0,complete:r!=null?r:void 0};else{let i;this&&Pe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&gs(t.next,i),error:t.error&&gs(t.error,i),complete:t.complete&&gs(t.complete,i)}):o=t}this.destination=new ys(o)}};function Kr(e){Pe.useDeprecatedSynchronousErrorHandling?rl(e):Qr(e)}function Fg(e){throw e}function ms(e,t){let{onStoppedNotification:n}=Pe;n&&on.setTimeout(()=>n(e,t))}var kg={closed:!0,next:Zn,error:Fg,complete:Zn};var an=typeof Symbol=="function"&&Symbol.observable||"@@observable";function we(e){return e}function Lg(...e){return vs(e)}function vs(e){return e.length===0?we:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var A=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Vg(n)?n:new Fe(n,r,o);return sn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=ol(r),new r((o,i)=>{let s=new Fe({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[an](){return this}pipe(...n){return vs(n)(this)}toPromise(n){return n=ol(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function ol(e){var t;return(t=e!=null?e:Pe.Promise)!==null&&t!==void 0?t:Promise}function jg(e){return e&&v(e.next)&&v(e.error)&&v(e.complete)}function Vg(e){return e&&e instanceof Ft||jg(e)&&Zr(e)}function Jr(e){return v(e[an])}function Xr(e){return Symbol.asyncIterator&&v(e==null?void 0:e[Symbol.asyncIterator])}function eo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Bg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var to=Bg();function no(e){return v(e==null?void 0:e[to])}function ro(e){return Kc(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ot(n.read());if(o)return yield Ot(void 0);yield yield Ot(r)}}finally{n.releaseLock()}})}function oo(e){return v(e==null?void 0:e.getReader)}function j(e){if(e instanceof A)return e;if(e!=null){if(Jr(e))return $g(e);if(nn(e))return Ug(e);if(qr(e))return Hg(e);if(Xr(e))return il(e);if(no(e))return zg(e);if(oo(e))return Gg(e)}throw eo(e)}function $g(e){return new A(t=>{let n=e[an]();if(v(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Ug(e){return new A(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Hg(e){return new A(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Qr)})}function zg(e){return new A(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function il(e){return new A(t=>{Wg(e,t).catch(n=>t.error(n))})}function Gg(e){return il(ro(e))}function Wg(e,t){var n,r,o,i;return Qc(this,void 0,void 0,function*(){try{for(n=Jc(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function S(e,t,n,r,o){return new Ds(e,t,n,r,o)}var Ds=class extends Ft{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function sl(e){return N((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i==null||i.unsubscribe(),i=null,r){r=!1;let c=o;o=null,n.next(c)}s&&n.complete()},u=()=>{i=null,s&&n.complete()};t.subscribe(S(n,c=>{r=!0,o=c,i||j(e(c)).subscribe(i=S(n,a,u))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}var io=class extends Y{constructor(t,n){super()}schedule(t,n=0){return this}};var Qn={setInterval(e,t,...n){let{delegate:r}=Qn;return r!=null&&r.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Qn;return((t==null?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0};var un=class extends io{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Qn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Qn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Rt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Kn={now(){return(Kn.delegate||Date).now()},delegate:void 0};var cn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};cn.now=Kn.now;var ln=class extends cn{constructor(t,n=cn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var kt=new ln(un),al=kt;function so(e){return e&&v(e.schedule)}function ul(e){return e instanceof Date&&!isNaN(e)}function ao(e=0,t,n=al){let r=-1;return t!=null&&(so(t)?n=t:r=t),new A(o=>{let i=ul(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function qg(e,t=kt){return sl(()=>ao(e,t))}function ws(e){return e[e.length-1]}function uo(e){return v(ws(e))?e.pop():void 0}function We(e){return so(ws(e))?e.pop():void 0}function cl(e,t){return typeof ws(e)=="number"?e.pop():t}function he(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Es(e){return N((t,n)=>{let r=null,o=!1,i;r=t.subscribe(S(n,void 0,void 0,s=>{i=j(e(s,Es(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}var{isArray:Yg}=Array,{getPrototypeOf:Zg,prototype:Qg,keys:Kg}=Object;function co(e){if(e.length===1){let t=e[0];if(Yg(t))return{args:t,keys:null};if(Jg(t)){let n=Kg(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Jg(e){return e&&typeof e=="object"&&Zg(e)===Qg}function lo(e,t=0){return N((n,r)=>{n.subscribe(S(r,o=>he(r,e,()=>r.next(o),t),()=>he(r,e,()=>r.complete(),t),o=>he(r,e,()=>r.error(o),t)))})}function fo(e,t=0){return N((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function ll(e,t){return j(e).pipe(fo(t),lo(t))}function dl(e,t){return j(e).pipe(fo(t),lo(t))}function fl(e,t){return new A(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function hl(e,t){return new A(n=>{let r;return he(n,t,()=>{r=e[to](),he(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>v(r==null?void 0:r.return)&&r.return()})}function ho(e,t){if(!e)throw new Error("Iterable cannot be null");return new A(n=>{he(n,t,()=>{let r=e[Symbol.asyncIterator]();he(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function pl(e,t){return ho(ro(e),t)}function gl(e,t){if(e!=null){if(Jr(e))return ll(e,t);if(nn(e))return fl(e,t);if(qr(e))return dl(e,t);if(Xr(e))return ho(e,t);if(no(e))return hl(e,t);if(oo(e))return pl(e,t)}throw eo(e)}function xe(e,t){return t?gl(e,t):j(e)}function ae(e,t){return N((n,r)=>{let o=0;n.subscribe(S(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Xg}=Array;function em(e,t){return Xg(t)?e(...t):e(t)}function dn(e){return ae(t=>em(e,t))}function po(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function tm(...e){let t=We(e),n=uo(e),{args:r,keys:o}=co(e);if(r.length===0)return xe([],t);let i=new A(nm(r,t,o?s=>po(o,s):we));return n?i.pipe(dn(n)):i}function nm(e,t,n=we){return r=>{ml(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)ml(t,()=>{let c=xe(e[u],t),l=!1;c.subscribe(S(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ml(e,t,n){e?he(n,e,t):t()}function yl(e,t,n,r,o,i,s,a){let u=[],c=0,l=0,d=!1,f=()=>{d&&!u.length&&!c&&t.complete()},h=y=>c<r?p(y):u.push(y),p=y=>{i&&t.next(y),c++;let m=!1;j(n(y,l++)).subscribe(S(t,g=>{o==null||o(g),i?h(g):t.next(g)},()=>{m=!0},void 0,()=>{if(m)try{for(c--;u.length&&c<r;){let g=u.shift();s?he(t,s,()=>p(g)):p(g)}f()}catch(g){t.error(g)}}))};return e.subscribe(S(t,h,()=>{d=!0,f()})),()=>{a==null||a()}}function rt(e,t,n=1/0){return v(t)?rt((r,o)=>ae((i,s)=>t(r,i,o,s))(j(e(r,o))),n):(typeof t=="number"&&(n=t),N((r,o)=>yl(r,o,e,n)))}function vl(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(S(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function Jn(e=1/0){return rt(we,e)}function Dl(){return Jn(1)}function Is(e,t){return v(t)?rt(e,t,1):rt(e,1)}var wl=rn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var _e=(()=>{class e extends A{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new go(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new wl}next(n){sn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){sn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){sn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?hs:(this.currentObservers=null,i.push(n),new Y(()=>{this.currentObservers=null,Rt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new A;return n.source=this,n}}return e.create=(t,n)=>new go(t,n),e})(),go=class extends _e{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:hs}};function rm(e,t=kt){return N((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),r.add(o);return}a()}n.subscribe(S(r,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Xn(e){return N((t,n)=>{let r=!1;t.subscribe(S(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function mo(...e){return Dl()(xe(e,We(e)))}var Lt=new A(e=>e.complete());function bs(e){return e<=0?()=>Lt:N((t,n)=>{let r=0;t.subscribe(S(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function om(e){return ae(()=>e)}function er(...e){let t=We(e);return xe(e,t)}function im(e,t){let n=v(e)?e:()=>e,r=o=>o.error(n());return new A(t?o=>t.schedule(r,0,o):r)}function jt(e,t){return N((n,r)=>{let o=0;n.subscribe(S(r,i=>e.call(t,i,o++)&&r.next(i)))})}var ot=rn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function yo(e=sm){return N((t,n)=>{let r=!1;t.subscribe(S(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function sm(){return new ot}function Cs(e,t){let n=arguments.length>=2;return r=>r.pipe(e?jt((o,i)=>e(o,i,r)):we,bs(1),n?Xn(t):yo(()=>new ot))}function Ms(e){return e<=0?()=>Lt:N((t,n)=>{let r=[];t.subscribe(S(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function am(e,t){let n=arguments.length>=2;return r=>r.pipe(e?jt((o,i)=>e(o,i,r)):we,Ms(1),n?Xn(t):yo(()=>new ot))}function Ts(){return N((e,t)=>{let n=null;e._refCount++;let r=S(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var xs=class extends A{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,fs(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t==null||t.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Y;let n=this.getSubject();t.add(this.source.subscribe(S(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Y.EMPTY)}return t}refCount(){return Ts()(this)}};var tr=class extends _e{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var vo=class extends _e{constructor(t=1/0,n=1/0,r=Kn){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let u=1;u<r.length&&r[u]<=s;u+=2)a=u;a&&r.splice(0,a+1)}}};function um(e=0,t=kt){return e<0&&(e=0),ao(e,e,t)}function cm(e,t){return N(vl(e,t,arguments.length>=2,!0))}function El(e={}){let{connector:t=()=>new _e,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1,f=()=>{a==null||a.unsubscribe(),a=void 0},h=()=>{f(),s=u=void 0,l=d=!1},p=()=>{let y=s;h(),y==null||y.unsubscribe()};return N((y,m)=>{c++,!d&&!l&&f();let g=u=u!=null?u:t();m.add(()=>{c--,c===0&&!d&&!l&&(a=_s(p,o))}),g.subscribe(m),!s&&c>0&&(s=new Fe({next:_=>g.next(_),error:_=>{d=!0,f(),a=_s(h,n,_),g.error(_)},complete:()=>{l=!0,f(),a=_s(h,r),g.complete()}}),j(y).subscribe(s))})(i)}}function _s(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Fe({next:()=>{r.unsubscribe(),e()}});return j(t(...n)).subscribe(r)}function Il(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e!=null?e:1/0,El({connector:()=>new vo(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function lm(...e){let t=We(e);return N((n,r)=>{(t?mo(e,n,t):mo(e,n)).subscribe(r)})}function Do(e,t){return N((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(S(r,u=>{o==null||o.unsubscribe();let c=0,l=i++;j(e(u,l)).subscribe(o=S(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function dm(e){return N((t,n)=>{j(e).subscribe(S(n,()=>n.complete(),Zn)),!n.closed&&t.subscribe(n)})}function wo(e,t,n){let r=v(e)||t||n?{next:e,error:t,complete:n}:e;return r?N((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(S(i,u=>{var c;(c=r.next)===null||c===void 0||c.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,u),i.error(u)},()=>{var u,c;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):we}function fm(e,t){return Object.is(e,t)}var J=null,Eo=!1,Io=1,gt=Symbol("SIGNAL");function R(e){let t=J;return J=e,t}var Ns={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function As(e){if(Eo)throw new Error("");if(J===null)return;J.consumerOnSignalRead(e);let t=J.nextProducerIndex++;if(fn(J),t<J.producerNode.length&&J.producerNode[t]!==e&&nr(J)){let n=J.producerNode[t];bo(n,J.producerIndexOfThis[t])}J.producerNode[t]!==e&&(J.producerNode[t]=e,J.producerIndexOfThis[t]=nr(J)?_l(e,J,t):0),J.producerLastReadVersion[t]=e.version}function hm(){Io++}function pm(e){if(!(nr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Io)){if(!e.producerMustRecompute(e)&&!Os(e)){e.dirty=!1,e.lastCleanEpoch=Io;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Io}}function bl(e){if(e.liveConsumerNode===void 0)return;let t=Eo;Eo=!0;try{for(let n of e.liveConsumerNode)n.dirty||gm(n)}finally{Eo=t}}function Cl(){return(J==null?void 0:J.consumerAllowSignalWrites)!==!1}function gm(e){var t;e.dirty=!0,bl(e),(t=e.consumerMarkedDirty)==null||t.call(e,e)}function Ml(e){return e&&(e.nextProducerIndex=0),R(e)}function Tl(e,t){if(R(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(nr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)bo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Os(e){fn(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(pm(n),r!==n.version))return!0}return!1}function xl(e){if(fn(e),nr(e))for(let t=0;t<e.producerNode.length;t++)bo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function _l(e,t,n){if(Sl(e),fn(e),e.liveConsumerNode.length===0)for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=_l(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function bo(e,t){if(Sl(e),fn(e),e.liveConsumerNode.length===1)for(let r=0;r<e.producerNode.length;r++)bo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];fn(o),o.producerIndexOfThis[r]=t}}function nr(e){var t,n;return e.consumerIsAlwaysLive||((n=(t=e==null?void 0:e.liveConsumerNode)==null?void 0:t.length)!=null?n:0)>0}function fn(e){var t,n,r;(t=e.producerNode)!=null||(e.producerNode=[]),(n=e.producerIndexOfThis)!=null||(e.producerIndexOfThis=[]),(r=e.producerLastReadVersion)!=null||(e.producerLastReadVersion=[])}function Sl(e){var t,n;(t=e.liveConsumerNode)!=null||(e.liveConsumerNode=[]),(n=e.liveConsumerIndexOfThis)!=null||(e.liveConsumerIndexOfThis=[])}function mm(){throw new Error}var Nl=mm;function Al(){Nl()}function Ol(e){Nl=e}var Ss=null;function Rl(e){let t=Object.create(Rs);t.value=e;let n=()=>(As(t),t.value);return n[gt]=t,n}function Co(e,t){Cl()||Al(),e.equal(e.value,t)||(e.value=t,ym(e))}function Pl(e,t){Cl()||Al(),Co(e,t(e.value))}var Rs=nt(re({},Ns),{equal:fm,value:void 0});function ym(e){e.version++,hm(),bl(e),Ss==null||Ss()}var hn={schedule(e){let t=requestAnimationFrame,n=cancelAnimationFrame,{delegate:r}=hn;r&&(t=r.requestAnimationFrame,n=r.cancelAnimationFrame);let o=t(i=>{n=void 0,e(i)});return new Y(()=>n==null?void 0:n(o))},requestAnimationFrame(...e){let{delegate:t}=hn;return((t==null?void 0:t.requestAnimationFrame)||requestAnimationFrame)(...e)},cancelAnimationFrame(...e){let{delegate:t}=hn;return((t==null?void 0:t.cancelAnimationFrame)||cancelAnimationFrame)(...e)},delegate:void 0};var Mo=class extends un{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}requestAsyncId(t,n,r=0){return r!==null&&r>0?super.requestAsyncId(t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=hn.requestAnimationFrame(()=>t.flush(void 0))))}recycleAsyncId(t,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(t,n,r);let{actions:i}=t;n!=null&&n===t._scheduled&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(hn.cancelAnimationFrame(n),t._scheduled=void 0)}};var To=class extends ln{flush(t){this._active=!0;let n;t?n=t.id:(n=this._scheduled,this._scheduled=void 0);let{actions:r}=this,o;t=t||r.shift();do if(o=t.execute(t.state,t.delay))break;while((t=r[0])&&t.id===n&&r.shift());if(this._active=!1,o){for(;(t=r[0])&&t.id===n&&r.shift();)t.unsubscribe();throw o}}};var vm=new To(Mo);function Dm(e){return!!e&&(e instanceof A||v(e.lift)&&v(e.subscribe))}function wm(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new Fe({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new ot)}});e.subscribe(i)})}function Em(e){return new A(t=>{j(e()).subscribe(t)})}function Im(...e){let t=uo(e),{args:n,keys:r}=co(e),o=new A(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;j(n[l]).subscribe(S(i,f=>{d||(d=!0,c--),a[l]=f},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?po(r,a):a),i.complete())}))}});return t?o.pipe(dn(t)):o}var bm=["addListener","removeListener"],Cm=["addEventListener","removeEventListener"],Mm=["on","off"];function Ps(e,t,n,r){if(v(n)&&(r=n,n=void 0),r)return Ps(e,t,n).pipe(dn(r));let[o,i]=_m(e)?Cm.map(s=>a=>e[s](t,a,n)):Tm(e)?bm.map(Fl(e,t)):xm(e)?Mm.map(Fl(e,t)):[];if(!o&&nn(e))return rt(s=>Ps(s,t,n))(j(e));if(!o)throw new TypeError("Invalid event target");return new A(s=>{let a=(...u)=>s.next(1<u.length?u:u[0]);return o(a),()=>i(a)})}function Fl(e,t){return n=>r=>e[n](t,r)}function Tm(e){return v(e.addListener)&&v(e.removeListener)}function xm(e){return v(e.on)&&v(e.off)}function _m(e){return v(e.addEventListener)&&v(e.removeEventListener)}function Sm(...e){let t=We(e),n=cl(e,1/0),r=e;return r.length?r.length===1?j(r[0]):Jn(n)(xe(r,t)):Lt}var xd="https://g.co/ng/security#xss",w=class extends Error{constructor(t,n){super(gi(t,n)),this.code=t}};function gi(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}var _d=Symbol("InputSignalNode#UNSET"),Nm=nt(re({},Rs),{transformFn:void 0,applyValueToInputSignal(e,t){Co(e,t)}});function Sd(e,t){let n=Object.create(Nm);n.value=e,n.transformFn=t==null?void 0:t.transform;function r(){if(As(n),n.value===_d)throw new w(-950,!1);return n.value}return r[gt]=n,r}function Mr(e){return{toString:e}.toString()}var xo="__parameters__";function Am(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Nd(e,t,n){return Mr(()=>{let r=Am(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(xo)?u[xo]:Object.defineProperty(u,xo,{value:[]})[xo];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var ge=globalThis;function B(e){for(let t in e)if(e[t]===B)return t;throw Error("Could not find renamed property on target object.")}function Om(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function le(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(le).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Js(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Rm=B({__forward_ref__:B});function Ad(e){return e.__forward_ref__=Ad,e.toString=function(){return le(this())},e}function ue(e){return Od(e)?e():e}function Od(e){return typeof e=="function"&&e.hasOwnProperty(Rm)&&e.__forward_ref__===Ad}function x(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function mi(e){return{providers:e.providers||[],imports:e.imports||[]}}function yi(e){return kl(e,Rd)||kl(e,Pd)}function pO(e){return yi(e)!==null}function kl(e,t){return e.hasOwnProperty(t)?e[t]:null}function Pm(e){let t=e&&(e[Rd]||e[Pd]);return t||null}function Ll(e){return e&&(e.hasOwnProperty(jl)||e.hasOwnProperty(Fm))?e[jl]:null}var Rd=B({\u0275prov:B}),jl=B({\u0275inj:B}),Pd=B({ngInjectableDef:B}),Fm=B({ngInjectorDef:B}),M=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=x({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Fd(e){return e&&!!e.\u0275providers}var km=B({\u0275cmp:B}),Lm=B({\u0275dir:B}),jm=B({\u0275pipe:B}),Vm=B({\u0275mod:B}),$o=B({\u0275fac:B}),ar=B({__NG_ELEMENT_ID__:B}),Vl=B({__NG_ENV_ID__:B});function U(e){return typeof e=="string"?e:e==null?"":String(e)}function Bm(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():U(e)}function $m(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new w(-200,e)}function cu(e,t){throw new w(-201,!1)}var P=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(P||{}),Xs;function kd(){return Xs}function pe(e){let t=Xs;return Xs=e,t}function Ld(e,t,n){let r=yi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&P.Optional)return null;if(t!==void 0)return t;cu(e,"Injector")}var Um={},dr=Um,ea="__NG_DI_FLAG__",Uo="ngTempTokenPath",Hm="ngTokenPath",zm=/\n/gm,Gm="\u0275",Bl="__source",wn;function Wm(){return wn}function yt(e){let t=wn;return wn=e,t}function qm(e,t=P.Default){if(wn===void 0)throw new w(-203,!1);return wn===null?Ld(e,void 0,t):wn.get(e,t&P.Optional?null:void 0,t)}function b(e,t=P.Default){return(kd()||qm)(ue(e),t)}function D(e,t=P.Default){return b(e,vi(t))}function vi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ta(e){let t=[];for(let n=0;n<e.length;n++){let r=ue(e[n]);if(Array.isArray(r)){if(r.length===0)throw new w(900,!1);let o,i=P.Default;for(let s=0;s<r.length;s++){let a=r[s],u=Ym(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(b(o,i))}else t.push(b(r))}return t}function jd(e,t){return e[ea]=t,e.prototype[ea]=t,e}function Ym(e){return e[ea]}function Zm(e,t,n,r){let o=e[Uo];throw t[Bl]&&o.unshift(t[Bl]),e.message=Qm(`
`+e.message,o,n,r),e[Hm]=o,e[Uo]=null,e}function Qm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Gm?e.slice(2):e;let o=le(t);if(Array.isArray(t))o=t.map(le).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):le(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(zm,`
  `)}`}var lu=jd(Nd("Optional"),8);var Vd=jd(Nd("SkipSelf"),4);function Ut(e,t){let n=e.hasOwnProperty($o);return n?e[$o]:null}function Km(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Jm(e){return e.flat(Number.POSITIVE_INFINITY)}function du(e,t){e.forEach(n=>Array.isArray(n)?du(n,t):t(n))}function Bd(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ho(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Xm(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function ey(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function fu(e,t,n){let r=Tr(e,t);return r>=0?e[r|1]=n:(r=~r,ey(e,r,t,n)),r}function Fs(e,t){let n=Tr(e,t);if(n>=0)return e[n|1]}function Tr(e,t){return ty(e,t,1)}function ty(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var In={},ce=[],bn=new M(""),$d=new M("",-1),Ud=new M(""),zo=class{get(t,n=dr){if(n===dr){let r=new Error(`NullInjectorError: No provider for ${le(t)}!`);throw r.name="NullInjectorError",r}return n}},Hd=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Hd||{}),$e=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}($e||{}),Dt=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Dt||{});function ny(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function na(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];ry(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function zd(e){return e===3||e===4||e===6}function ry(e){return e.charCodeAt(0)===64}function fr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?$l(e,n,o,null,t[++r]):$l(e,n,o,null,null))}}return e}function $l(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var Gd="ng-template";function oy(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&ny(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(hu(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function hu(e){return e.type===4&&e.value!==Gd}function iy(e,t,n){let r=e.type===4&&!n?Gd:e.value;return t===r}function sy(e,t,n){let r=4,o=e.attrs,i=o!==null?cy(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!ke(r)&&!ke(u))return!1;if(s&&ke(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!iy(e,u,n)||u===""&&t.length===1){if(ke(r))return!1;s=!0}}else if(r&8){if(o===null||!oy(e,o,u,n)){if(ke(r))return!1;s=!0}}else{let c=t[++a],l=ay(u,o,hu(e),n);if(l===-1){if(ke(r))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&c!==d){if(ke(r))return!1;s=!0}}}}return ke(r)||s}function ke(e){return(e&1)===0}function ay(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return ly(t,e)}function Wd(e,t,n=!1){for(let r=0;r<t.length;r++)if(sy(e,t[r],n))return!0;return!1}function uy(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if(!(n&1))return t[n+1]}return null}function cy(e){for(let t=0;t<e.length;t++){let n=e[t];if(zd(n))return t}return e.length}function ly(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function dy(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Ul(e,t){return e?":not("+t.trim()+")":t}function fy(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ke(s)&&(t+=Ul(i,o),o=""),r=s,i=i||!ke(r);n++}return o!==""&&(t+=Ul(i,o)),t}function hy(e){return e.map(fy).join(",")}function py(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ke(o))break;o=i}r++}return{attrs:t,classes:n}}function gO(e){return Mr(()=>{var o;let t=Qd(e),n=nt(re({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Hd.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:(o=e.signals)!=null?o:!1,data:e.data||{},encapsulation:e.encapsulation||$e.Emulated,styles:e.styles||ce,_:null,schemas:e.schemas||null,tView:null,id:""});Kd(n);let r=e.dependencies;return n.directiveDefs=zl(r,!1),n.pipeDefs=zl(r,!0),n.id=vy(n),n})}function gy(e){return Qe(e)||qd(e)}function my(e){return e!==null}function Di(e){return Mr(()=>({type:e.type,bootstrap:e.bootstrap||ce,declarations:e.declarations||ce,imports:e.imports||ce,exports:e.exports||ce,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Hl(e,t){var r;if(e==null)return In;let n={};for(let o in e)if(e.hasOwnProperty(o)){let i=e[o],s,a,u=Dt.None;Array.isArray(i)?(u=i[0],s=i[1],a=(r=i[2])!=null?r:s):(s=i,a=i),t?(n[s]=u!==Dt.None?[o,u]:o,t[s]=a):n[s]=o}return n}function xr(e){return Mr(()=>{let t=Qd(e);return Kd(t),t})}function Kt(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Qe(e){return e[km]||null}function qd(e){return e[Lm]||null}function Yd(e){return e[jm]||null}function yy(e){let t=Qe(e)||qd(e)||Yd(e);return t!==null?t.standalone:!1}function Zd(e,t){let n=e[Vm]||null;if(!n&&t===!0)throw new Error(`Type ${le(e)} does not have '\u0275mod' property.`);return n}function Qd(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||In,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||ce,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Hl(e.inputs,t),outputs:Hl(e.outputs),debugInfo:null}}function Kd(e){var t;(t=e.features)==null||t.forEach(n=>n(e))}function zl(e,t){if(!e)return null;let n=t?Yd:gy;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(my)}function vy(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function On(e){return{\u0275providers:e}}function Dy(...e){return{\u0275providers:Jd(!0,e),\u0275fromNgModule:!0}}function Jd(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return du(t,s=>{let a=s;ra(a,i,[],r)&&(o||(o=[]),o.push(a))}),o!==void 0&&Xd(o,i),n}function Xd(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];pu(o,i=>{t(i,r)})}}function ra(e,t,n,r){if(e=ue(e),!e)return!1;let o=null,i=Ll(e),s=!i&&Qe(e);if(!i&&!s){let u=e.ngModule;if(i=Ll(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)ra(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{du(i.imports,l=>{ra(l,t,n,r)&&(c||(c=[]),c.push(l))})}finally{}c!==void 0&&Xd(c,t)}if(!a){let c=Ut(o)||(()=>new o);t({provide:o,useFactory:c,deps:ce},o),t({provide:Ud,useValue:o,multi:!0},o),t({provide:bn,useValue:()=>b(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;pu(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function pu(e,t){for(let n of e)Fd(n)&&(n=n.\u0275providers),Array.isArray(n)?pu(n,t):t(n)}var wy=B({provide:String,useValue:B});function ef(e){return e!==null&&typeof e=="object"&&wy in e}function Ey(e){return!!(e&&e.useExisting)}function Iy(e){return!!(e&&e.useFactory)}function Cn(e){return typeof e=="function"}function by(e){return!!e.useClass}var wi=new M(""),Fo={},Cy={},ks;function Ei(){return ks===void 0&&(ks=new zo),ks}var Ke=class{},hr=class extends Ke{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,ia(t,s=>this.processProvider(s)),this.records.set($d,gn(void 0,this)),o.has("environment")&&this.records.set(Ke,gn(void 0,this));let i=this.records.get(wi);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ud,ce,P.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=R(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),R(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=yt(this),r=pe(void 0),o;try{return t()}finally{yt(n),pe(r)}}get(t,n=dr,r=P.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Vl))return t[Vl](this);r=vi(r);let o,i=yt(this),s=pe(void 0);try{if(!(r&P.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=Sy(t)&&yi(t);c&&this.injectableDefInScope(c)?u=gn(oa(t),Fo):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=r&P.Self?Ei():this.parent;return n=r&P.Optional&&n===dr?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Uo]=a[Uo]||[]).unshift(le(t)),i)throw a;return Zm(a,t,"R3InjectorError",this.source)}else throw a}finally{pe(s),yt(i)}}resolveInjectorInitializers(){let t=R(null),n=yt(this),r=pe(void 0),o;try{let i=this.get(bn,ce,P.Self);for(let s of i)s()}finally{yt(n),pe(r),R(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(le(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new w(205,!1)}processProvider(t){t=ue(t);let n=Cn(t)?t:ue(t&&t.provide),r=Ty(t);if(!Cn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=gn(void 0,Fo,!0),o.factory=()=>ta(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=R(null);try{return n.value===Fo&&(n.value=Cy,n.value=n.factory()),typeof n.value=="object"&&n.value&&_y(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{R(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ue(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function oa(e){let t=yi(e),n=t!==null?t.factory:Ut(e);if(n!==null)return n;if(e instanceof M)throw new w(204,!1);if(e instanceof Function)return My(e);throw new w(204,!1)}function My(e){if(e.length>0)throw new w(204,!1);let n=Pm(e);return n!==null?()=>n.factory(e):()=>new e}function Ty(e){if(ef(e))return gn(void 0,e.useValue);{let t=tf(e);return gn(t,Fo)}}function tf(e,t,n){let r;if(Cn(e)){let o=ue(e);return Ut(o)||oa(o)}else if(ef(e))r=()=>ue(e.useValue);else if(Iy(e))r=()=>e.useFactory(...ta(e.deps||[]));else if(Ey(e))r=()=>b(ue(e.useExisting));else{let o=ue(e&&(e.useClass||e.provide));if(xy(e))r=()=>new o(...ta(e.deps));else return Ut(o)||oa(o)}return r}function gn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function xy(e){return!!e.deps}function _y(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Sy(e){return typeof e=="function"||typeof e=="object"&&e instanceof M}function ia(e,t){for(let n of e)Array.isArray(n)?ia(n,t):n&&Fd(n)?ia(n.\u0275providers,t):t(n)}function gu(e,t){e instanceof hr&&e.assertNotDestroyed();let n,r=yt(e),o=pe(void 0);try{return t()}finally{yt(r),pe(o)}}function nf(){return kd()!==void 0||Wm()!=null}function Ny(e){if(!nf())throw new w(-203,!1)}function Ay(e){let t=ge.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function Oy(e){return typeof e=="function"}var Z=0,C=1,I=2,te=3,Ve=4,Ie=5,Se=6,pr=7,Ee=8,Mn=9,Ue=10,F=11,gr=12,Gl=13,Rn=14,me=15,_r=16,mn=17,Be=18,Ii=19,rf=20,vt=21,Ls=22,Ht=23,V=25,mu=1,mr=6,st=7,Go=8,Tn=9,oe=10,yu=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(yu||{});function it(e){return Array.isArray(e)&&typeof e[mu]=="object"}function be(e){return Array.isArray(e)&&e[mu]===!0}function vu(e){return(e.flags&4)!==0}function Pn(e){return e.componentOffset>-1}function bi(e){return(e.flags&1)===1}function wt(e){return!!e.template}function Du(e){return(e[I]&512)!==0}function Ry(e){return(e.type&16)===16}function Py(e){return(e[I]&32)===32}var sa=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function of(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function sf(){return af}function af(e){return e.type.prototype.ngOnChanges&&(e.setInput=ky),Fy}sf.ngInherit=!0;function Fy(){let e=cf(this),t=e==null?void 0:e.current;if(t){let n=e.previous;if(n===In)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function ky(e,t,n,r,o){let i=this.declaredInputs[r],s=cf(e)||Ly(e,{previous:In,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new sa(c&&c.currentValue,n,u===In),of(e,t,o,n)}var uf="__ngSimpleChanges__";function cf(e){return e[uf]||null}function Ly(e,t){return e[uf]=t}var Wl=null;var qe=function(e,t,n){Wl!=null&&Wl(e,t,n)},lf="svg",jy="math",Vy=!1;function By(){return Vy}function ee(e){for(;Array.isArray(e);)e=e[Z];return e}function df(e){for(;Array.isArray(e);){if(typeof e[mu]=="object")return e;e=e[Z]}return null}function ff(e,t){return ee(t[e])}function Ce(e,t){return ee(t[e.index])}function wu(e,t){return e.data[t]}function Ci(e,t){return e[t]}function bt(e,t){let n=t[e];return it(n)?n:n[Z]}function $y(e){return(e[I]&4)===4}function Eu(e){return(e[I]&128)===128}function Uy(e){return be(e[te])}function xn(e,t){return t==null?null:e[t]}function hf(e){e[mn]=0}function Hy(e){e[I]&1024||(e[I]|=1024,Eu(e)&&yr(e))}function zy(e,t){for(;e>0;)t=t[Rn],e--;return t}function Iu(e){var t;return!!(e[I]&9216||(t=e[Ht])!=null&&t.dirty)}function aa(e){var t,n;(t=e[Ue].changeDetectionScheduler)==null||t.notify(1),Iu(e)?yr(e):e[I]&64&&(By()?(e[I]|=1024,yr(e)):(n=e[Ue].changeDetectionScheduler)==null||n.notify())}function yr(e){var n;(n=e[Ue].changeDetectionScheduler)==null||n.notify();let t=vr(e);for(;t!==null&&!(t[I]&8192||(t[I]|=8192,!Eu(t)));)t=vr(t)}function pf(e,t){if((e[I]&256)===256)throw new w(911,!1);e[vt]===null&&(e[vt]=[]),e[vt].push(t)}function Gy(e,t){if(e[vt]===null)return;let n=e[vt].indexOf(t);n!==-1&&e[vt].splice(n,1)}function vr(e){let t=e[te];return be(t)?t[te]:t}var T={lFrame:wf(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Wy(){return T.lFrame.elementDepthCount}function qy(){T.lFrame.elementDepthCount++}function Yy(){T.lFrame.elementDepthCount--}function gf(){return T.bindingsEnabled}function Fn(){return T.skipHydrationRootTNode!==null}function Zy(e){return T.skipHydrationRootTNode===e}function Qy(e){T.skipHydrationRootTNode=e}function Ky(){T.skipHydrationRootTNode=null}function E(){return T.lFrame.lView}function G(){return T.lFrame.tView}function mO(e){return T.lFrame.contextLView=e,e[Ee]}function yO(e){return T.lFrame.contextLView=null,e}function ie(){let e=mf();for(;e!==null&&e.type===64;)e=e.parent;return e}function mf(){return T.lFrame.currentTNode}function Jy(){let e=T.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Jt(e,t){let n=T.lFrame;n.currentTNode=e,n.isParent=t}function bu(){return T.lFrame.isParent}function Cu(){T.lFrame.isParent=!1}function Xy(){return T.lFrame.contextLView}function ut(){let e=T.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Sr(){return T.lFrame.bindingIndex}function ev(e){return T.lFrame.bindingIndex=e}function kn(){return T.lFrame.bindingIndex++}function Xt(e){let t=T.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function tv(){return T.lFrame.inI18n}function nv(e,t){let n=T.lFrame;n.bindingIndex=n.bindingRootIndex=e,ua(t)}function rv(){return T.lFrame.currentDirectiveIndex}function ua(e){T.lFrame.currentDirectiveIndex=e}function ov(e){let t=T.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function yf(){return T.lFrame.currentQueryIndex}function Mu(e){T.lFrame.currentQueryIndex=e}function iv(e){let t=e[C];return t.type===2?t.declTNode:t.type===1?e[Ie]:null}function vf(e,t,n){if(n&P.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&P.Host);)if(o=iv(i),o===null||(i=i[Rn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=T.lFrame=Df();return r.currentTNode=t,r.lView=e,!0}function Tu(e){let t=Df(),n=e[C];T.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Df(){let e=T.lFrame,t=e===null?null:e.child;return t===null?wf(e):t}function wf(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Ef(){let e=T.lFrame;return T.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var If=Ef;function xu(){let e=Ef();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function sv(e){return(T.lFrame.contextLView=zy(e,T.lFrame.contextLView))[Ee]}function Ne(){return T.lFrame.selectedIndex}function zt(e){T.lFrame.selectedIndex=e}function Nr(){let e=T.lFrame;return wu(e.tView,e.selectedIndex)}function vO(){T.lFrame.currentNamespace=lf}function DO(){av()}function av(){T.lFrame.currentNamespace=null}function bf(){return T.lFrame.currentNamespace}var Cf=!0;function Mi(){return Cf}function Xe(e){Cf=e}function uv(e,t,n){var s,a,u,c,l;let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let d=af(t);((s=n.preOrderHooks)!=null?s:n.preOrderHooks=[]).push(e,d),((a=n.preOrderCheckHooks)!=null?a:n.preOrderCheckHooks=[]).push(e,d)}o&&((u=n.preOrderHooks)!=null?u:n.preOrderHooks=[]).push(0-e,o),i&&(((c=n.preOrderHooks)!=null?c:n.preOrderHooks=[]).push(e,i),((l=n.preOrderCheckHooks)!=null?l:n.preOrderCheckHooks=[]).push(e,i))}function Ti(e,t){var n,r,o,i,s,a,u;for(let c=t.directiveStart,l=t.directiveEnd;c<l;c++){let f=e.data[c].type.prototype,{ngAfterContentInit:h,ngAfterContentChecked:p,ngAfterViewInit:y,ngAfterViewChecked:m,ngOnDestroy:g}=f;h&&((n=e.contentHooks)!=null?n:e.contentHooks=[]).push(-c,h),p&&(((r=e.contentHooks)!=null?r:e.contentHooks=[]).push(c,p),((o=e.contentCheckHooks)!=null?o:e.contentCheckHooks=[]).push(c,p)),y&&((i=e.viewHooks)!=null?i:e.viewHooks=[]).push(-c,y),m&&(((s=e.viewHooks)!=null?s:e.viewHooks=[]).push(c,m),((a=e.viewCheckHooks)!=null?a:e.viewCheckHooks=[]).push(c,m)),g!=null&&((u=e.destroyHooks)!=null?u:e.destroyHooks=[]).push(c,g)}}function ko(e,t,n){Mf(e,t,3,n)}function Lo(e,t,n,r){(e[I]&3)===n&&Mf(e,t,n,r)}function js(e,t){let n=e[I];(n&3)===t&&(n&=16383,n+=1,e[I]=n)}function Mf(e,t,n,r){let o=r!==void 0?e[mn]&65535:0,i=r!=null?r:-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],r!=null&&a>=r)break}else t[u]<0&&(e[mn]+=65536),(a<i||i==-1)&&(cv(e,n,t,u),e[mn]=(e[mn]&**********)+u+2),u++}function ql(e,t){qe(4,e,t);let n=R(null);try{t.call(e)}finally{R(n),qe(5,e,t)}}function cv(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[I]>>14<e[mn]>>16&&(e[I]&3)===t&&(e[I]+=16384,ql(a,i)):ql(a,i)}var En=-1,Gt=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function lv(e){return e instanceof Gt}function dv(e){return e!=null&&typeof e=="object"&&(e.insertBeforeIndex===null||typeof e.insertBeforeIndex=="number"||Array.isArray(e.insertBeforeIndex))}function fv(e){return(e.flags&8)!==0}function hv(e){return(e.flags&16)!==0}function Tf(e){return e!==En}function Wo(e){return e&32767}function pv(e){return e>>16}function qo(e,t){let n=pv(e),r=t;for(;n>0;)r=r[Rn],n--;return r}var ca=!0;function Yo(e){let t=ca;return ca=e,t}var gv=256,xf=gv-1,_f=5,mv=0,Ye={};function yv(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(ar)&&(r=n[ar]),r==null&&(r=n[ar]=mv++);let o=r&xf,i=1<<o;t.data[e+(o>>_f)]|=i}function Zo(e,t){let n=Sf(e,t);if(n!==-1)return n;let r=t[C];r.firstCreatePass&&(e.injectorIndex=t.length,Vs(r.data,e),Vs(t,null),Vs(r.blueprint,null));let o=_u(e,t),i=e.injectorIndex;if(Tf(o)){let s=Wo(o),a=qo(o,t),u=a[C].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function Vs(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Sf(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function _u(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Pf(o),r===null)return En;if(n++,o=o[Rn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return En}function la(e,t,n){yv(e,t,n)}function vv(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(zd(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Nf(e,t,n){if(n&P.Optional||e!==void 0)return e;cu(t,"NodeInjector")}function Af(e,t,n,r){if(n&P.Optional&&r===void 0&&(r=null),!(n&(P.Self|P.Host))){let o=e[Mn],i=pe(void 0);try{return o?o.get(t,r,n&P.Optional):Ld(t,r,n&P.Optional)}finally{pe(i)}}return Nf(r,t,n)}function Of(e,t,n,r=P.Default,o){if(e!==null){if(t[I]&2048&&!(r&P.Self)){let s=Iv(e,t,n,r,Ye);if(s!==Ye)return s}let i=Rf(e,t,n,r,Ye);if(i!==Ye)return i}return Af(t,n,r,o)}function Rf(e,t,n,r,o){let i=wv(n);if(typeof i=="function"){if(!vf(t,e,r))return r&P.Host?Nf(o,n,r):Af(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&P.Optional))cu(n);else return s}finally{If()}}else if(typeof i=="number"){let s=null,a=Sf(e,t),u=En,c=r&P.Host?t[me][Ie]:null;for((a===-1||r&P.SkipSelf)&&(u=a===-1?_u(e,t):t[a+8],u===En||!Zl(r,!1)?a=-1:(s=t[C],a=Wo(u),t=qo(u,t)));a!==-1;){let l=t[C];if(Yl(i,a,l.data)){let d=Dv(a,t,n,s,r,c);if(d!==Ye)return d}u=t[a+8],u!==En&&Zl(r,t[C].data[a+8]===c)&&Yl(i,a,t)?(s=l,a=Wo(u),t=qo(u,t)):a=-1}}return o}function Dv(e,t,n,r,o,i){let s=t[C],a=s.data[e+8],u=r==null?Pn(a)&&ca:r!=s&&(a.type&3)!==0,c=o&P.Host&&i===a,l=jo(a,s,n,u,c);return l!==null?Wt(t,s,l,a):Ye}function jo(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=r?a:a+l,f=o?a+l:c;for(let h=d;h<f;h++){let p=s[h];if(h<u&&n===p||h>=u&&p.type===n)return h}if(o){let h=s[u];if(h&&wt(h)&&h.type===n)return u}return null}function Wt(e,t,n,r){let o=e[n],i=t.data;if(lv(o)){let s=o;s.resolving&&$m(Bm(i[n]));let a=Yo(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?pe(s.injectImpl):null,l=vf(e,r,P.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&uv(n,i[n],t)}finally{c!==null&&pe(c),Yo(a),s.resolving=!1,If()}}return o}function wv(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(ar)?e[ar]:void 0;return typeof t=="number"?t>=0?t&xf:Ev:t}function Yl(e,t,n){let r=1<<e;return!!(n[t+(e>>_f)]&r)}function Zl(e,t){return!(e&P.Self)&&!(e&P.Host&&t)}var $t=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Of(this._tNode,this._lView,t,vi(r),n)}};function Ev(){return new $t(ie(),E())}function wO(e){return Mr(()=>{let t=e.prototype.constructor,n=t[$o]||da(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[$o]||da(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function da(e){return Od(e)?()=>{let t=da(ue(e));return t&&t()}:Ut(e)}function Iv(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[I]&2048&&!(s[I]&512);){let a=Rf(i,s,n,r|P.Self,Ye);if(a!==Ye)return a;let u=i.parent;if(!u){let c=s[rf];if(c){let l=c.get(n,Ye,r);if(l!==Ye)return l}u=Pf(s),s=s[Rn]}i=u}return o}function Pf(e){let t=e[C],n=t.type;return n===2?t.declTNode:n===1?e[Ie]:null}function bv(e){return vv(ie(),e)}function Ql(e,t=null,n=null,r){let o=Ff(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ff(e,t=null,n=null,r,o=new Set){let i=[n||ce,Dy(e)];return r=r||(typeof e=="object"?void 0:le(e)),new hr(i,t||Ei(),r||null,o)}var Ct=(()=>{let t=class t{static create(r,o){var i;if(Array.isArray(r))return Ql({name:""},o,r,"");{let s=(i=r.name)!=null?i:"";return Ql({name:s},r.parent,r.providers,s)}}};t.THROW_IF_NOT_FOUND=dr,t.NULL=new zo,t.\u0275prov=x({token:t,providedIn:"any",factory:()=>b($d)}),t.__NG_ELEMENT_ID__=-1;let e=t;return e})();var Cv="ngOriginalError";function Bs(e){return e[Cv]}var He=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Bs(t);for(;n&&Bs(n);)n=Bs(n);return n||null}},kf=new M("",{providedIn:"root",factory:()=>D(He).handleError.bind(void 0)}),Su=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=Mv,t.__NG_ENV_ID__=r=>r;let e=t;return e})(),fa=class extends Su{constructor(t){super(),this._lView=t}onDestroy(t){return pf(this._lView,t),()=>Gy(this._lView,t)}};function Mv(){return new fa(E())}function Kl(e,t){return Sd(e,t)}function Tv(e){return Sd(_d,e)}var EO=(Kl.required=Tv,Kl);function xv(){return Ln(ie(),E())}function Ln(e,t){return new Mt(Ce(e,t))}var Mt=(()=>{let t=class t{constructor(r){this.nativeElement=r}};t.__NG_ELEMENT_ID__=xv;let e=t;return e})();function _v(e){return e instanceof Mt?e.nativeElement:e}var ha=class extends _e{constructor(t=!1){var n;super(),this.destroyRef=void 0,this.__isAsync=t,nf()&&(this.destroyRef=(n=D(Su,{optional:!0}))!=null?n:void 0)}emit(t){let n=R(null);try{super.next(t)}finally{R(n)}}subscribe(t,n,r){var u,c,l;let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let d=t;o=(u=d.next)==null?void 0:u.bind(d),i=(c=d.error)==null?void 0:c.bind(d),s=(l=d.complete)==null?void 0:l.bind(d)}this.__isAsync&&(i=$s(i),o&&(o=$s(o)),s&&(s=$s(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Y&&t.add(a),a}};function $s(e){return t=>{setTimeout(e,void 0,t)}}var je=ha;function Sv(){return this._results[Symbol.iterator]()}var pa=class e{get changes(){var t;return(t=this._changes)!=null?t:this._changes=new je}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=e.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=Sv)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Jm(t);(this._changesDetected=!Km(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){var t;this.dirty=!0,(t=this._onDirty)==null||t.call(this)}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}},Dr="ngSkipHydration",Nv="ngskiphydration";function Lf(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===Nv)return!0}return!1}function jf(e){return e.hasAttribute(Dr)}function Qo(e){return(e.flags&128)===128}function Ko(e){if(Qo(e))return!0;let t=e.parent;for(;t;){if(Qo(e)||Lf(t))return!0;t=t.parent}return!1}var Vf=new Map,Av=0;function Ov(){return Av++}function Rv(e){Vf.set(e[Ii],e)}function Pv(e){Vf.delete(e[Ii])}var Jl="__ngContext__";function Et(e,t){it(t)?(e[Jl]=t[Ii],Rv(t)):e[Jl]=t}function Bf(e){return Uf(e[gr])}function $f(e){return Uf(e[Ve])}function Uf(e){for(;e!==null&&!be(e);)e=e[Ve];return e}var ga;function Hf(e){ga=e}function Ar(){if(ga!==void 0)return ga;if(typeof document<"u")return document;throw new w(210,!1)}var xi=new M("",{providedIn:"root",factory:()=>Fv}),Fv="ng",Nu=new M(""),Ae=new M("",{providedIn:"platform",factory:()=>"unknown"});var IO=new M(""),Au=new M("",{providedIn:"root",factory:()=>{var e,t;return((t=(e=Ar().body)==null?void 0:e.querySelector("[ngCspNonce]"))==null?void 0:t.getAttribute("ngCspNonce"))||null}});function kv(){let e=new Tt;return D(Ae)==="browser"&&(e.store=Lv(Ar(),D(xi))),e}var Tt=(()=>{let t=class t{constructor(){this.store={},this.onSerializeCallbacks={}}get(r,o){return this.store[r]!==void 0?this.store[r]:o}set(r,o){this.store[r]=o}remove(r){delete this.store[r]}hasKey(r){return this.store.hasOwnProperty(r)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(r,o){this.onSerializeCallbacks[r]=o}toJson(){for(let r in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(r))try{this.store[r]=this.onSerializeCallbacks[r]()}catch(o){console.warn("Exception in onSerialize callback: ",o)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}};t.\u0275prov=x({token:t,providedIn:"root",factory:kv});let e=t;return e})();function Lv(e,t){let n=e.getElementById(t+"-state");if(n!=null&&n.textContent)try{return JSON.parse(n.textContent)}catch(r){console.warn("Exception while restoring TransferState for app "+t,r)}return{}}var Ou="h",Ru="b",wr=function(e){return e.FirstChild="f",e.NextSibling="n",e}(wr||{}),rr="e",or="t",Bt="c",yn="x",_n="r",ma="i",ir="n",pn="d",jv="__nghData__",Pu=jv,ur="ngh",Vv="nghm",zf=()=>null;function Bv(e,t,n=!1){var l;let r=e.getAttribute(ur);if(r==null)return null;let[o,i]=r.split("|");if(r=n?i:o,!r)return null;let s=i?`|${i}`:"",a=n?o:s,u={};if(r!==""){let d=t.get(Tt,null,{optional:!0});d!==null&&(u=d.get(Pu,[])[Number(r)])}let c={data:u,firstChild:(l=e.firstChild)!=null?l:null};return n&&(c.firstChild=e,_i(c,0,e.nextSibling)),a?e.setAttribute(ur,a):e.removeAttribute(ur),c}function $v(){zf=Bv}function Fu(e,t,n=!1){return zf(e,t,n)}function Gf(e){let t=e._lView;return t[C].type===2?null:(Du(t)&&(t=t[V]),t)}function Uv(e){var t;return(t=e.textContent)==null?void 0:t.replace(/\s/gm,"")}function Hv(e){let t=Ar(),n=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=Uv(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),r,o=[];for(;r=n.nextNode();)o.push(r);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function _i(e,t,n){var r;(r=e.segmentHeads)!=null||(e.segmentHeads={}),e.segmentHeads[t]=n}function ya(e,t){var n,r;return(r=(n=e.segmentHeads)==null?void 0:n[t])!=null?r:null}function zv(e,t){var o,i,s;let n=e.data,r=(i=(o=n[rr])==null?void 0:o[t])!=null?i:null;return r===null&&((s=n[Bt])!=null&&s[t])&&(r=ku(e,t)),r}function Wf(e,t){var n,r;return(r=(n=e.data[Bt])==null?void 0:n[t])!=null?r:null}function ku(e,t){var o,i;let n=(o=Wf(e,t))!=null?o:[],r=0;for(let s of n)r+=s[_n]*((i=s[yn])!=null?i:1);return r}function Si(e,t){var n;if(typeof e.disconnectedNodes>"u"){let r=e.data[pn];e.disconnectedNodes=r?new Set(r):null}return!!((n=e.disconnectedNodes)!=null&&n.has(t))}var _o=new M(""),qf=!1,Yf=new M("",{providedIn:"root",factory:()=>qf}),Gv=new M(""),So;function Wv(){if(So===void 0&&(So=null,ge.trustedTypes))try{So=ge.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return So}function Ni(e){var t;return((t=Wv())==null?void 0:t.createHTML(e))||e}var No;function Zf(){if(No===void 0&&(No=null,ge.trustedTypes))try{No=ge.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return No}function Xl(e){var t;return((t=Zf())==null?void 0:t.createHTML(e))||e}function ed(e){var t;return((t=Zf())==null?void 0:t.createScriptURL(e))||e}var at=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${xd})`}},va=class extends at{getTypeName(){return"HTML"}},Da=class extends at{getTypeName(){return"Style"}},wa=class extends at{getTypeName(){return"Script"}},Ea=class extends at{getTypeName(){return"URL"}},Ia=class extends at{getTypeName(){return"ResourceURL"}};function Oe(e){return e instanceof at?e.changingThisBreaksApplicationSecurity:e}function ct(e,t){let n=qv(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${xd})`)}return n===t}function qv(e){return e instanceof at&&e.getTypeName()||null}function Qf(e){return new va(e)}function Kf(e){return new Da(e)}function Jf(e){return new wa(e)}function Xf(e){return new Ea(e)}function eh(e){return new Ia(e)}function Yv(e){let t=new Ca(e);return Zv()?new ba(t):t}var ba=class{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Ni(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.removeChild(n.firstChild),n)}catch{return null}}},Ca=class{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Ni(t),n}};function Zv(){try{return!!new window.DOMParser().parseFromString(Ni(""),"text/html")}catch{return!1}}var Qv=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Ai(e){return e=String(e),e.match(Qv)?e:"unsafe:"+e}function lt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Or(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var th=lt("area,br,col,hr,img,wbr"),nh=lt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),rh=lt("rp,rt"),Kv=Or(rh,nh),Jv=Or(nh,lt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Xv=Or(rh,lt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),td=Or(th,Jv,Xv,Kv),oh=lt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),eD=lt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),tD=lt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),nD=Or(oh,eD,tD),rD=lt("script,style,template"),Ma=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=sD(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=iD(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=nd(t).toLowerCase();if(!td.hasOwnProperty(n))return this.sanitizedSomething=!0,!rD.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!nD.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;oh[a]&&(u=Ai(u)),this.buf.push(" ",s,'="',rd(u),'"')}return this.buf.push(">"),!0}endElement(t){let n=nd(t).toLowerCase();td.hasOwnProperty(n)&&!th.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(rd(t))}};function oD(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function iD(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw ih(t);return t}function sD(e){let t=e.firstChild;if(t&&oD(e,t))throw ih(t);return t}function nd(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function ih(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var aD=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,uD=/([^\#-~ |!])/g;function rd(e){return e.replace(/&/g,"&amp;").replace(aD,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(uD,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Ao;function Lu(e,t){let n=null;try{Ao=Ao||Yv(e);let r=t?String(t):"";n=Ao.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Ao.getInertBodyElement(r)}while(r!==i);let a=new Ma().sanitizeChildren(od(n)||n);return Ni(a)}finally{if(n){let r=od(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}function od(e){return"content"in e&&cD(e)?e.content:null}function cD(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var ze=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ze||{});function bO(e){let t=ju();return t?Xl(t.sanitize(ze.HTML,e)||""):ct(e,"HTML")?Xl(Oe(e)):Lu(Ar(),U(e))}function lD(e){let t=ju();return t?t.sanitize(ze.URL,e)||"":ct(e,"URL")?Oe(e):Ai(U(e))}function dD(e){let t=ju();if(t)return ed(t.sanitize(ze.RESOURCE_URL,e)||"");if(ct(e,"ResourceURL"))return ed(Oe(e));throw new w(904,!1)}function fD(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?dD:lD}function CO(e,t,n){return fD(t,n)(e)}function ju(){let e=E();return e&&e[Ue].sanitizer}var hD=/^>|^->|<!--|-->|--!>|<!-$/g,pD=/(<|>)/g,gD="\u200B$1\u200B";function mD(e){return e.replace(hD,t=>t.replace(pD,gD))}function MO(e){return e.ownerDocument.defaultView}function TO(e){return e.ownerDocument}function yD(e){return e.ownerDocument.body}function sh(e){return e instanceof Function?e():e}function sr(e){return(e!=null?e:D(Ct)).get(Ae)==="browser"}var Je=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Je||{}),vD;function Vu(e,t){return vD(e,t)}function vn(e,t,n,r,o){if(r!=null){let i,s=!1;be(r)?i=r:it(r)&&(s=!0,r=r[Z]);let a=ee(r);e===0&&n!==null?o==null?ch(t,n,a):Xo(t,n,a,o||null,!0):e===1&&n!==null?Xo(t,n,a,o||null,!0):e===2?Gu(t,a,s):e===3&&t.destroyNode(a),i!=null&&RD(t,e,i,n,o)}}function Bu(e,t){return e.createText(t)}function DD(e,t,n){e.setValue(t,n)}function $u(e,t){return e.createComment(mD(t))}function Oi(e,t,n){return e.createElement(t,n)}function wD(e,t){ah(e,t),t[Z]=null,t[Ie]=null}function ED(e,t,n,r,o,i){r[Z]=o,r[Ie]=t,Pi(e,r,n,1,o,i)}function ah(e,t){var n;(n=t[Ue].changeDetectionScheduler)==null||n.notify(1),Pi(e,t,t[F],2,null,null)}function ID(e){let t=e[gr];if(!t)return Us(e[C],e);for(;t;){let n=null;if(it(t))n=t[gr];else{let r=t[oe];r&&(n=r)}if(!n){for(;t&&!t[Ve]&&t!==e;)it(t)&&Us(t[C],t),t=t[te];t===null&&(t=e),it(t)&&Us(t[C],t),n=t&&t[Ve]}t=n}}function bD(e,t,n,r){let o=oe+r,i=n.length;r>0&&(n[o-1][Ve]=t),r<i-oe?(t[Ve]=n[o],Bd(n,oe+r,t)):(n.push(t),t[Ve]=null),t[te]=n;let s=t[_r];s!==null&&n!==s&&CD(s,t);let a=t[Be];a!==null&&a.insertView(e),aa(t),t[I]|=128}function CD(e,t){let n=e[Tn],o=t[te][te][me];t[me]!==o&&(e[I]|=yu.HasTransplantedViews),n===null?e[Tn]=[t]:n.push(t)}function uh(e,t){let n=e[Tn],r=n.indexOf(t);n.splice(r,1)}function Jo(e,t){if(e.length<=oe)return;let n=oe+t,r=e[n];if(r){let o=r[_r];o!==null&&o!==e&&uh(o,r),t>0&&(e[n-1][Ve]=r[Ve]);let i=Ho(e,oe+t);wD(r[C],r);let s=i[Be];s!==null&&s.detachView(i[C]),r[te]=null,r[Ve]=null,r[I]&=-129}return r}function Uu(e,t){if(!(t[I]&256)){let n=t[F];n.destroyNode&&Pi(e,t,n,3,null,null),ID(t)}}function Us(e,t){if(t[I]&256)return;let n=R(null);try{t[I]&=-129,t[I]|=256,t[Ht]&&xl(t[Ht]),TD(e,t),MD(e,t),t[C].type===1&&t[F].destroy();let r=t[_r];if(r!==null&&be(t[te])){r!==t[te]&&uh(r,t);let o=t[Be];o!==null&&o.detachView(e)}Pv(t)}finally{R(n)}}function MD(e,t){let n=e.cleanup,r=t[pr];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[pr]=null);let o=t[vt];if(o!==null){t[vt]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function TD(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Gt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];qe(4,a,u);try{u.call(a)}finally{qe(5,a,u)}}else{qe(4,o,i);try{i.call(o)}finally{qe(5,o,i)}}}}}function Hu(e,t,n){return xD(e,t.parent,n)}function xD(e,t,n){let r=t;for(;r!==null&&r.type&40;)t=r,r=t.parent;if(r===null)return n[Z];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===$e.None||i===$e.Emulated)return null}return Ce(r,n)}}function Xo(e,t,n,r,o){e.insertBefore(t,n,r,o)}function ch(e,t,n){e.appendChild(t,n)}function id(e,t,n,r,o){r!==null?Xo(e,t,n,r,o):ch(e,t,n)}function _D(e,t,n,r){e.removeChild(t,n,r)}function zu(e,t){return e.parentNode(t)}function SD(e,t){return e.nextSibling(t)}function lh(e,t,n){return AD(e,t,n)}function ND(e,t,n){return e.type&40?Ce(e,n):null}var AD=ND,sd;function Ri(e,t,n,r){let o=Hu(e,r,t),i=t[F],s=r.parent||t[Ie],a=lh(s,r,t);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)id(i,o,n[u],a,!1);else id(i,o,n,a,!1);sd!==void 0&&sd(i,r,t,n,o)}function cr(e,t){if(t!==null){let n=t.type;if(n&3)return Ce(t,e);if(n&4)return Ta(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return cr(e,r);{let o=e[t.index];return be(o)?Ta(-1,o):ee(o)}}else{if(n&32)return Vu(t,e)()||ee(e[t.index]);{let r=dh(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=vr(e[me]);return cr(o,r)}else return cr(e,t.next)}}}return null}function dh(e,t){if(t!==null){let r=e[me][Ie],o=t.projection;return r.projection[o]}return null}function Ta(e,t){let n=oe+e+1;if(n<t.length){let r=t[n],o=r[C].firstChild;if(o!==null)return cr(r,o)}return t[st]}function Gu(e,t,n){let r=zu(e,t);r&&_D(e,r,t,n)}function fh(e){e.textContent=""}function Wu(e,t,n,r,o,i,s){for(;n!=null;){let a=r[n.index],u=n.type;if(s&&t===0&&(a&&Et(ee(a),r),n.flags|=2),(n.flags&32)!==32)if(u&8)Wu(e,t,n.child,r,o,i,!1),vn(t,e,o,a,i);else if(u&32){let c=Vu(n,r),l;for(;l=c();)vn(t,e,o,l,i);vn(t,e,o,a,i)}else u&16?hh(e,t,r,n,o,i):vn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Pi(e,t,n,r,o,i){Wu(n,r,e.firstChild,t,o,i,!1)}function OD(e,t,n){let r=t[F],o=Hu(e,n,t),i=n.parent||t[Ie],s=lh(i,n,t);hh(r,0,t,n,o,s)}function hh(e,t,n,r,o,i){let s=n[me],u=s[Ie].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];vn(t,e,o,l,i)}else{let c=u,l=s[te];Qo(r)&&(c.flags|=128),Wu(e,t,c,l,o,i,!0)}}function RD(e,t,n,r,o){let i=n[st],s=ee(n);i!==s&&vn(t,e,r,i,o);for(let a=oe;a<n.length;a++){let u=n[a];Pi(u[C],u,e,t,r,i)}}function PD(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Je.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Je.Important),e.setStyle(n,r,o,i))}}function FD(e,t,n){e.setAttribute(t,"style",n)}function ph(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function gh(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&na(e,t,r),o!==null&&ph(e,t,o),i!==null&&FD(e,t,i)}var Q={};function xO(e=1){mh(G(),E(),Ne()+e,!1)}function mh(e,t,n,r){if(!r)if((t[I]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ko(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Lo(t,i,0,n)}zt(n)}function K(e,t=P.Default){let n=E();if(n===null)return b(e,t);let r=ie();return Of(r,n,ue(e),t)}function _O(){let e="invalid";throw new Error(e)}function yh(e,t,n,r,o,i){let s=R(null);try{let a=null;o&Dt.SignalBased&&(a=t[r][gt]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Dt.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):of(t,a,r,i)}finally{R(s)}}function kD(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)zt(~o);else{let i=o,s=n[++r],a=n[++r];nv(s,i);let u=t[i];a(2,u)}}}finally{zt(-1)}}function Fi(e,t,n,r,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[Z]=o,d[I]=r|4|128|8|64,(c!==null||e&&e[I]&2048)&&(d[I]|=2048),hf(d),d[te]=d[Rn]=e,d[Ee]=n,d[Ue]=s||e&&e[Ue],d[F]=a||e&&e[F],d[Mn]=u||e&&e[Mn]||null,d[Ie]=i,d[Ii]=Ov(),d[Se]=l,d[rf]=c,d[me]=t.type==2?e[me]:d,d}function jn(e,t,n,r,o){let i=e.data[t];if(i===null)i=LD(e,t,n,r,o),tv()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Jy();i.injectorIndex=s===null?-1:s.injectorIndex}return Jt(i,!0),i}function LD(e,t,n,r,o){let i=mf(),s=bu(),a=s?i:i&&i.parent,u=e.data[t]=zD(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=u),i!==null&&(s?i.child==null&&u.parent!==null&&(i.child=u):i.next===null&&(i.next=u,u.prev=i)),u}function vh(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Dh(e,t,n,r,o){let i=Ne(),s=r&2;try{zt(-1),s&&t.length>V&&mh(e,t,V,!1),qe(s?2:0,o),n(r,o)}finally{zt(i),qe(s?3:1,o)}}function qu(e,t,n){if(vu(t)){let r=R(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{R(r)}}}function Yu(e,t,n){gf()&&(QD(e,t,n,Ce(n,t)),(n.flags&64)===64&&bh(e,t,n))}function Zu(e,t,n=Ce){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function wh(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Qu(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Qu(e,t,n,r,o,i,s,a,u,c,l){let d=V+r,f=d+o,h=jD(d,f),p=typeof c=="function"?c():c;return h[C]={type:e,blueprint:h,template:n,queries:null,viewQuery:a,declTNode:t,data:h.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:p,incompleteFirstPass:!1,ssrId:l}}function jD(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Q);return n}function VD(e,t,n,r){let i=r.get(Yf,qf)||n===$e.ShadowDom,s=e.selectRootElement(t,i);return BD(s),s}function BD(e){Eh(e)}var Eh=()=>null;function $D(e){jf(e)?fh(e):Hv(e)}function UD(){Eh=$D}function HD(e,t,n,r){let o=Th(t);o.push(n),e.firstCreatePass&&xh(e).push(r,o.length-1)}function zD(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Fn()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function ad(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r!=null||(r={});let a,u=Dt.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?ud(r,n,c,a,u):ud(r,n,c,a)}return r}function ud(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function GD(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=r;l<o;l++){let d=i[l],f=n?n.get(d):null,h=f?f.inputs:null,p=f?f.outputs:null;u=ad(0,d.inputs,l,u,h),c=ad(1,d.outputs,l,c,p);let y=u!==null&&s!==null&&!hu(t)?aw(u,l,s):null;a.push(y)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function WD(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function ki(e,t,n,r,o,i,s,a){let u=Ce(t,n),c=t.inputs,l;!a&&c!=null&&(l=c[r])?(Ju(e,n,l,r,o),Pn(t)&&qD(n,t.index)):t.type&3?(r=WD(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(u,r,o)):t.type&12}function qD(e,t){let n=bt(t,e);n[I]&16||(n[I]|=64)}function Ku(e,t,n,r){if(gf()){let o=r===null?null:{"":-1},i=JD(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&Ih(e,t,n,s,o,a),o&&XD(n,r,o)}n.mergedAttrs=fr(n.mergedAttrs,n.attrs)}function Ih(e,t,n,r,o,i){var c,l;for(let d=0;d<r.length;d++)la(Zo(n,t),e,r[d].type);tw(n,e.data.length,r.length);for(let d=0;d<r.length;d++){let f=r[d];f.providersResolver&&f.providersResolver(f)}let s=!1,a=!1,u=vh(e,t,r.length,null);for(let d=0;d<r.length;d++){let f=r[d];n.mergedAttrs=fr(n.mergedAttrs,f.hostAttrs),nw(e,n,t,u,f),ew(u,f,o),f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!s&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&(((c=e.preOrderHooks)!=null?c:e.preOrderHooks=[]).push(n.index),s=!0),!a&&(h.ngOnChanges||h.ngDoCheck)&&(((l=e.preOrderCheckHooks)!=null?l:e.preOrderCheckHooks=[]).push(n.index),a=!0),u++}GD(e,n,i)}function YD(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;ZD(s)!=a&&s.push(a),s.push(n,r,i)}}function ZD(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function QD(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;Pn(n)&&rw(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Zo(n,t),Et(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let u=e.data[a],c=Wt(t,e,a,n);if(Et(c,t),s!==null&&sw(t,a-o,c,u,n,s),wt(u)){let l=bt(n.index,t);l[Ee]=Wt(t,e,a,n)}}}function bh(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=rv();try{zt(i);for(let a=r;a<o;a++){let u=e.data[a],c=t[a];ua(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&KD(u,c)}}finally{zt(-1),ua(s)}}function KD(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function JD(e,t){var i;let n=e.directiveRegistry,r=null,o=null;if(n)for(let s=0;s<n.length;s++){let a=n[s];if(Wd(t,a.selectors,!1))if(r||(r=[]),wt(a))if(a.findHostDirectiveDefs!==null){let u=[];o=o||new Map,a.findHostDirectiveDefs(a,u,o),r.unshift(...u,a);let c=u.length;xa(e,t,c)}else r.unshift(a),xa(e,t,0);else o=o||new Map,(i=a.findHostDirectiveDefs)==null||i.call(a,a,r,o),r.push(a)}return r===null?null:[r,o]}function xa(e,t,n){var r;t.componentOffset=n,((r=e.components)!=null?r:e.components=[]).push(t.index)}function XD(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new w(-301,!1);r.push(t[o],i)}}}function ew(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;wt(t)&&(n[""]=e)}}function tw(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function nw(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ut(o.type,!0)),s=new Gt(i,wt(o),K);e.blueprint[r]=s,n[r]=s,YD(e,t,r,vh(e,n,o.hostVars,Q),o)}function rw(e,t,n){let r=Ce(t,e),o=wh(n),i=e[Ue].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=Li(e,Fi(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function ow(e,t,n,r,o,i){let s=Ce(e,t);iw(t[F],s,i,e.value,n,r,o)}function iw(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?U(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function sw(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];yh(r,n,u,c,l,d)}}function aw(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function Ch(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Mh(e,t){let n=e.contentQueries;if(n!==null){let r=R(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Mu(i),a.contentQueries(2,t[s],s)}}}finally{R(r)}}}function Li(e,t){return e[gr]?e[Gl][Ve]=t:e[gr]=t,e[Gl]=t,t}function _a(e,t,n){Mu(0);let r=R(null);try{t(e,n)}finally{R(r)}}function Th(e){return e[pr]||(e[pr]=[])}function xh(e){return e.cleanup||(e.cleanup=[])}function _h(e,t){let n=e[Mn],r=n?n.get(He,null):null;r&&r.handleError(t)}function Ju(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],c=t[s],l=e.data[s];yh(l,c,r,a,u,o)}}function Vn(e,t,n){let r=ff(t,e);DD(e[F],r,n)}function uw(e,t){let n=bt(t,e),r=n[C];cw(r,n);let o=n[Z];o!==null&&n[Se]===null&&(n[Se]=Fu(o,n[Mn])),Xu(r,n,n[Ee])}function cw(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Xu(e,t,n){var r;Tu(t);try{let o=e.viewQuery;o!==null&&_a(1,o,n);let i=e.template;i!==null&&Dh(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),(r=t[Be])==null||r.finishViewCreation(e),e.staticContentQueries&&Mh(e,t),e.staticViewQueries&&_a(2,e.viewQuery,n);let s=e.components;s!==null&&lw(t,s)}catch(o){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),o}finally{t[I]&=-5,xu()}}function lw(e,t){for(let n=0;n<t.length;n++)uw(e,t[n])}function Sh(e,t,n,r){var i,s,a;let o=R(null);try{let u=t.tView,l=e[I]&4096?4096:16,d=Fi(e,u,n,l,null,t,null,null,(i=r==null?void 0:r.injector)!=null?i:null,(s=r==null?void 0:r.embeddedViewInjector)!=null?s:null,(a=r==null?void 0:r.dehydratedView)!=null?a:null),f=e[t.index];d[_r]=f;let h=e[Be];return h!==null&&(d[Be]=h.createEmbeddedView(u)),Xu(u,d,n),d}finally{R(o)}}function dw(e,t){let n=oe+t;if(n<e.length)return e[n]}function Sa(e,t){return!t||t.firstChild===null||Qo(e)}function Nh(e,t,n,r=!0){let o=t[C];if(bD(o,t,e,n),r){let s=Ta(n,e),a=t[F],u=zu(a,e[st]);u!==null&&ED(o,e[Ie],a,t,u,s)}let i=t[Se];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function fw(e,t){let n=Jo(e,t);return n!==void 0&&Uu(n[C],n),n}function Er(e,t,n,r,o=!1){for(;n!==null;){let i=t[n.index];i!==null&&r.push(ee(i)),be(i)&&Ah(i,r);let s=n.type;if(s&8)Er(e,t,n.child,r);else if(s&32){let a=Vu(n,t),u;for(;u=a();)r.push(u)}else if(s&16){let a=dh(t,n);if(Array.isArray(a))r.push(...a);else{let u=vr(t[me]);Er(u[C],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Ah(e,t){for(let n=oe;n<e.length;n++){let r=e[n],o=r[C].firstChild;o!==null&&Er(r[C],r,o,t)}e[st]!==e[Z]&&t.push(e[st])}var Oh=[];function hw(e){var t;return(t=e[Ht])!=null?t:pw(e)}function pw(e){var n;let t=(n=Oh.pop())!=null?n:Object.create(mw);return t.lView=e,t}function gw(e){e.lView[Ht]!==e&&(e.lView=null,Oh.push(e))}var mw=nt(re({},Ns),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{yr(e.lView)},consumerOnSignalRead(){this.lView[Ht]=this}}),Rh=100;function Ph(e,t=!0,n=0){var s,a,u;let r=e[Ue],o=r.rendererFactory,i=!1;i||(s=o.begin)==null||s.call(o);try{yw(e,n)}catch(c){throw t&&_h(e,c),c}finally{i||((a=o.end)==null||a.call(o),(u=r.inlineEffectRunner)==null||u.flush())}}function yw(e,t){Na(e,t);let n=0;for(;Iu(e);){if(n===Rh)throw new w(103,!1);n++,Na(e,1)}}function vw(e,t,n,r){var u;let o=t[I];if((o&256)===256)return;let i=!1;!i&&((u=t[Ue].inlineEffectRunner)==null||u.flush()),Tu(t);let s=null,a=null;!i&&Dw(e)&&(a=hw(t),s=Ml(a));try{hf(t),ev(e.bindingStartIndex),n!==null&&Dh(e,t,n,2,r);let c=(o&3)===3;if(!i)if(c){let f=e.preOrderCheckHooks;f!==null&&ko(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Lo(t,f,0,null),js(t,0)}if(ww(t),Fh(t,0),e.contentQueries!==null&&Mh(e,t),!i)if(c){let f=e.contentCheckHooks;f!==null&&ko(t,f)}else{let f=e.contentHooks;f!==null&&Lo(t,f,1),js(t,1)}kD(e,t);let l=e.components;l!==null&&Lh(t,l,0);let d=e.viewQuery;if(d!==null&&_a(2,d,r),!i)if(c){let f=e.viewCheckHooks;f!==null&&ko(t,f)}else{let f=e.viewHooks;f!==null&&Lo(t,f,2),js(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Ls]){for(let f of t[Ls])f();t[Ls]=null}i||(t[I]&=-73)}catch(c){throw yr(t),c}finally{a!==null&&(Tl(a,s),gw(a)),xu()}}function Dw(e){return e.type!==2}function Fh(e,t){for(let n=Bf(e);n!==null;n=$f(n))for(let r=oe;r<n.length;r++){let o=n[r];kh(o,t)}}function ww(e){for(let t=Bf(e);t!==null;t=$f(t)){if(!(t[I]&yu.HasTransplantedViews))continue;let n=t[Tn];for(let r=0;r<n.length;r++){let o=n[r],i=o[te];Hy(o)}}}function Ew(e,t,n){let r=bt(t,e);kh(r,n)}function kh(e,t){Eu(e)&&Na(e,t)}function Na(e,t){let r=e[C],o=e[I],i=e[Ht],s=!!(t===0&&o&16);if(s||(s=!!(o&64&&t===0)),s||(s=!!(o&1024)),s||(s=!!(i!=null&&i.dirty&&Os(i))),i&&(i.dirty=!1),e[I]&=-9217,s)vw(r,e,r.template,e[Ee]);else if(o&8192){Fh(e,1);let a=r.components;a!==null&&Lh(e,a,1)}}function Lh(e,t,n){for(let r=0;r<t.length;r++)Ew(e,t[r],n)}function ec(e){var t;for((t=e[Ue].changeDetectionScheduler)==null||t.notify();e;){e[I]|=64;let n=vr(e);if(Du(e)&&!n)return e;e=n}return null}var qt=class{get rootNodes(){let t=this._lView,n=t[C];return Er(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Ee]}set context(t){this._lView[Ee]=t}get destroyed(){return(this._lView[I]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[te];if(be(t)){let n=t[Go],r=n?n.indexOf(this):-1;r>-1&&(Jo(t,r),Ho(n,r))}this._attachedToViewContainer=!1}Uu(this._lView[C],this._lView)}onDestroy(t){pf(this._lView,t)}markForCheck(){ec(this._cdRefInjectingView||this._lView)}detach(){this._lView[I]&=-129}reattach(){aa(this._lView),this._lView[I]|=128}detectChanges(){this._lView[I]|=1024,Ph(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new w(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,ah(this._lView[C],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new w(902,!1);this._appRef=t,aa(this._lView)}},Yt=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=Cw;let e=t;return e})(),Iw=Yt,bw=class extends Iw{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){var t;return((t=this._declarationTContainer.tView)==null?void 0:t.ssrId)||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Sh(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new qt(o)}};function Cw(){return ji(ie(),E())}function ji(e,t){return e.type&4?new bw(t,e,Ln(e,t)):null}var Aa="<-- AT THIS LOCATION";function Mw(e){switch(e){case 4:return"view container";case 2:return"element";case 8:return"ng-container";case 32:return"icu";case 64:return"i18n";case 16:return"projection";case 1:return"text";default:return"<unknown>"}}function Tw(e,t){let n=`During serialization, Angular was unable to find an element in the DOM:

`,r=`${Aw(e,t,!1)}

`,o=Rw();throw new w(-502,n+r+o)}function xw(e){let t="During serialization, Angular detected DOM nodes that were created outside of Angular context and provided as projectable nodes (likely via `ViewContainerRef.createComponent` or `createComponent` APIs). Hydration is not supported for such cases, consider refactoring the code to avoid this pattern or using `ngSkipHydration` on the host element of the component.\n\n",n=`${Ow(e)}

`,r=t+n+Pw();return new w(-503,r)}function _w(e){let t=[];if(e.attrs)for(let n=0;n<e.attrs.length;){let r=e.attrs[n++];if(typeof r=="number")break;let o=e.attrs[n++];t.push(`${r}="${ei(o)}"`)}return t.join(" ")}var Sw=new Set(["ngh","ng-version","ng-server-context"]);function Nw(e){let t=[];for(let n=0;n<e.attributes.length;n++){let r=e.attributes[n];Sw.has(r.name)||t.push(`${r.name}="${ei(r.value)}"`)}return t.join(" ")}function Hs(e,t="\u2026"){switch(e.type){case 1:return`#text${e.value?`(${e.value})`:""}`;case 2:let r=_w(e),o=e.value.toLowerCase();return`<${o}${r?" "+r:""}>${t}</${o}>`;case 8:return"<!-- ng-container -->";case 4:return"<!-- container -->";default:return`#node(${Mw(e.type)})`}}function Vo(e,t="\u2026"){var r;let n=e;switch(n.nodeType){case Node.ELEMENT_NODE:let o=n.tagName.toLowerCase(),i=Nw(n);return`<${o}${i?" "+i:""}>${t}</${o}>`;case Node.TEXT_NODE:let s=n.textContent?ei(n.textContent):"";return`#text${s?`(${s})`:""}`;case Node.COMMENT_NODE:return`<!-- ${ei((r=n.textContent)!=null?r:"")} -->`;default:return`#node(${n.nodeType})`}}function Aw(e,t,n){let r="  ",o="";t.prev?(o+=r+`\u2026
`,o+=r+Hs(t.prev)+`
`):t.type&&t.type&12&&(o+=r+`\u2026
`),n?(o+=r+Hs(t)+`
`,o+=r+`<!-- container -->  ${Aa}
`):o+=r+Hs(t)+`  ${Aa}
`,o+=r+`\u2026
`;let i=t.type?Hu(e[C],t,e):null;return i&&(o=Vo(i,`
`+o)),o}function Ow(e){let t="  ",n="",r=e;return r.previousSibling&&(n+=t+`\u2026
`,n+=t+Vo(r.previousSibling)+`
`),n+=t+Vo(r)+`  ${Aa}
`,e.nextSibling&&(n+=t+`\u2026
`),e.parentNode&&(n=Vo(r.parentNode,`
`+n)),n}function Rw(e){return`To fix this problem:
  * check ${e?`the "${e}"`:"corresponding"} component for hydration-related issues
  * check to see if your template has valid HTML structure
  * or skip hydration by adding the \`ngSkipHydration\` attribute to its host node in a template

`}function Pw(){return`Note: attributes are only displayed to better represent the DOM but have no effect on hydration mismatches.

`}function Fw(e){return e.replace(/\s+/gm,"")}function ei(e,t=50){return e?(e=Fw(e),e.length>t?`${e.substring(0,t-1)}\u2026`:e):""}function jh(e){var o;let t=(o=e[mr])!=null?o:[],r=e[te][F];for(let i of t)kw(i,r);e[mr]=ce}function kw(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[_n];for(;n<o;){let i=r.nextSibling;Gu(t,r,!1),r=i,n++}}}function Vh(e){jh(e);for(let t=oe;t<e.length;t++)ti(e[t])}function Lw(e){var n;let t=(n=e[Se])==null?void 0:n.i18nNodes;if(t){let r=e[F];for(let o of t.values())Gu(r,o,!1);e[Se].i18nNodes=void 0}}function ti(e){Lw(e);let t=e[C];for(let n=V;n<t.bindingStartIndex;n++)if(be(e[n])){let r=e[n];Vh(r)}else it(e[n])&&ti(e[n])}function jw(e){let t=e._views;for(let n of t){let r=Gf(n);if(r!==null&&r[Z]!==null)if(it(r))ti(r);else{let o=r[Z];ti(o),Vh(r)}}}var Vw=new RegExp(`^(\\d+)*(${Ru}|${Ou})*(.*)`);function Bw(e,t){let n=[e];for(let r of t){let o=n.length-1;if(o>0&&n[o-1]===r){let i=n[o]||1;n[o]=i+1}else n.push(r,"")}return n.join("")}function $w(e){let t=e.match(Vw),[n,r,o,i]=t,s=r?parseInt(r,10):o,a=[];for(let[u,c,l]of i.matchAll(/(f|n)(\d*)/g)){let d=parseInt(l,10)||1;a.push(c,d)}return[s,...a]}function Uw(e){var t;return!e.prev&&((t=e.parent)==null?void 0:t.type)===8}function zs(e){return e.index-V}function Ir(e,t){var n;return!(e.type&16)&&!!t[e.index]&&!((n=ee(t[e.index]))!=null&&n.isConnected)}function Hw(e,t){let n=e.i18nNodes;if(n){let r=n.get(t);return r&&n.delete(t),r}return null}function Vi(e,t,n,r){var s;let o=zs(r),i=Hw(e,o);if(!i){let a=e.data[ir];if(a!=null&&a[o])i=Gw(a[o],n);else if(t.firstChild===r)i=e.firstChild;else{let u=r.prev===null,c=(s=r.prev)!=null?s:r.parent;if(Uw(r)){let l=zs(r.parent);i=ya(e,l)}else{let l=Ce(c,n);if(u)i=l.firstChild;else{let d=zs(c),f=ya(e,d);if(c.type===2&&f){let p=ku(e,d)+1;i=Bi(p,f)}else i=l.nextSibling}}}}return i}function Bi(e,t){let n=t;for(let r=0;r<e;r++)n=n.nextSibling;return n}function zw(e,t){let n=e;for(let r=0;r<t.length;r+=2){let o=t[r],i=t[r+1];for(let s=0;s<i;s++)switch(o){case wr.FirstChild:n=n.firstChild;break;case wr.NextSibling:n=n.nextSibling;break}}return n}function Gw(e,t){let[n,...r]=$w(e),o;if(n===Ou)o=t[me][Z];else if(n===Ru)o=yD(t[me][Z]);else{let i=Number(n);o=ee(t[i+V])}return zw(o,r)}function Oa(e,t){if(e===t)return[];if(e.parentElement==null||t.parentElement==null)return null;if(e.parentElement===t.parentElement)return Ww(e,t);{let n=t.parentElement,r=Oa(e,n),o=Oa(n.firstChild,t);return!r||!o?null:[...r,wr.FirstChild,...o]}}function Ww(e,t){let n=[],r=null;for(r=e;r!=null&&r!==t;r=r.nextSibling)n.push(wr.NextSibling);return r==null?null:n}function cd(e,t,n){let r=Oa(e,t);return r===null?null:Bw(n,r)}function qw(e,t){let n=e.parent,r,o,i;for(;n!==null&&Ir(n,t);)n=n.parent;n===null||!(n.type&3)?(r=i=Ou,o=t[me][Z]):(r=n.index,o=ee(t[r]),i=U(r-V));let s=ee(t[e.index]);if(e.type&12){let u=cr(t,e);u&&(s=u)}let a=cd(o,s,i);if(a===null&&o!==s){let u=o.ownerDocument.body;if(a=cd(u,s,Ru),a===null)throw Tw(t,e)}return a}function Yw(e,t){var r;let n=[];for(let o of t)for(let i=0;i<((r=o[yn])!=null?r:1);i++){let s={data:o,firstChild:null};o[_n]>0&&(s.firstChild=e,e=Bi(o[_n],e)),n.push(s)}return[e,n]}var Bh=()=>null;function Zw(e,t){let n=e[mr];return!t||n===null||n.length===0?null:n[0].data[ma]===t?n.shift():(jh(e),null)}function Qw(){Bh=Zw}function Ra(e,t){return Bh(e,t)}var ni=class{},Pa=class{},ri=class{};function Kw(e){let t=Error(`No component factory found for ${le(e)}.`);return t[Jw]=e,t}var Jw="ngComponent";var Fa=class{resolveComponentFactory(t){throw Kw(t)}},$i=(()=>{let t=class t{};t.NULL=new Fa;let e=t;return e})(),br=class{},Ui=(()=>{let t=class t{constructor(){this.destroyNode=null}};t.__NG_ELEMENT_ID__=()=>Xw();let e=t;return e})();function Xw(){let e=E(),t=ie(),n=bt(t.index,e);return(it(n)?n:e)[F]}var eE=(()=>{let t=class t{};t.\u0275prov=x({token:t,providedIn:"root",factory:()=>null});let e=t;return e})(),Gs={};var ld=new Set;function xt(e){var t;ld.has(e)||(ld.add(e),(t=performance==null?void 0:performance.mark)==null||t.call(performance,"mark_feature_usage",{detail:{feature:e}}))}function dd(...e){}function tE(){let e=typeof ge.requestAnimationFrame=="function",t=ge[e?"requestAnimationFrame":"setTimeout"],n=ge[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){let r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);let o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}var q=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new je(!1),this.onMicrotaskEmpty=new je(!1),this.onStable=new je(!1),this.onError=new je(!1),typeof Zone>"u")throw new w(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=tE().nativeRequestAnimationFrame,oE(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new w(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new w(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,nE,dd,dd);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},nE={};function tc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function rE(e){e.isCheckStableRunning||e.lastRequestAnimationFrameId!==-1||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(ge,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,ka(e),e.isCheckStableRunning=!0,tc(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),ka(e))}function oE(e){let t=()=>{rE(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(iE(a))return n.invokeTask(o,i,s,a);try{return fd(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),hd(e)}},onInvoke:(n,r,o,i,s,a,u)=>{try{return fd(e),n.invoke(o,i,s,a,u)}finally{e.shouldCoalesceRunChangeDetection&&t(),hd(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,ka(e),tc(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function ka(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.lastRequestAnimationFrameId!==-1?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function fd(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function hd(e){e._nesting--,tc(e)}var La=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new je,this.onMicrotaskEmpty=new je,this.onStable=new je,this.onError=new je}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function iE(e){var t;return!Array.isArray(e)||e.length!==1?!1:((t=e[0].data)==null?void 0:t.__ignore_ng_zone__)===!0}function sE(e="zone.js",t){return e==="noop"?new La:e==="zone.js"?new q(t):e}var Dn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Dn||{}),aE={destroy(){}};function uE(e,t){var c,l,d;!t&&Ny(uE);let n=(c=t==null?void 0:t.injector)!=null?c:D(Ct);if(!sr(n))return aE;xt("NgAfterNextRender");let r=n.get(nc),o=(l=r.handler)!=null?l:r.handler=new Va,i=(d=t==null?void 0:t.phase)!=null?d:Dn.MixedReadWrite,s=()=>{o.unregister(u),a()},a=n.get(Su).onDestroy(s),u=gu(n,()=>new ja(i,()=>{s(),e()}));return o.register(u),{destroy:s}}var ja=class{constructor(t,n){var r;this.phase=t,this.callbackFn=n,this.zone=D(q),this.errorHandler=D(He,{optional:!0}),(r=D(ni,{optional:!0}))==null||r.notify(1)}invoke(){var t;try{this.zone.runOutsideAngular(this.callbackFn)}catch(n){(t=this.errorHandler)==null||t.handleError(n)}}},Va=class{constructor(){this.executingCallbacks=!1,this.buckets={[Dn.EarlyRead]:new Set,[Dn.Write]:new Set,[Dn.MixedReadWrite]:new Set,[Dn.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},nc=(()=>{let t=class t{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){var r;this.executeInternalCallbacks(),(r=this.handler)==null||r.execute()}executeInternalCallbacks(){let r=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let o of r)o()}ngOnDestroy(){var r;(r=this.handler)==null||r.destroy(),this.handler=null,this.internalCallbacks.length=0}};t.\u0275prov=x({token:t,providedIn:"root",factory:()=>new t});let e=t;return e})();function oi(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Js(o,a);else if(i==2){let u=a,c=t[++s];r=Js(r,u+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var ii=class extends $i{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Qe(t);return new Zt(n,this.ngModule)}};function pd(e){let t=[];for(let n in e){if(!e.hasOwnProperty(n))continue;let r=e[n];r!==void 0&&t.push({propName:Array.isArray(r)?r[0]:r,templateName:n})}return t}function cE(e){let t=e.toLowerCase();return t==="svg"?lf:t==="math"?jy:null}var Ba=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=vi(r);let o=this.injector.get(t,Gs,r);return o!==Gs||n===Gs?o:this.parentInjector.get(t,n,r)}},Zt=class extends ri{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=pd(t.inputs);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return pd(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=hy(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=R(null);try{o=o||this.ngModule;let a=o instanceof Ke?o:o==null?void 0:o.injector;a&&this.componentDef.getStandaloneInjector!==null&&(a=this.componentDef.getStandaloneInjector(a)||a);let u=a?new Ba(t,a):t,c=u.get(br,null);if(c===null)throw new w(407,!1);let l=u.get(eE,null),d=u.get(nc,null),f=u.get(ni,null),h={rendererFactory:c,sanitizer:l,inlineEffectRunner:null,afterRenderEventManager:d,changeDetectionScheduler:f},p=c.createRenderer(null,this.componentDef),y=this.componentDef.selectors[0][0]||"div",m=r?VD(p,r,this.componentDef.encapsulation,u):Oi(p,y,cE(y)),g=512;this.componentDef.signals?g|=4096:this.componentDef.onPush||(g|=16);let _=null;m!==null&&(_=Fu(m,u,!0));let z=Qu(0,null,null,1,0,null,null,null,null,null,null),O=Fi(null,z,null,g,null,null,h,p,u,null,_);Tu(O);let se,De;try{let ne=this.componentDef,tt,qn=null;ne.findHostDirectiveDefs?(tt=[],qn=new Map,ne.findHostDirectiveDefs(ne,tt,qn),tt.push(ne)):tt=[ne];let At=lE(O,m),Yn=dE(At,m,ne,tt,O,h,p);De=wu(z,V),m&&pE(p,ne,m,r),n!==void 0&&gE(De,this.ngContentSelectors,n),se=hE(Yn,ne,tt,qn,O,[mE]),Xu(z,O,null)}finally{xu()}return new $a(this.componentType,se,Ln(De,O),O,De)}finally{R(i)}}},$a=class extends Pa{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new qt(o,void 0,!1),this.componentType=t}setInput(t,n){var i;let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if((i=this.previousInputValues)!=null||(this.previousInputValues=new Map),this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let s=this._rootLView;Ju(s[C],s,o,t,n),this.previousInputValues.set(t,n);let a=bt(this._tNode.index,s);ec(a)}}get injector(){return new $t(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function lE(e,t){let n=e[C],r=V;return e[r]=t,jn(n,r,2,"#host",null)}function dE(e,t,n,r,o,i,s){let a=o[C];fE(r,e,t,s);let u=null;t!==null&&(u=Fu(t,o[Mn]));let c=i.rendererFactory.createRenderer(t,n),l=16;n.signals?l=4096:n.onPush&&(l=64);let d=Fi(o,wh(n),null,l,o[e.index],e,i,c,null,null,u);return a.firstCreatePass&&xa(a,e,r.length-1),Li(o,d),o[e.index]=d}function fE(e,t,n,r){for(let o of e)t.mergedAttrs=fr(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(oi(t,t.mergedAttrs,!0),n!==null&&gh(r,n,t))}function hE(e,t,n,r,o,i){let s=ie(),a=o[C],u=Ce(s,o);Ih(a,o,s,n,null,r);for(let l=0;l<n.length;l++){let d=s.directiveStart+l,f=Wt(o,a,d,s);Et(f,o)}bh(a,o,s),u&&Et(u,o);let c=Wt(o,a,s.directiveStart+s.componentOffset,s);if(e[Ee]=o[Ee]=c,i!==null)for(let l of i)l(c,t);return qu(a,s,o),c}function pE(e,t,n,r){if(r)na(e,n,["ng-version","17.3.12"]);else{let{attrs:o,classes:i}=py(t.selectors[0]);o&&na(e,n,o),i&&i.length>0&&ph(e,n,i.join(" "))}}function gE(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function mE(){let e=ie();Ti(E()[C],e)}var Bn=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=yE;let e=t;return e})();function yE(){let e=ie();return Uh(e,E())}var vE=Bn,$h=class extends vE{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Ln(this._hostTNode,this._hostLView)}get injector(){return new $t(this._hostTNode,this._hostLView)}get parentInjector(){let t=_u(this._hostTNode,this._hostLView);if(Tf(t)){let n=qo(t,this._hostLView),r=Wo(t),o=n[C].data[r+8];return new $t(o,n)}else return new $t(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=gd(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-oe}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Ra(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Sa(this._hostTNode,s)),a}createComponent(t,n,r,o,i){var p,y,m;let s=t&&!Oy(t),a;if(s)a=n;else{let g=n||{};a=g.index,r=g.injector,o=g.projectableNodes,i=g.environmentInjector||g.ngModuleRef}let u=s?t:new Zt(Qe(t)),c=r||this.parentInjector;if(!i&&u.ngModule==null){let _=(s?c:this.parentInjector).get(Ke,null);_&&(i=_)}let l=Qe((p=u.componentType)!=null?p:{}),d=Ra(this._lContainer,(y=l==null?void 0:l.id)!=null?y:null),f=(m=d==null?void 0:d.firstChild)!=null?m:null,h=u.create(c,o,f,i);return this.insertImpl(h.hostView,a,Sa(this._hostTNode,d)),h}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Uy(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[te],c=new $h(u,u[Ie],u[te]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Nh(s,o,i,r),t.attachToViewContainerRef(),Bd(Ws(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=gd(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Jo(this._lContainer,n);r&&(Ho(Ws(this._lContainer),n),Uu(r[C],r))}detach(t){let n=this._adjustIndex(t,-1),r=Jo(this._lContainer,n);return r&&Ho(Ws(this._lContainer),n)!=null?new qt(r):null}_adjustIndex(t,n=0){return t==null?this.length+n:t}};function gd(e){return e[Go]}function Ws(e){return e[Go]||(e[Go]=[])}function Uh(e,t){let n,r=t[e.index];return be(r)?n=r:(n=Ch(r,t,null,e),t[e.index]=n,Li(t,n)),Hh(n,t,e,r),new $h(n,e,t)}function DE(e,t){let n=e[F],r=n.createComment(""),o=Ce(t,e),i=zu(n,o);return Xo(n,i,r,SD(n,o),!1),r}var Hh=zh,rc=()=>!1;function wE(e,t,n){return rc(e,t,n)}function zh(e,t,n,r){if(e[st])return;let o;n.type&8?o=ee(r):o=DE(t,n),e[st]=o}function EE(e,t,n){var l;if(e[st]&&e[mr])return!0;let r=n[Se],o=t.index-V;if(!r||Ko(t)||Si(r,o))return!1;let s=ya(r,o),a=(l=r.data[Bt])==null?void 0:l[o],[u,c]=Yw(s,a);return e[st]=u,e[mr]=c,!0}function IE(e,t,n,r){rc(e,n,t)||zh(e,t,n,r)}function bE(){Hh=IE,rc=EE}var Ua=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Ha=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)oc(t,n).matches!==null&&this.queries[n].setDirty()}},si=class{constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=AE(t):this.predicate=t}},za=class e{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Ga=class e{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,CE(n,i)),this.matchTNodeWithReadOption(t,n,jo(n,t,i,!1,!1))}else r===Yt?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,jo(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Mt||o===Bn||o===Yt&&n.type&4)this.addMatch(n.index,-2);else{let i=jo(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function CE(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function ME(e,t){return e.type&11?Ln(e,t):e.type&4?ji(e,t):null}function TE(e,t,n,r){return n===-1?ME(t,e):n===-2?xE(e,t,r):Wt(e,e[C],n,t)}function xE(e,t,n){if(n===Mt)return Ln(t,e);if(n===Yt)return ji(t,e);if(n===Bn)return Uh(t,e)}function Gh(e,t,n,r){let o=t[Be].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push(TE(t,l,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function Wa(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Gh(e,t,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=oe;d<l.length;d++){let f=l[d];f[_r]===f[te]&&Wa(f[C],f,c,r)}if(l[Tn]!==null){let d=l[Tn];for(let f=0;f<d.length;f++){let h=d[f];Wa(h[C],h,c,r)}}}}}return r}function _E(e,t){return e[Be].queries[t].queryList}function Wh(e,t,n){var i;let r=new pa((n&4)===4);return HD(e,t,r,r.destroy),((i=t[Be])!=null?i:t[Be]=new Ha).queries.push(new Ua(r))-1}function SE(e,t,n){let r=G();return r.firstCreatePass&&(qh(r,new si(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Wh(r,E(),t)}function NE(e,t,n,r){let o=G();if(o.firstCreatePass){let i=ie();qh(o,new si(t,n,r),i.index),OE(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Wh(o,E(),n)}function AE(e){return e.split(",").map(t=>t.trim())}function qh(e,t,n){e.queries===null&&(e.queries=new za),e.queries.track(new Ga(t,n))}function OE(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function oc(e,t){return e.queries.getByIndex(t)}function RE(e,t){let n=e[C],r=oc(n,t);return r.crossesNgTemplate?Wa(n,e,t,[]):Gh(n,e,r,t)}function PE(e){return typeof e=="function"&&e[gt]!==void 0}function AO(e,t){xt("NgSignals");let n=Rl(e),r=n[gt];return t!=null&&t.equal&&(r.equal=t.equal),n.set=o=>Co(r,o),n.update=o=>Pl(r,o),n.asReadonly=FE.bind(n),n}function FE(){let e=this[gt];if(e.readonlyFn===void 0){let t=()=>this();t[gt]=e,e.readonlyFn=t}return e.readonlyFn}function Yh(e){return PE(e)&&typeof e.set=="function"}function kE(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(BE))}return i}return ai.forEach((o,i)=>{var c,l;let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(d=>{o.template=d}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&((c=o.styleUrls)!=null&&c.length))throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if((l=o.styleUrls)!=null&&l.length){let d=o.styles.length,f=o.styleUrls;o.styleUrls.forEach((h,p)=>{a.push(""),s.push(r(h).then(y=>{a[d+p]=y,f.splice(f.indexOf(h),1),f.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(d=>{a.push(d),o.styleUrl=void 0}));let u=Promise.all(s).then(()=>$E(i));t.push(u)}),jE(),Promise.all(t).then(()=>{})}var ai=new Map,LE=new Set;function jE(){let e=ai;return ai=new Map,e}function VE(){return ai.size===0}function BE(e){return typeof e=="string"?e:e.text()}function $E(e){LE.delete(e)}function UE(e){return Object.getPrototypeOf(e.prototype).constructor}function HE(e){let t=UE(e.type),n=!0,r=[e];for(;t;){let o;if(wt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new w(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Oo(e.inputs),s.inputTransforms=Oo(e.inputTransforms),s.declaredInputs=Oo(e.declaredInputs),s.outputs=Oo(e.outputs);let a=o.hostBindings;a&&YE(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&WE(e,u),c&&qE(e,c),zE(e,o),Om(e.outputs,o.outputs),wt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===HE&&(n=!1)}}t=Object.getPrototypeOf(t)}GE(r)}function zE(e,t){var n;for(let r in t.inputs){if(!t.inputs.hasOwnProperty(r)||e.inputs.hasOwnProperty(r))continue;let o=t.inputs[r];if(o!==void 0&&(e.inputs[r]=o,e.declaredInputs[r]=t.declaredInputs[r],t.inputTransforms!==null)){let i=Array.isArray(o)?o[0]:o;if(!t.inputTransforms.hasOwnProperty(i))continue;(n=e.inputTransforms)!=null||(e.inputTransforms={}),e.inputTransforms[i]=t.inputTransforms[i]}}}function GE(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=fr(o.hostAttrs,n=fr(n,o.hostAttrs))}}function Oo(e){return e===In?{}:e===ce?[]:e}function WE(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function qE(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function YE(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function ZE(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var It=class{},qa=class{};var ui=class extends It{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new ii(this);let o=Zd(t);this._bootstrapComponents=sh(o.bootstrap),this._r3Injector=Ff(t,n,[{provide:It,useValue:this},{provide:$i,useValue:this.componentFactoryResolver},...r],le(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ci=class extends qa{constructor(t){super(),this.moduleType=t}create(t){return new ui(this.moduleType,t,[])}};function QE(e,t,n){return new ui(e,t,n)}var li=class extends It{constructor(t){super(),this.componentFactoryResolver=new ii(this),this.instance=null;let n=new hr([...t.providers,{provide:It,useValue:this},{provide:$i,useValue:this.componentFactoryResolver}],t.parent||Ei(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function KE(e,t,n=null){return new li({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Hi=(()=>{let t=class t{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new tr(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let r=this.taskId++;return this.pendingTasks.add(r),r}remove(r){this.pendingTasks.delete(r),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function Zh(e){return ic(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function JE(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function ic(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function $n(e,t,n){return e[t]=n}function Qh(e,t){return e[t]}function ye(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Sn(e,t,n,r){let o=ye(e,t,n);return ye(e,t+1,r)||o}function Kh(e,t,n,r,o){let i=Sn(e,t,n,r);return ye(e,t+2,o)||i}function Rr(e,t,n,r,o,i){let s=Sn(e,t,n,r);return Sn(e,t+2,o,i)||s}function Pr(e){return(e.flags&32)===32}function XE(e,t,n,r,o,i,s,a,u){let c=t.consts,l=jn(t,e,4,s||null,xn(c,a));Ku(t,n,l,xn(c,u)),Ti(t,l);let d=l.tView=Qu(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function eI(e,t,n,r,o,i,s,a){let u=E(),c=G(),l=e+V,d=c.firstCreatePass?XE(l,c,u,t,n,r,o,i,s):c.data[l];Jt(d,!1);let f=Jh(c,u,d,e);Mi()&&Ri(c,u,f,d),Et(f,u);let h=Ch(f,u,f,d);return u[l]=h,Li(u,h),wE(h,d,u),bi(d)&&Yu(c,u,d),s!=null&&Zu(u,d,a),eI}var Jh=Xh;function Xh(e,t,n,r){return Xe(!0),t[F].createComment("")}function tI(e,t,n,r){var l,d;let o=t[Se],i=!o||Fn()||Pr(n)||Si(o,r);if(Xe(i),i)return Xh(e,t,n,r);let s=(d=(l=o.data[or])==null?void 0:l[r])!=null?d:null;s!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=s);let a=Vi(o,e,t,n);_i(o,r,a);let u=ku(o,r);return Bi(u,a)}function nI(){Jh=tI}function rI(e,t,n,r){let o=E(),i=kn();if(ye(o,i,t)){let s=G(),a=Nr();ow(a,o,e,t,n,r)}return rI}function ep(e,t,n,r){return ye(e,kn(),n)?t+U(n)+r:Q}function oI(e,t,n,r,o,i){let s=Sr(),a=Sn(e,s,n,o);return Xt(2),a?t+U(n)+r+U(o)+i:Q}function iI(e,t,n,r,o,i,s,a){let u=Sr(),c=Kh(e,u,n,o,s);return Xt(3),c?t+U(n)+r+U(o)+i+U(s)+a:Q}function sI(e,t,n,r,o,i,s,a,u,c){let l=Sr(),d=Rr(e,l,n,o,s,u);return Xt(4),d?t+U(n)+r+U(o)+i+U(s)+a+U(u)+c:Q}function aI(e,t,n,r,o,i,s,a,u,c,l,d){let f=Sr(),h=Rr(e,f,n,o,s,u);return h=ye(e,f+4,l)||h,Xt(5),h?t+U(n)+r+U(o)+i+U(s)+a+U(u)+c+U(l)+d:Q}function uI(e,t,n,r,o,i,s,a,u,c,l,d,f,h){let p=Sr(),y=Rr(e,p,n,o,s,u);return y=Sn(e,p+4,l,f)||y,Xt(6),y?t+U(n)+r+U(o)+i+U(s)+a+U(u)+c+U(l)+d+U(f)+h:Q}function Ro(e,t){return e<<17|t<<2}function Qt(e){return e>>17&32767}function cI(e){return(e&2)==2}function lI(e,t){return e&131071|t<<17}function Ya(e){return e|2}function Nn(e){return(e&131068)>>2}function qs(e,t){return e&-131069|t<<2}function dI(e){return(e&1)===1}function Za(e){return e|1}function fI(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Qt(s),u=Nn(s);e[r]=n;let c=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Tr(d,l)>0)&&(c=!0)}else l=n;if(o)if(u!==0){let f=Qt(e[a+1]);e[r+1]=Ro(f,a),f!==0&&(e[f+1]=qs(e[f+1],r)),e[a+1]=lI(e[a+1],r)}else e[r+1]=Ro(a,0),a!==0&&(e[a+1]=qs(e[a+1],r)),a=r;else e[r+1]=Ro(u,0),a===0?a=r:e[u+1]=qs(e[u+1],r),u=r;c&&(e[r+1]=Ya(e[r+1])),md(e,l,r,!0),md(e,l,r,!1),hI(t,l,e,r,i),s=Ro(a,u),i?t.classBindings=s:t.styleBindings=s}function hI(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Tr(i,t)>=0&&(n[r+1]=Za(n[r+1]))}function md(e,t,n,r){let o=e[n+1],i=t===null,s=r?Qt(o):Nn(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];pI(u,t)&&(a=!0,e[s+1]=r?Za(c):Ya(c)),s=r?Qt(c):Nn(c)}a&&(e[n+1]=r?Ya(o):Za(o))}function pI(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Tr(e,t)>=0:!1}var Le={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function gI(e){return e.substring(Le.key,Le.keyEnd)}function mI(e){return yI(e),tp(e,np(e,0,Le.textEnd))}function tp(e,t){let n=Le.textEnd;return n===t?-1:(t=Le.keyEnd=vI(e,Le.key=t,n),np(e,t,n))}function yI(e){Le.key=0,Le.keyEnd=0,Le.value=0,Le.valueEnd=0,Le.textEnd=e.length}function np(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function vI(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function DI(e,t,n){let r=E(),o=kn();if(ye(r,o,t)){let i=G(),s=Nr();ki(i,s,r,e,t,r[F],n,!1)}return DI}function Qa(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";Ju(e,n,i[s],s,r)}function rp(e,t,n){return op(e,t,n,!1),rp}function wI(e,t){return op(e,t,null,!0),wI}function OO(e){II(_I,EI,e,!0)}function EI(e,t){for(let n=mI(t);n>=0;n=tp(t,n))fu(e,gI(t),!0)}function op(e,t,n,r){let o=E(),i=G(),s=Xt(2);if(i.firstUpdatePass&&sp(i,e,s,r),t!==Q&&ye(o,s,t)){let a=i.data[Ne()];ap(i,a,o,o[F],e,o[s+1]=NI(t,n),r,s)}}function II(e,t,n,r){let o=G(),i=Xt(2);o.firstUpdatePass&&sp(o,null,i,r);let s=E();if(n!==Q&&ye(s,i,n)){let a=o.data[Ne()];if(up(a,r)&&!ip(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=Js(u,n||"")),Qa(o,a,s,n,r)}else SI(o,a,s,s[F],s[i+1],s[i+1]=xI(e,t,n),r,i)}}function ip(e,t){return t>=e.expandoStartIndex}function sp(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Ne()],s=ip(e,n);up(i,r)&&t===null&&!s&&(t=!1),t=bI(o,i,t,r),fI(o,i,t,n,s,r)}}function bI(e,t,n,r){let o=ov(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Ys(null,e,t,n,r),n=Cr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Ys(o,e,t,n,r),i===null){let u=CI(e,t,r);u!==void 0&&Array.isArray(u)&&(u=Ys(null,e,t,u[1],r),u=Cr(u,t.attrs,r),MI(e,t,r,u))}else i=TI(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function CI(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Nn(r)!==0)return e[Qt(r)]}function MI(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Qt(o)]=r}function TI(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Cr(r,s,n)}return Cr(r,t.attrs,n)}function Ys(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Cr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Cr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),fu(e,s,n?!0:t[++i]))}return e===void 0?null:e}function xI(e,t,n){if(n==null||n==="")return ce;let r=[],o=Oe(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function _I(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&fu(e,r,n)}function SI(e,t,n,r,o,i,s,a){o===Q&&(o=ce);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let f=u<o.length?o[u+1]:void 0,h=c<i.length?i[c+1]:void 0,p=null,y;l===d?(u+=2,c+=2,f!==h&&(p=d,y=h)):d===null||l!==null&&l<d?(u+=2,p=l):(c+=2,p=d,y=h),p!==null&&ap(e,t,n,r,p,y,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function ap(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=dI(c)?yd(u,t,n,o,Nn(c),s):void 0;if(!di(l)){di(i)||cI(c)&&(i=yd(u,null,n,o,a,s));let d=ff(Ne(),n);PD(r,s,d,o,i)}}function yd(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,f=n[o+1];f===Q&&(f=d?ce:void 0);let h=d?Fs(f,r):l===r?f:void 0;if(c&&!di(h)&&(h=Fs(u,r)),di(h)&&(a=h,s))return a;let p=e[o+1];o=s?Qt(p):Nn(p)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=Fs(u,r))}return a}function di(e){return e!==void 0}function NI(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=le(Oe(e)))),e}function up(e,t){return(e.flags&(t?8:16))!==0}function RO(e,t,n){xt("NgControlFlow");let r=E(),o=kn(),i=AI(r,V+e),s=0;if(ye(r,o,t)){let a=R(null);try{if(fw(i,s),t!==-1){let u=OI(r[C],V+t),c=Ra(i,u.tView.ssrId),l=Sh(r,u,n,{dehydratedView:c});Nh(i,l,s,Sa(u,c))}}finally{R(a)}}else{let a=dw(i,s);a!==void 0&&(a[Ee]=n)}}function AI(e,t){return e[t]}function OI(e,t){return wu(e,t)}function RI(e,t,n,r,o,i){let s=t.consts,a=xn(s,o),u=jn(t,e,2,r,a);return Ku(t,n,u,xn(s,i)),u.attrs!==null&&oi(u,u.attrs,!1),u.mergedAttrs!==null&&oi(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function cp(e,t,n,r){let o=E(),i=G(),s=V+e,a=o[F],u=i.firstCreatePass?RI(s,i,o,t,n,r):i.data[s],c=dp(i,o,u,a,t,e);o[s]=c;let l=bi(u);return Jt(u,!0),gh(a,c,u),!Pr(u)&&Mi()&&Ri(i,o,c,u),Wy()===0&&Et(c,o),qy(),l&&(Yu(i,o,u),qu(i,u,o)),r!==null&&Zu(o,u),cp}function lp(){let e=ie();bu()?Cu():(e=e.parent,Jt(e,!1));let t=e;Zy(t)&&Ky(),Yy();let n=G();return n.firstCreatePass&&(Ti(n,e),vu(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&fv(t)&&Qa(n,t,E(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&hv(t)&&Qa(n,t,E(),t.stylesWithoutHost,!1),lp}function PI(e,t,n,r){return cp(e,t,n,r),lp(),PI}var dp=(e,t,n,r,o,i)=>(Xe(!0),Oi(r,o,bf()));function FI(e,t,n,r,o,i){let s=t[Se],a=!s||Fn()||Pr(n)||Si(s,i);if(Xe(a),a)return Oi(r,o,bf());let u=Vi(s,e,t,n);return Wf(s,i)&&_i(s,i,u.nextSibling),s&&(Lf(n)||jf(u))&&Pn(n)&&(Qy(n),fh(u)),u}function kI(){dp=FI}function LI(e,t,n,r,o){let i=t.consts,s=xn(i,r),a=jn(t,e,8,"ng-container",s);s!==null&&oi(a,s,!0);let u=xn(i,o);return Ku(t,n,a,u),t.queries!==null&&t.queries.elementStart(t,a),a}function jI(e,t,n){let r=E(),o=G(),i=e+V,s=o.firstCreatePass?LI(i,o,r,t,n):o.data[i];Jt(s,!0);let a=fp(o,r,s,e);return r[i]=a,Mi()&&Ri(o,r,a,s),Et(a,r),bi(s)&&(Yu(o,r,s),qu(o,s,r)),n!=null&&Zu(r,s),jI}function VI(){let e=ie(),t=G();return bu()?Cu():(e=e.parent,Jt(e,!1)),t.firstCreatePass&&(Ti(t,e),vu(e)&&t.queries.elementEnd(e)),VI}var fp=(e,t,n,r)=>(Xe(!0),$u(t[F],""));function BI(e,t,n,r){let o,i=t[Se],s=!i||Fn()||Pr(n);if(Xe(s),s)return $u(t[F],"");let a=Vi(i,e,t,n),u=zv(i,r);return _i(i,r,a),o=Bi(u,a),o}function $I(){fp=BI}function PO(){return E()}function UI(e,t,n){let r=E(),o=kn();if(ye(r,o,t)){let i=G(),s=Nr();ki(i,s,r,e,t,r[F],n,!0)}return UI}var Vt=void 0;function HI(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var zI=["en",[["a","p"],["AM","PM"],Vt],[["AM","PM"],Vt,Vt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Vt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Vt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Vt,"{1} 'at' {0}",Vt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",HI],Zs={};function ve(e){let t=GI(e),n=vd(t);if(n)return n;let r=t.split("-")[0];if(n=vd(r),n)return n;if(r==="en")return zI;throw new w(701,!1)}function vd(e){return e in Zs||(Zs[e]=ge.ng&&ge.ng.common&&ge.ng.common.locales&&ge.ng.common.locales[e]),Zs[e]}var W=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(W||{});function GI(e){return e.toLowerCase().replace(/_/g,"-")}var An="en-US",WI="USD";var qI=An;function hp(e){typeof e=="string"&&(qI=e.toLowerCase().replace(/_/g,"-"))}function pp(e,t,n){let r=e[F];switch(n){case Node.COMMENT_NODE:return $u(r,t);case Node.TEXT_NODE:return Bu(r,t);case Node.ELEMENT_NODE:return Oi(r,t,null)}}var YI=(e,t,n,r)=>(Xe(!0),pp(e,n,r));function ZI(e,t,n,r){return Xe(!0),pp(e,n,r)}function QI(){YI=ZI}function KI(e,t,n,r){let o=E(),i=G(),s=ie();return gp(i,o,o[F],s,e,t,r),KI}function JI(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[pr],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function gp(e,t,n,r,o,i,s){let a=bi(r),c=e.firstCreatePass&&xh(e),l=t[Ee],d=Th(t),f=!0;if(r.type&3||s){let y=Ce(r,t),m=s?s(y):y,g=d.length,_=s?O=>s(ee(O[r.index])):r.index,z=null;if(!s&&a&&(z=JI(e,t,o,r.index)),z!==null){let O=z.__ngLastListenerFn__||z;O.__ngNextListenerFn__=i,z.__ngLastListenerFn__=i,f=!1}else{i=wd(r,t,l,i,!1);let O=n.listen(m,o,i);d.push(i,O),c&&c.push(o,_,g,g+1)}}else i=wd(r,t,l,i,!1);let h=r.outputs,p;if(f&&h!==null&&(p=h[o])){let y=p.length;if(y)for(let m=0;m<y;m+=2){let g=p[m],_=p[m+1],se=t[g][_].subscribe(i),De=d.length;d.push(i,se),c&&c.push(o,r.index,De,-(De+1))}}}function Dd(e,t,n,r){let o=R(null);try{return qe(6,t,n),n(r)!==!1}catch(i){return _h(e,i),!1}finally{qe(7,t,n),R(o)}}function wd(e,t,n,r,o){return function i(s){if(s===Function)return r;let a=e.componentOffset>-1?bt(e.index,t):t;ec(a);let u=Dd(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)u=Dd(t,n,c,s)&&u,c=c.__ngNextListenerFn__;return o&&u===!1&&s.preventDefault(),u}}function FO(e=1){return sv(e)}function XI(e,t){let n=null,r=uy(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Wd(e,i,!0):dy(r,i))return o}return n}function kO(e){let t=E()[me][Ie];if(!t.projection){let n=e?e.length:1,r=t.projection=Xm(n,null),o=r.slice(),i=t.child;for(;i!==null;){let s=e?XI(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i),i=i.next}}}function LO(e,t=0,n){let r=E(),o=G(),i=jn(o,V+e,16,null,n||null);i.projection===null&&(i.projection=t),Cu(),(!r[Se]||Fn())&&(i.flags&32)!==32&&OD(o,r,i)}function eb(e,t,n){return mp(e,"",t,"",n),eb}function mp(e,t,n,r,o){let i=E(),s=ep(i,t,n,r);if(s!==Q){let a=G(),u=Nr();ki(a,u,i,e,s,i[F],o,!1)}return mp}function jO(e,t,n,r){NE(e,t,n,r)}function VO(e,t,n){SE(e,t,n)}function BO(e){let t=E(),n=G(),r=yf();Mu(r+1);let o=oc(n,r);if(e.dirty&&$y(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=RE(t,r);e.reset(i,_v),e.notifyOnChanges()}return!0}return!1}function $O(){return _E(E(),yf())}function tb(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function UO(e){let t=Xy();return Ci(t,V+e)}function HO(e,t=""){let n=E(),r=G(),o=e+V,i=r.firstCreatePass?jn(r,o,1,t,null):r.data[o],s=yp(r,n,i,t,e);n[o]=s,Mi()&&Ri(r,n,s,i),Jt(i,!1)}var yp=(e,t,n,r,o)=>(Xe(!0),Bu(t[F],r));function nb(e,t,n,r,o){let i=t[Se],s=!i||Fn()||Pr(n)||Si(i,o);return Xe(s),s?Bu(t[F],r):Vi(i,e,t,n)}function rb(){yp=nb}function ob(e){return vp("",e,""),ob}function vp(e,t,n){let r=E(),o=ep(r,e,t,n);return o!==Q&&Vn(r,Ne(),o),vp}function ib(e,t,n,r,o){let i=E(),s=oI(i,e,t,n,r,o);return s!==Q&&Vn(i,Ne(),s),ib}function sb(e,t,n,r,o,i,s){let a=E(),u=iI(a,e,t,n,r,o,i,s);return u!==Q&&Vn(a,Ne(),u),sb}function ab(e,t,n,r,o,i,s,a,u){let c=E(),l=sI(c,e,t,n,r,o,i,s,a,u);return l!==Q&&Vn(c,Ne(),l),ab}function ub(e,t,n,r,o,i,s,a,u,c,l){let d=E(),f=aI(d,e,t,n,r,o,i,s,a,u,c,l);return f!==Q&&Vn(d,Ne(),f),ub}function cb(e,t,n,r,o,i,s,a,u,c,l,d,f){let h=E(),p=uI(h,e,t,n,r,o,i,s,a,u,c,l,d,f);return p!==Q&&Vn(h,Ne(),p),cb}function lb(e,t,n){Yh(t)&&(t=t());let r=E(),o=kn();if(ye(r,o,t)){let i=G(),s=Nr();ki(i,s,r,e,t,r[F],n,!1)}return lb}function zO(e,t){let n=Yh(e);return n&&e.set(t),n}function db(e,t){let n=E(),r=G(),o=ie();return gp(r,n,n[F],o,e,t),db}function fb(e,t,n){let r=G();if(r.firstCreatePass){let o=wt(e);Ka(n,r.data,r.blueprint,o,!0),Ka(t,r.data,r.blueprint,o,!1)}}function Ka(e,t,n,r,o){if(e=ue(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ka(e[i],t,n,r,o);else{let i=G(),s=E(),a=ie(),u=Cn(e)?e:ue(e.provide),c=tf(e),l=a.providerIndexes&1048575,d=a.directiveStart,f=a.providerIndexes>>20;if(Cn(e)||!e.multi){let h=new Gt(c,o,K),p=Ks(u,t,o?l:l+f,d);p===-1?(la(Zo(a,s),i,u),Qs(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(h),s.push(h)):(n[p]=h,s[p]=h)}else{let h=Ks(u,t,l+f,d),p=Ks(u,t,l,l+f),y=h>=0&&n[h],m=p>=0&&n[p];if(o&&!m||!o&&!y){la(Zo(a,s),i,u);let g=gb(o?pb:hb,n.length,o,r,c);!o&&m&&(n[p].providerFactory=g),Qs(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(g),s.push(g)}else{let g=Dp(n[o?p:h],c,!o&&r);Qs(i,e,h>-1?h:p,g)}!o&&r&&m&&n[p].componentProviders++}}}function Qs(e,t,n,r){let o=Cn(t),i=by(t);if(o||i){let u=(i?ue(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(n);l===-1?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function Dp(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Ks(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function hb(e,t,n,r){return Ja(this.multi,[])}function pb(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Wt(n,n[C],this.providerFactory.index,r);i=a.slice(0,s),Ja(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],Ja(o,i);return i}function Ja(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function gb(e,t,n,r,o){let i=new Gt(e,n,K);return i.multi=[],i.index=t,i.componentProviders=0,Dp(i,o,r&&!n),i}function GO(e,t=[]){return n=>{n.providersResolver=(r,o)=>fb(r,o?o(e):e,t)}}var mb=(()=>{let t=class t{constructor(r){this._injector=r,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(r){if(!r.standalone)return null;if(!this.cachedInjectors.has(r)){let o=Jd(!1,r.type),i=o.length>0?KE([o],this._injector,`Standalone[${r.type.name}]`):null;this.cachedInjectors.set(r,i)}return this.cachedInjectors.get(r)}ngOnDestroy(){try{for(let r of this.cachedInjectors.values())r!==null&&r.destroy()}finally{this.cachedInjectors.clear()}}};t.\u0275prov=x({token:t,providedIn:"environment",factory:()=>new t(b(Ke))});let e=t;return e})();function WO(e){xt("NgStandalone"),e.getStandaloneInjector=t=>t.get(mb).getOrCreateStandaloneInjector(e)}function qO(e,t,n){let r=ut()+e,o=E();return o[r]===Q?$n(o,r,n?t.call(n):t()):Qh(o,r)}function YO(e,t,n,r){return wp(E(),ut(),e,t,n,r)}function ZO(e,t,n,r,o){return Ep(E(),ut(),e,t,n,r,o)}function QO(e,t,n,r,o,i){return Ip(E(),ut(),e,t,n,r,o,i)}function KO(e,t,n,r,o,i,s){return yb(E(),ut(),e,t,n,r,o,i,s)}function JO(e,t,n,r,o,i,s,a){let u=ut()+e,c=E(),l=Rr(c,u,n,r,o,i);return ye(c,u+4,s)||l?$n(c,u+5,a?t.call(a,n,r,o,i,s):t(n,r,o,i,s)):Qh(c,u+5)}function zi(e,t){let n=e[t];return n===Q?void 0:n}function wp(e,t,n,r,o,i){let s=t+n;return ye(e,s,o)?$n(e,s+1,i?r.call(i,o):r(o)):zi(e,s+1)}function Ep(e,t,n,r,o,i,s){let a=t+n;return Sn(e,a,o,i)?$n(e,a+2,s?r.call(s,o,i):r(o,i)):zi(e,a+2)}function Ip(e,t,n,r,o,i,s,a){let u=t+n;return Kh(e,u,o,i,s)?$n(e,u+3,a?r.call(a,o,i,s):r(o,i,s)):zi(e,u+3)}function yb(e,t,n,r,o,i,s,a,u){let c=t+n;return Rr(e,c,o,i,s,a)?$n(e,c+4,u?r.call(u,o,i,s,a):r(o,i,s,a)):zi(e,c+4)}function XO(e,t){var u;let n=G(),r,o=e+V;n.firstCreatePass?(r=vb(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&((u=n.destroyHooks)!=null?u:n.destroyHooks=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Ut(r.type,!0)),s,a=pe(K);try{let c=Yo(!1),l=i();return Yo(c),tb(n,E(),o,l),l}finally{pe(a)}}function vb(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function eR(e,t,n){let r=e+V,o=E(),i=Ci(o,r);return sc(o,r)?wp(o,ut(),t,i.transform,n,i):i.transform(n)}function tR(e,t,n,r){let o=e+V,i=E(),s=Ci(i,o);return sc(i,o)?Ep(i,ut(),t,s.transform,n,r,s):s.transform(n,r)}function nR(e,t,n,r,o){let i=e+V,s=E(),a=Ci(s,i);return sc(s,i)?Ip(s,ut(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function sc(e,t){return e[C].data[t].pure}function rR(e,t){return ji(e,t)}var Po=null;function Db(e){Po!==null&&(e.defaultEncapsulation!==Po.defaultEncapsulation||e.preserveWhitespaces!==Po.preserveWhitespaces)||(Po=e)}var bp=(()=>{let t=class t{log(r){console.log(r)}warn(r){console.warn(r)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})();var Cp=new M(""),Mp=new M(""),wb=(()=>{let t=class t{constructor(r,o,i){this._ngZone=r,this.registry=o,this._pendingCount=0,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,lr||(Eb(i),i.addToWindow(o)),this._watchAngularEvents(),r.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{q.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&this._pendingCount===0&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let r=this._callbacks.pop();clearTimeout(r.timeoutId),r.doneCb()}});else{let r=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>o.updateCb&&o.updateCb(r)?(clearTimeout(o.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(r=>({source:r.source,creationLocation:r.creationLocation,data:r.data})):[]}addCallback(r,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),r()},o)),this._callbacks.push({doneCb:r,timeoutId:s,updateCb:i})}whenStable(r,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(r,o,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(r){this.registry.registerApplication(r,this)}unregisterApplication(r){this.registry.unregisterApplication(r)}findProviders(r,o,i){return[]}};t.\u0275fac=function(o){return new(o||t)(b(q),b(Tp),b(Mp))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Tp=(()=>{let t=class t{constructor(){this._applications=new Map}registerApplication(r,o){this._applications.set(r,o)}unregisterApplication(r){this._applications.delete(r)}unregisterAllApplications(){this._applications.clear()}getTestability(r){return this._applications.get(r)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(r,o=!0){var i;return(i=lr==null?void 0:lr.findTestabilityInTree(this,r,o))!=null?i:null}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})();function Eb(e){lr=e}var lr;function Gi(e){return!!e&&typeof e.then=="function"}function ac(e){return!!e&&typeof e.subscribe=="function"}var Ib=new M(""),uc=(()=>{let t=class t{constructor(){var r;this.initialized=!1,this.done=!1,this.donePromise=new Promise((o,i)=>{this.resolve=o,this.reject=i}),this.appInits=(r=D(Ib,{optional:!0}))!=null?r:[]}runInitializers(){if(this.initialized)return;let r=[];for(let i of this.appInits){let s=i();if(Gi(s))r.push(s);else if(ac(s)){let a=new Promise((u,c)=>{s.subscribe({complete:u,error:c})});r.push(a)}}let o=()=>{this.done=!0,this.resolve()};Promise.all(r).then(()=>{o()}).catch(i=>{this.reject(i)}),r.length===0&&o(),this.initialized=!0}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Wi=new M("");function xp(){Ol(()=>{throw new w(600,!1)})}function bb(e){return e.isBoundToModule}function _p(e,t,n){try{let r=n();return Gi(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Sp(e,t){return Array.isArray(t)?t.reduce(Sp,e):re(re({},e),t)}var en=(()=>{let t=class t{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=D(kf),this.afterRenderEffectManager=D(nc),this.externalTestViews=new Set,this.beforeRender=new _e,this.afterTick=new _e,this.componentTypes=[],this.components=[],this.isStable=D(Hi).hasPendingTasks.pipe(ae(r=>!r)),this._injector=D(Ke)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(r,o){let i=r instanceof ri;if(!this._injector.get(uc).done){let h=!i&&yy(r),p=!1;throw new w(405,p)}let a;i?a=r:a=this._injector.get($i).resolveComponentFactory(r),this.componentTypes.push(a.componentType);let u=bb(a)?void 0:this._injector.get(It),c=o||a.selector,l=a.create(Ct.NULL,[],c,u),d=l.location.nativeElement,f=l.injector.get(Cp,null);return f==null||f.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),Bo(this.components,l),f==null||f.unregisterApplication(d)}),this._loadComponent(l),l}tick(){this._tick(!0)}_tick(r){if(this._runningTick)throw new w(101,!1);let o=R(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(r)}catch(i){this.internalErrorHandler(i)}finally{this.afterTick.next(),this._runningTick=!1,R(o)}}detectChangesInAttachedViews(r){let o=0,i=this.afterRenderEffectManager;for(;;){if(o===Rh)throw new w(103,!1);if(r){let s=o===0;this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:u}of this._views)Cb(a,s,u)}if(o++,i.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Xa(s))&&(i.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>Xa(s))))break}}attachView(r){let o=r;this._views.push(o),o.attachToAppRef(this)}detachView(r){let o=r;Bo(this._views,o),o.detachFromAppRef()}_loadComponent(r){this.attachView(r.hostView),this.tick(),this.components.push(r);let o=this._injector.get(Wi,[]);[...this._bootstrapListeners,...o].forEach(i=>i(r))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(r=>r()),this._views.slice().forEach(r=>r.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(r){return this._destroyListeners.push(r),()=>Bo(this._destroyListeners,r)}destroy(){if(this._destroyed)throw new w(406,!1);let r=this._injector;r.destroy&&!r.destroyed&&r.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function Bo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}var mt;function cc(e){mt!=null||(mt=new WeakMap);let t=mt.get(e);if(t)return t;let n=e.isStable.pipe(Cs(r=>r)).toPromise().then(()=>{});return mt.set(e,n),e.onDestroy(()=>mt==null?void 0:mt.delete(e)),n}function Cb(e,t,n){!t&&!Xa(e)||Mb(e,n,t)}function Xa(e){return Iu(e)}function Mb(e,t,n){let r;n?(r=0,e[I]|=1024):e[I]&64?r=0:r=1,Ph(e,t,r)}var eu=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},oR=(()=>{let t=class t{compileModuleSync(r){return new ci(r)}compileModuleAsync(r){return Promise.resolve(this.compileModuleSync(r))}compileModuleAndAllComponentsSync(r){let o=this.compileModuleSync(r),i=Zd(r),s=sh(i.declarations).reduce((a,u)=>{let c=Qe(u);return c&&a.push(new Zt(c)),a},[]);return new eu(o,s)}compileModuleAndAllComponentsAsync(r){return Promise.resolve(this.compileModuleAndAllComponentsSync(r))}clearCache(){}clearCacheFor(r){}getModuleId(r){}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Tb=new M("");function xb(e,t,n){let r=new ci(n);return Promise.resolve(r)}function Ed(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var _b=(()=>{let t=class t{constructor(){this.zone=D(q),this.applicationRef=D(en)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){var r;(r=this._onMicrotaskEmptySubscription)==null||r.unsubscribe()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function Np(e){return[{provide:q,useFactory:e},{provide:bn,multi:!0,useFactory:()=>{let t=D(_b,{optional:!0});return()=>t.initialize()}},{provide:bn,multi:!0,useFactory:()=>{let t=D(Ab);return()=>{t.initialize()}}},{provide:kf,useFactory:Sb}]}function Sb(){let e=D(q),t=D(He);return n=>e.runOutsideAngular(()=>t.handleError(n))}function Nb(e){let t=Np(()=>new q(Ap(e)));return On([[],t])}function Ap(e){var t,n;return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:(t=e==null?void 0:e.eventCoalescing)!=null?t:!1,shouldCoalesceRunChangeDetection:(n=e==null?void 0:e.runCoalescing)!=null?n:!1}}var Ab=(()=>{let t=class t{constructor(){this.subscription=new Y,this.initialized=!1,this.zone=D(q),this.pendingTasks=D(Hi)}initialize(){if(this.initialized)return;this.initialized=!0;let r=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(r=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{q.assertNotInAngularZone(),queueMicrotask(()=>{r!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(r),r=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{q.assertInAngularZone(),r!=null||(r=this.pendingTasks.add())}))}ngOnDestroy(){this.subscription.unsubscribe()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function Ob(){return typeof $localize<"u"&&$localize.locale||An}var Un=new M("",{providedIn:"root",factory:()=>D(Un,P.Optional|P.SkipSelf)||Ob()}),Op=new M("",{providedIn:"root",factory:()=>WI});var lc=new M(""),Rp=(()=>{let t=class t{constructor(r){this._injector=r,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(r,o){let i=sE(o==null?void 0:o.ngZone,Ap({eventCoalescing:o==null?void 0:o.ngZoneEventCoalescing,runCoalescing:o==null?void 0:o.ngZoneRunCoalescing}));return i.run(()=>{let s=QE(r.moduleType,this.injector,Np(()=>i)),a=s.injector.get(He,null);return i.runOutsideAngular(()=>{let u=i.onError.subscribe({next:c=>{a.handleError(c)}});s.onDestroy(()=>{Bo(this._modules,s),u.unsubscribe()})}),_p(a,i,()=>{let u=s.injector.get(uc);return u.runInitializers(),u.donePromise.then(()=>{let c=s.injector.get(Un,An);return hp(c||An),this._moduleDoBootstrap(s),s})})})}bootstrapModule(r,o=[]){let i=Sp({},o);return xb(this.injector,i,r).then(s=>this.bootstrapModuleFactory(s,i))}_moduleDoBootstrap(r){let o=r.injector.get(en);if(r._bootstrapComponents.length>0)r._bootstrapComponents.forEach(i=>o.bootstrap(i));else if(r.instance.ngDoBootstrap)r.instance.ngDoBootstrap(o);else throw new w(-403,!1);this._modules.push(r)}onDestroy(r){this._destroyListeners.push(r)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new w(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());let r=this._injector.get(lc,null);r&&(r.forEach(o=>o()),r.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}};t.\u0275fac=function(o){return new(o||t)(b(Ct))},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"platform"});let e=t;return e})(),Ze=null,Pp=new M("");function Rb(e){if(Ze&&!Ze.get(Pp,!1))throw new w(400,!1);xp(),Ze=e;let t=e.get(Rp);return jp(e),t}function Fp(e,t,n=[]){let r=`Platform: ${t}`,o=new M(r);return(i=[])=>{let s=Lp();if(!s||s.injector.get(Pp,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):Rb(kp(a,r))}return Pb(o)}}function kp(e=[],t){return Ct.create({name:t,providers:[{provide:wi,useValue:"platform"},{provide:lc,useValue:new Set([()=>Ze=null])},...e]})}function Pb(e){let t=Lp();if(!t)throw new w(401,!1);return t}function Lp(){var e;return(e=Ze==null?void 0:Ze.get(Rp))!=null?e:null}function Fb(e=[]){if(Ze)return Ze;let t=kp(e);return Ze=t,xp(),jp(t),t}function jp(e){let t=e.get(Nu,null);t==null||t.forEach(n=>n())}var dc=(()=>{let t=class t{};t.__NG_ELEMENT_ID__=kb;let e=t;return e})();function kb(e){return Lb(ie(),E(),(e&16)===16)}function Lb(e,t,n){if(Pn(e)&&!n){let r=bt(e.index,t);return new qt(r,r)}else if(e.type&47){let r=t[me];return new qt(r,t)}return null}var tu=class{constructor(){}supports(t){return Zh(t)}create(t){return new nu(t)}},jb=(e,t)=>t,nu=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||jb}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Id(r,o,i)?n:r,a=Id(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let f=0;f<c;f++){let h=f<i.length?i[f]:i[f]=0,p=h+f;l<=p&&p<c&&(i[f]=h+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Zh(t))throw new w(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,JE(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new ru(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new fi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new fi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},ru=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},ou=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},fi=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new ou,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Id(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}var iu=class{constructor(){}supports(t){return t instanceof Map||ic(t)}create(){return new su}},su=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||ic(t)))throw new w(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new au(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},au=class{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function bd(){return new fc([new tu])}var fc=(()=>{let t=class t{constructor(r){this.factories=r}static create(r,o){if(o!=null){let i=o.factories.slice();r=r.concat(i)}return new t(r)}static extend(r){return{provide:t,useFactory:o=>t.create(r,o||bd()),deps:[[t,new Vd,new lu]]}}find(r){let o=this.factories.find(i=>i.supports(r));if(o!=null)return o;throw new w(901,!1)}};t.\u0275prov=x({token:t,providedIn:"root",factory:bd});let e=t;return e})();function Cd(){return new hc([new iu])}var hc=(()=>{let t=class t{constructor(r){this.factories=r}static create(r,o){if(o){let i=o.factories.slice();r=r.concat(i)}return new t(r)}static extend(r){return{provide:t,useFactory:o=>t.create(r,o||Cd()),deps:[[t,new Vd,new lu]]}}find(r){let o=this.factories.find(i=>i.supports(r));if(o)return o;throw new w(901,!1)}};t.\u0275prov=x({token:t,providedIn:"root",factory:Cd});let e=t;return e})();var Vb=Fp(null,"core",[]);function Vp(e){try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Fb(r),i=[Nb(),...n||[]],a=new li({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1}).injector,u=a.get(q);return u.run(()=>{a.resolveInjectorInitializers();let c=a.get(He,null),l;u.runOutsideAngular(()=>{l=u.onError.subscribe({next:h=>{c.handleError(h)}})});let d=()=>a.destroy(),f=o.get(lc);return f.add(d),a.onDestroy(()=>{l.unsubscribe(),f.delete(d)}),_p(c,u,()=>{let h=a.get(uc);return h.runInitializers(),h.donePromise.then(()=>{let p=a.get(Un,An);hp(p||An);let y=a.get(en);return t!==void 0&&y.bootstrap(t),y})})})}catch(t){return Promise.reject(t)}}var Md=!1,Bp=!1;function Bb(){Md||(Md=!0,$v(),kI(),rb(),$I(),nI(),bE(),Qw(),UD(),QI())}function $b(e,t){return cc(e)}function $p(){return On([{provide:_o,useFactory:()=>{let e=!0;if(sr()){let t=D(Tt,{optional:!0});e=!!(t!=null&&t.get(Pu,null))}return e&&xt("NgHydration"),e}},{provide:bn,useValue:()=>{Bp=!!D(Gv,{optional:!0}),sr()&&D(_o)&&(Hb(),Bb())},multi:!0},{provide:Yf,useFactory:()=>sr()&&D(_o)},{provide:Wi,useFactory:()=>{if(sr()&&D(_o)){let e=D(en),t=D(Ct);return()=>{$b(e,t).then(()=>{jw(e)})}}return()=>{}},multi:!0}])}function Ub(){return Bp}function Hb(){var n;let e=Ar(),t;for(let r of e.body.childNodes)if(r.nodeType===Node.COMMENT_NODE&&((n=r.textContent)==null?void 0:n.trim())===Vv){t=r;break}if(!t)throw new w(-507,!1)}var uu=class{constructor(){this.views=[],this.indexByContent=new Map}add(t){let n=JSON.stringify(t);if(!this.indexByContent.has(n)){let r=this.views.length;return this.views.push(t),this.indexByContent.set(n,r),r}return this.indexByContent.get(n)}getAll(){return this.views}},zb=0;function Up(e){return e.ssrId||(e.ssrId=`t${zb++}`),e.ssrId}function Hp(e,t,n){let r=[];return Er(e,t,n,r),r.length}function Gb(e){let t=[];return Ah(e,t),t.length}function zp(e,t){let n=e[Z];return n&&!n.hasAttribute(Dr)?pi(n,e,t):null}function Gp(e,t){let n=df(e[Z]),r=zp(n,t),o=ee(n[Z]),i=e[te],s=pi(o,i,t),a=n[F],u=`${r}|${s}`;a.setAttribute(o,ur,u)}function iR(e,t){let n=new uu,r=new Map,o=e._views;for(let a of o){let u=Gf(a);if(u!==null){let c={serializedViewCollection:n,corruptedTextNodes:r};be(u)?Gp(u,c):zp(u,c),Zb(r,t)}}let i=n.getAll();e.injector.get(Tt).set(Pu,i)}function Wb(e,t){var o;let n=[],r="";for(let i=oe;i<e.length;i++){let s=e[i],a,u,c;if(Du(s)&&(s=s[V],be(s))){u=Gb(s)+1,Gp(s,t);let d=df(s[Z]);c={[ma]:d[C].ssrId,[_n]:u}}if(!c){let d=s[C];d.type===1?(a=d.ssrId,u=1):(a=Up(d),u=Hp(d,s,d.firstChild)),c=re({[ma]:a,[_n]:u},Wp(e[i],t))}let l=JSON.stringify(c);if(n.length>0&&l===r){let d=n[n.length-1];(o=d[yn])!=null||(d[yn]=1),d[yn]++}else r=l,n.push(c)}return n}function hi(e,t,n){var o;let r=t.index-V;(o=e[ir])!=null||(e[ir]={}),e[ir][r]=qw(t,n)}function Td(e,t){var r;let n=t.index-V;(r=e[pn])!=null||(e[pn]=[]),e[pn].includes(n)||e[pn].push(n)}function Wp(e,t){var o,i,s,a;let n={},r=e[C];for(let u=V;u<r.bindingStartIndex;u++){let c=r.data[u],l=u-V;if(dv(c)){if(Ir(c,e)&&Qb(c)){Td(n,c);continue}if(Array.isArray(c.projection)){for(let d of c.projection)if(d)if(!Array.isArray(d))!Ry(d)&&!Ko(d)&&(Ir(d,e)?Td(n,d):hi(n,d,e));else throw xw(ee(e[u]))}if(qb(n,c,e),be(e[u])){let d=c.tView;d!==null&&((o=n[or])!=null||(n[or]={}),n[or][l]=Up(d));let f=e[u][Z];if(Array.isArray(f)){let h=ee(f);h.hasAttribute(Dr)||pi(h,f,t)}(i=n[Bt])!=null||(n[Bt]={}),n[Bt][l]=Wb(e[u],t)}else if(Array.isArray(e[u])){let d=ee(e[u][Z]);d.hasAttribute(Dr)||pi(d,e[u],t)}else if(c.type&8)(s=n[rr])!=null||(n[rr]={}),n[rr][l]=Hp(r,e,c.child);else if(c.type&16){let d=c.next;for(;d!==null&&d.type&16;)d=d.next;d&&!Ko(d)&&hi(n,d,e)}else if(c.type&1){let d=ee(e[u]);d.textContent===""?t.corruptedTextNodes.set(d,"ngetn"):((a=d.nextSibling)==null?void 0:a.nodeType)===Node.TEXT_NODE&&t.corruptedTextNodes.set(d,"ngtns")}}}return n}function qb(e,t,n){t.projectionNext&&t.projectionNext!==t.next&&!Ko(t.projectionNext)&&hi(e,t.projectionNext,n),t.prev===null&&t.parent!==null&&Ir(t.parent,n)&&!Ir(t,n)&&hi(e,t,n)}function Yb(e){var n;let t=e[Ee];return t!=null&&t.constructor?((n=Qe(t.constructor))==null?void 0:n.encapsulation)===$e.ShadowDom:!1}function pi(e,t,n){let r=t[F];if(Py(t)&&!Ub()||Yb(t))return r.setAttribute(e,Dr,""),null;{let o=Wp(t,n),i=n.serializedViewCollection.add(o);return r.setAttribute(e,ur,i.toString()),i}}function Zb(e,t){for(let[n,r]of e)n.after(t.createComment(r))}function Qb(e){let t=e;for(;t!=null;){if(Pn(t))return!0;t=t.parent}return!1}function Kb(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function pc(e){let t=R(null);try{return e()}finally{R(t)}}function sR(e,t){let n=Qe(e),r=t.elementInjector||Ei();return new Zt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function aR(e){let t=Qe(e);if(!t)return null;let n=new Zt(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}function uR(...e){return e.reduce((t,n)=>Object.assign(t,n,{providers:[...t.providers,...n.providers]}),{providers:[]})}var Xi=null;function zn(){return Xi}function tg(e){Xi!=null||(Xi=e)}var ts=class{};var fe=new M(""),_c=(()=>{let t=class t{historyGo(r){throw new Error("")}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:()=>D(Xb),providedIn:"platform"});let e=t;return e})(),xR=new M(""),Xb=(()=>{let t=class t extends _c{constructor(){super(),this._doc=D(fe),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return zn().getBaseHref(this._doc)}onPopState(r){let o=zn().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",r,!1),()=>o.removeEventListener("popstate",r)}onHashChange(r){let o=zn().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",r,!1),()=>o.removeEventListener("hashchange",r)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(r){this._location.pathname=r}pushState(r,o,i){this._history.pushState(r,o,i)}replaceState(r,o,i){this._history.replaceState(r,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(r=0){this._history.go(r)}getState(){return this._history.state}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:()=>new t,providedIn:"platform"});let e=t;return e})();function Sc(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function qp(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function ft(e){return e&&e[0]!=="?"?"?"+e:e}var os=(()=>{let t=class t{historyGo(r){throw new Error("")}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:()=>D(eC),providedIn:"root"});let e=t;return e})(),ng=new M(""),eC=(()=>{let t=class t extends os{constructor(r,o){var i,s,a;super(),this._platformLocation=r,this._removeListenerFns=[],this._baseHref=(a=(s=o!=null?o:this._platformLocation.getBaseHrefFromDOM())!=null?s:(i=D(fe).location)==null?void 0:i.origin)!=null?a:""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(r){this._removeListenerFns.push(this._platformLocation.onPopState(r),this._platformLocation.onHashChange(r))}getBaseHref(){return this._baseHref}prepareExternalUrl(r){return Sc(this._baseHref,r)}path(r=!1){let o=this._platformLocation.pathname+ft(this._platformLocation.search),i=this._platformLocation.hash;return i&&r?`${o}${i}`:o}pushState(r,o,i,s){let a=this.prepareExternalUrl(i+ft(s));this._platformLocation.pushState(r,o,a)}replaceState(r,o,i,s){let a=this.prepareExternalUrl(i+ft(s));this._platformLocation.replaceState(r,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(r=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,r)}};t.\u0275fac=function(o){return new(o||t)(b(_c),b(ng,8))},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),_R=(()=>{let t=class t extends os{constructor(r,o){super(),this._platformLocation=r,this._baseHref="",this._removeListenerFns=[],o!=null&&(this._baseHref=o)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(r){this._removeListenerFns.push(this._platformLocation.onPopState(r),this._platformLocation.onHashChange(r))}getBaseHref(){return this._baseHref}path(r=!1){var i;let o=(i=this._platformLocation.hash)!=null?i:"#";return o.length>0?o.substring(1):o}prepareExternalUrl(r){let o=Sc(this._baseHref,r);return o.length>0?"#"+o:o}pushState(r,o,i,s){let a=this.prepareExternalUrl(i+ft(s));a.length==0&&(a=this._platformLocation.pathname),this._platformLocation.pushState(r,o,a)}replaceState(r,o,i,s){let a=this.prepareExternalUrl(i+ft(s));a.length==0&&(a=this._platformLocation.pathname),this._platformLocation.replaceState(r,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(r=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,r)}};t.\u0275fac=function(o){return new(o||t)(b(_c),b(ng,8))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),tC=(()=>{let t=class t{constructor(r){this._subject=new je,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=r;let o=this._locationStrategy.getBaseHref();this._basePath=oC(qp(Yp(o))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){var r;(r=this._urlChangeSubscription)==null||r.unsubscribe(),this._urlChangeListeners=[]}path(r=!1){return this.normalize(this._locationStrategy.path(r))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(r,o=""){return this.path()==this.normalize(r+ft(o))}normalize(r){return t.stripTrailingSlash(rC(this._basePath,Yp(r)))}prepareExternalUrl(r){return r&&r[0]!=="/"&&(r="/"+r),this._locationStrategy.prepareExternalUrl(r)}go(r,o="",i=null){this._locationStrategy.pushState(i,"",r,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(r+ft(o)),i)}replaceState(r,o="",i=null){this._locationStrategy.replaceState(i,"",r,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(r+ft(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(r=0){var o,i;(i=(o=this._locationStrategy).historyGo)==null||i.call(o,r)}onUrlChange(r){var o;return this._urlChangeListeners.push(r),(o=this._urlChangeSubscription)!=null||(this._urlChangeSubscription=this.subscribe(i=>{this._notifyUrlChangeListeners(i.url,i.state)})),()=>{var s;let i=this._urlChangeListeners.indexOf(r);this._urlChangeListeners.splice(i,1),this._urlChangeListeners.length===0&&((s=this._urlChangeSubscription)==null||s.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(r="",o){this._urlChangeListeners.forEach(i=>i(r,o))}subscribe(r,o,i){return this._subject.subscribe({next:r,error:o,complete:i})}};t.normalizeQueryParams=ft,t.joinWithSlash=Sc,t.stripTrailingSlash=qp,t.\u0275fac=function(o){return new(o||t)(b(os))},t.\u0275prov=x({token:t,factory:()=>nC(),providedIn:"root"});let e=t;return e})();function nC(){return new tC(b(os))}function rC(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Yp(e){return e.replace(/\/index.html$/,"")}function oC(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var rg={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},og=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(og||{});var de=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(de||{}),$=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}($||{}),Me=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Me||{}),Te={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function iC(e){return ve(e)[W.LocaleId]}function sC(e,t,n){let r=ve(e),o=[r[W.DayPeriodsFormat],r[W.DayPeriodsStandalone]],i=Re(o,t);return Re(i,n)}function aC(e,t,n){let r=ve(e),o=[r[W.DaysFormat],r[W.DaysStandalone]],i=Re(o,t);return Re(i,n)}function uC(e,t,n){let r=ve(e),o=[r[W.MonthsFormat],r[W.MonthsStandalone]],i=Re(o,t);return Re(i,n)}function cC(e,t){let r=ve(e)[W.Eras];return Re(r,t)}function qi(e,t){let n=ve(e);return Re(n[W.DateFormat],t)}function Yi(e,t){let n=ve(e);return Re(n[W.TimeFormat],t)}function Zi(e,t){let r=ve(e)[W.DateTimeFormat];return Re(r,t)}function ht(e,t){let n=ve(e),r=n[W.NumberSymbols][t];if(typeof r>"u"){if(t===Te.CurrencyDecimal)return n[W.NumberSymbols][Te.Decimal];if(t===Te.CurrencyGroup)return n[W.NumberSymbols][Te.Group]}return r}function lC(e,t){return ve(e)[W.NumberFormats][t]}function dC(e){return ve(e)[W.Currencies]}function ig(e){if(!e[W.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[W.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function fC(e){let t=ve(e);return ig(t),(t[W.ExtraData][2]||[]).map(r=>typeof r=="string"?mc(r):[mc(r[0]),mc(r[1])])}function hC(e,t,n){let r=ve(e);ig(r);let o=[r[W.ExtraData][0],r[W.ExtraData][1]],i=Re(o,t)||[];return Re(i,n)||[]}function Re(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function mc(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}function pC(e,t,n="en"){let r=dC(n)[e]||rg[e]||[],o=r[1];return t==="narrow"&&typeof o=="string"?o:r[0]||e}var gC=2;function mC(e){let t,n=rg[e];return n&&(t=n[2]),typeof t=="number"?t:gC}var yC=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Hn={},vC=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,pt=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(pt||{}),L=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(L||{}),k=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(k||{});function DC(e,t,n,r){let o=_C(e);t=dt(n,t)||t;let s=[],a;for(;t;)if(a=vC.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let u=o.getTimezoneOffset();r&&(u=ag(r,u),o=xC(o,r,!0));let c="";return s.forEach(l=>{let d=MC(l);c+=d?d(o,n,u):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function ns(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function dt(e,t){var o;let n=iC(e);if((o=Hn[n])!=null||(Hn[n]={}),Hn[n][t])return Hn[n][t];let r="";switch(t){case"shortDate":r=qi(e,Me.Short);break;case"mediumDate":r=qi(e,Me.Medium);break;case"longDate":r=qi(e,Me.Long);break;case"fullDate":r=qi(e,Me.Full);break;case"shortTime":r=Yi(e,Me.Short);break;case"mediumTime":r=Yi(e,Me.Medium);break;case"longTime":r=Yi(e,Me.Long);break;case"fullTime":r=Yi(e,Me.Full);break;case"short":let i=dt(e,"shortTime"),s=dt(e,"shortDate");r=Qi(Zi(e,Me.Short),[i,s]);break;case"medium":let a=dt(e,"mediumTime"),u=dt(e,"mediumDate");r=Qi(Zi(e,Me.Medium),[a,u]);break;case"long":let c=dt(e,"longTime"),l=dt(e,"longDate");r=Qi(Zi(e,Me.Long),[c,l]);break;case"full":let d=dt(e,"fullTime"),f=dt(e,"fullDate");r=Qi(Zi(e,Me.Full),[d,f]);break}return r&&(Hn[n][t]=r),r}function Qi(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function Ge(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function wC(e,t){return Ge(e,3).substring(0,t)}function X(e,t,n=0,r=!1,o=!1){return function(i,s){let a=EC(e,i);if((n>0||a>-n)&&(a+=n),e===L.Hours)a===0&&n===-12&&(a=12);else if(e===L.FractionalSeconds)return wC(a,t);let u=ht(s,Te.MinusSign);return Ge(a,t,u,r,o)}}function EC(e,t){switch(e){case L.FullYear:return t.getFullYear();case L.Month:return t.getMonth();case L.Date:return t.getDate();case L.Hours:return t.getHours();case L.Minutes:return t.getMinutes();case L.Seconds:return t.getSeconds();case L.FractionalSeconds:return t.getMilliseconds();case L.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function H(e,t,n=de.Format,r=!1){return function(o,i){return IC(o,i,e,t,n,r)}}function IC(e,t,n,r,o,i){switch(n){case k.Months:return uC(t,o,r)[e.getMonth()];case k.Days:return aC(t,o,r)[e.getDay()];case k.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(i){let c=fC(t),l=hC(t,o,r),d=c.findIndex(f=>{if(Array.isArray(f)){let[h,p]=f,y=s>=h.hours&&a>=h.minutes,m=s<p.hours||s===p.hours&&a<p.minutes;if(h.hours<p.hours){if(y&&m)return!0}else if(y||m)return!0}else if(f.hours===s&&f.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return sC(t,o,r)[s<12?0:1];case k.Eras:return cC(t,r)[e.getFullYear()<=0?0:1];default:let u=n;throw new Error(`unexpected translation type ${u}`)}}function Ki(e){return function(t,n,r){let o=-1*r,i=ht(n,Te.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case pt.Short:return(o>=0?"+":"")+Ge(s,2,i)+Ge(Math.abs(o%60),2,i);case pt.ShortGMT:return"GMT"+(o>=0?"+":"")+Ge(s,1,i);case pt.Long:return"GMT"+(o>=0?"+":"")+Ge(s,2,i)+":"+Ge(Math.abs(o%60),2,i);case pt.Extended:return r===0?"Z":(o>=0?"+":"")+Ge(s,2,i)+":"+Ge(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var bC=0,es=4;function CC(e){let t=ns(e,bC,1).getDay();return ns(e,0,1+(t<=es?es:es+7)-t)}function sg(e){let t=e.getDay(),n=t===0?-3:es-t;return ns(e.getFullYear(),e.getMonth(),e.getDate()+n)}function yc(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=sg(n),s=CC(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Ge(o,e,ht(r,Te.MinusSign))}}function Ji(e,t=!1){return function(n,r){let i=sg(n).getFullYear();return Ge(i,e,ht(r,Te.MinusSign),t)}}var vc={};function MC(e){if(vc[e])return vc[e];let t;switch(e){case"G":case"GG":case"GGG":t=H(k.Eras,$.Abbreviated);break;case"GGGG":t=H(k.Eras,$.Wide);break;case"GGGGG":t=H(k.Eras,$.Narrow);break;case"y":t=X(L.FullYear,1,0,!1,!0);break;case"yy":t=X(L.FullYear,2,0,!0,!0);break;case"yyy":t=X(L.FullYear,3,0,!1,!0);break;case"yyyy":t=X(L.FullYear,4,0,!1,!0);break;case"Y":t=Ji(1);break;case"YY":t=Ji(2,!0);break;case"YYY":t=Ji(3);break;case"YYYY":t=Ji(4);break;case"M":case"L":t=X(L.Month,1,1);break;case"MM":case"LL":t=X(L.Month,2,1);break;case"MMM":t=H(k.Months,$.Abbreviated);break;case"MMMM":t=H(k.Months,$.Wide);break;case"MMMMM":t=H(k.Months,$.Narrow);break;case"LLL":t=H(k.Months,$.Abbreviated,de.Standalone);break;case"LLLL":t=H(k.Months,$.Wide,de.Standalone);break;case"LLLLL":t=H(k.Months,$.Narrow,de.Standalone);break;case"w":t=yc(1);break;case"ww":t=yc(2);break;case"W":t=yc(1,!0);break;case"d":t=X(L.Date,1);break;case"dd":t=X(L.Date,2);break;case"c":case"cc":t=X(L.Day,1);break;case"ccc":t=H(k.Days,$.Abbreviated,de.Standalone);break;case"cccc":t=H(k.Days,$.Wide,de.Standalone);break;case"ccccc":t=H(k.Days,$.Narrow,de.Standalone);break;case"cccccc":t=H(k.Days,$.Short,de.Standalone);break;case"E":case"EE":case"EEE":t=H(k.Days,$.Abbreviated);break;case"EEEE":t=H(k.Days,$.Wide);break;case"EEEEE":t=H(k.Days,$.Narrow);break;case"EEEEEE":t=H(k.Days,$.Short);break;case"a":case"aa":case"aaa":t=H(k.DayPeriods,$.Abbreviated);break;case"aaaa":t=H(k.DayPeriods,$.Wide);break;case"aaaaa":t=H(k.DayPeriods,$.Narrow);break;case"b":case"bb":case"bbb":t=H(k.DayPeriods,$.Abbreviated,de.Standalone,!0);break;case"bbbb":t=H(k.DayPeriods,$.Wide,de.Standalone,!0);break;case"bbbbb":t=H(k.DayPeriods,$.Narrow,de.Standalone,!0);break;case"B":case"BB":case"BBB":t=H(k.DayPeriods,$.Abbreviated,de.Format,!0);break;case"BBBB":t=H(k.DayPeriods,$.Wide,de.Format,!0);break;case"BBBBB":t=H(k.DayPeriods,$.Narrow,de.Format,!0);break;case"h":t=X(L.Hours,1,-12);break;case"hh":t=X(L.Hours,2,-12);break;case"H":t=X(L.Hours,1);break;case"HH":t=X(L.Hours,2);break;case"m":t=X(L.Minutes,1);break;case"mm":t=X(L.Minutes,2);break;case"s":t=X(L.Seconds,1);break;case"ss":t=X(L.Seconds,2);break;case"S":t=X(L.FractionalSeconds,1);break;case"SS":t=X(L.FractionalSeconds,2);break;case"SSS":t=X(L.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=Ki(pt.Short);break;case"ZZZZZ":t=Ki(pt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Ki(pt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=Ki(pt.Long);break;default:return null}return vc[e]=t,t}function ag(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function TC(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function xC(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=ag(t,o);return TC(e,r*(i-o))}function _C(e){if(Zp(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return ns(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(yC))return SC(r)}let t=new Date(e);if(!Zp(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function SC(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,u=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,u,c),t}function Zp(e){return e instanceof Date&&!isNaN(e.valueOf())}var NC=/^(\d+)?\.((\d+)(-(\d+))?)?$/,Qp=22,rs=".",Fr="0",AC=";",OC=",",Dc="#",Kp="\xA4";function RC(e,t,n,r,o,i,s=!1){let a="",u=!1;if(!isFinite(e))a=ht(n,Te.Infinity);else{let c=LC(e);s&&(c=kC(c));let l=t.minInt,d=t.minFrac,f=t.maxFrac;if(i){let _=i.match(NC);if(_===null)throw new Error(`${i} is not a valid digit info`);let z=_[1],O=_[3],se=_[5];z!=null&&(l=wc(z)),O!=null&&(d=wc(O)),se!=null?f=wc(se):O!=null&&d>f&&(f=d)}jC(c,d,f);let h=c.digits,p=c.integerLen,y=c.exponent,m=[];for(u=h.every(_=>!_);p<l;p++)h.unshift(0);for(;p<0;p++)h.unshift(0);p>0?m=h.splice(p,h.length):(m=h,h=[0]);let g=[];for(h.length>=t.lgSize&&g.unshift(h.splice(-t.lgSize,h.length).join(""));h.length>t.gSize;)g.unshift(h.splice(-t.gSize,h.length).join(""));h.length&&g.unshift(h.join("")),a=g.join(ht(n,r)),m.length&&(a+=ht(n,o)+m.join("")),y&&(a+=ht(n,Te.Exponential)+"+"+y)}return e<0&&!u?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function PC(e,t,n,r,o){let i=lC(t,og.Currency),s=FC(i,ht(t,Te.MinusSign));return s.minFrac=mC(r),s.maxFrac=s.minFrac,RC(e,s,t,Te.CurrencyGroup,Te.CurrencyDecimal,o).replace(Kp,n).replace(Kp,"").trim()}function FC(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(AC),o=r[0],i=r[1],s=o.indexOf(rs)!==-1?o.split(rs):[o.substring(0,o.lastIndexOf(Fr)+1),o.substring(o.lastIndexOf(Fr)+1)],a=s[0],u=s[1]||"";n.posPre=a.substring(0,a.indexOf(Dc));for(let l=0;l<u.length;l++){let d=u.charAt(l);d===Fr?n.minFrac=n.maxFrac=l+1:d===Dc?n.maxFrac=l+1:n.posSuf+=d}let c=a.split(OC);if(n.gSize=c[1]?c[1].length:0,n.lgSize=c[2]||c[1]?(c[2]||c[1]).length:0,i){let l=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(Dc);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+l).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function kC(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function LC(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(rs))>-1&&(t=t.replace(rs,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===Fr;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===Fr;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>Qp&&(r=r.splice(0,Qp-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function jC(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let u=i!==0,c=t+e.integerLen,l=r.reduceRight(function(d,f,h,p){return f=f+d,p[h]=f<10?f:f-10,u&&(p[h]===0&&h>=c?p.pop():u=!1),f>=10?1:0},0);l&&(r.unshift(l),e.integerLen++)}function wc(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}function is(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Ec=/\s+/,Jp=[],SR=(()=>{let t=class t{constructor(r,o){this._ngEl=r,this._renderer=o,this.initialClasses=Jp,this.stateMap=new Map}set klass(r){this.initialClasses=r!=null?r.trim().split(Ec):Jp}set ngClass(r){this.rawClass=typeof r=="string"?r.trim().split(Ec):r}ngDoCheck(){for(let o of this.initialClasses)this._updateState(o,!0);let r=this.rawClass;if(Array.isArray(r)||r instanceof Set)for(let o of r)this._updateState(o,!0);else if(r!=null)for(let o of Object.keys(r))this._updateState(o,!!r[o]);this._applyStateDiff()}_updateState(r,o){let i=this.stateMap.get(r);i!==void 0?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(r,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(let r of this.stateMap){let o=r[0],i=r[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(r,o){r=r.trim(),r.length>0&&r.split(Ec).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}};t.\u0275fac=function(o){return new(o||t)(K(Mt),K(Ui))},t.\u0275dir=xr({type:t,selectors:[["","ngClass",""]],inputs:{klass:[Dt.None,"class","klass"],ngClass:"ngClass"},standalone:!0});let e=t;return e})();var Ic=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},NR=(()=>{let t=class t{set ngForOf(r){this._ngForOf=r,this._ngForOfDirty=!0}set ngForTrackBy(r){this._trackByFn=r}get ngForTrackBy(){return this._trackByFn}constructor(r,o,i){this._viewContainer=r,this._template=o,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(r){r&&(this._template=r)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let r=this._ngForOf;if(!this._differ&&r)if(0)try{}catch{}else this._differ=this._differs.find(r).create(this.ngForTrackBy)}if(this._differ){let r=this._differ.diff(this._ngForOf);r&&this._applyChanges(r)}}_applyChanges(r){let o=this._viewContainer;r.forEachOperation((i,s,a)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new Ic(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)o.remove(s===null?void 0:s);else if(s!==null){let u=o.get(s);o.move(u,a),Xp(u,i)}});for(let i=0,s=o.length;i<s;i++){let u=o.get(i).context;u.index=i,u.count=s,u.ngForOf=this._ngForOf}r.forEachIdentityChange(i=>{let s=o.get(i.currentIndex);Xp(s,i)})}static ngTemplateContextGuard(r,o){return!0}};t.\u0275fac=function(o){return new(o||t)(K(Bn),K(Yt),K(fc))},t.\u0275dir=xr({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0});let e=t;return e})();function Xp(e,t){e.context.$implicit=t.item}var AR=(()=>{let t=class t{constructor(r,o){this._viewContainer=r,this._context=new bc,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=o}set ngIf(r){this._context.$implicit=this._context.ngIf=r,this._updateView()}set ngIfThen(r){eg("ngIfThen",r),this._thenTemplateRef=r,this._thenViewRef=null,this._updateView()}set ngIfElse(r){eg("ngIfElse",r),this._elseTemplateRef=r,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(r,o){return!0}};t.\u0275fac=function(o){return new(o||t)(K(Bn),K(Yt))},t.\u0275dir=xr({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0});let e=t;return e})(),bc=class{constructor(){this.$implicit=null,this.ngIf=null}};function eg(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${le(t)}'.`)}var OR=(()=>{let t=class t{constructor(r,o,i){this._ngEl=r,this._differs=o,this._renderer=i,this._ngStyle=null,this._differ=null}set ngStyle(r){this._ngStyle=r,!this._differ&&r&&(this._differ=this._differs.find(r).create())}ngDoCheck(){if(this._differ){let r=this._differ.diff(this._ngStyle);r&&this._applyChanges(r)}}_setStyle(r,o){let[i,s]=r.split("."),a=i.indexOf("-")===-1?void 0:Je.DashCase;o!=null?this._renderer.setStyle(this._ngEl.nativeElement,i,s?`${o}${s}`:o,a):this._renderer.removeStyle(this._ngEl.nativeElement,i,a)}_applyChanges(r){r.forEachRemovedItem(o=>this._setStyle(o.key,null)),r.forEachAddedItem(o=>this._setStyle(o.key,o.currentValue)),r.forEachChangedItem(o=>this._setStyle(o.key,o.currentValue))}};t.\u0275fac=function(o){return new(o||t)(K(Mt),K(hc),K(Ui))},t.\u0275dir=xr({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0});let e=t;return e})();function Wn(e,t){return new w(2100,!1)}var Cc=class{createSubscription(t,n){return pc(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){pc(()=>t.unsubscribe())}},Mc=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},VC=new Mc,BC=new Cc,RR=(()=>{let t=class t{constructor(r){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=r}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(r){if(!this._obj){if(r)try{this.markForCheckOnValueUpdate=!1,this._subscribe(r)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return r!==this._obj?(this._dispose(),this.transform(r)):this._latestValue}_subscribe(r){this._obj=r,this._strategy=this._selectStrategy(r),this._subscription=this._strategy.createSubscription(r,o=>this._updateLatestValue(r,o))}_selectStrategy(r){if(Gi(r))return VC;if(ac(r))return BC;throw Wn(t,r)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(r,o){var i;r===this._obj&&(this._latestValue=o,this.markForCheckOnValueUpdate&&((i=this._ref)==null||i.markForCheck()))}};t.\u0275fac=function(o){return new(o||t)(K(dc,16))},t.\u0275pipe=Kt({name:"async",type:t,pure:!1,standalone:!0});let e=t;return e})(),PR=(()=>{let t=class t{transform(r){if(r==null)return null;if(typeof r!="string")throw Wn(t,r);return r.toLowerCase()}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Kt({name:"lowercase",type:t,pure:!0,standalone:!0});let e=t;return e})(),$C=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,FR=(()=>{let t=class t{transform(r){if(r==null)return null;if(typeof r!="string")throw Wn(t,r);return r.replace($C,o=>o[0].toUpperCase()+o.slice(1).toLowerCase())}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Kt({name:"titlecase",type:t,pure:!0,standalone:!0});let e=t;return e})();var UC="mediumDate",HC=new M(""),zC=new M(""),kR=(()=>{let t=class t{constructor(r,o,i){this.locale=r,this.defaultTimezone=o,this.defaultOptions=i}transform(r,o,i,s){var a,u,c,l,d;if(r==null||r===""||r!==r)return null;try{let f=(u=o!=null?o:(a=this.defaultOptions)==null?void 0:a.dateFormat)!=null?u:UC,h=(d=(l=i!=null?i:(c=this.defaultOptions)==null?void 0:c.timezone)!=null?l:this.defaultTimezone)!=null?d:void 0;return DC(r,f,s||this.locale,h)}catch(f){throw Wn(t,f.message)}}};t.\u0275fac=function(o){return new(o||t)(K(Un,16),K(HC,24),K(zC,24))},t.\u0275pipe=Kt({name:"date",type:t,pure:!0,standalone:!0});let e=t;return e})();var LR=(()=>{let t=class t{constructor(r,o="USD"){this._locale=r,this._defaultCurrencyCode=o}transform(r,o=this._defaultCurrencyCode,i="symbol",s,a){if(!GC(r))return null;a||(a=this._locale),typeof i=="boolean"&&(i=i?"symbol":"code");let u=o||this._defaultCurrencyCode;i!=="code"&&(i==="symbol"||i==="symbol-narrow"?u=pC(u,i==="symbol"?"wide":"narrow",a):u=i);try{let c=WC(r);return PC(c,a,u,o,s)}catch(c){throw Wn(t,c.message)}}};t.\u0275fac=function(o){return new(o||t)(K(Un,16),K(Op,16))},t.\u0275pipe=Kt({name:"currency",type:t,pure:!0,standalone:!0});let e=t;return e})();function GC(e){return!(e==null||e===""||e!==e)}function WC(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var jR=(()=>{let t=class t{transform(r,o,i){if(r==null)return null;if(!this.supports(r))throw Wn(t,r);return r.slice(o,i)}supports(r){return typeof r=="string"||Array.isArray(r)}};t.\u0275fac=function(o){return new(o||t)},t.\u0275pipe=Kt({name:"slice",type:t,pure:!1,standalone:!0});let e=t;return e})();var qC=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275mod=Di({type:t}),t.\u0275inj=mi({});let e=t;return e})(),Nc="browser",YC="server";function ZC(e){return e===Nc}function kr(e){return e===YC}var VR=(()=>{let t=class t{};t.\u0275prov=x({token:t,providedIn:"root",factory:()=>ZC(D(Ae))?new Tc(D(fe),window):new xc});let e=t;return e})(),Tc=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=QC(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function QC(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var xc=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},Gn=class{};var jr=class{},Vr=class{},et=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=o.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Rc=class{encodeKey(t){return ug(t)}encodeValue(t){return ug(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function XC(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],u=n.get(s)||[];u.push(a),n.set(s,u)}),n}var e0=/%(\d[a-f0-9])/gi,t0={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ug(e){return encodeURIComponent(e).replace(e0,(t,n)=>{var r;return(r=t0[n])!=null?r:t})}function ss(e){return`${e}`}var St=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new Rc,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=XC(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(ss):[ss(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(ss(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(ss(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Pc=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function n0(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function cg(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function lg(e){return typeof Blob<"u"&&e instanceof Blob}function dg(e){return typeof FormData<"u"&&e instanceof FormData}function r0(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Lr=class e{constructor(t,n,r,o){var s,a;this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let i;if(n0(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),(s=this.headers)!=null||(this.headers=new et),(a=this.context)!=null||(this.context=new Pc),!this.params)this.params=new St,this.urlWithParams=n;else{let u=this.params.toString();if(u.length===0)this.urlWithParams=n;else{let c=n.indexOf("?"),l=c===-1?"?":c<n.length-1?"&":"";this.urlWithParams=n+l+u}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||cg(this.body)||lg(this.body)||dg(this.body)||r0(this.body)?this.body:this.body instanceof St?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||dg(this.body)?null:lg(this.body)?this.body.type||null:cg(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof St?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){var f,h,p,y;let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=(f=t.transferCache)!=null?f:this.transferCache,s=t.body!==void 0?t.body:this.body,a=(h=t.withCredentials)!=null?h:this.withCredentials,u=(p=t.reportProgress)!=null?p:this.reportProgress,c=t.headers||this.headers,l=t.params||this.params,d=(y=t.context)!=null?y:this.context;return t.setHeaders!==void 0&&(c=Object.keys(t.setHeaders).reduce((m,g)=>m.set(g,t.setHeaders[g]),c)),t.setParams&&(l=Object.keys(t.setParams).reduce((m,g)=>m.set(g,t.setParams[g]),l)),new e(n,r,s,{params:l,headers:c,context:d,reportProgress:u,responseType:o,withCredentials:a,transferCache:i})}},Nt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Nt||{}),Br=class{constructor(t,n=$r.Ok,r="OK"){this.headers=t.headers||new et,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},us=class e extends Br{constructor(t={}){super(t),this.type=Nt.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},tn=class e extends Br{constructor(t={}){super(t),this.type=Nt.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},_t=class extends Br{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},$r=function(e){return e[e.Continue=100]="Continue",e[e.SwitchingProtocols=101]="SwitchingProtocols",e[e.Processing=102]="Processing",e[e.EarlyHints=103]="EarlyHints",e[e.Ok=200]="Ok",e[e.Created=201]="Created",e[e.Accepted=202]="Accepted",e[e.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",e[e.NoContent=204]="NoContent",e[e.ResetContent=205]="ResetContent",e[e.PartialContent=206]="PartialContent",e[e.MultiStatus=207]="MultiStatus",e[e.AlreadyReported=208]="AlreadyReported",e[e.ImUsed=226]="ImUsed",e[e.MultipleChoices=300]="MultipleChoices",e[e.MovedPermanently=301]="MovedPermanently",e[e.Found=302]="Found",e[e.SeeOther=303]="SeeOther",e[e.NotModified=304]="NotModified",e[e.UseProxy=305]="UseProxy",e[e.Unused=306]="Unused",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e[e.BadRequest=400]="BadRequest",e[e.Unauthorized=401]="Unauthorized",e[e.PaymentRequired=402]="PaymentRequired",e[e.Forbidden=403]="Forbidden",e[e.NotFound=404]="NotFound",e[e.MethodNotAllowed=405]="MethodNotAllowed",e[e.NotAcceptable=406]="NotAcceptable",e[e.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",e[e.RequestTimeout=408]="RequestTimeout",e[e.Conflict=409]="Conflict",e[e.Gone=410]="Gone",e[e.LengthRequired=411]="LengthRequired",e[e.PreconditionFailed=412]="PreconditionFailed",e[e.PayloadTooLarge=413]="PayloadTooLarge",e[e.UriTooLong=414]="UriTooLong",e[e.UnsupportedMediaType=415]="UnsupportedMediaType",e[e.RangeNotSatisfiable=416]="RangeNotSatisfiable",e[e.ExpectationFailed=417]="ExpectationFailed",e[e.ImATeapot=418]="ImATeapot",e[e.MisdirectedRequest=421]="MisdirectedRequest",e[e.UnprocessableEntity=422]="UnprocessableEntity",e[e.Locked=423]="Locked",e[e.FailedDependency=424]="FailedDependency",e[e.TooEarly=425]="TooEarly",e[e.UpgradeRequired=426]="UpgradeRequired",e[e.PreconditionRequired=428]="PreconditionRequired",e[e.TooManyRequests=429]="TooManyRequests",e[e.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",e[e.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",e[e.InternalServerError=500]="InternalServerError",e[e.NotImplemented=501]="NotImplemented",e[e.BadGateway=502]="BadGateway",e[e.ServiceUnavailable=503]="ServiceUnavailable",e[e.GatewayTimeout=504]="GatewayTimeout",e[e.HttpVersionNotSupported=505]="HttpVersionNotSupported",e[e.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",e[e.InsufficientStorage=507]="InsufficientStorage",e[e.LoopDetected=508]="LoopDetected",e[e.NotExtended=510]="NotExtended",e[e.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired",e}($r||{});function Ac(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var o0=(()=>{let t=class t{constructor(r){this.handler=r}request(r,o,i={}){let s;if(r instanceof Lr)s=r;else{let c;i.headers instanceof et?c=i.headers:c=new et(i.headers);let l;i.params&&(i.params instanceof St?l=i.params:l=new St({fromObject:i.params})),s=new Lr(r,o,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:l,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let a=er(s).pipe(Is(c=>this.handler.handle(c)));if(r instanceof Lr||i.observe==="events")return a;let u=a.pipe(jt(c=>c instanceof tn));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return u.pipe(ae(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return u.pipe(ae(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return u.pipe(ae(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return u.pipe(ae(c=>c.body))}case"response":return u;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(r,o={}){return this.request("DELETE",r,o)}get(r,o={}){return this.request("GET",r,o)}head(r,o={}){return this.request("HEAD",r,o)}jsonp(r,o){return this.request("JSONP",r,{params:new St().append(o,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(r,o={}){return this.request("OPTIONS",r,o)}patch(r,o,i={}){return this.request("PATCH",r,Ac(i,o))}post(r,o,i={}){return this.request("POST",r,Ac(i,o))}put(r,o,i={}){return this.request("PUT",r,Ac(i,o))}};t.\u0275fac=function(o){return new(o||t)(b(jr))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),i0=/^\)\]\}',?\n/,s0="X-Request-URL";function fg(e){if(e.url)return e.url;let t=s0.toLocaleLowerCase();return e.headers.get(t)}var Oc=(()=>{let t=class t{constructor(){var r,o;this.fetchImpl=(o=(r=D(Fc,{optional:!0}))==null?void 0:r.fetch)!=null?o:fetch.bind(globalThis),this.ngZone=D(q)}handle(r){return new A(o=>{let i=new AbortController;return this.doRequest(r,i.signal,o).then(kc,s=>o.error(new _t({error:s}))),()=>i.abort()})}doRequest(r,o,i){return Gr(this,null,function*(){var p,y,m,g;let s=this.createRequestInit(r),a;try{let _=this.fetchImpl(r.urlWithParams,re({signal:o},s));a0(_),i.next({type:Nt.Sent}),a=yield _}catch(_){i.error(new _t({error:_,status:(p=_.status)!=null?p:0,statusText:_.statusText,url:r.urlWithParams,headers:_.headers}));return}let u=new et(a.headers),c=a.statusText,l=(y=fg(a))!=null?y:r.urlWithParams,d=a.status,f=null;if(r.reportProgress&&i.next(new us({headers:u,status:d,statusText:c,url:l})),a.body){let _=a.headers.get("content-length"),z=[],O=a.body.getReader(),se=0,De,ne,tt=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>Gr(this,null,function*(){for(;;){let{done:At,value:Yn}=yield O.read();if(At)break;if(z.push(Yn),se+=Yn.length,r.reportProgress){ne=r.responseType==="text"?(ne!=null?ne:"")+(De!=null?De:De=new TextDecoder).decode(Yn,{stream:!0}):void 0;let qc=()=>i.next({type:Nt.DownloadProgress,total:_?+_:void 0,loaded:se,partialText:ne});tt?tt.run(qc):qc()}}}));let qn=this.concatChunks(z,se);try{let At=(m=a.headers.get("Content-Type"))!=null?m:"";f=this.parseBody(r,qn,At)}catch(At){i.error(new _t({error:At,headers:new et(a.headers),status:a.status,statusText:a.statusText,url:(g=fg(a))!=null?g:r.urlWithParams}));return}}d===0&&(d=f?$r.Ok:0),d>=200&&d<300?(i.next(new tn({body:f,headers:u,status:d,statusText:c,url:l})),i.complete()):i.error(new _t({error:f,headers:u,status:d,statusText:c,url:l}))})}parseBody(r,o,i){switch(r.responseType){case"json":let s=new TextDecoder().decode(o).replace(i0,"");return s===""?null:JSON.parse(s);case"text":return new TextDecoder().decode(o);case"blob":return new Blob([o],{type:i});case"arraybuffer":return o.buffer}}createRequestInit(r){var s;let o={},i=r.withCredentials?"include":void 0;if(r.headers.forEach((a,u)=>o[a]=u.join(",")),(s=o.Accept)!=null||(o.Accept="application/json, text/plain, */*"),!o["Content-Type"]){let a=r.detectContentTypeHeader();a!==null&&(o["Content-Type"]=a)}return{body:r.serializeBody(),method:r.method,headers:o,credentials:i}}concatChunks(r,o){let i=new Uint8Array(o),s=0;for(let a of r)i.set(a,s),s+=a.length;return i}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Fc=class{};function kc(){}function a0(e){e.then(kc,kc)}function u0(e,t){return t(e)}function c0(e,t,n){return(r,o)=>gu(n,()=>t(r,i=>e(i,o)))}var Lc=new M(""),Ig=new M(""),bg=new M("");var hg=(()=>{let t=class t extends jr{constructor(r,o){super(),this.backend=r,this.injector=o,this.chain=null,this.pendingTasks=D(Hi);let i=D(bg,{optional:!0});this.backend=i!=null?i:r}handle(r){if(this.chain===null){let i=Array.from(new Set([...this.injector.get(Lc),...this.injector.get(Ig,[])]));this.chain=i.reduceRight((s,a)=>c0(s,a,this.injector),u0)}let o=this.pendingTasks.add();return this.chain(r,i=>this.backend.handle(i)).pipe(Wr(()=>this.pendingTasks.remove(o)))}};t.\u0275fac=function(o){return new(o||t)(b(Vr),b(Ke))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})();var l0=/^\)\]\}',?\n/;function d0(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var pg=(()=>{let t=class t{constructor(r){this.xhrFactory=r}handle(r){if(r.method==="JSONP")throw new w(-2800,!1);let o=this.xhrFactory;return(o.\u0275loadImpl?xe(o.\u0275loadImpl()):er(null)).pipe(Do(()=>new A(s=>{let a=o.build();if(a.open(r.method,r.urlWithParams),r.withCredentials&&(a.withCredentials=!0),r.headers.forEach((m,g)=>a.setRequestHeader(m,g.join(","))),r.headers.has("Accept")||a.setRequestHeader("Accept","application/json, text/plain, */*"),!r.headers.has("Content-Type")){let m=r.detectContentTypeHeader();m!==null&&a.setRequestHeader("Content-Type",m)}if(r.responseType){let m=r.responseType.toLowerCase();a.responseType=m!=="json"?m:"text"}let u=r.serializeBody(),c=null,l=()=>{if(c!==null)return c;let m=a.statusText||"OK",g=new et(a.getAllResponseHeaders()),_=d0(a)||r.url;return c=new us({headers:g,status:a.status,statusText:m,url:_}),c},d=()=>{let{headers:m,status:g,statusText:_,url:z}=l(),O=null;g!==$r.NoContent&&(O=typeof a.response>"u"?a.responseText:a.response),g===0&&(g=O?$r.Ok:0);let se=g>=200&&g<300;if(r.responseType==="json"&&typeof O=="string"){let De=O;O=O.replace(l0,"");try{O=O!==""?JSON.parse(O):null}catch(ne){O=De,se&&(se=!1,O={error:ne,text:O})}}se?(s.next(new tn({body:O,headers:m,status:g,statusText:_,url:z||void 0})),s.complete()):s.error(new _t({error:O,headers:m,status:g,statusText:_,url:z||void 0}))},f=m=>{let{url:g}=l(),_=new _t({error:m,status:a.status||0,statusText:a.statusText||"Unknown Error",url:g||void 0});s.error(_)},h=!1,p=m=>{h||(s.next(l()),h=!0);let g={type:Nt.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(g.total=m.total),r.responseType==="text"&&a.responseText&&(g.partialText=a.responseText),s.next(g)},y=m=>{let g={type:Nt.UploadProgress,loaded:m.loaded};m.lengthComputable&&(g.total=m.total),s.next(g)};return a.addEventListener("load",d),a.addEventListener("error",f),a.addEventListener("timeout",f),a.addEventListener("abort",f),r.reportProgress&&(a.addEventListener("progress",p),u!==null&&a.upload&&a.upload.addEventListener("progress",y)),a.send(u),s.next({type:Nt.Sent}),()=>{a.removeEventListener("error",f),a.removeEventListener("abort",f),a.removeEventListener("load",d),a.removeEventListener("timeout",f),r.reportProgress&&(a.removeEventListener("progress",p),u!==null&&a.upload&&a.upload.removeEventListener("progress",y)),a.readyState!==a.DONE&&a.abort()}})))}};t.\u0275fac=function(o){return new(o||t)(b(Gn))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Cg=new M(""),f0="XSRF-TOKEN",h0=new M("",{providedIn:"root",factory:()=>f0}),p0="X-XSRF-TOKEN",g0=new M("",{providedIn:"root",factory:()=>p0}),cs=class{},m0=(()=>{let t=class t{constructor(r,o,i){this.doc=r,this.platform=o,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let r=this.doc.cookie||"";return r!==this.lastCookieString&&(this.parseCount++,this.lastToken=is(r,this.cookieName),this.lastCookieString=r),this.lastToken}};t.\u0275fac=function(o){return new(o||t)(b(fe),b(Ae),b(h0))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})();function y0(e,t){let n=e.url.toLowerCase();if(!D(Cg)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=D(cs).getToken(),o=D(g0);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var jc=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(jc||{});function Mg(e,t){return{\u0275kind:e,\u0275providers:t}}function QR(...e){let t=[o0,pg,hg,{provide:jr,useExisting:hg},{provide:Vr,useExisting:pg},{provide:Lc,useValue:y0,multi:!0},{provide:Cg,useValue:!0},{provide:cs,useClass:m0}];for(let n of e)t.push(...n.\u0275providers);return On(t)}function KR(e){return Mg(jc.Interceptors,e.map(t=>({provide:Lc,useValue:t,multi:!0})))}function JR(){return Mg(jc.Fetch,[Oc,{provide:Vr,useExisting:Oc},{provide:bg,useExisting:Oc}])}var gg="b",mg="h",yg="s",vg="st",Dg="u",wg="rt",as=new M(""),v0=["GET","HEAD"];function D0(e,t){var f;let d=D(as),{isCacheActive:n}=d,r=Yc(d,["isCacheActive"]),{transferCache:o,method:i}=e;if(!n||i==="POST"&&!r.includePostRequests&&!o||i!=="POST"&&!v0.includes(i)||o===!1||((f=r.filter)==null?void 0:f.call(r,e))===!1)return t(e);let s=D(Tt),a=E0(e),u=s.get(a,null),c=r.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(c=o.includeHeaders),u){let{[gg]:h,[wg]:p,[mg]:y,[yg]:m,[vg]:g,[Dg]:_}=u,z=h;switch(p){case"arraybuffer":z=new TextEncoder().encode(h).buffer;break;case"blob":z=new Blob([h]);break}let O=new et(y);return er(new tn({body:z,headers:O,status:m,statusText:g,url:_}))}let l=kr(D(Ae));return t(e).pipe(wo(h=>{h instanceof tn&&l&&s.set(a,{[gg]:h.body,[mg]:w0(h.headers,c),[yg]:h.status,[vg]:h.statusText,[Dg]:h.url||"",[wg]:e.responseType})}))}function w0(e,t){if(!t)return{};let n={};for(let r of t){let o=e.getAll(r);o!==null&&(n[r]=o)}return n}function Eg(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function E0(e){let{params:t,method:n,responseType:r,url:o}=e,i=Eg(t),s=e.serializeBody();s instanceof URLSearchParams?s=Eg(s):typeof s!="string"&&(s="");let a=[n,r,o,s,i].join("|"),u=I0(a);return u}function I0(e){let t=0;for(let n of e)t=Math.imul(31,t)+n.charCodeAt(0)<<0;return t+=**********,t.toString()}function Tg(e){return[{provide:as,useFactory:()=>(xt("NgHttpTransferCache"),re({isCacheActive:!0},e))},{provide:Ig,useValue:D0,multi:!0,deps:[Tt,as]},{provide:Wi,multi:!0,useFactory:()=>{let t=D(en),n=D(as);return()=>{cc(t).then(()=>{n.isCacheActive=!1})}}}]}var $c=class extends ts{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Uc=class e extends $c{static makeCurrent(){tg(new e)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.parentNode&&t.parentNode.removeChild(t)}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=b0();return n==null?null:C0(n)}resetBaseElement(){Ur=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return is(document.cookie,t)}},Ur=null;function b0(){return Ur=Ur||document.querySelector("base"),Ur?Ur.getAttribute("href"):null}function C0(e){return new URL(e,document.baseURI).pathname}var M0=(()=>{let t=class t{build(){return new XMLHttpRequest}};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Hc=new M(""),Ng=(()=>{let t=class t{constructor(r,o){this._zone=o,this._eventNameToPlugin=new Map,r.forEach(i=>{i.manager=this}),this._plugins=r.slice().reverse()}addEventListener(r,o,i){return this._findPluginFor(o).addEventListener(r,o,i)}getZone(){return this._zone}_findPluginFor(r){let o=this._eventNameToPlugin.get(r);if(o)return o;if(o=this._plugins.find(s=>s.supports(r)),!o)throw new w(5101,!1);return this._eventNameToPlugin.set(r,o),o}};t.\u0275fac=function(o){return new(o||t)(b(Hc),b(q))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),ls=class{constructor(t){this._doc=t}},Vc="ng-app-id",Ag=(()=>{let t=class t{constructor(r,o,i,s={}){this.doc=r,this.appId=o,this.nonce=i,this.platformId=s,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=kr(s),this.resetHostNodes()}addStyles(r){for(let o of r)this.changeUsageCount(o,1)===1&&this.onStyleAdded(o)}removeStyles(r){for(let o of r)this.changeUsageCount(o,-1)<=0&&this.onStyleRemoved(o)}ngOnDestroy(){let r=this.styleNodesInDOM;r&&(r.forEach(o=>o.remove()),r.clear());for(let o of this.getAllStyles())this.onStyleRemoved(o);this.resetHostNodes()}addHost(r){this.hostNodes.add(r);for(let o of this.getAllStyles())this.addStyleToHost(r,o)}removeHost(r){this.hostNodes.delete(r)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(r){for(let o of this.hostNodes)this.addStyleToHost(o,r)}onStyleRemoved(r){var i,s;let o=this.styleRef;(s=(i=o.get(r))==null?void 0:i.elements)==null||s.forEach(a=>a.remove()),o.delete(r)}collectServerRenderedStyles(){var o;let r=(o=this.doc.head)==null?void 0:o.querySelectorAll(`style[${Vc}="${this.appId}"]`);if(r!=null&&r.length){let i=new Map;return r.forEach(s=>{s.textContent!=null&&i.set(s.textContent,s)}),i}return null}changeUsageCount(r,o){let i=this.styleRef;if(i.has(r)){let s=i.get(r);return s.usage+=o,s.usage}return i.set(r,{usage:o,elements:[]}),o}getStyleElement(r,o){let i=this.styleNodesInDOM,s=i==null?void 0:i.get(o);if((s==null?void 0:s.parentNode)===r)return i.delete(o),s.removeAttribute(Vc),s;{let a=this.doc.createElement("style");return this.nonce&&a.setAttribute("nonce",this.nonce),a.textContent=o,this.platformIsServer&&a.setAttribute(Vc,this.appId),r.appendChild(a),a}}addStyleToHost(r,o){var u;let i=this.getStyleElement(r,o),s=this.styleRef,a=(u=s.get(o))==null?void 0:u.elements;a?a.push(i):s.set(o,{elements:[i],usage:1})}resetHostNodes(){let r=this.hostNodes;r.clear(),r.add(this.doc.head)}};t.\u0275fac=function(o){return new(o||t)(b(fe),b(xi),b(Au,8),b(Ae))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Bc={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},Wc=/%COMP%/g,Og="%COMP%",T0=`_nghost-${Og}`,x0=`_ngcontent-${Og}`,_0=!0,S0=new M("",{providedIn:"root",factory:()=>_0});function N0(e){return x0.replace(Wc,e)}function A0(e){return T0.replace(Wc,e)}function Rg(e,t){return t.map(n=>n.replace(Wc,e))}var xg=(()=>{let t=class t{constructor(r,o,i,s,a,u,c,l=null){this.eventManager=r,this.sharedStylesHost=o,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=a,this.platformId=u,this.ngZone=c,this.nonce=l,this.rendererByCompId=new Map,this.platformIsServer=kr(u),this.defaultRenderer=new Hr(r,a,c,this.platformIsServer)}createRenderer(r,o){if(!r||!o)return this.defaultRenderer;this.platformIsServer&&o.encapsulation===$e.ShadowDom&&(o=nt(re({},o),{encapsulation:$e.Emulated}));let i=this.getOrCreateRenderer(r,o);return i instanceof ds?i.applyToHost(r):i instanceof zr&&i.applyStyles(),i}getOrCreateRenderer(r,o){let i=this.rendererByCompId,s=i.get(o.id);if(!s){let a=this.doc,u=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,f=this.platformIsServer;switch(o.encapsulation){case $e.Emulated:s=new ds(c,l,o,this.appId,d,a,u,f);break;case $e.ShadowDom:return new zc(c,l,r,o,a,u,this.nonce,f);default:s=new zr(c,l,o,d,a,u,f);break}i.set(o.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}};t.\u0275fac=function(o){return new(o||t)(b(Ng),b(Ag),b(xi),b(S0),b(fe),b(Ae),b(q),b(Au))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Hr=class{constructor(t,n,r,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(Bc[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(_g(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(_g(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){t&&t.removeChild(n)}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new w(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Bc[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Bc[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Je.DashCase|Je.Important)?t.style.setProperty(n,r,o&Je.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Je.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r){if(typeof t=="string"&&(t=zn().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function _g(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var zc=class extends Hr{constructor(t,n,r,o,i,s,a,u){super(t,i,s,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let c=Rg(o.id,o.styles);for(let l of c){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=l,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(this.nodeOrShadowRoot(t),n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},zr=class extends Hr{constructor(t,n,r,o,i,s,a,u){super(t,i,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=u?Rg(u,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},ds=class extends zr{constructor(t,n,r,o,i,s,a,u){let c=o+"-"+r.id;super(t,n,r,i,s,a,u,c),this.contentAttr=N0(c),this.hostAttr=A0(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},O0=(()=>{let t=class t extends ls{constructor(r){super(r)}supports(r){return!0}addEventListener(r,o,i){return r.addEventListener(o,i,!1),()=>this.removeEventListener(r,o,i)}removeEventListener(r,o,i){return r.removeEventListener(o,i)}};t.\u0275fac=function(o){return new(o||t)(b(fe))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})(),Sg=["alt","control","meta","shift"],R0={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},P0={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},F0=(()=>{let t=class t extends ls{constructor(r){super(r)}supports(r){return t.parseEventName(r)!=null}addEventListener(r,o,i){let s=t.parseEventName(o),a=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>zn().onAndCancel(r,s.domEventName,a))}static parseEventName(r){let o=r.toLowerCase().split("."),i=o.shift();if(o.length===0||!(i==="keydown"||i==="keyup"))return null;let s=t._normalizeKey(o.pop()),a="",u=o.indexOf("code");if(u>-1&&(o.splice(u,1),a="code."),Sg.forEach(l=>{let d=o.indexOf(l);d>-1&&(o.splice(d,1),a+=l+".")}),a+=s,o.length!=0||s.length===0)return null;let c={};return c.domEventName=i,c.fullKey=a,c}static matchEventFullKeyCode(r,o){let i=R0[r.key]||r.key,s="";return o.indexOf("code.")>-1&&(i=r.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Sg.forEach(a=>{if(a!==i){let u=P0[a];u(r)&&(s+=a+".")}}),s+=i,s===o)}static eventCallback(r,o,i){return s=>{t.matchEventFullKeyCode(s,r)&&i.runGuarded(()=>o(s))}}static _normalizeKey(r){return r==="esc"?"escape":r}};t.\u0275fac=function(o){return new(o||t)(b(fe))},t.\u0275prov=x({token:t,factory:t.\u0275fac});let e=t;return e})();function dP(e,t){return Vp(re({rootComponent:e},k0(t)))}function k0(e){var t;return{appProviders:[...$0,...(t=e==null?void 0:e.providers)!=null?t:[]],platformProviders:B0}}function L0(){Uc.makeCurrent()}function j0(){return new He}function V0(){return Hf(document),document}var B0=[{provide:Ae,useValue:Nc},{provide:Nu,useValue:L0,multi:!0},{provide:fe,useFactory:V0,deps:[]}];var $0=[{provide:wi,useValue:"root"},{provide:He,useFactory:j0,deps:[]},{provide:Hc,useClass:O0,multi:!0,deps:[fe,q,Ae]},{provide:Hc,useClass:F0,multi:!0,deps:[fe]},xg,Ag,Ng,{provide:br,useExisting:xg},{provide:Gn,useClass:M0,deps:[]},[]];var fP=(()=>{let t=class t{constructor(r){this._doc=r}getTitle(){return this._doc.title}setTitle(r){this._doc.title=r||""}};t.\u0275fac=function(o){return new(o||t)(b(fe))},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();var U0=(()=>{let t=class t{};t.\u0275fac=function(o){return new(o||t)},t.\u0275prov=x({token:t,factory:function(o){let i=null;return o?i=new(o||t):i=b(H0),i},providedIn:"root"});let e=t;return e})(),H0=(()=>{let t=class t extends U0{constructor(r){super(),this._doc=r}sanitize(r,o){if(o==null)return null;switch(r){case ze.NONE:return o;case ze.HTML:return ct(o,"HTML")?Oe(o):Lu(this._doc,String(o)).toString();case ze.STYLE:return ct(o,"Style")?Oe(o):o;case ze.SCRIPT:if(ct(o,"Script"))return Oe(o);throw new w(5200,!1);case ze.URL:return ct(o,"URL")?Oe(o):Ai(String(o));case ze.RESOURCE_URL:if(ct(o,"ResourceURL"))return Oe(o);throw new w(5201,!1);default:throw new w(5202,!1)}}bypassSecurityTrustHtml(r){return Qf(r)}bypassSecurityTrustStyle(r){return Kf(r)}bypassSecurityTrustScript(r){return Jf(r)}bypassSecurityTrustUrl(r){return Xf(r)}bypassSecurityTrustResourceUrl(r){return eh(r)}};t.\u0275fac=function(o){return new(o||t)(b(fe))},t.\u0275prov=x({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})(),Gc=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e}(Gc||{});function hP(...e){let t=[],n=new Set,r=n.has(Gc.HttpTransferCacheOptions);for(let{\u0275providers:o,\u0275kind:i}of e)n.add(i),o.length&&t.push(o);return On([[],$p(),n.has(Gc.NoHttpTransferCache)||r?[]:Tg({}),t])}export{Y as a,Lg as b,A as c,Ts as d,xs as e,_e as f,tr as g,vm as h,Lt as i,xe as j,er as k,im as l,Dm as m,ot as n,wm as o,ae as p,tm as q,rt as r,Jn as s,mo as t,Em as u,Im as v,Ps as w,um as x,Sm as y,jt as z,qg as A,Es as B,Is as C,rm as D,Xn as E,bs as F,om as G,Wr as H,Cs as I,Ms as J,am as K,cm as L,Il as M,lm as N,Do as O,dm as P,wo as Q,w as R,Ad as S,x as T,mi as U,pO as V,M as W,P as X,b as Y,D as Z,lu as _,Vd as $,Dt as aa,gO as ba,Di as ca,xr as da,Kt as ea,On as fa,Dy as ga,Ke as ha,gu as ia,Ny as ja,sf as ka,mO as la,yO as ma,vO as na,DO as oa,wO as pa,bv as qa,Ct as ra,Su as sa,EO as ta,Mt as ua,je as va,Hf as wa,xi as xa,Nu as ya,Ae as za,IO as Aa,Tt as Ba,Vv as Ca,_o as Da,ze as Ea,bO as Fa,lD as Ga,CO as Ha,MO as Ia,TO as Ja,xO as Ka,K as La,_O as Ma,Yt as Na,ni as Oa,br as Pa,Ui as Qa,q as Ra,uE as Sa,Bn as Ta,AO as Ua,HE as Va,ZE as Wa,qa as Xa,KE as Ya,Hi as Za,eI as _a,rI as $a,DI as ab,rp as bb,wI as cb,OO as db,RO as eb,cp as fb,lp as gb,PI as hb,jI as ib,VI as jb,PO as kb,UI as lb,KI as mb,FO as nb,kO as ob,LO as pb,eb as qb,mp as rb,jO as sb,VO as tb,BO as ub,$O as vb,UO as wb,HO as xb,ob as yb,vp as zb,ib as Ab,sb as Bb,ab as Cb,ub as Db,cb as Eb,lb as Fb,zO as Gb,db as Hb,GO as Ib,WO as Jb,qO as Kb,YO as Lb,ZO as Mb,QO as Nb,KO as Ob,JO as Pb,XO as Qb,eR as Rb,tR as Sb,nR as Tb,rR as Ub,bp as Vb,Cp as Wb,wb as Xb,Gi as Yb,Ib as Zb,Wi as _b,en as $b,cc as ac,oR as bc,Pp as cc,Fp as dc,dc as ec,Vb as fc,iR as gc,Kb as hc,sR as ic,aR as jc,uR as kc,zn as lc,tg as mc,fe as nc,_c as oc,xR as pc,os as qc,ng as rc,eC as sc,_R as tc,tC as uc,SR as vc,NR as wc,AR as xc,OR as yc,RR as zc,PR as Ac,FR as Bc,kR as Cc,LR as Dc,jR as Ec,qC as Fc,YC as Gc,ZC as Hc,kr as Ic,VR as Jc,xc as Kc,Gn as Lc,et as Mc,St as Nc,Nt as Oc,tn as Pc,_t as Qc,o0 as Rc,Ig as Sc,QR as Tc,KR as Uc,JR as Vc,Uc as Wc,Hc as Xc,ls as Yc,xg as Zc,dP as _c,fP as $c,U0 as ad,hP as bd};
